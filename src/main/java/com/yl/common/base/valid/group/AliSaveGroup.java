package com.yl.common.base.valid.group;


import com.yl.common.base.valid.annation.FromOperation;
import com.yl.common.base.valid.enums.FromEnum;
import com.yl.common.base.valid.enums.OperationEnum;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-08-02 14:05 <br>
 * @Author: <a href="<EMAIL>">qiang.hu</a>
 */
@FromOperation(from = FromEnum.ALI,operation = OperationEnum.SAVE)
public interface AliSaveGroup {
}
