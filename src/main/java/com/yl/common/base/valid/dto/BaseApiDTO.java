package com.yl.common.base.valid.dto;

import com.yl.common.base.annotation.log.operation.OperationParamLog;
import com.yl.common.base.enums.log.OperationParamTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-08-03 16:41 <br>
 * @Author: <a href="<EMAIL>">qiang.hu</a>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
//@ApiModel(value = "apiDto请求基类", description = "apiDto请求基类")
public abstract class BaseApiDTO {
    @ApiModelProperty(value = "请求来源 1.jms 2.edi 3.巴枪app 4.PCCS")
    @OperationParamLog(message = "请求来源 1.jms 2.edi 3.巴枪app 4.PCCS", type = OperationParamTypeEnum.ID)
    protected Integer from;
}
