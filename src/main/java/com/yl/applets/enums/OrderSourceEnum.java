/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: OrderSourceEnum
 * Author:   luhong
 * Date:     2020-10-15 11:45
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.enums;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-15
 * @since 1.0.0
 */
public enum OrderSourceEnum {
    CUSTOMER_APP("D01", "客户APP"),
    VIP_SYSTEM("D02", "VIP系统"),
    MALL("D03", "电商客户"),
    BQ_PDA("D04", "巴枪PDA"),
    CALL_CENTER("D05", "呼叫中心"),
    JMS("D06", "JMS"),
    API("D07", "标准API"),
    WX_MINIPROGRAM("D08", "微信小程序"),
    PDD("D09", "拼多多"),
    JD("D10", "京东"),
    ALI("D11", "菜鸟"),
    OW("D12", "官网"),
    WX_QR_NETWORK("D13", "网点二维码"),
    WX_QR_SALEMAN("D14", "业务员二维码"),
    DD("D15", "多多"),
    XF("D16", "旋风"),
    KDN("D17", "快递鸟"),
    LTDD("D18", "灵通打单"),
    WDRJ("D19", "我打软件"),
    KDZS("D20", "快递助手"),
    CMDY("D21", "传美打印"),
    GYY("D22", "管易云"),
    JST("D23", "聚水潭"),
    KRKD("D24", "宽容快递'"),
    YYJY("D25", "in有尽有"),
    CK("D26", "臣康"),
    ZZKDY("D27", "猪猪快递云"),
    HPK("D28", "海拍客"),
    KH("D29", "宽昊"),
    AKYD("D30", "阿康药店"),
    KDW("D31", "快递网"),
    KD100("D32", "快递100"),
    WZG("D33", "微掌柜"),
    JY("D34", "九曳"),
    SJ("D35", "双捷"),
    DANGDANG("D36", "当当网"),
    ZYQS("D37", "中运全速"),
    KDY("D38", "快刀云"),
    JSH("D39", "金丝猴"),
    YOUYIERP("D40", "优易erp"),
    YCD("D41", "易查单"),
    PDD_SCAN("D60", "桃花岛扫码寄件"),
    BFDF("D46", "百福东方"),
    CLOUDPRINT_KD100("D47", "云打印-快递100"),
    YJSJ("D48", "一加手机"),
    KDGJ("D51", "快递管家"),
    WX_QR_PAPER_CODE("D61", "纸质二维码"),
    JW("D42", "巨沃"),
    BYJ("D908", "毕业季"),

    ZSJ("D1133", "专属寄"),

    ;

    private static Map<String, OrderSourceEnum> ORDER_SOURCE_ENUM_MAP;
    private String code;
    private String name;

    private OrderSourceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static OrderSourceEnum of(String code, String name) {
        return StringUtils.isAllBlank(new CharSequence[]{code, name}) ? null : (OrderSourceEnum)Arrays.stream(values()).filter((e) -> {
            return e.getCode().equals(code) && e.getName().equals(name);
        }).findFirst().orElse(null);
    }

    public static OrderSourceEnum of(String code) {
        return StringUtils.isBlank(code) ? null : (OrderSourceEnum)Arrays.stream(values()).filter((e) -> {
            return e.getCode().equals(code);
        }).findFirst().orElse(null);
    }

    public static String getName(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        } else {
            OrderSourceEnum orderSourceEnum = (OrderSourceEnum)Arrays.stream(values()).filter((e) -> {
                return e.getCode().equals(code);
            }).findFirst().orElse(null);
            return Objects.isNull(orderSourceEnum) ? null : orderSourceEnum.getName();
        }
    }

    public static OrderSourceEnum getEnumByCode(String code) {
        if (CollectionUtils.isEmpty(ORDER_SOURCE_ENUM_MAP)) {
            initMap();
        }

        return (OrderSourceEnum)ORDER_SOURCE_ENUM_MAP.get(code);
    }


    private static void initMap() {
        Class var0 = OrderSourceEnum.class;
        synchronized(OrderSourceEnum.class) {
            if (CollectionUtils.isEmpty(ORDER_SOURCE_ENUM_MAP)) {
                ORDER_SOURCE_ENUM_MAP = (Map) Arrays.stream(values()).collect(Collectors.toMap(OrderSourceEnum::getCode, Function.identity(), (a, b) -> {
                    return b;
                }, ConcurrentHashMap::new));
            }

        }
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

}