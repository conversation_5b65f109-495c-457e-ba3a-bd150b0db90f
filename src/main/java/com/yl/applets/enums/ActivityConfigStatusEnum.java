package com.yl.applets.enums;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动配置状态
 * @author: xiongweibin
 * @create: 2020-08-24 18:12
 */
public enum ActivityConfigStatusEnum {
    STAY_RELEASE(1,"待发布"),
    ALREADY_RELEASE(2,"发布中"),
    STAY_OFFLINE(3,"已下线");
    private Integer code;
    private String name;

    ActivityConfigStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
