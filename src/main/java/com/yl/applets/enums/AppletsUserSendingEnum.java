package com.yl.applets.enums;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/14 14:53
 */
public enum AppletsUserSendingEnum {


     STUDENT(1,"学生"),
    ;
    private String name;
    private Integer code;
    AppletsUserSendingEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }
}
