package com.yl.applets.enums;

import java.util.Arrays;

/**
 *
 * vip流量
 * 用户行为-枚举类
 *
 */
public enum VipActionSourceEnum {


    /**
     * type对应code
     * name对应values
     */
    B1(1,1,"页面浏览","权益介绍PV","99",true),
    B2(1, 2,"页面浏览","权益介绍UV","98",true),
    B3(2, 3,"权益点击","icon-兔星薅礼","1",true),
    B4(2, 4,"权益点击","icon-1小时电联","2",true),
    B5(2, 5,"权益点击","icon-1小时赔付","3",true),
    B6(2, 6,"权益点击","icon-VIP管家","4",true),
    B7(3, 7,"权益使用","按钮-查看优惠卷","5",true),
    B8(3, 8,"权益使用","按钮-即刻下单","6",true),
    B9(3, 9,"权益使用","按钮-咨询理赔","7",true),
    B10(3, 10,"权益使用","按钮-联系管家","8",true);

    private Integer type;
    private Integer sort;


    private String name;
    private String code;

    private String values;

    private Boolean showFlag;

    VipActionSourceEnum(Integer type, Integer sort, String code, String name, String  values, Boolean showFlag) {
        this.type = type;
        this.sort = sort;
        this.code = code;
        this.name = name;
        this.values = values;
        this.showFlag = showFlag;
    }



    public Boolean getShowFlag() {
        return showFlag;
    }

    public void setShowFlag(Boolean showFlag) {
        this.showFlag = showFlag;
    }


    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValues() {
        return values;
    }

    public void setValues(String values) {
        this.values = values;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * type->code
     * @param type
     * @return
     */
    public static String getCodeByType(Integer type) {
        VipActionSourceEnum first = Arrays.stream(VipActionSourceEnum.values()).filter(r -> r.type.equals(type)).findFirst().orElse(null);
        return first==null?null:first.getCode();
    }

    /**
     * actionType->values
     * @param actionType
     * @return
     */
    public static String getNameByValues(String actionType) {
        VipActionSourceEnum first = Arrays.stream(VipActionSourceEnum.values()).filter(r -> r.values.equals(actionType)).findFirst().orElse(null);
        return first==null?null:first.getName();
    }


    /**
     * actionType->sort
     * @param actionType
     * @return
     */
    public static Integer getSortByType(String actionType) {
        VipActionSourceEnum first = Arrays.stream(VipActionSourceEnum.values()).filter(r -> r.values.equals(actionType)).findFirst().orElse(null);
        return first==null?null:first.getSort();
    }
}
