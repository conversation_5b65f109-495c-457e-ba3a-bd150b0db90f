package com.yl.applets.enums;

import io.swagger.models.auth.In;

import java.io.Serializable;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-11-19
 */
public enum BaseDictionaryEnums implements Serializable {


    /**
     *  风控类型:1.总库存2.日核销3.网点日核销4.业务员日核销
     */
    WARN_TYPE_1("WARN",1,"库存",0),
    WARN_TYPE_2("WARN",2,"日核销",0),
    WARN_TYPE_3("WARN",3,"网点日核销",0),
    WARN_TYPE_4("WARN",4,"业务员日核销",0),

    /**
     *  优惠类型1.直减2.折扣
     */
    COUPON_TYPE_1("COUPON_TYPE",1,"直减",0),
    COUPON_TYPE_2("COUPON_TYPE",2,"折扣",0),


    /**
     * 发放主体1.市场部2.品牌部0.其他
     *
     */
    GRANT_SUBJECT_0("GRANT_SUBJECT",0,"其他",0),
    GRANT_SUBJECT_1("GRANT_SUBJECT",1,"市场部",0),
    GRANT_SUBJECT_2("GRANT_SUBJECT",2,"品牌部",0),

    /**
     * 发放平台1.小程序2.网点
     */
    GRANT_PLATFORM_1("GRANT_PLATFORM",1,"小程序",0),
    GRANT_PLATFORM_2("GRANT_PLATFORM",2,"网点",0),
    GRANT_PLATFORM_3("GRANT_PLATFORM",3,"签到券",0),



    /**
     * 活动类型：连续签到3天，连续签到7天，开通星球------特别注意，下面的key不能重复使用，新活动都要用新枚举，不然优惠券中台数据库有唯一索引
     */
    ACTIVITY_TYPE_1("ACTIVITY_TYPE",1,"连续签到3天",3),
    ACTIVITY_TYPE_2("ACTIVITY_TYPE",2,"连续签到7天",7),
    ACTIVITY_TYPE_3("ACTIVITY_TYPE",3,"开通星球",0),
    ACTIVITY_TYPE_4("ACTIVITY_TYPE",4,"电信活动页",0),
    ACTIVITY_TYPE_5("ACTIVITY_TYPE",5,"王者荣耀活动页",0),
    ACTIVITY_TYPE_6("ACTIVITY_TYPE",6,"天翼支付活动页",0),
    ACTIVITY_TYPE_7("ACTIVITY_TYPE",7,"王者荣耀活动页-福利第二波",0),
    ACTIVITY_TYPE_8("ACTIVITY_TYPE",8,"兔兔优惠券",0),

    /**
     * 赠品类型：签到劵，成长值，积分
     */
    GIFT_TYPE_1("GIFT_TYPE",1,"积分",0),
    GIFT_TYPE_2("GIFT_TYPE",2,"成长值",0),
    GIFT_TYPE_3("GIFT_TYPE",3,"签到劵",0),


    /**
     * 活动用户：全部/新用户/老用户
     */
    ACTIVITY_USER_1("ACTIVITY_USER",1,"全部",0),
    ACTIVITY_USER_2("ACTIVITY_USER",2,"新用户",0),
    ACTIVITY_USER_3("ACTIVITY_USER",3,"老用户",0),


    IMAGE_TEXT_TYPE_1("IMAGE_TEXT_TYPE",1,"签到页弹窗",0),
    IMAGE_TEXT_TYPE_2("IMAGE_TEXT_TYPE",2,"分享笔记",0),


    ;









    private String type;
    private Integer code;
    private String value;
    private Integer count;

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }


    BaseDictionaryEnums(String type, Integer code, String value, Integer count) {
        this.type = type;
        this.code = code;
        this.value = value;
        this.count = count;
    }

}
