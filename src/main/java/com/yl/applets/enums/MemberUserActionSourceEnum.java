package com.yl.applets.enums;

/**
 *
 * 会员/成长+积分配置枚举类
 * 用户行为-枚举类
 *
 */
public enum MemberUserActionSourceEnum {


    /**
     * 渠道来源,除了渠道来源，禁止别的数据写在此枚举类
     */
    ZC(1,"register","注册","注册"),
    DL(2, "login","登录","登录"),
    QD(3, "sign","签到","每日签到"),
    FX(4, "share","分享","分享"),
    XD(5, "order","下单","去下单"),
    QXXD(6, "cancelOrder","取消下单","取消下单"),
    HDQD(7, "activity","活动","活动");

    private Integer key;

    private String code;

    private String name;

    private String show;


    MemberUserActionSourceEnum(Integer key, String code, String name, String show) {
        this.key = key;
        this.code = code;
        this.name = name;
        this.show = show;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShow() {
        return show;
    }

    public void setShow(String show) {
        this.show = show;
    }

}
