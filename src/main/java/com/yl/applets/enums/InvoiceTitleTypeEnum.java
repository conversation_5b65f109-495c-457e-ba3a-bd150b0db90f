package com.yl.applets.enums;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> l<PERSON><PERSON><PERSON><PERSON>
 * @since Created in 2019-09-20
 */
public enum InvoiceTitleTypeEnum {

    PERSONAL(2, "个人"),
    COMPANY(1, "企业");

    private Integer code;

    private String name;

    InvoiceTitleTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
