package com.yl.applets.enums;

import com.yl.common.base.enums.RespCodeEnum;
import lombok.Getter;

@Getter
public enum ExcelExceptionEnum implements RespCodeEnum {
    SORT_LIST_IS_NULL(149000109, "sort list is null", "排序集合或者字段名称为空"),
    DATA_LIST_IS_NULL(149000110, "data list is null", "没有可导出的数据"),
    FILE_SIZE_ERROR(149000111, "file_size_error", "文件大于10M"),
    ;
    private int code;
    private String key;
    private String msg;

    ExcelExceptionEnum(int code, String key, String msg) {
        this.code = code;
        this.key = key;
        this.msg = msg;
    }
    @Override
    public void setArgs(Object[] var1) {

    }
}
