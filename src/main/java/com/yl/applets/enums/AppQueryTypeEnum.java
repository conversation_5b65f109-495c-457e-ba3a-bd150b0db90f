/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: AppQueryTypeEnum
 * Author:   luhong
 * Date:     2020-10-13 16:02
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.enums;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-13
 * @since 1.0.0
 */
public enum AppQueryTypeEnum {
    ALL(1, "所有数据"),
    PRODUCT_TYPE(2, "产品类型"),
    ARTICLE_TYPE(3, "物品类型"),
    MATERIAL(4, "物料管理"),
    TRANDPORT_MANNER(5, "运输方式"),
    PAY_TYPE(6, "支付类型"),
    PAYMENT_MANNER(7, "结算类型"),
    SIGN_TYPE(8, "签收方式");

    private int code;
    private String name;

    private AppQueryTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public static String getNameByCode(Integer code) {
        AppQueryTypeEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            AppQueryTypeEnum enable = var1[var3];
            if (enable.getCode() == code) {
                return enable.getName();
            }
        }

        return "";
    }

    public static AppQueryTypeEnum getQueryType(int max) {
        switch(max) {
            case 1:
                return ALL;
            case 2:
                return PRODUCT_TYPE;
            case 3:
                return ARTICLE_TYPE;
            case 4:
                return MATERIAL;
            case 5:
                return TRANDPORT_MANNER;
            case 6:
                return PAY_TYPE;
            case 7:
                return PAYMENT_MANNER;
            case 8:
                return SIGN_TYPE;
            default:
                return ALL;
        }
    }
}