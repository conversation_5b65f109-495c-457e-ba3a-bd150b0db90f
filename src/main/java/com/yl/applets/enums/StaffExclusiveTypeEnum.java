package com.yl.applets.enums;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-20
 */
public enum StaffExclusiveTypeEnum {

    NEW(1, "新码"),
    OLD(2, "老码");

    private int code;

    private String name;

    StaffExclusiveTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
