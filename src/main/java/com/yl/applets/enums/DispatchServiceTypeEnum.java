/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: DispatchServiceTypeEnum
 * Author:   luhong
 * Date:     2020-10-15 11:29
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.enums;

import com.yl.common.base.util.StringUtils;

import java.util.Arrays;
import java.util.Objects;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-15
 * @since 1.0.0
 */
public enum DispatchServiceTypeEnum {
    DISPATCH_DOOR("03", "派送上门"),
    SELF_PROMITION("04", "站点自提"),
    EXPRESS_CABINET("05", "快递柜自提"),
    SELF_COLLECTION("06", "代收点自提");

    private String code;
    private String name;

    private DispatchServiceTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        } else {
            DispatchServiceTypeEnum dispatchServiceTypeEnum = (DispatchServiceTypeEnum) Arrays.stream(values()).filter((e) -> {
                return e.code.equals(code);
            }).findFirst().get();
            return Objects.isNull(dispatchServiceTypeEnum) ? null : dispatchServiceTypeEnum.name;
        }
    }

    public static DispatchServiceTypeEnum of(String code) {
        return (DispatchServiceTypeEnum)Arrays.stream(values()).filter((r) -> {
            return r.code.equals(code);
        }).findFirst().orElse(null);
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }
}