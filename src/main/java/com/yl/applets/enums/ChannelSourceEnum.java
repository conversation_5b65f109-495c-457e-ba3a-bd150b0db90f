package com.yl.applets.enums;

import java.util.Arrays;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> hhf
 * @since Created in 2021-6-9 08:19:36
 */
public enum ChannelSourceEnum {
    /**
     * 渠道来源,除了渠道来源，禁止别的数据写在此枚举类
     */
    HTSS(1,"ntss","农腾盛世","OAUTH:ORDER_H5:TOKEN:"),
    HW(2, "huawei","华为负一屏","OAUTH:ORDER_H5:TOKEN:"),
    ZS(3, "cmb","招商银行","OAUTH:ORDER_H5:TOKEN:"),
    WX(4, "weixin","微信小程序","CHANNEL_OAUTH:APPLETS:TOKEN:"),
    BD(5, "baidu","百度小程序","OAUTH:BAIDU_APPLETS:TOKEN:"),
    HM(6, "hongmeng","鸿蒙","OAUTH:ORDER_HARMONYOSWEI:TOKEN:"),
    VIP(7, "vip","大客户","OAUTH:ORDER_VIP:TOKEN:"),
    VIP_PDD(8, "vip_pdd","兔兔打单","OAUTH:ORDER_VIP_PDD:TOKEN:"),
    MI(9, "xiaomi","小米","OAUTH:ORDER_H5:TOKEN:"),
    ALI_PAY(10, "alipay","支付宝小程序","OAUTH:ALIPAY_APPLETS:TOKEN:"),
    CMCC(20, "cmcc","中国移动","OAUTH:CMCC:TOKEN:"),
    TIANYI(21, "tianyi","翼支付","OAUTH:TIANYI:TOKEN:"),
    ;

    private Integer key;

    private String code;

    private String name;

    private String redisKey;

    public String getRedisKey() {
        return redisKey;
    }

    public void setRedisKey(String redisKey) {
        this.redisKey = redisKey;
    }

    ChannelSourceEnum(Integer key, String code, String name, String redisKey) {
        this.key = key;
        this.code = code;
        this.name = name;
        this.redisKey=redisKey;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}


