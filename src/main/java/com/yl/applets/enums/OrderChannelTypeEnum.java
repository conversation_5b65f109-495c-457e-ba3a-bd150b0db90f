package com.yl.applets.enums;

import com.yl.applets.service.OrderChannelCheck;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.util.CollectionUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/3/24 20:19
 */
public enum OrderChannelTypeEnum {



    ONLINE_SHOPPING_RETURNS(1, "Online_Shopping_Returns","网购退货","1",null),
    STUDENT_ONLY_SHIPPING(2, "Student_only_shipping","学生专享","1","studentOrderChannelCheck"),
    ;

    private Integer code;
    private String value;
    private String desc;
    private String fieldValue;
    private String checkName;
    private OrderChannelTypeEnum(Integer code, String value, String desc,String fieldValue,String checkName) {
        this.code = code;
        this.value = value;
        this.desc = desc;
        this.fieldValue = fieldValue;
        this.checkName = checkName;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public String getCheckName() {
        return checkName;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }

    public static OrderChannelTypeEnum getMappingValue(Integer numberId ,Integer code, Map<String, OrderChannelCheck> orderChannelCheckMap){
        for (OrderChannelTypeEnum anEnum : OrderChannelTypeEnum.values()) {
            if (Objects.equals(anEnum.code,code)) {
                String checkName= anEnum.getCheckName();
                if(StringUtils.isNoneBlank(checkName)){
                    orderChannelCheckMap.get(anEnum.getCheckName()).check(numberId);
                }
                return anEnum;
            }
        }
        throw new BusinessException(ResultCodeEnum.STUDENT_CHANNEL_ERROR);
    }

    public static Integer getMappingCodeByValue(String value){
        for (OrderChannelTypeEnum anEnum : OrderChannelTypeEnum.values()) {
            if (Objects.equals(anEnum.value,value)) {
                return anEnum.code;
            }
        }
        return null;
    }


    /**
     * 根据标签类型值 获取对应的枚举信息
     * orderChannelType 下单的渠道类型在业务上只会存在一条
     * @param values
     * @return
     */
    public static Integer getMappingCode(List<String> values){
        if(CollectionUtils.isEmpty(values)){
            return null;
        }
        List<String> mappingValues = Arrays.stream(OrderChannelTypeEnum.values()).map(OrderChannelTypeEnum::getValue).collect(Collectors.toList());
        //获取共同的枚举信息,业务上只会存在最多一条数据
        List<String> intersectionValue = values.stream()
                .filter(mappingValues::contains)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(intersectionValue)){
            return null;
        }
        return getMappingCodeByValue(intersectionValue.get(0));
    }

    public static List<Integer> getOrderChannelTypeList(){
        List<OrderChannelTypeEnum> orderChannelTypeEnums = Arrays.asList(OrderChannelTypeEnum.values());
        return orderChannelTypeEnums.stream().map(OrderChannelTypeEnum::getCode).collect(Collectors.toList());
    }

}
