package com.yl.applets.enums;

import java.util.Arrays;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：   用户行为记录
 * @Author： <PERSON>hanzhihong
 * @Date： 2022/07/25
 */
public enum UserActionEnum {

    SIGN_IN_PAGE("1",0,"签到弹窗"),
    SIGN_IN_BUTTON("2",1,"签到分享按钮"),
    QUERY_EQUITY_DETAIL("3",0,"查看权益详情"),
    QUERY_MAO("4",0,"权益听MAO说"),
    APPLY_VIP("5",1,"”解锁兔星VIP身份“按钮"),
    GIVE_UP_VIP("6",0,"含泪放弃”按钮"),
    REGISTER_GIFT("7",0,"注册有礼"),
    ONE_HOUR_CLAIM("8",0,"1小时赔付"),
    ONE_HOUR_MOBILE("9",0,"1小时电联"),
    VIP_MANAGE("10",0,"VIP管家"),
    QUERY_COUPON("11",0,"查看优惠卷"),
    ASK_CLAIM("12",0,"咨询理赔"),
    NOW_CREATE_ORDER("13",0,"即刻下单"),
    CALL_MANAGE("14",0,"联系管家"),






    ;

    private String code;
    private Integer type;
    private String name;

    UserActionEnum(String code, Integer type, String name) {
        this.code = code;
        this.type = type;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public static UserActionEnum getByCode(String code){
        return Arrays.stream(UserActionEnum.values()).filter(f -> code.equals(f.getCode())).findFirst().orElse(null);
    }
}
