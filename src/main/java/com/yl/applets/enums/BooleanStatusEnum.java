package com.yl.applets.enums;

import java.util.Arrays;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 * @since  Created in 2019-08-17 10:20
 * <AUTHOR>
 */
public enum BooleanStatusEnum {
    VALID(1, "是"),
    INVALID(0, "否")
    ;
    private Integer code;

    private String name;

    BooleanStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        BooleanStatusEnum first = Arrays.stream(BooleanStatusEnum.values()).filter(r -> r.code.equals(code)).findFirst().orElse(null);
        if (null == first) {
            return "";
        }
        return first.getName();
    }

    public static BooleanStatusEnum of(Integer waybillStatusCode) {
        return Arrays.stream(BooleanStatusEnum.values()).filter(r -> r.code.equals(waybillStatusCode)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }}