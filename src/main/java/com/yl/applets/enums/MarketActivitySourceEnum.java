package com.yl.applets.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/3/9 13:47
 * @description
 */
@AllArgsConstructor
@Getter
public enum MarketActivitySourceEnum {
    /**
     * - code: woda
     * name: 我打
     * - code: chuanmei
     * name: 传美
     * - code: fahuoyi
     * name: 发货易
     * - code: fenghuodi
     * name: 风火递
     * - code: jushuitan
     * name: 聚水谭
     * - code: fengsuPrint
     * name: 风速打单
     * - code: expressCat
     * name: 快递猫
     * - code: youyiErp
     * name: 优易erp
     * - code: bestsmart
     * name: 灵通打单
     * - code: kbydy
     * name: 微掌柜
     * - code: shopManage
     * name: 店管家
     * - code: guanjiapo
     * name: 管家婆
     * - code: yizhanggui
     * name: 易掌柜
     * - code: kuaidaoyun
     * name: 快刀云
     * - code: yiprint
     * name: 易打单
     * - code: expressAssist
     * name: 快递助手
     * - code: wanliCattle
     * name: 万里牛
     * - code: expressManage
     * name: 快递管家
     * - code: leafletPage
     * name: 宣传单页
     * - code: dyBIC
     * name: 抖音BIC仓
     * - code: sms
     * name: 短信
     * - code: jt
     * name: 新短信
     * - code: media
     * name: 媒体投放
     * - code: wechatmp
     * name: 极兔小程序
     * - code: web
     * name: 极兔官网
     */
    woda("woda", "我打"),
    chuanmei("chuanmei", "传美"),
    fahuoyi("fahuoyi", "发货易"),
    fenghuodi("fenghuodi", "风火递"),
    fengsuPrint("fengsuPrint", "风速打单"),
    expressCat("expressCat", "快递猫"),
    youyiErp("youyiErp", "优易erp"),
    bestsmart("bestsmart", "灵通打单"),
    kbydy("kbydy", "微掌柜"),
    shopManage("shopManage", "店管家"),
    yizhanggui("yizhanggui", "易掌柜"),
    kuaidaoyun("kuaidaoyun", "快刀云"),
    yiprint("yiprint", "易打单"),
    expressAssist("expressAssist", "快递助手"),
    wanliCattle("wanliCattle", "万里牛"),
    leafletPage("leafletPage", "宣传单页"),
    jtmp("jtmp", "微信公众号"),
    wechatmp("wechatmp", "微信小程序"),
    sms("sms", "短信"),
    ;
    private final String code;
    private final String desc;


    public static boolean contains(final String source) {
        boolean contained = false;
        for (MarketActivitySourceEnum anEnum : MarketActivitySourceEnum.values()) {
            if (StrUtil.equals(anEnum.code, source)) {
                contained = true;
            }
        }
        return contained;
    }

    public static void main(String[] args) {
        System.out.println(contains("woda"));
    }
}
