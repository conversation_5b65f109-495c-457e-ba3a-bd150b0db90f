package com.yl.applets.enums;

import java.util.Arrays;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2020
 * <p>
 * 客户证件类型枚举类
 *
 * <AUTHOR>
 * @since Created in 2020年5月20日15:33:25
 */
public enum CustomerCardTypeEnum {

    BUSINESS_LICENSE(1, "enterprise","businessLicense", "统一信用代码"),
    ORGANIZATION_CODE(2, "enterprise","organizationCode", "组织结构代码"),
    TAX_REGISTRATION(3, "enterprise","taxRegistration", "税务登记号"),

    IDENTITY_CARD(4, "persional","identityCard", "居民身份证"),
    INTERIM_IDENTITY_CARD(5, "persional","interimIdentityCard", "临时身份证"),
    RESIDENCE_BOOKLET(6, "persional","residenceBooklet", "户口簿"),
    ID_CARD_OF_PLA_SOLDIERS(7, "persional","idCardOfPLASoldiers", "中国人民解放军军人身份证"),
    ID_CARD_OF_CHINESE_ARMED_POLICE(8, "persional","idCardOfChineseArmedPolice", "中国人民武装警察身份证"),
    HOME_RETURN_PERMIT(9, "persional","homeReturnPermit", "港澳居民来往内地通行证"),
    TAIWAN_RETURN_PERMIT(10, "persional","taiwanReturnPermit", "台湾居民来往内地通行证"),
    PASSPORT_OF_FOREIGN_CITIZEN(11, "persional","passportOfForeignCitizen", "外国公民护照"),
    CHINESE_PASSPORT(12, "persional","chinesePassport", "中国公民护照"),
    FOREIGN_IDENTITY_CARD(13, "persional","foreignIdentityCard", "外国人永久居留身份证"),
    ;

    //类型id
    private int type;
    //性质
    private String nature;
    //编码
    private String code;
    //描述
    private String desc;

    CustomerCardTypeEnum(int type, String nature, String code, String desc) {
        this.nature = nature;
        this.type = type;
        this.code = code;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getNature() {
        return nature;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }

    public static CustomerCardTypeEnum getByType(Integer typeId) {
        return Arrays.stream(CustomerCardTypeEnum.values()).filter(it -> typeId.equals(it.getType())).findFirst().orElse(null);
    }
}
