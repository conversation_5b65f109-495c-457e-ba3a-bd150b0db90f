package com.yl.applets.enums;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： zhanzhihong
 * @Date： 2022/07/22
 */
public enum MemberRechargeRecordEnum {
    /**
     * applyStatus 申请状态
     */
    SUCCESS(0,"成功"),
    FAIL(1,"失败"),

    /**
     * applyType (申请方式)
     */
    RECHARGE(1, "充值"),

    /**
     * activityType(活动类型)
     */
    ZERO_YUAN_APPLY(1, "0元申请会员"),
    ;

    private Integer coed;

    private String name;

    public Integer getCoed() {
        return coed;
    }

    public void setCoed(Integer coed) {
        this.coed = coed;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    MemberRechargeRecordEnum(Integer coed, String name) {
        this.coed = coed;
        this.name = name;
    }
}
