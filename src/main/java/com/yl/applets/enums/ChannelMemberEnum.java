package com.yl.applets.enums;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-08-02
 */
public class ChannelMemberEnum {

    public static class MemberUserRedis{

        //会员等级key
        final static public String CHANNEL_MEMBER_LEVEL_CONFIG ="BAIDU:APPLETS:CHANNEL_MEMBER_LEVEL_CONFIG";

        // 存规则配置
        final static public String CHANNEL_MEMBER_SCORE_CONFIG ="BAIDU:APPLETS:CHANNEL_MEMBER_SCORE_CONFIG";

        //存用户当前成长值:grow的总值
        final static public String CHANEL_USER_GROW_MAP ="{GROWRENAME}BAIDU:APPLETS:CHANEL_USER_GROW_MAP";
        //存用户当前成长值:grow的总值：二级缓存
        final static public String CHANEL_USER_GROW_MAP_2 ="{GROWRENAME}BAIDU:APPLETS:CHANEL_USER_GROW_MAP:CACHE";

        //存用户当前积分值:score的总值
        final static public String CHANEL_USER_SCORE_MAP ="{SCORERENAME}BAIDU:APPLETS:CHANEL_USER_SCORE_MAP";
        //存用户当前积分值:score的总值:二级缓存
        final static public String CHANEL_USER_SCORE_MAP_2 ="{SCORERENAME}BAIDU:APPLETS:CHANEL_USER_SCORE_MAP:CACHE";

        //redis加锁
        final static String CHANNEL_MEMBER_LOCK = "BAIDU:APPLETS:CHANNEL_MEMBER_LOCK";

        final static public String  CHANNEL_PRE="BAIDU:APPLETS:CHANNEL";

        final static public String SCORE_LIMIT="SCORE_LIMIT";

        final static public String GROW_LIMIT="GROW_LIMIT";

        /**
         * 会员申请
         */
        public static final String  APPLY_MEMBER = "WX_APPLETS:APPLY_MEMBER:";
        public static final String  APPLY_MEMBER_EXTENSION = "WX_APPLETS:APPLY_MEMBER:EXTENSION:";

    }
}
