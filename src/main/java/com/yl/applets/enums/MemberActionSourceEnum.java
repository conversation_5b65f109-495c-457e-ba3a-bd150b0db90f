package com.yl.applets.enums;

import java.util.Arrays;

/**
 *
 * vip流量
 * 用户行为-枚举类
 *
 */
public enum MemberActionSourceEnum {


    /**
     * type对应code
     * name对应values
     */
    B1(1,1,"页面浏览","兔星登录页PV","99",true),
    B2(1, 2,"页面浏览","兔星登录页UV","98",true),
    B3(1, 3,"页面浏览","查看权益详情","97",true),
    B4(2, 4,"权益点击","权益-兔星薅礼","1",true),
    B5(2, 5,"权益点击","权益-1小时电联","2",true),
    B6(2, 6,"权益点击","权益-1小时赔付","3",true),
    B7(2, 7,"权益点击","权益-VIP管家","4",true),
    B8(3, 8,"开通情况","放弃开通兔星","5",true),
    B9(3, 9,"开通情况","兔星开通异常","6",true),
    B10(3, 10,"开通情况","兔星开通异常","7",true);

    private Integer type;
    private Integer sort;


    private String name;
    private String code;

    private String values;

    private Boolean showFlag;

    MemberActionSourceEnum(Integer type, Integer sort, String code, String name, String  values, Boolean showFlag) {
        this.type = type;
        this.sort = sort;
        this.code = code;
        this.name = name;
        this.values = values;
        this.showFlag = showFlag;
    }



    public Boolean getShowFlag() {
        return showFlag;
    }

    public void setShowFlag(Boolean showFlag) {
        this.showFlag = showFlag;
    }


    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValues() {
        return values;
    }

    public void setValues(String values) {
        this.values = values;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }



    /**
     * type->code
     * @param type
     * @return
     */
    public static String getCodeByType(Integer type) {
        MemberActionSourceEnum first = Arrays.stream(MemberActionSourceEnum.values()).filter(r -> r.type.equals(type)).findFirst().orElse(null);
        return first==null?null:first.getCode();
    }

    /**
     * actionType->values
     * @param actionType
     * @return
     */
    public static String getNameByValues(String actionType) {
        MemberActionSourceEnum first = Arrays.stream(MemberActionSourceEnum.values()).filter(r -> r.values.equals(actionType)).findFirst().orElse(null);
        return first==null?null:first.getName();
    }


    /**
     * actionType->sort
     * @param actionType
     * @return
     */
    public static Integer getSortByType(String actionType) {
        MemberActionSourceEnum first = Arrays.stream(MemberActionSourceEnum.values()).filter(r -> r.values.equals(actionType)).findFirst().orElse(null);
        return first==null?null:first.getSort();
    }

}
