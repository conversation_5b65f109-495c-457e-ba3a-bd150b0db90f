/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: SearchUnit
 * Author:   luhong
 * Date:     2020-11-30 16:50
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.enums;

import lombok.Data;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-11-30
 * @since 1.0.0
 */
@Data
public class SearchUnit {
    private SearchTypeEnum searchTypeEnum;
    private String[] fields;
    private Object[] values;
    private String minimumShouldMatch;
    private RangeEnum[] ranges;

    public SearchUnit fields(String... fields) {
        this.fields = fields;
        return this;
    }

    public SearchUnit values(Object... values) {
        this.values = values;
        return this;
    }

    public SearchUnit ranges(RangeEnum... ranges) {
        this.ranges = ranges;
        return this;
    }

    public SearchUnit minimumShouldMatch(Integer percentage) {
        this.minimumShouldMatch = String.valueOf(percentage).concat("%");
        return this;
    }

    public SearchUnit setSearchTypeEnum(SearchTypeEnum searchTypeEnum) {
        this.searchTypeEnum = searchTypeEnum;
        return this;
    }
}