/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: OperationTypeEnum
 * Author:   luhong
 * Date:     2020-10-14 17:42
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.enums;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-14
 * @since 1.0.0
 */
public enum OperationTypeEnum {
    ABNORMAL_PIECE_SCAN,
    ARRIVAL_SCAN,
    DELIVERY_OUT,
    EXPRESS_COLLECTION,
    HEADLESS_REGISTER,
    LOADING_SCAN,
    PACK_SCAN,
    RECEIPT,
    REMAIN_STORAGE_SCAN,
    SEND_SCAN,
    SIGNING_SCAN,
    UNLOADING_SCAN,
    UNPACK_SCAN,
    UNLOADING_ARRIVAL_SCAN,
    UNLOADING_ARRIVAL_DELIVERY_SCAN,
    PACK_SEND_SCAN,
    PARTNER_RECEIPT,
    PARTNER_SIGNING_SCAN,
    PARTNER_DELIVERY_OUT,
    LOADING_SEND_SCAN,
    REBA<PERSON>KEXPRESS,
    INTERCEPTOR_SCAN,
    INTERCEPTOR_REGISTRATION,
    UNCAR_SCAN,
    COLLECTION_BY_PROXY,
    VEHICLE_ARRIVAL_SCAN,
    COLLECTION_RECEIPT_PACK,
    COLLECTION_RECEIPT_LOADSEND;

    private OperationTypeEnum() {
    }
}