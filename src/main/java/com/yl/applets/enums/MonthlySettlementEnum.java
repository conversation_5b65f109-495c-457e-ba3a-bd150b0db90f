package com.yl.applets.enums;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/12/23 10:14
 */
public interface MonthlySettlementEnum {

     enum MonthlySettlementPayEnumPay{

         PP_PM("PP_PM","寄付月结"),
         PP_CASH("PP_CASH","寄付现结"),
         CC_CASH("CC_CASH","到付现结"),
            ;
        public String code;
        public String name;
         MonthlySettlementPayEnumPay(String code, String name){
            this.code = code;
            this.name = name;
        }

    }


    enum MonthlySettlementEnableEnum{

        ENABLE(1,"启用"),
        NO_ENABLE(2,"不启用"),
        ;
        public Integer code;
        public String name;
        MonthlySettlementEnableEnum(Integer code, String name){
            this.code = code;
            this.name = name;
        }

    }

    enum MonthlySettlementRabbitDeliveryEnum{

        TYD(1,"TYD"),
        NO_TYD(2,"NO_TYD"),
        ;
        public Integer code;
        public String name;
        MonthlySettlementRabbitDeliveryEnum(Integer code, String name){
            this.code = code;
            this.name = name;
        }

    }


    enum MonthlySettlementShareEnum{

        SHARE(1,"共享客户"),
        NO_SHARE(2,"非共享客户"),
        ;
        public Integer code;
        public String name;
        MonthlySettlementShareEnum(Integer code, String name){
            this.code = code;
            this.name = name;
        }

    }


}
