package com.yl.applets.enums;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/26 20:20
 */
public enum OrderPromotionproductTypeEnum {

    ALL(0, "全部","default_no"),
    TUYOUDA(1, "兔优达","TYD"),
    STANDARD_EXPRESS(2, "标准快递","EZ");
    private final Integer code;
    private final String description;
    private final String expressTypeCode;

    OrderPromotionproductTypeEnum(Integer code, String description, String expressTypeCode) {
        this.code = code;
        this.description = description;
        this.expressTypeCode=expressTypeCode;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String getExpressTypeCode() {
        return expressTypeCode;
    }

    public static String getDescriptionByCode(Integer code) {
        for (OrderPromotionproductTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDescription();
            }
        }
        return null;
    }

    public static OrderPromotionproductTypeEnum getTypeEnum(Integer code){
        for (OrderPromotionproductTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static Integer getCodeByExpressCode(String expressTypeCode){
        for (OrderPromotionproductTypeEnum value : values()) {
            if (value.getExpressTypeCode().equals(expressTypeCode)) {
                return value.getCode();
            }
        }
        return null;
    }
}
