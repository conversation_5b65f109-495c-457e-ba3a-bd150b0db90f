package com.yl.applets.enums;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-09-22 14:24
 */
public enum SmsSourceEnum {
    APPLES("test001","小程序"),
    BAIDU("22","百度"),
    ;
    private String code;
    private String name;

    SmsSourceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
