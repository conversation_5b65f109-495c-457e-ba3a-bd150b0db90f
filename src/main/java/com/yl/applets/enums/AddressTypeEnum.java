package com.yl.applets.enums;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-20
 */
public enum AddressTypeEnum {

    SEND(1, "寄件"),
    RECEIVE(2, "收件");

    private int code;

    private String name;

    AddressTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
