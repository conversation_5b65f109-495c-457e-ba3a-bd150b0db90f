package com.yl.applets.enums;

import java.util.Arrays;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: 订单状态
 * @date 2021-08-02 15:16
 */

public enum OmsOrderStatusEnum {
    unassigned(100, "未调派"),
    dispatch_proxy_area(106, "己调派代理区"),
    dispatched_network(101, "已调派网点"),
    dispatched_saleman(102, "已调派业务员"),
    already_pick(103, "已取件"),
    already_cancle(104, "已取消"),
    pick_fail(105, "取件失败"),
    ;
    private Integer code;

    private String name;

    OmsOrderStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static OmsOrderStatusEnum of(Integer orderStatusCode) {
        return Arrays.stream(OmsOrderStatusEnum.values()).filter(r -> r.code.equals(orderStatusCode)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}