package com.yl.applets.enums;

import com.yl.common.base.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: 运单状态
 * @Project:
 * @CreateDate: Created in 2019-07-30 18:00
 * @Author: <a href="<EMAIL>">chunlin.zhang</a>
 */
public enum OrderMarkingEnum {

    SHPH_GZR_SM("SHPH_GZR_SM", "送货上门","工作日"),
    SHPH_GZR_YZ("SHPH_GZR_YZ", "代收点","工作日"),
    SHPH_GZR_KDG("SHPH_GZR_KDG", "快递柜","工作日"),
    SHPH_XXR_SM("SHPH_XXR_SM", "送货上门","休息日"),
    SHPH_XXR_YZ("SHPH_XXR_YZ", "代收点","休息日"),
    SHPH_XXR_KDG("SHPH_XXR_KDG", "快递柜","休息日"),

    ;


    private String code;

    private String name;


    private String type;



    OrderMarkingEnum(String code, String name, String type) {
        this.code = code;
        this.name = name;
        this.type = type;
    }

    public static OrderMarkingEnum of(String code) {
        return Arrays.stream(OrderMarkingEnum.values()).filter(r -> r.code.equals(code)).findFirst().orElse(null);
    }



    public static String ofType(String code) {
        List<String> collect = Arrays.stream(OrderMarkingEnum.values()).filter(r -> r.code.equals(code)).map(OrderMarkingEnum::getType).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(collect)){
            return collect.get(0);
        }
        return null;
    }

    public static Boolean check(List<String> codes) {
        if(CollectionUtils.isEmpty(codes)){
            return false;
        }
        List<String> codeList = Arrays.stream(OrderMarkingEnum.values()).map(OrderMarkingEnum::getCode).collect(Collectors.toList());
        for (String code : codes) {
            if (!codeList.contains(code)) {
                return false;
            }
        }
        return true;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}