package com.yl.applets.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-10-12 17:49
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum EquipmentEnum {

    WINDOWS("windows", "01", "PC"),
    IOS("ios", "02", "手机"),
    ANDROID("android", "02", "手机"),
    DEVTOOLS("devtools", "99", "其他类型"),
    MAC("mac", "03", "平板电脑"),
    ;

    private String type;
    private String code;
    private String desc;

    public static String getCodeByType(String str) {
        return Arrays.stream(EquipmentEnum.values()).filter(item -> StrUtil.equals(item.getType(), str)).findFirst().orElse(DEVTOOLS).getCode();
    }

}
