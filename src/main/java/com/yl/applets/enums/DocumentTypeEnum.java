/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: DocumentTypeEnum
 * Author:   luhong
 * Date:     2020-10-15 15:05
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.enums;

import java.util.Arrays;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-15
 * @since 1.0.0
 */
public enum DocumentTypeEnum {
    BUSINESS_LICENSE(1, "统一信用代码"),
    ORGANIZATION_CODE(2, "组织机构代码"),
    TAX_REGISTRATION(3, "税务登记号"),
    IDENTITY_CARD(4, "居民身份证"),
    INTERIM_IDENTITY_CARD(5, "临时身份证"),
    RESIDENCE_BOOKLET(6, "户口簿"),
    ID_CARD_OF_PLA_SOLDIERS(7, "中国人民解放军军人身份证"),
    ID_CARD_OF_CHINESE_ARMED_POLICE(8, "中国人民武装警察身份证"),
    HOME_RETURN_PERMIT(9, "港澳居民来往内地通行证"),
    TAIWAN_RETURN_PERMIT(10, "台湾居民来往内地通行证"),
    PASSPORT_OF_FOREIGN_CITIZEN(11, "外国公民护照"),
    CHINESE_PASSPORT(12, "中国公民护照");

    private Integer code;
    private String name;

    public static String getNameByCode(Integer code) {
        DocumentTypeEnum first = (DocumentTypeEnum)Arrays.stream(values()).filter((r) -> {
            return r.code.equals(code);
        }).findFirst().orElse(null);
        return null == first ? "" : first.getName();
    }

    public static DocumentTypeEnum of(Integer code) {
        return (DocumentTypeEnum) Arrays.stream(values()).filter((r) -> {
            return r.code.equals(code);
        }).findFirst().orElse(null);
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    private DocumentTypeEnum(final Integer code, final String name) {
        this.code = code;
        this.name = name;
    }
}