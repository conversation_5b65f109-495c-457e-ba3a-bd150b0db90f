package com.yl.applets.enums;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/3/18 20:44
 */
public enum MarketActivitStatusEnum {
    NO(0,"禁用"),
    YES(1,"启用"),
    ;
    private Integer code;
    private String name;

    MarketActivitStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
