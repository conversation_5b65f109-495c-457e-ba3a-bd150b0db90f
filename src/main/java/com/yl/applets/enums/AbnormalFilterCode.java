/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: AbnormalFilterCode
 * Author:   luhong
 * Date:     2020-10-14 17:43
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.enums;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-14
 * @since 1.0.0
 */
public enum AbnormalFilterCode {
    REJECTTION("16", "拒收"),
    CANCEL_S5("S5", "取件取消"),
    CANCEL_Z5("Z5", "分拣分拨取消");

    private String code;
    private String name;

    private AbnormalFilterCode(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}