package com.yl.applets.enums.monthlysettlement;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/1/12 20:55
 */
public interface MonthlySettlementEnum {



    /**
     * 订单自动调度枚举
     */
    public enum MonthlySettlementAutoSchedulEnum{
        /***
         *   1启用
         *   2不启用

         */
        AUTOSCHEDUL_NOT("0", "不调度"),
        AUTOSCHEDUL("1", "自动调度"),
        ;
        public  String code;

        public  String description;

        MonthlySettlementAutoSchedulEnum( String code,  String description) {
            this.code = code;
            this.description = description;
        }
    }


    /**
     * 客户标签枚举
     */
    public enum MonthlySettlementCusLabelEnum{
        /***
         * 客户标签（多个逗号组合）（1-务必电联，2-送货上门，3-放入驿站，4-工作日派送，5-休息日派送，6-共享客户无需收费，7-终端寄件无需收费，8-按需配送）
         *   1启用
         *   2不启用

         */
        NUM_01("1", "务必电联"),
        NUM_02("2", "自动调度"),
        NUM_03("3", "放入驿站"),
        NUM_04("4", "工作日派送"),
        NUM_05("5", "休息日派送"),
        NUM_06("6", "共享客户无需收费"),
        NUM_07("7", "终端寄件无需收费"),
        NUM_08("8", "按需配送"),
        ;
        public  String code;

        public  String description;

        MonthlySettlementCusLabelEnum( String code,  String description) {
            this.code = code;
            this.description = description;
        }
    }


}
