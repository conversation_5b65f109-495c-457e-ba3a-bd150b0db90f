/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: SortEnum
 * Author:   luhong
 * Date:     2020-11-30 16:54
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.enums;

import java.util.Locale;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-11-30
 * @since 1.0.0
 */
public enum SortEnum {
    ASC,
    DESC;

    private SortEnum() {
    }

    public static SortEnum get(String name) {
        return valueOf(name.toUpperCase(Locale.ROOT));
    }
}