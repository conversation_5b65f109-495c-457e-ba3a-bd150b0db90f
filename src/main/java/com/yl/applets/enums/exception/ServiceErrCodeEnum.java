package com.yl.applets.enums.exception;

import com.yl.common.base.enums.RespCodeEnum;

/**
 * 业务错误代码
 */
public enum ServiceErrCodeEnum implements RespCodeEnum {

    WAYBILLNO_ISNULL(135010001, "waybillno_isnull", "运单号不能为空"),
    ORDERID_ISNULL(135010002, "orderid_isnull", "订单号不能为空"),
    PARAM_ERROR(135010003, "oauth_param_error", "参数有误"),
    LISTID_ISNULL(135010004, "listid_isnull", "清单编号不能为空"),
    SIGNID_ISNULL(135010005, "signid_isnull", "签收类型id不能为空"),
    SIGNCODE_ISNULL(135010006, "signcode_isnull", "签收类型编码不能为空"),
    SIGNNAME_ISNULL(135010007, "signname_isnull", "签收类型名称不能为空"),
    SCANTIME_ISNULL(135010008, "scantime_isnull", "扫描时间不能为空"),
    SCANPDA_ISNULL(135010009, "scanpda_isnull", "扫描PDA编号不能为空"),
    ABNORMALPIECEID_ISNULL(135010010, "abnormalpieceid_isnull", "异常件类型id不能为空"),
    ABNORMALPIECECODE_ISNULL(135010011, "abnormalpiececode_isnull", "异常件类型编码不能为空"),
    ABNORMALPIECENAME_ISNULL(135010012, "abnormalpiecename_isnull", "异常件类型名称不能为空"),
    ABNORMALTYPECODE_ISNULL(135010013, "abnormaltypecode_isnull", "问题件类型不能为空"),
    ABNORMALTYPENAME_ISNULL(135010014, "abnormaltypename_isnull", "问题件名称不能为空"),
    SALESMANID_ISNULL(135010015, "salesmanid_isnull", "业务员id不能为空"),
    SALESMANCODE_ISNULL(135010016, "salesmancode_isnull", "业务员编号不能为空"),
    SALESMANNAME_ISNULL(135010017, "salesmanname_isnull", "业务员姓名不能为空"),
    REMAINSTORAGETYPEID_ISNULL(135010018, "remainstoragetypeid_isnull", "滞留件类型id不能为空"),
    REMAINSTORAGETYPECODE_ISNULL(135010019, "remainstoragetypecode_isnull", "滞留件类型编码不能为空"),
    REMAINSTORAGETYPENAME_ISNULL(135010020, "remainstoragetypename_isnull", "滞留件类型名称不能为空"),
    PACKAGENUMBER_ISNULL(135010021, "packagenumber_isnull", "封条号/包号不能为空"),
    CARNUMBER_ISNULL(135010022, "carnumber_isnull", "车牌号不能为空"),
    DELIVERYCODE_ISNULL(135010023, "deliverycode_isnull", "派件员编号不能为空"),
    DELIVERYNAME_ISNULL(135010024, "deliveryname_isnull", "派件员姓名不能为空"),
    DELIVERYBY_ISNULL(135010025, "deliveryby_isnull", "派件员id不能为空"),
    NEXTSTOPID_ISNULL(135010026, "nextstopid_isnull", "下一站网点id不能为空"),
    NEXTSTOPCODE_ISNULL(135010027, "nextstopcode_isnull", "下一站网点编码不能为空"),
    NEXTSTOPNAME_ISNULL(135010028, "nextstopname_isnull", "下一站网点名称不能为空"),
    COMMAND_ISNULL(135010029, "command_isnull", "操作类型不能为空"),
    SCANLIST_ISNULL(135010030, "scanlist_isnull", "上传的扫描清单数据不能为空"),
    SCANLIST_SIZE_ERROR(135010031, "scanlist_size_error", "上传的扫描清单数据不能超过200条"),
    STAFF_OR_PASSWORD_ERROR(135010032, "staff_or_password_error", "工号或密码错误"),
    MODULENAME_ISNULL(135010033, "modulename_isnull", "模块名称不能为空"),
    FILETYPE_ISNULL(135010034, "filetype_isnull", "文件类型不能为空"),
    DOCUMENTATION_ISNULL(135010035, "documentation_isnull", "业务类型不能为空"),
    FILE_ISNULL(135010036, "file_isnull", "文件不能为空"),
    LOGIN_HAS_EXPIRED(135010037, "LOGIN_HAS_EXPIRED", "登录已过期"),
    MOBILE_BIND_FAIL(135010038, "mobile_bind_fail", "手机绑定失败,请重试"),
    MOBILE_EXISTS(135010039, "mobile_exists", "手机号已被绑定！"),
    NOT_BIND_MOBILE(135010040, "not_bind_mobile", "请先绑定手机号！"),
    OAUTH_NO_ACCESS(135010041, "OAUTH_NO_ACCESS", "没有操作权限"),
    LOGIN_LOCK_ERROR(135010042, "login_lock_error", "登录操作频繁，请稍后再试"),
    MOBILE_IS_ERROR(135010043, "mobile_is_error", "手机号格式不对！"),
    IS_NO_ACCESS_RESET_LOGIN(135010044, "is_no_access_reset_login", "获取用户信息失败，请退出重试！"),

    SEARCHTYPE_ISNULL(135020001, "searchtype_isnull", "查询类型不能为空"),
    SEARCHTYPE_ERROR(135020002, "searchtype_error", "查询类型有误"),
    ADDRESSID_ISNULL(135020003, "addressid_isnull", "地址id不能为空"),
    MOBILE_EQUALS(135020004, "mobile_equals", "新旧手机号不能相同"),
    VERIFICATIONCODE_ERROR(135020005, "verificationcode_error", "验证码有误"),
    VERIFICATIONCODE_ERROR_ISUSED(135020010, "verificationcode_error_isused", "短信验证码错误或已被使用！"),
    WAYBILLID_ISNULL(135020006, "waybillid_isnull", "运单id不能为空"),
    SENDVERIFICATIONCODE_ERROR(135020007, "sendverificationcode_error", "发送验证码失败"),
    VERIFICATIONCODE_TYPE_ERROR(135020008, "verificationcode_type_error", "验证码类型有误"),
    SENDVERIFICATIONCODE_HOURCOUNT_ERROR(135020009, "sendverificationcode_hourcount_error", "最近获取验证码次数过于频繁，请于%s后获取验证码"),
    SENDVERIFICATIONCODE_DAYCOUNT_ERROR(135020010, "sendverificationcode_daycount_error", "今日获取验证码次数已达上限，请于次日获取验证码"),
    SENDVERIFICATIONCODE_INTERVALTIME_ERROR(135020011, "sendverificationcode_intervaltime_error", "获取验证码的间隔时间太短，请稍后再试"),
    DETAILEDADDRESS_EQUALS(135020012, "detailedaddress_equals", "寄件详细地址和收件详细地址不能相同"),
    SIGN_ERR(135020013, "sign_err", "签名错误！"),
    SEND_ADDR_ERR(135020013, "send_addr_err", "寄件区域与业务员收派区域不一致，请重新选择！"),
    SEND_ADDR_ERR_NETWORK(135020013, "send_addr_err_network", "寄件区域与网点收派区域不一致，请重新选择！"),
    ADDR_ERR(135020015, "addr_err", "地址已存在！"),
    NO_CARD_ERR(135020016, "no_card_err", "未实名！"),
    CARD_NUM_ERR(135020017, "card_num_err", "该证件号已实名！"),
    CARD_MOBILE_ERR(135020018, "card_mobile_err", "该手机号号已实名！"),
    OP_ERR(135020019, "op_err", "操作失败！"),
    YD_NO_EXEITS(135020020, "yd_no_exeits", "无此运单信息！"),
    IDENTITY_CARD_ERR(135020021, "identity_card_err", "请输入符合规范的身份证号！"),
    IDENTITY_CARD_FILE_ERR(135020022, "identity_card_file_err", "身份证件不能为空！"),
    SYSAREA_CHECK_ERR(135020023, "sysarea_check_err", "未能识别匹配省市区"),
    ADDR_STR_ERROR(135020024, "addr_str_error", "地址格式错误"),
    NOT_OPEN_SEND_ADDR(135020025, "not_open_addr", "该寄件地址尚未开放，敬请期待"),
    NOT_OPEN_RES_ADDR(135020026, "not_open_res_addr", "该收件地址尚未开放，敬请期待"),
    IMAGE_RES_ERROR(135020027, "image_res_error", "未识别到地址信息!"),
    BIND_PRINT_ERROR(135020028, "bind_print_error", "绑定打印机失败!"),
    BIND_PWD_ERROR(135020029, "bind_pwd_error", "绑定密码错误!"),
    BIND_PWD_TIME_ERROR(135020030, "bind_pwd_time_error", "操作太频繁，请30分钟后重新尝试！"),
    EXCEED_MAXIMUM_ERROR(1350200230, "exceed_maximum_error", "批量打印最大支持10票!"),
    STOPMESSAGE_ISNULL(1350200231, "stopmessage_isnull", "网点信息不能为空"),
    PLEASE_BIND(1350200232, "please_bind", "请先绑定打印机"),
    PRINT_STATUS_ERROR(1350200233, "print_status_error", "该状态暂不支持打印！"),
    PRINT_DATE_LIMIT_ERROR(1350200234, "print_date_limit_error", "超出48小时，打印失败！"),
    WAYBILLI_ERROR(*********, "waybilli_error", "下单失败，该运单号已被其他人占用"),

    WAYBILLI_ERROR_2(*********, "waybilli_error", "下单失败，非法运单号"),
    CAN_NOT_INSURED(1350200236, "can_not_insured", "物品类型不能报价！"),
    TAXNUMBER_BLANK(1350200237, "taxnumber_blank", "税号不能为空！"),
    INVOICE_REPEAT(1350200238, "invoice_repeat", "抬头已存在！"),
    ID_IS_NULL(1350200239, "id_is_null", "id不能为空！"),
    PRINT_IS_BIND(1350200236, "print_is_bind", "打印机已绑定！"),
    URL_GET_ERROR(1350200237, "url_get_error", "上传图文消息内的图片获取URL异常！"),
    BOOK_TIME_ERROR(1350200241, "book_time_error", "该预约时间已过期，请重新选择"),
    BOOK_TIME_END_ERROR(1350200242, "book_time_end_error", "预约结束时间不能为空"),
    OP_REPETITION_ERR(*********, "op_repetition_err", "操作过于频繁,请稍后再试！"),
    OP_REPETITION_TIME_ERR(*********, "op_repetition_TIME_err", "短信发送过于频繁或超过上限，请第二天再试"),
    ORDER_IS_NULL(1350200243, "order_is_null", "订单不存在"),
    ORDER_IS_ERROR(1350200244, "order_is_error", "非法操作"),
    ORDER_UPDATE_ERROR(1350200245, "order_update_error", "不能修改多次"),
    ORDER_ADD_TIME_ERROR(1350200246, "order_add_time_error", "非法操作，不能添加预约时间"),
    CAPTCHA_ERROR(149000011, "captcha_error", "验证码错误!"),
    CAPTCHA_EX(149000012, "captcha_ex", "验证码已过期!请刷新再试"),

    PHONE_VERIFY_ERROR(1350200247, "phone_verify_error", "手机尾号校验失败"),
    USER_MOBILE_ERROR(1350200249, "user_mobile_error", "当前用户手机号不可查此运单"),
    PHONE_VERIFY_IS_NULL(1350200248, "phone_verify_is_null", "手机尾号输入错误或为空"),
    ID_CARD_NULL(1350200235, "id_card_null", "用户未实名"),

    USER_CARD_INVALID_ERR(1350200236, "user_card_invalid_err", "证件号和姓名不匹配！"),
    IS_INVALID_ORDER_ID(999010011,"is_invalid_order_id","无效的订单号！"),
    IS_USERCARD_ERROR(999010013,"is_usercard_error","已实名,请勿重复操作！"),
    COUPON_APPLETS_ADDRESS_ERROR(999010014,"coupon_applets_address_error","当前地址无优惠券活动,请刷新定位信息！"),
    COUPON_ORDER_FRIEGHT_ERROR(999010015,"coupon_order_frieght_error","下单费用有误，请重新填写订单！"),
    COUPON_NETWORK_ADDRESS_ERROR(999010016,"coupon_network_address_error","当前寄件地址不可使用优惠券,请刷新定位信息再试！"),
    ORDER_SOURCE_IS_NOT_OPS(999010017,"order_source_is_not_ops","当前订单暂不能操作"),
    UPLOAD_FILE_IMAGE_SUFFIX_NAME_ERROR(999010018,"upload_file_image_suffix_name_error","请上传正确的图片格式"),
    BESTPICK_TIME_IS_MISS(999010019,"pick_time_can_not_be_null","预约时间不能为空"),

    IDENTITY_CARD_STATUS_ERR(999010020, "identity_card_status_err", "身份证状态有误"),
    IDENTITY_CARD_RESOLVE_ERR(999010021, "identity_card_resolve_err", "身份证识别失败"),

    ORDER_MARKING_ERROR(999020001, "order_marking_error", "订单打标不合法"),
    PRE_ORDER_NO_ERROR(1350200250, "pre_order_no_error", "一段快递单号不能为空"),
    NO_XINJIANG_AREA_ERROR(1350200251, "no_xinjiang_area_error", "非新疆地区不能登记"),
    UN_PAY_ORDER_ERROR(1350200252, "un_pay_order_error", "您有%s个包裹未支付"),
    REPEAT_ORDER_ERROR(1350200253, "repeat_order_error", "此快递单号已登记过，不可再次登记"),
    UPDATE_ORDER_INFO_ERROR(1350200254, "update_order_info_error", "修改时必填信息为空"),
    QUERY_ID_INFO_ERROR(1350200255, "query_id_info_error", "必填信息为空"),
    QUERY_ORDER_INFO_ERROR(1350200256, "query_order_info_error", "必填信息为空"),
    QUERY_STATUS_INFO_ERROR(1350200257, "query_status_info_error", "查询时信息参数为空"),
    QUERY_ORDER_NOT_EXIST_ERROR(1350200258, "query_order_not_exist_error", "集运查询订单信息不存在"),
    QUERY_ORDER_EQUALS_PRE_ERROR(1350200259, "query_order_equals_pre_error", "修改时，头程运单号不一致"),
    QUERY_ORDER_ABNORMAL_ERROR(1350200260, "QUERY_ORDER_ABNORMAL_ERROR", "当前收件人信息不匹配"),
    ;

    private int code;

    private String key;

    private String msg;

    ServiceErrCodeEnum(int code, String key, String msg) {
        this.code = code;
        this.key = key;
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public void setArgs(Object[] var1) {

    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}
