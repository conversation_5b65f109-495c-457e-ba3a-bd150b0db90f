package com.yl.applets.enums.exception;

import com.yl.common.base.enums.RespCodeEnum;
import lombok.Getter;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-06-22 09:12
 * @Author: <a href="<EMAIL>">chunlin.zhang</a>
 */
@Getter
public enum OmsErrorEnum implements RespCodeEnum {
    OMS_DATE_START_ERROR(135000001, "oms_date_range_error", "开始时间大于结束时间"),
    OMS_DATE_RANGE_ERROR(135000002, "oms_date_range_error", "开始时间与结束时间之差大于30天"),
    OMS_ID_MAX_ERROR(135000003, "oms_id_max_error", "订单号超过500条"),
    OMS_WAYBILL_MAX_ERROR(135000004, "oms_waybill_max_error", "运单号超过500条"),
    OMS_SELECT_NULL_ERROR(135000005, "oms_select_null_error", "请至少勾选一条记录"),
    OMS_SELECT_OVERIDE_ERROR(135000006, "oms_select_overide_error", "勾选记录超过20条"),
    OMS_CANCLEREASON_EMPTY_ERROR(135000007, "oms_canclereason_empty_error", "取消原因不能为空"),
    OMS_DISPATCHREASON_EMPTY_ERROR(135000008, "oms_dispatchreason_empty_error", "调派原因不能为空"),
    OMS_GET_FAIL_ERROR(135000009, "oms_get_fail_error", "取件失败原因不能为空"),
    OMS_DISPATCH_NETWORK_CODE_ERROR(135000010, "oms_dispatch_network_code_error", "取件网点编码不能为空"),
    OMS_DISPATCH_NETWORK_NAME_ERROR(135000011, "oms_dispatch_network_name_error", "取件网点名称不能为空"),
    OMS_ID_WAYBILL_SAME_ERROR(135000012, "oms_id_waybill_same_error", "订单和运单不能同时输入"),
    OMS_STATUS_ERROR(135000013, "oms_status_error", "当前状态不允许此操作"),
    OMS_DISPATCH_SALEMAN_DIFF_ERROR(135000014, "oms_dispatch_saleman_diff_error", "所选数据网点不一致，请重新确认数据。"),
    OMS_DISPATCH_NETWORK_NO_ERROR(135000015, "oms_dispatch_network_no_error", "订单未调派网点"),
    OMS_DATA_TRANSFER_ERROR(135000016, "oms_data_transfer_error", "数据转换错误"),
    OMS_ORDERID_FORMAT_ERROR(135000017, "oms_orderid_format_error", "订单号格式错误"),
    OMS_DISPATCH_SALEMAN_CODE_ERROR(135000018, "oms_dispatch_saleman_code_error", "调派业务员编码不能为空"),
    OMS_DISPATCH_SALEMAN_NAME_ERROR(135000019, "oms_dispatch_saleman_name_error", "调派业务员名称不能为空"),
    OMS_CANNOT_CANCLE_ERROR(135000020, "oms_cannot_cancle_error", "存在不可取消的数据，请重新确认数据"),
    OMS_NO_AUTH_ERROR(135000021, "oms_no_auth_error", "没有权限查询该网点"),
    OMS_FEIGN_PRODUCT_TYPE_ERROR(135000022, "oms_feign_product_type_error", "快件类型数据有误"),
    OMS_FEIGN_ARTICLE_TYPE_ERROR(135000023, "oms_feign_article_type_error", "物品类型数据有误"),
    OMS_FEIGN_CUSTOMER_TYPE_ERROR(135000024, "oms_feign_customer_type_error", "客户数据有误"),
    OMS_FEIGN_PAYMENT_TYPE_ERROR(135000025, "oms_feign_payment_type_error", "付款方式数据有误"),
    OMS_FEIGN_NETWORK_ERROR(135000026, "oms_feign_network_error", "网点数据有误"),
    OMS_ORDER_SENDER_PHONE_ERROR(135000027, "oms_order_sender_phone_error", "发件人手机号码、座机必填一项"),
    OMS_ORDER_RECEIVER_PHONE_ERROR(135000028, "oms_order_receiver_phone", "收件人手机号码、座机必填一项"),
    OMS_CANCLEREASON_LONG_ERROR(135000029, "oms_canclereason_long_error", "取消原因内容过长"),
    OMS_DISPATCHREASON_LONG_ERROR(135000030, "oms_dispatchreason_long_error", "调派原因内容过长"),
    OMS_FAILREASON_LONG_ERROR(135000031, "oms_failreason_long_error", "失败原因内容过长"),
    OMS_ORDER_STATUS_EDIT_ERROR(135000032, "oms_order_edit_status_error", "当前订单状态不可编辑"),
    OMS_CANNOT_DISPATCH_NETWORK_ERROR(135000033, "oms_cannot_dispatch_network_error", "存在不可调派网点的数据，请重新确认数据"),
    OMS_CANNOT_DISPATCH_SALEMAN_ERROR(135000034, "oms_cannot_dispatch_saleman_error", "存在不可调派业务员的数据，请重新确认数据"),
    OMS_CANNOT_PICKUP_FAIL_ERROR(135000035, "oms_cannot_pickup_fail_error", "存在不可做取件失败操作的数据，请重新确认数据"),
    OMS_TASK_STATUS_ERROR(135000036, "oms_task_status_error", "存在已取件的订单，请重新确认数据"),
    OMS_VOLUME_VALUE_ERROR(135000037, "oms_volume_value_error", "体积重量为空，或者体积重量小于等于0"),
    OMS_VOLUME_VALUE_CALCULATION_ERROR(135000038, "oms_volume_value_calculation_error", "体积重量计算错误"),
    OMS_INSURED_VALUE_CANNOT_NULL(135000039, "oms_insured_value_not_null", "保费不能为空"),
    OMS_INSURED_VALUE_ERROR(135000040, "oms_insured_value_error", "保费计算错误"),
    OMS_TOTAL_FREIGHT_ERROR(135000041, "oms_total_freight_error", "总运费计算错误"),
    OMS_GENERATOR_ORDER_ID_ERROR(135000042, "oms_generator_order_id_error", "生成orderid错误"),
    OMS_PACKAGECHARGEWEIGHT_CALCULATION_ERROR(135000043, "oms_packagechargeweight_calculation_error", "包裹计费重量计算错误"),
    OMS_SEND_STATUS_ERROR(135000044, "oms_send_status_error", "该运单己发件"),
    OMS_CUSTOM_COLUMN_CODE_ERROR(135000045, "oms_custom_column_code_error", "找不到对应的列表字段"),

    OMS_PAPER_NEED_WAYBILL_ERROR(135000046, "oms_paper_need_waybill_error", "纸质面单必须填写运单编号"),
    OMS_EXISTS_WAYBILL_ERROR(135000047, "oms_exists_waybill_error", "运单编号已经存在"),
    OMS_EMPTY_STAFF_CODE_ERROR(135000048, "oms_empty_staff_code_error", "取件员编码不能为空"),
    OMS_EMPTY_STAFF_NAME_ERROR(135000049, "oms_empty_staff_name_error", "取件员名称不能为空"),

    OMS_EMPTY_PRODUCT_ID_ERROR(135000050, "oms_empty_product_id_error", "产品类型id不能为空"),
    OMS_EMPTY_PRODUCT_CODE_ERROR(135000051, "oms_empty_product_code_error", "产品类型编码不能为空"),
    OMS_EMPTY_PRODUCT_NAME_ERROR(135000052, "oms_empty_product_name_error", "产品类型名称不能为空"),

    OMS_EMPTY_DISPATCH_CODE_ERROR(135000053, "oms_empty_dispatch_code_error", "派件方式编码不能为空"),
    OMS_EMPTY_DISPATCH_NAME_ERROR(135000054, "oms_empty_dispatch_name_error", "派件方式名称不能为空"),

    OMS_EMPTY_PICKUP_TIME_ERROR(135000055, "oms_empty_pickup_time_error", "寄件时间不能为空"),
    OMS_EMPTY_PICKUP_NAME_ERROR(135000056, "oms_empty_pickup_name_error", "寄件姓名不能为空"),
    OMS_EMPTY_PROVINCE_ID_ERROR(135000057, "oms_empty_province_id_error", "寄方省份id不能为空"),
    OMS_EMPTY_PROVINCE_NAME_ERROR(135000058, "oms_empty_province_name_error", "寄方省份名称不能为空"),
    OMS_EMPTY_CITY_ID_ERROR(135000059, "oms_empty_city_id_error", "寄方城市id不能为空"),
    OMS_EMPTY_CITY_NAME_ERROR(135000060, "oms_empty_city_name_error", "寄方城市名称不能为空"),
    OMS_EMPTY_AREA_ID_ERROR(135000061, "oms_empty_area_id_error", "寄方区县id不能为空"),
    OMS_EMPTY_AREA_NAME_ERROR(135000062, "oms_empty_area_name_error", "寄方区县名称不能为空"),

    OMS_OVERLENGTH_SENDER_STREET_ERROR(135000063, "oms_overlength_sender_street_error", "寄方村庄/街道/路号/楼宇长度超过限制200个字符"),
    OMS_EMPTY_SENDER_DETAIL_STREET_ERROR(135000064, "oms_empty_sender_detail_street_error", "寄方详细地址不能为空"),
    OMS_OVERLENGTH_SENDER_DETAIL_STREET_ERROR(135000065, "oms_overlength_sender_detail_street_error", "寄方详细地址长度超过限制200个字符"),

    OMS_EMPTY_ORIGIN_ID_ERROR(135000066, "oms_empty_origin_id_error", "始发地id不能为空"),
    OMS_EMPTY_ORIGIN_CODE_ERROR(135000067, "oms_empty_origin_code_error", "始发地编码不能为空"),
    OMS_EMPTY_ORIGIN_NAME_ERROR(135000068, "oms_empty_origin_name_error", "始发地名称不能为空"),

    OMS_EMPTY_IDNO_ERROR(135000069, "oms_empty_idno_error", "身份证号码必须填写"),
    OMS_OVERLENGTH_IDNO_ERROR(135000070, "oms_overlength_idno_error", "身份证号码长度只能为15或者18个字符"),

    OMS_EMPTY_RECEIVER_NAME_ERROR(135000071, "oms_empty_receiver_name_error", "收方姓名不能为空"),
    OMS_EMPTY_RECEIVER_MOBILE_TEL_ERROR(135000072, "oms_empty_receiver_mobile_tel_error", "收件人手机号码、座机必填一项"),
    OMS_EMPTY_SENDER_MOBILE_TEL_ERROR(135000073, "oms_empty_sender_mobile_tel_error", "寄件人手机号码、座机必填一项"),

    OMS_EMPTY_RECEIVER_PROVINCE_ID_ERROR(135000074, "oms_empty_receiver_province_id_error", "收方省份id不能为空"),
    OMS_EMPTY_RECEIVER_PROVINCE_NAME_ERROR(135000075, "oms_empty_receiver_province_name_error", "收方省份名称不能为空"),
    OMS_EMPTY_RECEIVER_CITY_ID_ERROR(135000076, "oms_empty_receiver_city_id_error", "收方城市id不能为空"),
    OMS_EMPTY_RECEIVER_CITY_NAME_ERROR(135000077, "oms_empty_receiver_city_name_error", "收方城市名称不能为空"),
    OMS_EMPTY_RECEIVER_AREA_ID_ERROR(135000078, "oms_empty_receiver_area_id_error", "收方区县id不能为空"),
    OMS_EMPTY_RECEIVER_AREA_NAME_ERROR(135000079, "oms_empty_receiver_area_name_error", "收方区县名称不能为空"),

    OMS_OVERLENGTH_RECEIVER_STREET_ERROR(135000080, "oms_overlength_sender_street_error", "收方村庄/街道/路号/楼宇长度超过限制200个字符"),
    OMS_EMPTY_RECEIVER_DETAIL_STREET_ERROR(135000081, "oms_empty_sender_detail_street_error", "收方详细地址不能为空"),
    OMS_OVERLENGTH_RECEIVER_DETAIL_STREET_ERROR(135000082, "oms_overlength_sender_detail_street_error", "收方详细地址长度超过限制200个字符"),

    OMS_EMPTY_DESTINATION_ID_ERROR(135000083, "oms_empty_destination_id_error", "目的地id不能为空"),
    OMS_EMPTY_DESTINATION_CODE_ERROR(135000084, "oms_empty_destination_code_error", "目的地编码不能为空"),
    OMS_EMPTY_DESTINATION_NAME_ERROR(135000085, "oms_empty_destination_name_error", "目的地名称不能为空"),

    OMS_EMPTY_GOODSTYPE_ID_ERROR(135000086, "oms_empty_goodstype_id_error", "物品类型id不能为空"),
    OMS_EMPTY_GOODSTYPE_CODE_ERROR(135000087, "oms_empty_goodstype_code_error", "物品类型编码不能为空"),
    OMS_EMPTY_GOODSTYPE_NAME_ERROR(135000088, "oms_empty_goodstype_name_error", "物品类型名称不能为空"),

    OMS_OVERLENGTH_GOODS_LENGTH_ERROR(135000089, "oms_overlength_goods_length_error", "物品名称长度超过限制50个字符"),
    OMS_EMPTY_PACKAGE_NUMBER_ERROR(135000090, "oms_empty_package_number_error", "包裹件数不能为空"),
    OMS_PACKAGE_NUMBER_RANGE_ERROR(135000091, "oms_package_number_range_error", "包裹件数只能为1-99"),

    OMS_EMPTY_CHARGE_WEIGHT_ERROR(135000092, "oms_empty_charge_weight_error", "计费重量不能为空"),
    OMS_EMPTY_SETTELEMENT_ID_ERROR(135000093, "oms_empty_settelement_id_error", "结算方式id不能为空"),
    OMS_EMPTY_SETTELEMENT_NAME_ERROR(135000094, "oms_empty_settelement_name_error", "结算方式名称不能为空"),

    OMS_OVERLENGTH_INVOICE_LENGTH_ERROR(135000095, "oms_overlength_invoice_length_error", "发票编号长度超过限制50个字符"),
    OMS_EMPTY_COD_MONEY_ERROR(135000096, "oms_empty_cod_money_error", "代收货款金额不能为空"),
    OMS_EMPTY_COD_CODE_ERROR(135000097, "oms_empty_cod_code_error", "代收货款币别编码不能为空"),
    OMS_EMPTY_COD_NAME_ERROR(135000098, "oms_empty_cod_name_error", "代收货款币别名称不能为空"),

    OMS_EMPTY_INSURANCE_AMOUNT_ERROR(135000099, "oms_empty_insurance_amount_error", "保价金额不能为空"),
    OMS_EMPTY_RECEIPT_NO_ERROR(135000100, "oms_empty_receipt_no_error", "回单编号不能为空"),
    OMS_OVERLENGTH_REMARKS_ERROR(135000101, "oms_overlength_remarks_error", "备注长度超过限制5000个字符"),

    OMS_WAYBILL_EDIT_ERROR(135000102, "oms_waybill_edit_error", "已签收或已审核的运单不允许编辑"),

    OMS_WAYBILL_DISPATCH_CODE_ERROR(135000103, "oms_waybill_dispatch_code_error", "派件方式编码错误"),
    OMS_EMPTY_SENDER_MOBILE_ERROR(135000104, "oms_empty_sender_mobile_error", "寄件人手机号码不能为空"),
    OMS_EMPTY_RECEIVER_MOBILE_ERROR(135000105, "oms_empty_receiver_mobile_error", "收件人手机号码不能为空"),
    OMS_PACKAGE_NUMBER_RANGE_BAC_ERROR(135000106, "oms_package_number_range_bac_error", "包裹件数只能为1-999"),
    OMS_EMPTY_WEIGHT_ERROR(135000107, "oms_empty_weight_error", "重量不能为空"),
    OMS_WAYBILL_SOURCE_CODE_ERROR(135000108, "oms_waybill_source_code_error", "运单来源编码错误"),
    OMS_OVERLENGTH_PRODUCT_NAME_ERROR(135000109, "oms_overlength_product_name_error", "产品类型名称长度超过限制"),
    OMS_OVERLENGTH_DISPATCH_NAME_ERROR(135000110, "oms_overlength_dispatch_name_error", "派件方式名称长度超过限制"),

    OMS_OVERLENGTH_CUSTOMER_NAME_ERROR(135000111, "oms_overlength_customer_name_error", "客户名称长度超过限制"),
    OMS_OVERLENGTH_SENDER_PROVIDER_NAME_ERROR(135000112, "oms_overlength_sender_province_name_error", "寄件人省份名称长度超过限制"),
    OMS_OVERLENGTH_SENDER_CITY_NAME_ERROR(135000113, "oms_overlength_sender_city_name_error", "寄件人城市名称长度超过限制"),
    OMS_OVERLENGTH_SENDER_AREA_NAME_ERROR(135000114, "oms_overlength_sender_area_name_error", "寄件人区/县名称长度超过限制"),

    OMS_OVERLENGTH_RECEIVER_PROVIDER_NAME_ERROR(135000115, "oms_overlength_receiver_province_name_error", "收件人省份名称长度超过限制"),
    OMS_OVERLENGTH_RECEIVER_CITY_NAME_ERROR(135000116, "oms_overlength_receiver_city_name_error", "收件人城市名称长度超过限制"),
    OMS_OVERLENGTH_RECEIVER_AREA_NAME_ERROR(135000117, "oms_overlength_receiver_area_name_error", "收件人区/县名称长度超过限制"),
    OMS_OVERLENGTH_ORIGIN_NAME_ERROR(135000118, "oms_overlength_origin_name_error", "起始地名称长度超过限制"),
    OMS_OVERLENGTH_DESTINATION_NAME_ERROR(135000119, "oms_overlength_destination_name_error", "目的地名称长度超过限制"),
    OMS_OVERLENGTH_PAID_MODE_NAME_ERROR(135000120, "oms_overlength_paid_mode_name_error", "支付方式名称长度超过限制"),
    OMS_OVERLENGTH_INPUT_STAFF_NAME_ERROR(135000121, "oms_overlength_input_staff_name_error", "录入人名称长度超过限制"),
    OMS_NULL_ORDER_ERROR(135000122, "oms_null_order_error", "订单不存在"),
    OMS_EXIST_WAYBILL_ERROR(135000123, "oms_exist_waybill_error", "运单已存在"),
    OMS_GENERATE_WAYBILL_NO_FAIL(135000124, "oms_generate_waybill_no_fail", "获取运单号失败"),
    OMS_TWO_MOBILE_NEED_FAIL(135000125, "oms_two_mobile_need_fail", "寄件人和收件人手机号码必填"),
    OMS_RECEIVE_CODE_FAIL(135000126, "oms_receive_code_fail", "获取收件分拣码失败"),
    OMS_DISPATCH_NETWORK_FAIL(135000127, "oms_dispatch_network_fail", "获取派件网点失败"),
    OMS_PICK_NETWORK_FAIL(135000128, "oms_pick_network_fail", "获取寄件网点失败"),
    OMS_SEND_MOBILE_NEED_FAIL(135000129, "oms_send_mobile_need_fail", "寄件短信通知勾选后，请填写寄件人手机号和收件人手机号码"),
    OMS_RECEIVER_MOBILE_NEED_FAIL(135000130, "oms_receiver_mobile_need_fail", "签收短信通知勾选后，请填写寄件人手机号"),

    OMS_OVERLENGTH_PACKAGE_LENGTH_ERROR(135000131, "oms_overlength_package_length_error", "包裹总长超出了允许范围(只允许在3位整数和2位小数范围内)"),
    OMS_OVERLENGTH_PACKAGE_WIDE_ERROR(135000132, "oms_overlength_package_wide_error", "包裹总宽超出了允许范围(只允许在3位整数和2位小数范围内)"),
    OMS_OVERLENGTH_PACKAGE_HIGH_ERROR(135000133, "oms_overlength_package_high_error", "包裹总高超出了允许范围(只允许在3位整数和2位小数范围内)"),
    OMS_OVERLENGTH_CHARGEWEIGHT_ERROR(135000134, "oms_overlength_chargeweight_error", "包裹计费重量超出了允许范围(只允许在5位整数和2位小数范围内)"),
    OMS_OVERLENGTH_TOTALWEIGHT_ERROR(135000135, "oms_overlength_totalweight_error", "包裹总重量超出了允许范围(只允许在5位整数和2位小数范围内)"),

    OMS_OVERLENGTH_TOTALVOLUME_ERROR(135000136, "oms_overlength_totalvolume_error", "包裹总体积超出了允许范围(只允许在5位整数和2位小数范围内)"),
    OMS_OVERLENGTH_PACKAGEVOLUME_ERROR(135000137, "oms_overlength_packagevolume_error", "包裹体积重超出了允许范围(只允许在5位整数和2位小数范围内)"),

    OMS_OVERLENGTH_CODMONEY_ERROR(135000138, "oms_overlength_codmoney_error", "代收货款金额超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_INSURED_MONEY_ERROR(135000139, "oms_overlength_insured_amount_error", "保价金额超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_INSURED_FEE_ERROR(135000140, "oms_overlength_insured_fee_error", "保价费超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_COUPON_AMOUNT_ERROR(*********, "oms_overlength_coupon_amount_error", "优惠金额超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_PACKAGE_COST_ERROR(*********, "oms_overlength_package_cost_error", "包材费超出了允许范围(只允许在10位整数和2位小数范围内)"),

    OMS_OVERLENGTH_FREIGHT_ERROR(*********, "oms_overlength_freight_error", "运费超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_TAX_ERROR(*********, "oms_overlength_tax_error", "税金超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_HANDICRAFT_ERROR(*********, "oms_overlength_handicraft_fee_error", "手工运费超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_OTHER_ERROR(*********, "oms_overlength_other_fee_error", "其他费超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_AFTERTAX_ERROR(*********, "oms_overlength_aftertax_fee_error", "税后总运费超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_TOTALFEE_ERROR(*********, "oms_overlength_total_fee_error", "总运费超出了允许范围(只允许在10位整数和2位小数范围内)"),
    OMS_OVERLENGTH_PICKNETWORK_NAME_ERROR(*********, "oms_overlength_picknetwork_name_error", "取件网点名称长度超过限制"),
    OMS_OVERLENGTH_PICKNETWORK_CODE_ERROR(*********, "oms_overlength_picknetwork_code_error", "取件网点编码长度超过限制"),

    OMS_OVERLENGTH_ORIGIN_CODE_ERROR(*********, "oms_overlength_origin_code_error", "起始地编码长度超过限制"),
    OMS_OVERLENGTH_DESTINATION_CODE_ERROR(*********, "oms_overlength_destination_code_error", "目的地编码长度超过限制"),
    OMS_OVERLENGTH_PAID_MODE_CODE_ERROR(*********, "oms_overlength_paid_mode_code_error", "支付方式编码长度超过限制"),
    OMS_OVERLENGTH_INPUT_STAFF_CODE_ERROR(*********, "oms_overlength_input_staff_code_error", "录入人编码长度超过限制"),
    OMS_OVERLENGTH_INPUT_NETWORK_CODE_ERROR(135000155, "oms_overlength_input_network_code_error", "录入网点编码长度超过限制"),
    OMS_EMPTY_SEND_NETWORK_CODE_ERROR(135000156, "oms_empty_send_network_code_error", "寄件网点编码不能为空"),
    OMS_ORDER_SENDER_PHONE_FORMAT_ERROR(135000157, "oms_order_sender_phone_error", "发件人手机号码格式有误"),
    OMS_ORDER_SENDER_POSTAL_CODE_ERROR(135000158, "oms_order_sender_postal_code_error", "发件人邮编格式有误"),
    OMS_ORDER_RECEIVER_PHONE_FORMAT_ERROR(135000159, "oms_order_receiver_phone_format_error", "收件人手机号码格式有误"),
    OMS_ORDER_RECEIVER_POSTAL_CODE_ERROR(135000160, "oms_order_receiver_postal_code_error", "收件人邮编格式有误"),
    OMS_ORDER_BOX_NUMBER_MAX_ERROR(135000161, "oms_order_box_number_max_error", "箱子数量超过了最大值999"),
    OMS_ORDER_COUPON_CODE_ERROR(135000162, "oms_order_coupon_code_error", "优惠券只能为字母加数字且长度不能超过19位"),
    OMS_OVERLENGTH_OTHER_FEE_ERROR(135000163, "oms_overlength_other_fee_error", "其他费超出了允许范围(只允许在5位整数和2位小数范围内)"),
    OMS_OVERLENGTH_HANDICRAFT_FEE_ERROR(135000164, "oms_overlength_handicraft_fee_error", "手工运费超出了允许范围(只允许在5位整数和2位小数范围内)"),
    OMS_OVERLENGTH_COD_MONEY_ERROR(135000165, "oms_overlength_cod_money_error", "代收货款金额超出了允许范围(只允许在4位整数和2位小数范围内)"),
    OMS_OVERLENGTH_INSURED_AMOUNT_ERROR(135000166, "oms_overlength_insured_amount_error", "保价金额超出了允许范围(只允许在5位整数和2位小数范围内)"),
    OMS_EMPTY_SETTELEMENT_CODE_ERROR(135000167, "oms_empty_settelement_code_error", "结算方式编码不能为空"),
    OMS_COLLECT_TO_WAYBILL_ERROR(135000168, "oms_collect_to_waybill_error", "揽收生成运单失败"),
    OMS_OVERLENGTH_INPUT_NETWORK_NAME_ERROR(135000169, "oms_overlength_input_network_name_error", "录入网点名称长度超过限制"),

    ;

    private int code;
    private String key;
    private String msg;

    OmsErrorEnum(int code, String key, String msg) {
        this.code = code;
        this.key = key;
        this.msg = msg;
    }

    @Override
    public void setArgs(Object[] var1) {
        throw new UnsupportedOperationException();
    }

    }