package com.yl.applets.enums;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/1/13 10:02
 */
public enum OrderTypeEnum {

    TEMPORARY(1, "散客"),
    MONTHLYSETTLEMENT(2, "月结");

    private int code;

    private String name;

    OrderTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
