package com.yl.applets.enums;

/**
 *
 * 会员/成长+积分配置枚举类
 * 用户行为-枚举类
 *
 */
public enum OrderMarkingSourceEnum {


    /**
     * 渠道来源,除了渠道来源，禁止别的数据写在此枚举类
     */
    h5("h5App","id","WgtqaT1HNTZPZNMDJu3k","H5"),
    weixin("wxApp", "userId","WgtqaT1HNTZPZNMDJu3k","微信小程序"),
    alipay("aliApp", "userId","WgtqaT1HNTZPZNMDJu3k","支付宝小程序"),
    baidu("bdApp", "userId","WgtqaT1HNTZPZNMDJu3k","百度小程序"),
    douyin("dyApp", "userId","WgtqaT1HNTZPZNMDJu3k","字节小程序"),
    ta<PERSON><PERSON>("tbApp", "userId","WgtqaT1HNTZPZNMDJu3k","淘宝小程序")

    ;

    private String source;

    private String uuid;

    private String secret;

    private String name;



    OrderMarkingSourceEnum(String source, String uuid, String secret, String name) {
        this.source = source;
        this.uuid = uuid;
        this.secret = secret;
        this.name = name;
    }


    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


}
