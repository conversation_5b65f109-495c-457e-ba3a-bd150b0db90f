package com.yl.applets.enums;

import com.yl.applets.utils.BcRedisKeyEnum;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2022/10/18 21:00
 * @Version 1.0
 */

public enum AppletsStatisticsEnum {

    APPLETS_PV(1, "PV"),
    APPLETS_UV(2, "UV"),
    APPLETS_SHARE(3, "SHARE"),
    APPLETS_BANNER(4, "BANNER"),
    SIGN_IN_THREE(5, "SIGN_THREE"),
    SIGN_IN_SEVEN(6, "SIGN_SEVEN"),
    ;
    private Integer type;
    private String name;

    AppletsStatisticsEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String getNameByType(Integer type) {
        for (AppletsStatisticsEnum value : AppletsStatisticsEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getName();
            }
        }
        return null;
    }
}
