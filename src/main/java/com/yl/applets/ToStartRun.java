/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: ToStartRun
 * Author:   luhong
 * Date:     2020-12-24 10:30
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yl.applets.config.WxMaProperties;
import com.yl.applets.entity.WxUser;
import com.yl.applets.mapper.applets.WxUserMapper;
import com.yl.applets.service.IWxUserService;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.utils.JWTUtils;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.Executor;

/**
 * 〈一句话功能简述〉<br> 
 * 〈本地调试用，用来获取用户authtoken〉
 *
 * <AUTHOR>
 * @create 2020-12-24
 * @since 1.0.0
 */
@Slf4j
//@Component
public class ToStartRun implements CommandLineRunner {

    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private WxMpService wxMpService;


    @Autowired
    private OrikaBeanMapper beanMapper;

    @Autowired
    private WxUserMapper wxUserMapper;

    @Value("${jwt.expiration}")
    private int expiration;

    @Autowired
    private Executor appletsExecutor;

    @Autowired
    private WxMaProperties wxMaProperties;

    /**
     * 快递签收模板id
     */
    @Value("${wx.templateId.sign:9VruZg9xJJszx0gAfXU_ZD_jQSIe6w9jjleDPHS0bgM}")
    private String signTemplateId;

    @Override
    public void run(String... args) throws Exception {

        QueryWrapper<WxUser> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("openid", "otjNL5Mefq20Sv9RGMiDXbDOj5qs");
        queryWrapper.eq("is_delete",0);
        queryWrapper.eq("mobile","13673608113");
        WxUser wxUser = wxUserService.getOne(queryWrapper);
        WxUserVo wxUserVo = beanMapper.map(wxUser, WxUserVo.class);
        wxUserVo.setNickName( new String(Base64.decodeBase64(wxUser.getNickName()), StandardCharsets.UTF_8));
        wxUserVo.setToken(JWTUtils.generateById(wxUser.getId(), expiration));
        log.info(">>>>>>>>>>>>>token:{}",wxUserVo.getToken());
        RedisUtil.setEx(BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(wxUserVo.getId().toString()), JSON.toJSONString(wxUserVo), expiration);
        log.info(">>>>>>>>>>>>>wxUserVo:{}", JsonUtils.toJson(wxUserVo));
    }
}