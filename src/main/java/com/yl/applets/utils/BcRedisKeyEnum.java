package com.yl.applets.utils;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.Assert;

/**
 * RedisKey 枚举类
 */
@Getter
public enum BcRedisKeyEnum {

    /**
     * key较多的情况：不定义具体的key, key当参数传给keyBuilder
     */
    CHANNEL_SHARE_ORDER("CHANNEL", "SHARE", "ORDER", ""),

    OAUTH_APP_TOKEN("CHANNEL_OAUTH", "APPLETS", "TOKEN", ""),

    /**
     * H5token
     */
    OAUTH_H5_TOKEN("CHANNEL_OAUTH", "ORDER_H5", "TOKEN", ""),

    APPLETS_SESSION_KEY("APPLETS", "WX_SESSION", "KEY", ""),

    /**
     * key较少的情况：
     * 1) 像下面这种定义具体的key
     * 2）在ConstantKeyEnum里定义具体的key
     * 2种方式习惯用哪个都行
     */
    STORE_MODULE3_FUN3_343("STORE", "MODULE3", "FUN3", "343", ""),

    APPLETS_VERIFYCODE_BINDMOBILE("APPLETS", "VERIFYCODE", "BINDMOBILE", ""),

    APPLETS_VERIFYCODE_MODIFYMOBILEOLD("APPLETS", "VERIFYCODE", "MODIFYMOBILEOLD", ""),

    APPLETS_VERIFYCODE_MODIFYMOBILENEW("APPLETS", "VERIFYCODE", "MODIFYMOBILENEW", ""),

    APPLETS_VERIFYCODE_UNBINDMOBILE("APPLETS", "VERIFYCODE", "UNBINDMOBILE", ""),

    APPLETS_VERIFYCODE_USERCARD("APPLETS", "VERIFYCODE", "USERCARD", ""),

    APPLETS_VERIFYCODE_INVALIDTIME("APPLETS", "VERIFYCODE", "INVALIDTIME", ""),

    APPLETS_VERIFYCODE_INTERVALTIME("APPLETS", "VERIFYCODE", "INTERVALTIME", ""),
    /**
     * 理赔发送验证码
     */
    APPLETS_VERIFYCODE_INDEMNITY("APPLETS", "VERIFYCODE", "INDEMNITY", ""),

    APPLETS_USER_LOGIN_LOCK("APPLETS", "LOGIN", "LOCK", ""),

    APPLETS_USER_SUBSCRIBE_STATUS("APPLETS", "SUBSCRIBE", "STATUS", ""),
    APPLETS_CAPTCHA("APPLETS", "CAPTCHA", "CAPTCHA", ""),
    APPLETS_PRIZE_MEMBERID("APPLETS", "PRIZE", "MEMBERID", ""),
    APPLETS_GET_PRIZE("APPLETS", "GET", "PRIZE", ""),
    PRIZE_POOL_DAY_KEY("PRIZE", "POOL", "DAY", ""),
    PRIZE_CONFIG_ITEMSURL_KEY("PRIZE", "CONFIG", "ITEMSURL", ""),
    BANNER_COUNT_DAY("BANNER", "COUNT", "DAY", ""),
    INDEX_COUNT_PV_DAY("INDEX", "PV", "DAY", ""),
    INDEX_COUNT_UV_DAY("INDEX", "UV", "DAY", ""),
    VIP_COUNT_PV_MONTH("APPLETS:VIP", "PV", "MONTH", ""),
    VIP_COUNT_UV_MONTH("APPLETS:VIP", "UV", "MONTH", ""),
    VIP_ACTION_COUNT_DAY("APPLETS:VIP", "ACTION", "DAY", ""),
    MEMBER_COUNT_PV_MONTH("APPLETS:MEMBER", "PV", "MONTH", ""),
    MEMBER_COUNT_UV_MONTH("APPLETS:MEMBER", "UV", "MONTH", ""),
    MEMBER_ACTION_COUNT_DAY("APPLETS:MEMBER", "ACTION", "DAY", ""),

    STATISTICS_SIGN_VIP_PV_MONTH("APPLETS:STATISTICS:SIGN:VIP", "PV", "MONTH", ""),
    STATISTICS_SIGN_VIP_UV_MONTH("APPLETS:STATISTICS:SIGN:VIP", "UV", "MONTH", ""),
    STATISTICS_SIGN_COMMON_PV_MONTH("APPLETS:STATISTICS:SIGN:COMMON", "PV", "MONTH", ""),
    STATISTICS_SIGN_COMMON_UV_MONTH("APPLETS:STATISTICS:SIGN:COMMON", "UV", "MONTH", ""),

    STATISTICS_SIGN_VIP_SHARE_MONTH("APPLETS:STATISTICS:SIGN:VIP", "SHARE", "MONTH", ""),
    STATISTICS_SIGN_COMMON_SHARE_MONTH("APPLETS:STATISTICS:SIGN:COMMON", "SHARE", "MONTH", ""),

    STATISTICS_SIGN_COMMON_BANNER_MONTH("APPLETS:STATISTICS:SIGN:COMMON", "BANNER", "MONTH", ""),
    STATISTICS_SIGN_VIP_BANNER_MONTH("APPLETS:STATISTICS:SIGN:VIP", "BANNER", "MONTH", ""),

    STATISTICS_SIGN_VIP_THREE_MONTH("APPLETS:STATISTICS:SIGN:VIP", "THREE", "MONTH", ""),
    STATISTICS_SIGN_COMMON_THREE_MONTH("APPLETS:STATISTICS:SIGN:COMMON", "THREE", "MONTH", ""),
    STATISTICS_SIGN_VIP_SEVEN_MONTH("APPLETS:STATISTICS:SIGN:VIP", "SEVEN", "MONTH", ""),
    STATISTICS_SIGN_COMMON_SEVEN_MONTH("APPLETS:STATISTICS:SIGN:COMMON", "SEVEN", "MONTH", ""),
    OUTSIDE_CMCC_COUPONS("APPLETS:OUTSIDE", "CMCC", "COUPONS", ""),
    WAYBILL_QUERY_COUNT_PER_MINUTER("APPLETS:WAYBILL", "QUERY", "COUNT", ""),

    OUTSIDE_TY_COUPONS("APPLETS:OUTSIDE", "TY", "TRADE", ""),
    OUTSIDE_TY_PHONE("APPLETS:OUTSIDE", "TY", "PHONE", ""),
    BAIDU_OCR_ACCESS_TOKEN("APPLETS:BAIDU", "OCR", "TOKEN", ""),
    APPLETS_COUPONS_COMMON_GET("APPLETS:WX", "COUPONS", "ID", ""),

    APPLETS_CONSOLIDATION_JY_LOCK_KEY("APPLETS:CONSOLIDATION", "JY", "LOCK", ""),

    APPLETS_MEMBER_ID_LOCK_KEY("APPLETS:MEMBER", "ID", "LOCK", ""),


    APPLETS_PRINTER_ERROR("APPLETS:CUS", "PRINTER", "ERROR", ""),

     //小程序学生认证标识
    APPLETS_STUDENT_AUTHENTICATION_STATUS("APPLETS:STUDENT", "AUTHENTICATION", "STATUS", ""),
    ;

    /**
     * 系统标识
     */
    private String keyPrefix;
    /**
     * 模块名称
     */
    private String module;
    /**
     * 方法名称
     */
    private String func;
    /**
     * key
     */
    private String key;
    /**
     * 描述
     */
    private String remark;

    BcRedisKeyEnum(String keyPrefix, String module, String func, String remark) {
        this.keyPrefix = keyPrefix;
        this.module = module;
        this.func = func;
        this.remark = remark;
    }

    BcRedisKeyEnum(String keyPrefix, String module, String func, String key, String remark) {
        this.keyPrefix = keyPrefix;
        this.module = module;
        this.func = func;
        this.key = key;
        this.remark = remark;
    }

    public String keyBuilder() {
        return checkAndGetValue(key);
    }

    public String keyBuilder(String key) {
        return checkAndGetValue(key);
    }

    private String checkAndGetValue(String key) {
        Assert.notNull(keyPrefix, "RedisKeyEnum: keyPrefix can not be null");
        Assert.notNull(module, "RedisKeyEnum: module can not be null");
        Assert.notNull(func, "RedisKeyEnum: func can not be null");
        Assert.notNull(key, "RedisKeyEnum: key can not be null");
        return keyPrefix + ":" + module + ":" + func + ":" + key;
    }

    /***
     * Redis常量key在此枚举中申明
     *
     * <AUTHOR>
     * @date 2019/6/11
     */
    @Getter
    @AllArgsConstructor
    enum ConstantKeyEnum {
        /**
         * 常量Key样例
         */
        LMDM_TOKEN_EXPIRE_TIME("LMDM:TOKEN:EXPIRE:TIME", "token过期时间"),
        LMDM_SMS_CODE_EXPIRE("LMDM:SMS:CODE:EXPIRE", "短信验证码过期时间");
        String key;
        String remark;
    }

}

