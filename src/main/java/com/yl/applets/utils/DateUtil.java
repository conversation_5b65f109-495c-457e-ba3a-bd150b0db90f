
package com.yl.applets.utils;


import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by lwk on 2020/3/16
 * 日期工具类<br>
 */
public class DateUtil {
	/** 默认的格式: yyyy-MM-dd HH:mm:ss */
	public static final String FMT_DEFAULT = "yyyy-MM-dd HH:mm:ss";
	/** 格式: yyyyMMddHHmmss */
	public static final String FMT_YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
	/** 格式: yyyyMMddHH */
	public static final String FMT_YYYYMMDDHH = "yyyyMMddHH";
	/** 格式: yyyyMMdd */
	public static final String FMT_YYYYMMDD = "yyyyMMdd";
	/** 格式: yyyy */
	public static final String FMT_YYYY = "yyyy";
	/** 格式: yyyy-MM-dd */
	public static final String FMT_YYYY_MM_DD = "yyyy-MM-dd";
	/** 格式: yyyy-MM */
	public static final String FMT_YYYY_MM = "yyyy-MM";
	/** 格式: yyyy-MM-dd HH */
	public static final String FMT_YYYY_MM_DD_HH = "yyyy-MM-dd HH";
	/** 格式: yyyy-MM-dd HH:mm */
	public static final String FMT_YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
	/** 格式: yyyy/MM/dd HH:mm:ss */
	public static final String FMT_YYYY_MM_DD_HH_MM_SS = "yyyy/MM/dd HH:mm:ss";
	/** 格式: yyyy/MM/dd HH:mm */
	public static final String FMT_YYYY_MM_DD_HH_MM_I = "yyyy/MM/dd HH:mm";
	private final static DateTimeFormatter formatter_all = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	/**
	 * 日期格式转为字符串格式[格式：yyyy-MM-dd HH:mm:ss]
	 * @param date：传入的日期
	 * @return    ：String
	 */
	public static String parseString(Date date) {
		SimpleDateFormat simpDateFormat = new SimpleDateFormat(FMT_DEFAULT);
		return simpDateFormat.format(date);
	}

	/**
	 * 日期格式转为字符串格式
	 * @param date：传入的日期
	 * @param fmt ：转为字符的格式
	 * @return    ：String
	 */
	public static String parseString(Date date, String fmt) {
		if(date == null) {
			return null;
		}
		SimpleDateFormat simpDateFormat = new SimpleDateFormat(fmt);
		return simpDateFormat.format(date);
	}


	public static String getCurrentDateStr() {
		Calendar cal = Calendar.getInstance();
		Date currDate = cal.getTime();

		return format(currDate);
	}

	/**
	 *      * 将Timestamp类型的日期转换为系统参数定义的格式的字符串。
	 *      * @param aTs_Datetime 需要转换的日期。
	 *      * @return 转换后符合给定格式的日期字符串
	 *     
	 */
	public static String format(Date aTs_Datetime) {
		return format(aTs_Datetime, FMT_YYYYMMDDHHMMSS);
	}

	public static String format(Date aTs_Datetime, String as_Pattern) {
		if (aTs_Datetime == null || as_Pattern == null)
			return null;

		SimpleDateFormat dateFromat = new SimpleDateFormat();
		dateFromat.applyPattern(as_Pattern);

		return dateFromat.format(aTs_Datetime);
	}

	/**
	 * 字符串格式转日期
	 * @param str：传入的字符串
	 * @return   ：Date
	 */
	public static Date parseDate(String str) {
		SimpleDateFormat simpDateFormat = new SimpleDateFormat(FMT_DEFAULT);
		try {
			return simpDateFormat.parse(str);
		} catch (ParseException e) {
			throw new RuntimeException("日期格式不正确 value[" + str + "]");
		}
	}

	/**
	 * 字符串格式转日期
	 * @param str：传入的字符串
	 * @param fmt：转为的日期格式
	 * @return   ：Date
	 */
	public static Date parseDate(String str, String fmt) {
		try {
			SimpleDateFormat simpDateFormat = new SimpleDateFormat(fmt);
			return simpDateFormat.parse(str);
		} catch (ParseException e) {
			throw new RuntimeException("日期格式不正确 value[" + str + "] fmt[" + fmt + "]");
		}
	}
	
	/**
	 * 获取当前字符串日期格式[yyyy-MM-dd]
	 * @return
	 */
	public static String getTodayYyyyMmDd() {
		return DateUtil.parseString(DateUtil.getTime(), DateUtil.FMT_YYYY_MM_DD);
	}

	/**
	 * 获取当前字符串日期格式[yyyy]
	 * @return
	 */
	public static String getTodayYyyy() {
		return DateUtil.parseString(DateUtil.getTime(), DateUtil.FMT_YYYY);
	}

	/**
	 * 获取当前时间[yyyy-MM-dd HH:mm:ss]
	 * @return
	 */
	public static String getStrTime() {
		return parseString(getTime(), DateUtil.FMT_DEFAULT);
	}

	/**
	 * 获取当前时间
	 * @return
	 */
	public static Date getTime() {
		return new Date();
	}

	/**
	 * 在指定日期上添加指定的年
	 * @param date		：传入的日期
	 * @param amount 	：添加的年
	 * @return
	 */
	public static Date addYears(Date date, Integer amount) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.YEAR, amount);
		return calendar.getTime();
	}

	/**
	 * 在指定日期上添加指定的月份
	 * @param date		：传入的日期
	 * @param amount 	：添加的月份
	 * @return
	 */
	public static Date addMonths(Date date, Integer amount) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MONTH, amount);
		return calendar.getTime();
	}
	
	/**
	 * 在指定日期上添加指定的天数
	 * @param date		：传入的日期
	 * @param amount 	：添加的天数
	 * @return
	 */
	public static Date addDays(Date date, Integer amount) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DAY_OF_YEAR, amount);
		return calendar.getTime();
	}
	
	/**
	 * 在指定日期上倒退指定的天数
	 * @param date		：传入的日期
	 * @param amount 	：倒退的天数
	 * @return
	 */
	public static Date backDays(Date date, Integer amount) {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - amount);  
		return calendar.getTime();
	}

	/**
	 * 在指定日期上添加指定的小时
	 * @param date		：传入的日期
	 * @param amount 	：添加的小时
	 * @return
	 */
	public static Date addHours(Date date, Integer amount) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.HOUR, amount);
		return calendar.getTime();
	}

	/**
	 * 在指定日期上添加指定的分钟
	 * @param date		：传入的日期
	 * @param amount 	：添加的分钟
	 * @return
	 */
	public static Date addMinutes(Date date, Integer amount) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MINUTE, amount);
		return calendar.getTime();
	}

	/**
	 * 在指定日期上添加指定的秒数
	 * @param date		：传入的日期
	 * @param amount 	：添加的秒数
	 * @return
	 */
	public static Date addSeconds(Date date, Integer amount){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.SECOND, amount);
		return calendar.getTime();
	}

	/**
	 * 获取时间差
	 * @param beginTime	开始时间
	 * @param endTime	结束时间
	 * @param type		时间的类型[0秒/1分钟/2小时]
	 * @return
	 */
	public static Long getDateDiff(Date beginTime, Date endTime, Integer type) {
		long time = 0;
		long diffTime = endTime.getTime() - beginTime.getTime();
		switch (type) {
		case 0:
			time = diffTime/1000;
			break;
		case 1:
			time = diffTime/(60*1000);
			break;
		case 2:
			time = diffTime/(60*60*1000);
			break;
		default:
			break;
		}
		return time;
	}

	/**
	 * 根据日期计算年龄
	 * @param date
	 * @return
	 */
	public static int getAge(Date date) {
		Calendar cal = Calendar.getInstance();
		int yearNow = cal.get(Calendar.YEAR);
		int monthNow = cal.get(Calendar.MONTH) + 1;
		int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);

		cal.setTime(date);
		int yearBirth = cal.get(Calendar.YEAR);
		int monthBirth = cal.get(Calendar.MONTH) + 1;
		int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
		int age = yearNow - yearBirth;

		if (monthNow <= monthBirth) {
			if (monthNow == monthBirth) {
				if (dayOfMonthNow < dayOfMonthBirth) {
					age--;
				}
			} else {
				age--;
			}
		}
		return age;
	}

	/**
	 * 获取简单时间[比如30秒前 20分钟前等]
	 * @param timeStr
	 * @return
	 */
	public static String getSimpleTime(String timeStr) {
		Date time = DateUtil.parseDate(timeStr);
		return getSimpleTime(time);
	}
	/**
	 * 获取简单时间[比如30秒前 20分钟前等]
	 * @param time
	 * @return
	 */
	public static String getSimpleTime(Date time) {
		long ssDiff = DateUtil.getDateDiff(time, DateUtil.getTime(), 0);
		StringBuilder result = new StringBuilder();
		if(ssDiff < 60) {
			if(ssDiff < 0) {
				result.append(parseString(time, FMT_YYYY_MM_DD));
				return result.toString();
			}
			result.append(ssDiff).append("秒钟前");
			return result.toString();
		}
		long mmDiff = DateUtil.getDateDiff(time, DateUtil.getTime(), 1);
		if(mmDiff < 60) {
			result.append(mmDiff).append("分钟前");
			return result.toString();
		}
		String timeStr = DateUtil.parseString(time);
		if(DateUtil.getTodayYyyyMmDd().equals(timeStr.substring(0, 10))) {
			result.append("今天 ").append(DateUtil.parseString(time, "HH:mm"));
			return result.toString();
		}
		if(getTodayYyyy().equals(timeStr.substring(0, 4))) {
			return DateUtil.parseString(time, "MM月dd日 HH:mm");
		}
		return DateUtil.parseString(time, "yyyy年MM月dd日");
	}
	/**
	 * 判断两个日期是否 同年月
	 * 2018/9
	 * <AUTHOR>
	 * @param date1
	 * @param date2
	 * @return
	 */
	public static boolean isSameDate(Date date1, Date date2) {
     try {
         Calendar cal1 = Calendar.getInstance();
            cal1.setTime(date1);

            Calendar cal2 = Calendar.getInstance();
            cal2.setTime(date2);

            boolean isSameYear = cal1.get(Calendar.YEAR) == cal2
                    .get(Calendar.YEAR);
            boolean isSameMonth = isSameYear
                    && cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
            boolean isSameDate = isSameMonth
                    && cal1.get(Calendar.DAY_OF_MONTH) == cal2
                            .get(Calendar.DAY_OF_MONTH);

            return isSameDate;
     } catch (Exception e) {
         e.printStackTrace();
     }
     return false;
    }
	
	/**
     * 通过时间秒毫秒数判断两个时间的间隔天数
     * <AUTHOR>
     * @param date1
     * @param date2
     * @return
     */
    public static int differentDaysByMillisecond(Date date1,Date date2)
    {
        int days = (int) ((date2.getTime() - date1.getTime()) / (1000*3600*24));
        return days;
    }
    
    /**
	 * 判断该日期是否是该月的最后一天
	 * <AUTHOR>
	 * @param date
	 *            需要判断的日期
	 * @return
	 */
	public static boolean isLastDayOfMonth(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar.get(Calendar.DAY_OF_MONTH) == calendar
				.getActualMaximum(Calendar.DAY_OF_MONTH);
	}



	public static Date getFirstDate(Date date) {
	     Calendar calendar = Calendar.getInstance();
	     calendar.setTime(date);
	     calendar.set(Calendar.DATE, 1);        //设置为该月第一天
	     return calendar.getTime();
	     
	}
	
	/**
	 * 返回第一天
	 * 可以根据Format进行转换	  FMT_YYYY_MM_DD
	 * @param date
	 * @return
	 */
	public static Date getFirstDate(Date date,String fmt) {
	     Calendar calendar = Calendar.getInstance();
	     calendar.setTime(date);
	     calendar.set(Calendar.DATE, 1);        //设置为该月第一天
	     SimpleDateFormat simpDateFormat = new SimpleDateFormat(fmt);
	     simpDateFormat.format(calendar.getTime());
	     try {
			return simpDateFormat.parse(simpDateFormat.format(calendar.getTime()));
		} catch (ParseException e) {
			e.printStackTrace();
		}
	     return null;
	}
	
	/**
	 * 获取日期中的Month
	 * @return
	 */
	public static Integer getDateMonth(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		int month = cal.get(Calendar.MONTH)+1;
		return month;
	}
	/**
	 * 获取当月日期中的day
	 * @return
	 */
	public static Integer getDateDay(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		int day = cal.get(Calendar.DAY_OF_MONTH);
		return day;
		
	}
	
	/**
	 * 判断某个日期是否落在某两个时间的区间之内
	 * @param
	 * @return
	 */
	public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
		 if (nowTime.getTime() == startTime.getTime()
	                || nowTime.getTime() == endTime.getTime()) {
	            return true;
	        }
	        Calendar date = Calendar.getInstance();
	        date.setTime(nowTime);

	        Calendar begin = Calendar.getInstance();
	        begin.setTime(startTime);

	        Calendar end = Calendar.getInstance();
	        end.setTime(endTime);

	        if (date.after(begin) && date.before(end)) {
	            return true;
	        } else {
	            return false;
	        }
	}	
	
	public static String getFirstDateStr(Date date) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
	     Calendar calendar = Calendar.getInstance();
	     calendar.setTime(date);
	     calendar.set(Calendar.DATE, 1);        //设置为该月第一天
	     return df.format(calendar.getTime());
	}
	

	/**
	 * 判断时间是否在时间段内
	 * @param nowTime
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public static boolean belongCalendar(Date nowTime, Date beginTime, Date endTime) {

		if(nowTime == null || beginTime == null || endTime == null){
			return false;
		}

		Calendar date = Calendar.getInstance();
		date.setTime(nowTime);

		Calendar begin = Calendar.getInstance();
		begin.setTime(beginTime);

		Calendar end = Calendar.getInstance();
		end.setTime(endTime);

		if (date.after(begin) && date.before(end)) {
			return true;
		} else {
			return false;
		}
	}
	

	/**
	 * 转换 带斜杠的时间  如：2018/8/22 0:00:00
	 * @param dateString
	 * @return
	 */
	public static  Date convertDateFormat(String  dateString) {
		if(StringUtils.isEmpty(dateString)) {
			return null;
		}else {
			if(dateString.contains("/")) {
				String strDate =dateString.replace("/", "-");
				if(strDate.length()<19) {
					return DateUtil.fromatDate(strDate);
				}else {
					return DateUtil.parseDate(strDate);
				}
				
			}else {
				if(dateString.length()<19) {
					return DateUtil.fromatDate(dateString);
				}else {
					return DateUtil.parseDate(dateString);
				}
			}
		}
	}
	
	
	
	/** 
	 * 将时间格式yyyy-MM-dd HH:mm 转为   yyyy-MM-dd HH:mm:ss
	 * @param strdate
	 * @return
	 */
	public  static  Date  fromatDate(String strdate) {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
		SimpleDateFormat sdf = new SimpleDateFormat(FMT_DEFAULT);
		try {
			Date parse = simpleDateFormat.parse(strdate);
			String strTime =  sdf.format(parse);
			Date   dateTime =  sdf.parse(strTime);
			return  dateTime;
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 *  计算end日期比start日期大几天
	 * @param start
	 * @param end
	 * @return
	 */
	public static int differentDays(Date start, Date end) {
		try {
			Calendar startCalendar = Calendar.getInstance();
			startCalendar.setTime(start);
			Calendar endCalendar = Calendar.getInstance();
			endCalendar.setTime(end);
			int startDay = startCalendar.get(Calendar.DAY_OF_YEAR);
			int endDay = endCalendar.get(Calendar.DAY_OF_YEAR);

			int startYear = startCalendar.get(Calendar.YEAR);
			int endYear = endCalendar.get(Calendar.YEAR);
			if (startYear != endYear){ // 不同年
				int dayDiff = 0; // 相差的年天数
				for (int i = startYear; i < endYear; i++) {
					if (i %4 == 0 && i%100 != 0 || i%400 == 0) { // 闰年
						dayDiff = dayDiff + 366;
					} else { // 平年
						dayDiff = dayDiff + 365;
					}
				}
				return dayDiff + (endDay - startDay);
			} else {
				return endDay - startDay;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}

	public static LocalDateTime getStrToDate(String dateTime){
		if(StringUtils.isEmpty(dateTime)){
			return null;
		}
		return LocalDateTime.parse(dateTime,formatter_all);
	}


	/**
	 * 获取当前时间加指定天数后的时间
	 * @param days 要增加的天数
	 */
	public static LocalDateTime nowPlusDays(long days) {
		return LocalDateTime.now().plusDays(days);
	}
}