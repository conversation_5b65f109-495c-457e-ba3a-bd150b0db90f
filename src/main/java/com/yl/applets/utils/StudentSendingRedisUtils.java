package com.yl.applets.utils;

import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/4/18 10:48
 */
@Component
@Slf4j
public class StudentSendingRedisUtils {


    //保存学生认证标识
    @Value("${applets.student.sending.expiration:2592000}")
    private int EXPIRE_TIME = 2592000;


    public Integer getFromRedisById(Integer numberId) {
        if (numberId == null) {
            return null;
        }
        Object obj = RedisUtil.get(getKey(numberId));
        if (obj != null) {
            return Integer.parseInt(obj.toString());
        }
        return null;
    }

    //保存学生认证标识 1认证成功 非1认证失败
    public void saveFromRedisById(Integer numberId) {
        if (numberId == null) {
            return ;
        }
        RedisUtil.setEx(getKey(numberId),"1",EXPIRE_TIME);
    }

    private String getKey(Integer numberId){
        return BcRedisKeyEnum.APPLETS_STUDENT_AUTHENTICATION_STATUS.keyBuilder(numberId + "");
    }


}
