package com.yl.applets.utils;

import com.alibaba.fastjson.JSON;
import com.baidu.aip.client.BaseClient;
import com.baidu.aip.http.AipRequest;
import com.baidu.aip.http.EBodyFormat;
import com.baidu.aip.nlp.ESimnetType;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.util.StopWatch;

import java.util.HashMap;
import java.util.UUID;


@Slf4j
public class YLAipNlp  extends BaseClient {

    public YLAipNlp(String appId, String apiKey, String secretKey) {
        super(appId, apiKey, secretKey);
    }


    public JSONObject address(String text, HashMap<String, Object> options) {
        StopWatch stopWatch = new StopWatch();
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        AipRequest request = new AipRequest();
        log.info("请求百度接口token。参数：{}，请求id：{}", JSON.toJSONString(request),requestId);
        stopWatch.start("百度token");
        this.preOperation(request);
        stopWatch.stop();
        request.addBody("text", text);
        if (options != null) {
            request.addBody(options);
        }

        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v1/address");
        request.addHeader("Content-Encoding", "GBK");
        request.addHeader("Content-Type", "application/json");
        request.setBodyFormat(EBodyFormat.RAW_JSON);
        this.postOperation(request);
        log.info("请求百度接口address。参数：{}，请求id：{}", JSON.toJSONString(request),requestId);
        stopWatch.start("百度address");
        JSONObject jsonObject = this.requestServer(request);
        stopWatch.stop();
        log.info("请求id==>{}出参==>{},耗时详情==>{},总耗时==>{}",requestId,jsonObject,stopWatch.prettyPrint(),stopWatch.getTotalTimeSeconds());
        return jsonObject;
    }


    public JSONObject lexer(String text, HashMap<String, Object> options) {
        AipRequest request = new AipRequest();
        this.preOperation(request);
        request.addBody("text", text);
        if (options != null) {
            request.addBody(options);
        }

        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v1/lexer");
        request.addHeader("Content-Encoding", "GBK");
        request.addHeader("Content-Type", "application/json");
        request.setBodyFormat(EBodyFormat.RAW_JSON);
        this.postOperation(request);
        return this.requestServer(request);
    }

    public JSONObject lexerCustom(String text, HashMap<String, Object> options) {
        AipRequest request = new AipRequest();
        this.preOperation(request);
        request.addBody("text", text);
        if (options != null) {
            request.addBody(options);
        }

        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v1/lexer_custom");
        request.addHeader("Content-Encoding", "GBK");
        request.addHeader("Content-Type", "application/json");
        request.setBodyFormat(EBodyFormat.RAW_JSON);
        this.postOperation(request);
        return this.requestServer(request);
    }

    public JSONObject depParser(String text, HashMap<String, Object> options) {
        AipRequest request = new AipRequest();
        this.preOperation(request);
        request.addBody("text", text);
        if (options != null) {
            request.addBody(options);
        }

        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v1/depparser");
        request.addHeader("Content-Encoding", "GBK");
        request.addHeader("Content-Type", "application/json");
        request.setBodyFormat(EBodyFormat.RAW_JSON);
        this.postOperation(request);
        return this.requestServer(request);
    }

    public JSONObject wordEmbedding(String word, HashMap<String, Object> options) {
        AipRequest request = new AipRequest();
        this.preOperation(request);
        request.addBody("word", word);
        if (options != null) {
            request.addBody(options);
        }

        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v2/word_emb_vec");
        request.addHeader("Content-Encoding", "GBK");
        request.addHeader("Content-Type", "application/json");
        request.setBodyFormat(EBodyFormat.RAW_JSON);
        this.postOperation(request);
        return this.requestServer(request);
    }

    public JSONObject dnnlmCn(String text, HashMap<String, Object> options) {
        AipRequest request = new AipRequest();
        this.preOperation(request);
        request.addBody("text", text);
        if (options != null) {
            request.addBody(options);
        }

        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v2/dnnlm_cn");
        request.addHeader("Content-Encoding", "GBK");
        request.addHeader("Content-Type", "application/json");
        request.setBodyFormat(EBodyFormat.RAW_JSON);
        this.postOperation(request);
        return this.requestServer(request);
    }

    public JSONObject wordSimEmbedding(String word1, String word2, HashMap<String, Object> options) {
        AipRequest request = new AipRequest();
        this.preOperation(request);
        request.addBody("word_1", word1);
        request.addBody("word_2", word2);
        if (options != null) {
            request.addBody(options);
        }

        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v2/word_emb_sim");
        request.addHeader("Content-Encoding", "GBK");
        request.addHeader("Content-Type", "application/json");
        request.setBodyFormat(EBodyFormat.RAW_JSON);
        this.postOperation(request);
        return this.requestServer(request);
    }

    public JSONObject simnet(String text1, String text2, HashMap<String, Object> options) {
        AipRequest request = new AipRequest();
        this.preOperation(request);
        request.addBody("text_1", text1);
        request.addBody("text_2", text2);
        if (options != null) {
            request.addBody(options);
        }

        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v2/simnet");
        request.addHeader("Content-Encoding", "GBK");
        request.addHeader("Content-Type", "application/json");
        request.setBodyFormat(EBodyFormat.RAW_JSON);
        this.postOperation(request);
        return this.requestServer(request);
    }

    public JSONObject commentTag(String text, ESimnetType type, HashMap<String, Object> options) {
        AipRequest request = new AipRequest();
        this.preOperation(request);
        request.addBody("text", text);
        request.addBody("type", type.ordinal());
        if (options != null) {
            request.addBody(options);
        }

        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v2/comment_tag");
        request.addHeader("Content-Encoding", "GBK");
        request.addHeader("Content-Type", "application/json");
        request.setBodyFormat(EBodyFormat.RAW_JSON);
        this.postOperation(request);
        return this.requestServer(request);
    }

    public JSONObject sentimentClassify(String text, HashMap<String, Object> options) {
        AipRequest request = new AipRequest();
        this.preOperation(request);
        request.addBody("text", text);
        if (options != null) {
            request.addBody(options);
        }

        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v1/sentiment_classify");
        request.addHeader("Content-Encoding", "GBK");
        request.addHeader("Content-Type", "application/json");
        request.setBodyFormat(EBodyFormat.RAW_JSON);
        this.postOperation(request);
        return this.requestServer(request);
    }

    public JSONObject keyword(String title, String content, HashMap<String, Object> options) {
        AipRequest request = new AipRequest();
        this.preOperation(request);
        request.addBody("title", title);
        request.addBody("content", content);
        if (options != null) {
            request.addBody(options);
        }

        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v1/keyword");
        request.addHeader("Content-Encoding", "GBK");
        request.addHeader("Content-Type", "application/json");
        request.setBodyFormat(EBodyFormat.RAW_JSON);
        this.postOperation(request);
        return this.requestServer(request);
    }

    public JSONObject topic(String title, String content, HashMap<String, Object> options) {
        AipRequest request = new AipRequest();
        this.preOperation(request);
        request.addBody("title", title);
        request.addBody("content", content);
        if (options != null) {
            request.addBody(options);
        }

        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v1/topic");
        request.addHeader("Content-Encoding", "GBK");
        request.addHeader("Content-Type", "application/json");
        request.setBodyFormat(EBodyFormat.RAW_JSON);
        this.postOperation(request);
        return this.requestServer(request);
    }

    public JSONObject ecnet(String text, HashMap<String, Object> options) {
        AipRequest request = new AipRequest();
        this.preOperation(request);
        request.addBody("text", text);
        if (options != null) {
            request.addBody(options);
        }

        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v1/ecnet");
        request.addHeader("Content-Encoding", "GBK");
        request.addHeader("Content-Type", "application/json");
        request.setBodyFormat(EBodyFormat.RAW_JSON);
        this.postOperation(request);
        return this.requestServer(request);
    }

    public JSONObject emotion(String text, HashMap<String, Object> options) {
        AipRequest request = new AipRequest();
        this.preOperation(request);
        request.addBody("text", text);
        if (options != null) {
            request.addBody(options);
        }

        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v1/emotion");
        request.addHeader("Content-Encoding", "GBK");
        request.addHeader("Content-Type", "application/json");
        request.setBodyFormat(EBodyFormat.RAW_JSON);
        this.postOperation(request);
        return this.requestServer(request);
    }

    public JSONObject newsSummary(String content, int maxSummaryLen, HashMap<String, Object> options) {
        AipRequest request = new AipRequest();
        this.preOperation(request);
        request.addBody("content", content);
        request.addBody("max_summary_len", maxSummaryLen);
        if (options != null) {
            request.addBody(options);
        }

        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v1/news_summary");
        request.addHeader("Content-Encoding", "GBK");
        request.addHeader("Content-Type", "application/json");
        request.setBodyFormat(EBodyFormat.RAW_JSON);
        this.postOperation(request);
        return this.requestServer(request);
    }

}
