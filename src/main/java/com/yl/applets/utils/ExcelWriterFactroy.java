package com.yl.applets.utils;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;

import java.io.IOException;
import java.io.OutputStream;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: excel写工厂类
 * @Project:
 * @CreateDate: Created in 2019-06-18 14:05
 * @Author: <a href="<EMAIL>">zhangchunlin</a>
 */
public class ExcelWriterFactroy extends ExcelWriter {
    private OutputStream outputStream;
    private Integer sheetNo;

    public ExcelWriterFactroy(OutputStream outputStream, ExcelTypeEnum typeEnum, Integer sheetNo) {
        super(outputStream, typeEnum);
        this.outputStream = outputStream;
        this.sheetNo = sheetNo;
    }

    public ExcelWriterF<PERSON>roy write(List<? extends BaseRowModel> list, String sheetName,
                                    BaseRowModel object) {
        this.sheetNo++;
        try {
            Sheet sheet = new Sheet(sheetNo, 0, object.getClass());
            sheet.setSheetName(sheetName);
            this.write(list, sheet);
        } catch (Exception ex) {
            ex.printStackTrace();
            try {
                outputStream.flush();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return this;
    }

    @Override
    public void finish() {
        super.finish();
        try {
            outputStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}