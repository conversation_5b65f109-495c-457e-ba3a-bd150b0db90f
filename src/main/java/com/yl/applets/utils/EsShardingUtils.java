/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: EsShardingUtils
 * Author:   luhong
 * Date:     2020-11-30 17:02
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-11-30
 * @since 1.0.0
 */
public class EsShardingUtils {
    private static final Logger log = LoggerFactory.getLogger(EsShardingUtils.class);
    public static final DateTimeFormatter DATE_TIME_LINE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_TIME_LINE_T_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
    public static final DateTimeFormatter DATE_LINE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    public EsShardingUtils() {
    }

    public static String[] numberShardingValues(Object start, Object end) {
        List<String> result = new ArrayList();
        if (start instanceof Integer && end instanceof Integer) {
            Integer startInt = (Integer)start;

            for(Integer endInt = (Integer)end; startInt.compareTo(endInt) <= 0; startInt = startInt + 1) {
                result.add(startInt.toString());
            }
        } else {
            Long startLong;
            Long endLong;
            if (start instanceof Long && end instanceof Long) {
                startLong = (Long)start;

                for(endLong = (Long)end; startLong.compareTo(endLong) <= 0; startLong = startLong + 1L) {
                    result.add(startLong.toString());
                }
            } else if (start instanceof String && end instanceof String) {
                startLong = Long.parseLong(start.toString());

                for(endLong = Long.parseLong(end.toString()); startLong.compareTo(endLong) <= 0; startLong = startLong + 1L) {
                    result.add(startLong.toString());
                }
            } else {
                log.warn("sharding value parse error. Unsupported range value type");
            }
        }

        return (String[])result.toArray(new String[0]);
    }

    public static String dateShardingValue(Object dateTime) {
        if (dateTime instanceof LocalDateTime) {
            return dateShardingValue((LocalDateTime)dateTime);
        } else if (dateTime instanceof LocalDate) {
            return dateShardingValue((LocalDate)dateTime);
        } else if (dateTime instanceof Date) {
            return dateShardingValue((Date)dateTime);
        } else if (dateTime instanceof String) {
            String result = (String)dateTime;
            TemporalAccessor parse;
            if (result.contains(":")) {
                if (result.contains("T")) {
                    parse = DATE_TIME_LINE_T_FORMATTER.parse(result);
                    return dateShardingValue(LocalDateTime.from(parse));
                } else {
                    parse = DATE_TIME_LINE_FORMATTER.parse(result);
                    return dateShardingValue(LocalDateTime.from(parse));
                }
            } else {
                parse = DATE_LINE_FORMATTER.parse(result);
                return dateShardingValue(LocalDate.from(parse));
            }
        } else {
            log.warn("sharding value parse error. Unsupported value type");
            return null;
        }
    }

    public static String dateShardingValue(LocalDateTime localDateTime) {
        return localDateTime.toLocalDate().format(DATE_FORMATTER);
    }

    public static String dateShardingValue(LocalDate localDate) {
        return localDate.format(DATE_FORMATTER);
    }

    public static String dateShardingValue(Date date) {
        return dateToLocalDateTime(date).toLocalDate().format(DATE_FORMATTER);
    }

    public static String[] dateShardingValues(Object start, Object end) {
        LocalDate startDate;
        LocalDate endDate;
        if (start instanceof LocalDateTime && end instanceof LocalDateTime) {
            startDate = ((LocalDateTime)start).toLocalDate();
            endDate = ((LocalDateTime)end).toLocalDate();
        } else if (start instanceof LocalDate && end instanceof LocalDate) {
            startDate = (LocalDate)start;
            endDate = (LocalDate)end;
        } else if (start instanceof Date && end instanceof Date) {
            startDate = dateToLocalDateTime((Date)start).toLocalDate();
            endDate = dateToLocalDateTime((Date)end).toLocalDate();
        } else {
            if (!(start instanceof String) || !(end instanceof String)) {
                log.warn("sharding value parse error. Unsupported range value type");
                return null;
            }

            String startStr = (String)start;
            String endStr = (String)end;
            if (startStr.contains(":")) {
                if (startStr.contains("T")) {
                    startDate = LocalDate.from(DATE_TIME_LINE_T_FORMATTER.parse(startStr));
                    endDate = LocalDate.from(DATE_TIME_LINE_T_FORMATTER.parse(endStr));
                } else {
                    startDate = LocalDate.from(DATE_TIME_LINE_FORMATTER.parse(startStr));
                    endDate = LocalDate.from(DATE_TIME_LINE_FORMATTER.parse(endStr));
                }
            } else {
                startDate = LocalDate.from(DATE_LINE_FORMATTER.parse(startStr));
                endDate = LocalDate.from(DATE_LINE_FORMATTER.parse(endStr));
            }
        }

        ArrayList result;
        for(result = new ArrayList(); startDate.compareTo(endDate) <= 0; startDate = startDate.plusDays(1L)) {
            result.add(startDate.format(DATE_FORMATTER));
        }

        return (String[])result.toArray(new String[0]);
    }

    private static LocalDateTime dateToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
        return localDateTime;
    }
}