package com.yl.applets.utils;

import com.alibaba.fastjson.JSON;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataRetrievalFailureException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-04-16 9:51
 * @Version 1.0
 */
@Slf4j
@Component
public class SmsAccessUtils {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 限流锁定
     *
     * @param key      加锁key
     * @param limit    失败多少次
     * @param msg      日志msg
     * @param timeout  记录key过期时间
     * @param lockTime 锁定时间
     */
    public void increment(String key, Integer limit, String msg, Long timeout, Long lockTime) {
        log.info("失败限流方法开始，key:{} limit:{} msg:{} timeout:{}", key, limit, msg, timeout);
        log.info("{}==>进行限额次数的key: {}", msg, key);
        RedisAtomicLong atomicLong = this.getAtomicLong(key);
        // 不存在这个key
        long invokeCount = 0;
        try {
            invokeCount = atomicLong.get();
        }catch (DataRetrievalFailureException e) {
            log.info("当前key:{}，不存在/已失效", key);
        }
//        long invokeCount = atomicLong.get();
        log.info("{}==>之前执行次数: {}", msg, invokeCount);
        if (invokeCount == 0) {
            log.info("{}==>初次执行", msg);
            atomicLong.incrementAndGet();
            atomicLong.expire(timeout, TimeUnit.SECONDS);
            return;
        }
        if (invokeCount + 1 > limit) {
            log.info("{}==>当日当前执行次数超过{}次数", msg, limit);
            String lockKey = "LOCK:" + key;
            Object obj = redisTemplate.opsForValue().get(lockKey);
            log.info("获取当前运单号锁定返回数据：{}", JSON.toJSONString(obj));

            if (Objects.isNull(obj)) {
                redisTemplate.opsForValue().set(lockKey, 1, lockTime, TimeUnit.SECONDS);
            }

            throw new BusinessException(ResultCodeEnum.WAYBILL_PERVIFY_OFTEN);
        }
        long get = atomicLong.incrementAndGet();
        log.info("{}==>当前执行次数: {}", msg, get);
    }


    private RedisAtomicLong getAtomicLong(final String key) {
        return new RedisAtomicLong(key, Objects.requireNonNull(redisTemplate.getConnectionFactory()));
    }


    /**
     * 限流
     *
     * @param key      加锁key
     * @param limit    请求最多多少次
     * @param msg      日志msg
     * @param timeout  记录key过期时间
     *
     */
    public void currentLimiting(String key, Integer limit, String msg, Long timeout) {
        log.info("失败限流方法开始，key:{} limit:{} msg:{} timeout:{}", key, limit, msg, timeout);
        log.info("{}==>进行限额次数的key: {}", msg, key);
        RedisAtomicLong atomicLong = this.getAtomicLong(key);
        // 不存在这个key
        long invokeCount = 0;
        try {
         invokeCount = atomicLong.get();
        }catch (DataRetrievalFailureException e) {
            log.info("当前key:{}，不存在/已失效", key);
        }
        log.info("{}==>之前执行次数: {}", msg, invokeCount);
        if (invokeCount == 0) {
            log.info("{}==>初次执行", msg);
            atomicLong.incrementAndGet();
            atomicLong.expire(timeout, TimeUnit.SECONDS);
            return;
        }
        if (invokeCount + 1 > limit) {
            log.info("{}==>当前执行次数超过{}次数", msg, limit);
            throw new BusinessException(ResultCodeEnum.PLEASE_TRY_AGAIN);
        }
        long get = atomicLong.incrementAndGet();
        if (get>=limit) {
            log.info("{}==>当前执行次数超过{}次数 主动过期", msg, limit);
            atomicLong.expire(1, TimeUnit.SECONDS);
        }
        log.info("{}==>当前执行次数: {}", msg, get);
    }

}
