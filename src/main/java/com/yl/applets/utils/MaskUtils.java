package com.yl.applets.utils;

import org.apache.commons.lang.StringUtils;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/6/17 16:21
 */
public class MaskUtils {
    /**
     * 将手机号除最后四位外全部用 * 替代
     *
     * @param phoneNumber 手机号
     * @return 脱敏后的手机号
     */
    public static String getLastFourDigits(String phoneNumber) {

        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return "";
        }
        int length = phoneNumber.length();

        if (length == 11) {
            return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(7);
        } else if ((length > 4 && length <= 10) || length > 11) {
            return "****" + phoneNumber.substring(length - 4);
        } else {
            // 长度 <= 4，全部明文
            return phoneNumber;
        }
    }

    /**
     * 对姓名进行脱敏处理：
     * 保留第一个字符，其余用 * 替代
     *
     * @param name 原始姓名
     * @return 脱敏后的姓名
     */
    public static String maskName(String name) {
        if (StringUtils.isBlank(name)) {
            return name;
        }

        int length = name.length();
        if (length == 1) {
            return name; // 单字不脱敏
        }

        StringBuilder sb = new StringBuilder();
        sb.append(name.charAt(0)); // 保留第一位
        for (int i = 1; i < length; i++) {
            sb.append('*');
        }
        return sb.toString();
    }


    /**
     * 对详细地址进行脱敏处理：
     * 显示省市，其余使用6个*替换；
     *
     * @param provinceName 原始详细地址
     * @return 脱敏后的详细地址
     */
    public static String maskAddress(String provinceName) {
        if (StringUtils.isBlank(provinceName)) {
            return provinceName;
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 1; i < 7; i++) {
            sb.append('*');
        }
        return provinceName+sb.toString();
    }


    /**
     * 生成指定长度的占位符字符串
     *
     * @param length 占位符长度
     * @return 构造好的占位符字符串
     */
    public static String generatePlaceholder( int length) {
        if (length <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append("*");
        }
        return sb.toString();
    }




}
