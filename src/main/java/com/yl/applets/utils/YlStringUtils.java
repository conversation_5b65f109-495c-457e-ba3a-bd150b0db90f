package com.yl.applets.utils;

import cn.hutool.core.util.StrUtil;
import jodd.util.StringUtil;
import org.apache.commons.lang.StringUtils;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class YlStringUtils {
    /**
     * 字符串转换unicode
     */
    public static String stringToUnicode(String string) {

        StringBuffer unicode = new StringBuffer();

        for (int i = 0; i < string.length(); i++) {

            // 取出每一个字符
            char c = string.charAt(i);

            // 转换为unicode
            unicode.append(String.format("\\u%04x", Integer.valueOf(c)));
        }

        return unicode.toString();
    }

    /**
     * 判断是否全是数字
     * @param str
     * @return
     */
    public static boolean isNumeric(String str) {
        for (int i = str.length(); --i >= 0; ) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * unicode 转字符串
     */
    public static String unicodeToString(String unicode) {

        StringBuffer string = new StringBuffer();

        String[] hex = unicode.split("\\\\u");

        for (int i = 1; i < hex.length; i++) {

            // 转换出每一个代码点
            int data = Integer.parseInt(hex[i], 16);

            // 追加成string
            string.append((char) data);
        }

        return string.toString();
    }

    // 全角转半角的 转换函数
    public static final String full2HalfChange(String fullStr)
            throws UnsupportedEncodingException {
        if (StringUtil.isBlank(fullStr)) {
            return fullStr;
        }
        fullStr = stringToUnicode(fullStr);
        fullStr = unicodeToString(fullStr);
        StringBuffer outStrBuf = new StringBuffer("");
        String Tstr = "";
        byte[] b = null;
        for (int i = 0; i < fullStr.length(); i++) {
            Tstr = fullStr.substring(i, i + 1);
            // 全角空格转换成半角空格
            if (("　").equals(Tstr)){
                outStrBuf.append(" ");
                continue;
            }
            b = Tstr.getBytes("unicode");
            // 得到 unicode 字节数据
            if (b[2] == -1) {
                // 表示全角？
                b[3] = (byte) (b[3] + 32);
                b[2] = 0;
                outStrBuf.append(new String(b, "unicode"));
            } else {
                outStrBuf.append(Tstr);
            }
        }
        return outStrBuf.toString();
    }


    public static boolean isContainChinese(String str) {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        }
        return false;
    }


    /**
     * 将含有任意中英文标点符号的字符串以任何标点分割并返回List集合
     * 例如： splitWithAnyChracter("WX009R ;WX0082， ,sao*cao，zuo=666")
     * ->
     * 返回：[WX009R, WX0082, sao, cao, zuo, 666]
     *
     * @param source 源字符串
     * @return List<String>
     * @throws
     * <AUTHOR>
     */
    public static List<String> splitWithAnyChracter(String source) {
        if (StringUtils.isBlank(source)) {return null;}
        List<String> rs = new ArrayList<String>();
        try {
            if (isContainChinese(source)) {
                source = full2HalfChange(source);
            }
            char[] chars = source.toCharArray();
            int k = 0;
            boolean flag = false;
            for (int i = 0; i < chars.length; i++) {
                char c = chars[i];
                if (Integer.valueOf(c) != null && ((Integer.valueOf(c) >= 32 && Integer.valueOf(c) <= 47) || (Integer.valueOf(c) >= 58 && Integer.valueOf(c) <= 64))) {
                    String niceStr = StringUtils.trim(StringUtils.substring(source, flag ? k + 1 : k, i));
                    if (StringUtils.isNotBlank(niceStr)) {
                        rs.add(niceStr);
                    }
                    k = i;
                    flag = true;
                }
            }
            String lastChar = StringUtils.substring(source, flag ? k + 1 : k, chars.length);
            if (StringUtils.isNotBlank(lastChar)) {
                rs.add(lastChar);
            }
        } catch (Exception e) {
//            e.printStackTrace();
        }
        return rs;
    }

    public static String subText(String name){
        return StrUtil.isBlank(name) ? "" : StringUtils.substring(name, 0, 50);
    }


}
