package com.yl.applets.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.yl.applets.entity.OmsWaybill;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 反射帮助类
 * @Date 2020-10-26 16:28
 * @Created by wangjie
 */
@Slf4j
public class ReflectUtil {

	/**
	 * 通过反射获取类型的所有字段名(大写)
	 *
	 * @param clazz
	 * @return
	 */
	public static List<String> getAllFields(Class<?> clazz) {
		List<String> collect = Arrays.stream(clazz.getDeclaredFields()).map(x -> x.getName()).collect(Collectors.toList());
		if (CollectionUtil.isEmpty(collect)) {
			log.error("实体类:{}搜索字段为空", clazz.getName());
		}
		return collect;
	}

	public static void main(String[] args) {
		List<String> allFields = getAllFields(OmsWaybill.class);
		System.out.println(JSON.toJSONString(allFields));
		boolean contains = allFields.contains("waybillStatusCode");
		System.out.println(contains);
	}
}
