package com.yl.applets.utils;

import com.yl.applets.service.IWxUserService;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-06-22
 */

@Component
@Slf4j
public class NumberIdUtils {

    @Value("${redis.numberid.key:1000000000}")
    private  Integer redisNumberId;


    /**
     *  这个key不要动、不要改名
     */
    private final static String CHANNEL_NUMBER_ID="channel_number_id";

    public final static String CHANNEL_NUMBER_ID_LOCK="APPLETS_CHANNEL:PHONE:LOCK:";

    /**
     *  这个key不要动、不要改名
     */
    private final static String APPLETS_JY_USER_ID="YL_JMS_APPLETS:JY_USER_ID";

    public final static String JY_NUMBER_ID_LOCK="APPLETS_JY:USER:ID:LOCK:";

    @Autowired
    private IWxUserService wxUserService;

    @Resource
    protected RedissonClient redissonClient;

    /**
     * redis获取numberid
     * @return
     */
    public Integer getNumberId() {
        Object o = RedisUtil.get(CHANNEL_NUMBER_ID);
        if (o != null) {
            return RedisUtil.incrBy(CHANNEL_NUMBER_ID, 1L).intValue();
        }
        //加锁
        RLock lock = redissonClient.getLock(BcRedisKeyEnum.APPLETS_MEMBER_ID_LOCK_KEY.keyBuilder("USER_ID"));
        try {
            boolean locked = lock.tryLock(10, 3, TimeUnit.SECONDS);// 设置锁的超时时
            if (locked) {
                o = RedisUtil.get(CHANNEL_NUMBER_ID);
                if (o != null) {
                    return RedisUtil.incrBy(CHANNEL_NUMBER_ID, 1L).intValue();
                } else {
                    Integer maxNumberId = wxUserService.maxNumberId();
                    maxNumberId = maxNumberId == null ? redisNumberId : maxNumberId;
                    if (maxNumberId.intValue() < redisNumberId.intValue()) { //redis不存在，且库里不存在redisNumberId自增的数据
                        RedisUtil.set(CHANNEL_NUMBER_ID, redisNumberId);
                    } else if (maxNumberId.intValue() > redisNumberId.intValue()) { //redis不存在，且库里存在redisNumberId自增的数据
                        RedisUtil.set(CHANNEL_NUMBER_ID, maxNumberId);
                    }
                }
                return RedisUtil.incrBy(CHANNEL_NUMBER_ID, 1L).intValue();
            } else {
                log.error("获取唯一memberId失败");
                throw new BusinessException(ResultCodeEnum.LOGIN_LOCK_ERROR);
            }
        } catch (Exception e) {
            log.error("获取唯一memberId失败");
            throw new BusinessException(ResultCodeEnum.LOGIN_LOCK_ERROR);
        } finally {
            lock.unlock();
        }
    }


    /**
     * redis获取JY_USER_ID
     * @return
     */
    public Integer getJyUserId() {
        Object o = RedisUtil.get(APPLETS_JY_USER_ID);
        if (o != null) {
            return RedisUtil.incrBy(APPLETS_JY_USER_ID, 1L).intValue();
        }
        //加锁
        RLock lock = redissonClient.getLock(BcRedisKeyEnum.APPLETS_CONSOLIDATION_JY_LOCK_KEY.keyBuilder("USER_ID"));
        try {
            // 获取锁，防止并发获取最大值并更新
            boolean locked = lock.tryLock(10, 3, TimeUnit.SECONDS);// 设置锁的超时时
            if (locked) {
                o = RedisUtil.get(APPLETS_JY_USER_ID);
                if (o != null) {
                    return RedisUtil.incrBy(APPLETS_JY_USER_ID, 1L).intValue();
                }
                Integer maxJyId = wxUserService.maxJYUserId();
                maxJyId = maxJyId == null ? 100000000 : maxJyId;
                RedisUtil.set(APPLETS_JY_USER_ID, maxJyId);
                return RedisUtil.incrBy(APPLETS_JY_USER_ID, 1L).intValue();
            } else {
                log.error("获取唯一id失败");
                throw new BusinessException(ResultCodeEnum.SERVICE_BUSY_ERROR);
            }
        } catch (Exception e) {
            log.error("获取唯一id异常==>{}", e);
            throw new BusinessException(ResultCodeEnum.SERVICE_BUSY_ERROR);
        } finally {
            lock.unlock();
        }
    }



}
