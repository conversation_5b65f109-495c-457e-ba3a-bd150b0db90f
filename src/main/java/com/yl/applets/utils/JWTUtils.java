package com.yl.applets.utils;

import com.alibaba.fastjson.util.TypeUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2020-02-25 15:31 <br>
 * @Author: zhipeng.liu
 */

@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public abstract class JWTUtils {

    public static final String UID = "userId";
    private static final String SECRET = "WgtqaT1HNTZPZNMDJu3k";
    private static final long EXPIRE = 1000L;

    public static final String H5_UID = "id";
    private static final String H5_SECRET = "WgtqaT1HNTZPZNMDJu3k";



    /**
     * 生成token
     *
     * @param userId
     * @return
     */
    public static String generateById(Integer userId, long expire) {
        Date nowDate = new Date();
        //过期时间
        Date expireDate = new Date(nowDate.getTime() + EXPIRE * expire);
        Map<String, Object> claims = new HashMap<>(1);
        claims.put(UID, userId);
        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(nowDate)
                .setExpiration(expireDate)
                .signWith(SignatureAlgorithm.HS512, SECRET)
                .compact();
    }

    /**
     * 生成token
     *
     * @param openId
     * @return
     */
    public static String generateH5Token(String openId, long expire) {
        Date nowDate = new Date();
        //过期时间
        Date expireDate = new Date(nowDate.getTime() + EXPIRE * expire);
        Map<String, Object> claims = new HashMap<>(1);
        claims.put(H5_UID, openId);
        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(nowDate)
                .setExpiration(expireDate)
                .signWith(SignatureAlgorithm.HS512, H5_SECRET)
                .compact();
    }
    /**
     * 解析Claims
     *
     * @param token
     * @return
     */
    public static Claims getClaim(String token) {
        Claims claims = null;
        try {
            claims = Jwts.parser()
                    .setSigningKey(SECRET)
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            //解析失败
            log.info("无效的token.{}",e.getMessage());
        }
        return claims;
    }

    /**
     * 获取jwt发布时间
     */
    public static Date getIssuedAt(String token) {
        Claims claims = getClaim(token);
        if(claims==null){
            return null;
        }
        return claims.getIssuedAt();
    }

    /**
     * 获取openid
     */
    public static String getOpenId(String token) {
        Claims claims = getClaim(token);
        if(claims==null){
            return null;
        }
        return TypeUtils.castToString(claims.get(UID));
    }

    public static Integer getUserId(String token) {
        Claims claims = getClaim(token);
        if(claims==null){
            return null;
        }
        return TypeUtils.castToInt(claims.get(UID));
    }

    /**
     * 获取jwt失效时间
     */
    public static Date getExpiration(String token) {
        Claims claims = getClaim(token);
        if(claims==null){
            return null;
        }
        return claims.getExpiration();
    }

    /**
     * 验证token是否失效
     *
     * @param token
     * @return true:过期   false:没过期
     */
    public static boolean isExpired(String token) {
        try {
            final Date expiration = getExpiration(token);
            if(expiration==null){
                return false;
            }
            return expiration.before(new Date());
        } catch (ExpiredJwtException expiredJwtException) {
            return true;
        }

    }
}
