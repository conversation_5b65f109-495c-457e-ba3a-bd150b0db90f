package com.yl.applets.utils;

import lombok.extern.slf4j.Slf4j;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-27 16:52
 */
@Slf4j
public class SmsUtil {

//    public static final String MSG_CONTENT = "您的验证码是%s,此验证码用于解绑或更换手机号。15分钟内有效。";
//
//    public static void main(String[] args) {
//        //短信下发
//        String sendUrl = "https://smssh1.253.com/msg/send/json";
//        Map map = new HashMap();
//        map.put("account", "N3573862");//API账号
//        map.put("password", "Gl7Dq7Cga");//API密码
//        map.put("msg", "您好，您的验证码是******");//短信内容
//        map.put("phone", "***********");//手机号
//        map.put("report", "true");//是否需要状态报告
//        map.put("extend", "123");//自定义扩展码
//        JSONObject js = (JSONObject) JSONObject.toJSON(map);
//        System.out.println(sendSmsByPost(sendUrl, js.toString()));
//        //查询余额
//        String balanceUrl = "https://smssh1.253.com/msg/balance/json";
//        Map map1 = new HashMap();
//        map1.put("account", "N3573862");
//        map1.put("password", "Gl7Dq7Cga");
//        JSONObject js1 = (JSONObject) JSONObject.toJSON(map1);
//        System.out.println(sendSmsByPost(balanceUrl, js1.toString()));
//    }
//
//    /**
//     * 创蓝短信平台同一个手机号的短信发送频率为一天10条 一小时5条 一分钟2条，超过会被限制，需要找客服解除限制
//     *
//     * @param phone
//     * @param msg
//     * @return
//     */
//    public static String send(String phone, String msg) {
//        //短信下发
//        String sendUrl = "https://smssh1.253.com/msg/send/json";
//        Map map = new HashMap();
//        map.put("account", "N3573862");//API账号
//        map.put("password", "Gl7Dq7Cga");//API密码
//        map.put("msg", msg);//短信内容
//        map.put("phone", phone);//手机号
//        map.put("report", "true");//是否需要状态报告
//        map.put("extend", "123");//自定义扩展码
//        JSONObject js = (JSONObject) JSONObject.toJSON(map);
//        return sendSmsByPost(sendUrl, js.toString());
//    }
//
//    public static String sendSmsByPost(String path, String postContent) {
//        URL url;
//        try {
//            url = new URL(path);
//            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
//            httpURLConnection.setRequestMethod("POST");
//            httpURLConnection.setConnectTimeout(10000);
//            httpURLConnection.setReadTimeout(10000);
//            httpURLConnection.setDoOutput(true);
//            httpURLConnection.setDoInput(true);
//            httpURLConnection.setRequestProperty("Charset", "UTF-8");
//            httpURLConnection.setRequestProperty("Content-Type", "application/json");
//            httpURLConnection.connect();
//            OutputStream os = httpURLConnection.getOutputStream();
//            os.write(postContent.getBytes("UTF-8"));
//            os.flush();
//            StringBuilder sb = new StringBuilder();
//            int httpRspCode = httpURLConnection.getResponseCode();
//            if (httpRspCode == HttpURLConnection.HTTP_OK) {
//                BufferedReader br = new BufferedReader(
//                        new InputStreamReader(httpURLConnection.getInputStream(), "utf-8"));
//                String line;
//                while ((line = br.readLine()) != null) {
//                    sb.append(line);
//                }
//                br.close();
//                return sb.toString();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return null;
//    }

}
