package com.yl.applets.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yl.applets.dto.PreferencesAddDto;
import com.yl.applets.dto.PreferencesCommonDto;
import com.yl.applets.feign.ReceivingPreferencesApiClient;
import com.yl.applets.vo.OrderMarkingVo;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2024-04-25
 */
@Component
@Slf4j
public class OrderMarkingHelper {

    @Autowired
    private ReceivingPreferencesApiClient receivingPreferencesApiClient;

    /**
     * 校验标识,并赋值
     * @param dto
     */
    public void checkMarking(PreferencesCommonDto dto) {

        List<PreferencesCommonDto.OrderMarkingStatus> markingStatusList = dto.getMarkingStatusList();
        if(CollectionUtils.isEmpty(markingStatusList)){
            log.info("标签为空");
            throw new ServiceException(ResultCodeEnum.MARKING_IS_NULL);
        }
        Result<List<OrderMarkingVo>> resource = receivingPreferencesApiClient.getResource(null);
        log.info("调用中台,获取订单标识==>{}", JSON.toJSONString(resource));
        List<OrderMarkingVo> result = resource.result();
        Map<String, OrderMarkingVo> map= Maps.newHashMap();
        for (OrderMarkingVo orderMarkingVo : result) {
            map.put(orderMarkingVo.getCode()+"&"+orderMarkingVo.getName(),orderMarkingVo);
            if(!CollectionUtils.isEmpty(orderMarkingVo.getChild())){
                orderMarkingVo.getChild().forEach(child->{
                    map.put(child.getCode()+"&"+child.getName(),child);
                });
            }
        }
        for (PreferencesCommonDto.OrderMarkingStatus orderMarkingStatus : markingStatusList) {
            String code = orderMarkingStatus.getCode();
            String name = orderMarkingStatus.getName();
            List<PreferencesCommonDto.OrderMarkingStatus> childList = orderMarkingStatus.getChild();
            if(StringUtils.isNotBlank(code)&&StringUtils.isNotBlank(name)){//一级标签
                OrderMarkingVo orderMarkingVo = map.get(code + "&" + name);
                if(orderMarkingVo==null){
                    log.info("一级标签校验失败");
                    throw new ServiceException(ResultCodeEnum.MARKING1_IS_NOT_EXIST);
                }else {
                    //赋值一级
                    if(code.contains("GZR")){
                        dto.setWeekdayOneLevelCode(code);
                        dto.setWeekdayOneLevelName(orderMarkingVo.getType()+name);
                    }else {
                        dto.setWeekendOneLevelCode(code);
                        dto.setWeekendOneLevelName(orderMarkingVo.getType()+name);
                    }
                    if(CollectionUtils.isNotEmpty(childList)) {//一级标签相同,校验二级标签
                        if (childList.size() > 1) {
                            log.info("工作日二级标签校验失败");
                            throw new ServiceException(ResultCodeEnum.MARKING2_IS_NOT_EXIST);
                        }
                        String weekdayTwoLevelNameZDY = "";
                        String weekdayTwoLevelCode = "";
                        String weekdayTwoLevelName = "";
                        for (PreferencesAddDto.OrderMarkingStatus markingStatus : childList) {
                            weekdayTwoLevelNameZDY = markingStatus.getName();
                            weekdayTwoLevelName = markingStatus.getName();
                            weekdayTwoLevelCode = markingStatus.getCode();
                            break;
                        }
                        //自定义处理
                        if (weekdayTwoLevelCode.contains("ZDY")) {
                            weekdayTwoLevelName = "自定义";
                        }
                        String code1Level = orderMarkingVo.getCode();
                        OrderMarkingVo orderMarkingVo2 = map.get(weekdayTwoLevelCode + "&" + weekdayTwoLevelName);
                        if (orderMarkingVo2 == null || !StringUtils.equals(code1Level, orderMarkingVo2.getType())) {
                            log.info("工作日二级标签校验失败");
                            throw new ServiceException(ResultCodeEnum.MARKING2_IS_NOT_EXIST);
                        } else {
                            //赋值二级
                            if (code.contains("GZR")) {
                                dto.setWeekdayTwoLevelCode(weekdayTwoLevelCode);
                                if (weekdayTwoLevelCode.contains("ZDY")) {
                                    dto.setWeekdayTwoLevelName(weekdayTwoLevelNameZDY);
                                } else {
                                    dto.setWeekdayTwoLevelName(weekdayTwoLevelName);
                                }
                            } else {
                                dto.setWeekendTwoLevelCode(weekdayTwoLevelCode);
                                if (weekdayTwoLevelCode.contains("ZDY")) {
                                    dto.setWeekendTwoLevelName(weekdayTwoLevelNameZDY);
                                } else {
                                    dto.setWeekendTwoLevelName(weekdayTwoLevelName);
                                }
                            }
                        }
                    }
                }
            }else {
                log.info("标签为空");
                throw new ServiceException(ResultCodeEnum.MARKING_IS_NULL);
            }
        }

    }


}
