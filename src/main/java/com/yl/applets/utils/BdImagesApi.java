package com.yl.applets.utils;

import com.baidu.aip.ocr.AipOcr;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.HashMap;

@Slf4j
public class BdImagesApi {
    //设置APPID/AK/SK
    public static final String APP_ID = "19162268";
    public static final String API_KEY = "G3pyrrbqjqtvfFLoeqRoeWsW";
    public static final String SECRET_KEY = "oNmW11FicUtpXo5M8FamLGBfti0eYfCb";

    // 初始化一个AipOcr
    public static     AipOcr client = new AipOcr(APP_ID, API_KEY, SECRET_KEY);
    static{
        // 可选：设置网络连接参数
        client.setConnectionTimeoutInMillis(2000);
        client.setSocketTimeoutInMillis(60000);
    }

    public static void main(String[] args) {

        AipOcr client = new AipOcr(APP_ID, API_KEY, SECRET_KEY);

        // 可选：设置网络连接参数
        client.setConnectionTimeoutInMillis(2000);
        client.setSocketTimeoutInMillis(60000);

        // 可选：设置代理服务器地址, http和socket二

        // 可选：设置log4j日志输出格式，若不设置，则使用默认配置
        // 也可以直接通过jvm启动参数设置此环境变量
//        System.setProperty("aip.log4j.conf", "path/to/your/log4j.properties");

        // 调用接口
        String path = "/Users/<USER>/1.png";
        JSONObject res = client.basicGeneral(path, new HashMap<String, String>());
//        System.out.println(res.toString(2));
    }

    /**
     * 智能识别
     * @param args
     * @return
     */
    public static JSONObject getResult(byte[] args) {
        try {
            return client.basicGeneral(args, new HashMap<>());
        } catch (Exception e) {
            log.error("BdImagesApi error:{}",e.getMessage());
            throw new ServiceException(ResultCodeEnum.INTERFACE_OUTER_INVOKE_ERROR);
        }

    }



}
