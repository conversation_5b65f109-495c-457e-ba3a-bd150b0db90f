package com.yl.applets.utils;

import com.yl.common.base.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/2/6 14:01
 * @description
 */
@Component
@Slf4j
public class InvokeCardUtil {

    @Resource
    private RedisTemplate<String, Integer> redisTemplate;

    /**
     * 获取当前时间到凌晨12点的秒数
     */
    public Long getSecondsNextEarlyMorning() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return (cal.getTimeInMillis() - System.currentTimeMillis()) / 1000;
    }

    /**
     * 获取当前年月日
     */
    private static String getCurrentDate() {
        LocalDate localDate = LocalDate.now();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
        return df.format(localDate);
    }

    /**
     * @param key   key
     * @param limit 限制次数
     */
    public void increment(String key, Integer limit) {
        key += InvokeCardUtil.getCurrentDate();
        log.info("[新增实名制信息]==>进行限额次数的key: {}", key);
        RedisAtomicLong atomicLong = this.getAtomicLong(key);
        // 不存在这个key
        long invokeCount = atomicLong.get();
        log.info("[新增实名制信息]==>之前执行次数: {}", invokeCount);
        if (invokeCount == 0) {
            log.info("[新增实名制信息]==>初次执行");
            atomicLong.incrementAndGet();
            atomicLong.expire(this.getSecondsNextEarlyMorning(), TimeUnit.SECONDS);
            return;
        }
        if (invokeCount + 1 > limit) {
            log.info("[新增实名制信息]==>当日当前执行次数超过3次");
            throw new BusinessException(999999, "认证次数达到每日上限");
        }
        long get = atomicLong.incrementAndGet();
        log.info("[新增实名制信息]==>当前执行次数: {}", get);
    }


    private RedisAtomicLong getAtomicLong(final String key) {
        return new RedisAtomicLong(key, Objects.requireNonNull(redisTemplate.getConnectionFactory()));
    }

}
