package com.yl.applets.utils;

import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.common.base.exception.BusinessException;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegexUtils {

    public static List<Map<String,String>> addressResolution(String address){
        String regex="((?<province>[^省]+省|.+自治区)|上海|北京|天津|重庆)(?<city>[^市]+市|.+自治州)(?<county>[^县]+县|.+区|.+镇|.+局)?(?<town>[^区]+区|.+镇)?(?<village>.*)";
        Matcher m= Pattern.compile(regex).matcher(address);
        String province=null,city=null,county=null,town=null,village=null;
        List<Map<String,String>> table=new ArrayList<Map<String,String>>();
        Map<String,String> row=null;
        while(m.find()){
            row=new LinkedHashMap<String,String>();
            province=m.group("province");
            row.put("province", province==null?"":province.trim());
            city=m.group("city");
            row.put("city", city==null?"":city.trim());
            county=m.group("county");
            row.put("county", county==null?"":county.trim());
            town=m.group("town");
            row.put("town", town==null?"":town.trim());
            village=m.group("village");
            row.put("village", village==null?"":village.trim());
            table.add(row);
        }
        return table;
    }

    public static String handlePhone(String moblile){
        String reg = "^[0-9\\+]*?1[3456789]\\d{9}$";
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = null;
        //过滤非数字字符  如 158-517 234 34  =》 ***********
        moblile = moblile.replaceAll("\\D", "");
        //匹配手机号
        matcher = pattern.matcher(moblile);
        if(moblile.length() >= 11 && matcher.find()){
            moblile = moblile.substring(moblile.length() - 11);
        }else if(!matcher.find()){
            throw new BusinessException(ServiceErrCodeEnum.MOBILE_IS_ERROR);
        }
        return moblile;
    }

    public static void main(String[] args) {
        String[] placeList = new String[]{"浙江省杭州市滨江区","上海上海市金山区","浙江省台州市玉环县","湖北省潜江市潜江经济开发区","湖北省潜江市江汉石油管理局","湖北省天门市马湾镇"};
        for (String place : placeList) {
//            System.out.println(addressResolution(place));
        }
    }

}
