package com.yl.applets.utils;

public class RedisUtil {
    final static String APP_KEY = "_BC:";

    /**
     * @Description: session命名空间
     * @param:
     * @return:
     * @auther: <PERSON>
     * @date: 2018/6/4 0004-12:08
     */
    public static String getSessionKey(String key) {
        key = "_SESSION:" + key;
        return getRedisKey(key);
    }

    /**
     * @Description: 验证码命名空间
     * @param:
     * @return:
     * @auther: <PERSON>
     * @date: 2018/6/4 0004-12:08
     */
    public static String getCodePrefix() {
        return getRedisKey("_CODE");
    }

    public static String getRedisKey(String key) {
        return APP_KEY + key;
    }
}
