/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: ResultUtil
 * Author:   luhong
 * Date:     2020-10-14 17:47
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.utils;

import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-14
 * @since 1.0.0
 */
public class ResultUtil {
    private static final Logger log = LoggerFactory.getLogger(ResultUtil.class);

    public ResultUtil() {
    }

    public static void checkFeignResult(Result result) {
        boolean success = result != null && result.getCode() == ResultCodeEnum.SUCCESS.getCode();
        if (result == null) {
            throw new BusinessException(ResultCodeEnum.SERVICE_TRANSFER_ERROR);
        } else if (!success) {
            log.error("远程调用获取数据失败:{}", result.getMsg());
            throw new BusinessException(result.getCode(), result.getMsg());
        }
    }
}