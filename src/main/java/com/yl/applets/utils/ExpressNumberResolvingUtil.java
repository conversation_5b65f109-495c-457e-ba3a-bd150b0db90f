package com.yl.applets.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.URLEncoder;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/11/22 15:56
 */
@Slf4j
public class ExpressNumberResolvingUtil {

    public static String numberResolving(String url) {
        String result = null;
        CloseableHttpClient httpClient = null;
        try {

            // 超时时间设置（单位：毫秒）
            int connectTimeout = 3000;   // 连接超时（3秒）
            int socketTimeout = 5000;   // 读取超时（3秒）
            // 创建 RequestConfig 配置
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(connectTimeout)
                    .setSocketTimeout(socketTimeout)
                    .build();

            // 创建 HttpClient 并应用超时设置
            httpClient = HttpClients.custom()
                    .setDefaultRequestConfig(requestConfig) // 设置默认超时
                    .build();


            result = "";

            // 创建get方式请求对象
            HttpGet httpGet = new HttpGet(url);
            httpGet.addHeader("Content-type", "application/json");
            // 通过请求对象获取响应对象
            CloseableHttpResponse response = httpClient.execute(httpGet);
            // 获取结果实体
            // 判断网络连接状态码是否正常(0--200都数正常)
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                result = EntityUtils.toString(response.getEntity(), "utf-8");
            }
            // 释放链接
            response.close();
        } catch (IOException e) {
            log.error("nmberResolving err:{}", e.getMessage());
        } finally {
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    log.error("httpClient关闭失败：{}", e);
                }
            }
        }

        return result;
    }
}
