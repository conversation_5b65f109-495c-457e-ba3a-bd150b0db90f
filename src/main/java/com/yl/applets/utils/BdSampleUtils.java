package com.yl.applets.utils;

import org.json.JSONObject;

public class BdSampleUtils {
    //设置APPID/AK/SK
    public static final String APP_ID = "19103566";
    public static final String API_KEY = "SIm6Tma6y5MBBGONRozARO4T";
    public static final String SECRET_KEY = "EUbZhk2akGFUxelzmMxlL1KtFNdcA5kR";
    // 初始化一个AipNlp
    public static    YLAipNlp client = new YLAipNlp(APP_ID, API_KEY, SECRET_KEY);
    static{
        // 可选：设置网络连接参数
        client.setConnectionTimeoutInMillis(2000);
        client.setSocketTimeoutInMillis(60000);
    }
    public static void main(String[] args) {
        // 初始化一个AipNlp
        YLAipNlp client = new YLAipNlp(APP_ID, API_KEY, SECRET_KEY);

        // 可选：设置网络连接参数
        client.setConnectionTimeoutInMillis(2000);
        client.setSocketTimeoutInMillis(60000);
        JSONObject x = client.address("上海青浦区华新镇凤溪公园", null);
        System.out.println(x);
        // 可选：设置代理服务器地址, http和socket二选一，或者均不设置
//        client.setHttpProxy("proxy_host", proxy_port);  // 设置http代理
//        client.setSocketProxy("proxy_host", proxy_port);  // 设置socket代理
//        AipRequest request = new AipRequest();
//        request.setUri("https://aip.baidubce.com/rpc/2.0/nlp/v1/address");
//        request.addHeader("Content-Encoding", "GBK");
//        request.addHeader("Content-Type", "application/json");
//        request.setBodyFormat(EBodyFormat.RAW_JSON);
//        // 调用接口
//        org.json.JSONObject lexer = client.address("地址详情", null);
//        System.out.println(lexer);


//        AipOcr client = new AipOcr(APP_ID, API_KEY, SECRET_KEY);
//
//        // 可选：设置网络连接参数
//        client.setConnectionTimeoutInMillis(2000);
//        client.setSocketTimeoutInMillis(60000);
//
//        // 可选：设置代理服务器地址, http和socket二
//
//        // 可选：设置log4j日志输出格式，若不设置，则使用默认配置
//        // 也可以直接通过jvm启动参数设置此环境变量
////        System.setProperty("aip.log4j.conf", "path/to/your/log4j.properties");
//
//        // 调用接口
//        String path = "/Users/<USER>/1.png";
//        JSONObject res = client.basicGeneral(path, new HashMap<String, String>());
//        System.out.println(res.toString(2));
    }

    /**
     * 智能识别
     * @param args
     * @return
     */
    public static JSONObject getResult(String args) {

        // 调用接口
        return client.address(args, null);
    }



}
