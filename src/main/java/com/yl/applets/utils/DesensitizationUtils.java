package com.yl.applets.utils;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang.StringUtils;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-05-23 16:51
 * @Version 1.0
 */

public class DesensitizationUtils {


    /**
     * 姓名脱敏
     *
     * @param name 参数
     * @return
     */
    public static String desensitizationName(String name) {
        if (StrUtil.isBlank(name)) {
            return "";
        }
        if (name.length() > 1) {
            return name.substring(0, 1).concat("*");
        }
        return name;
    }


    /**
     * 地址脱敏
     *
     * @param address 参数
     * @return
     */
    public static String desensitizationAddress(String address) {
        if (StrUtil.isBlank(address)) {
            return "";
        }
        return address.replaceAll("[a-zA-Z0-9]", "*");

    }


    /**
     * 手机号脱敏
     *
     * @param phone 参数
     * @return
     */
    public static String desensitizationPhone(String phone) {

        if (StrUtil.isBlank(phone)) {
            return "";
        }
        return phone.length() > 4 ? phone.substring(0, phone.length() - 4).replaceAll("[a-zA-Z0-9\\*]", "*").concat(phone.substring(phone.length() - 4)) : phone;

    }

    /**
     * 手机号脱敏 保留最后4位
     * @param mobileStr
     * @return
     */
    public static String desensitizeString(String mobileStr) {
        // 如果字符串为null或长度小于等于4位，直接返回原字符串
        if (StringUtils.isEmpty(mobileStr) || mobileStr.length() <= 4 ) {
            return mobileStr;
        }
        int length = mobileStr.length();
        if (length == 11) {
            // 保留前3位和后4位字符
            String frontPart = mobileStr.substring(0, 3);
            String endPart = mobileStr.substring(length - 4);
            return frontPart + "****" + endPart;
        } else {
            // 保留最后4位字符
            String endPart = mobileStr.substring(length - 4);
            int maskLength = length - 4;
            StringBuilder maskedString = new StringBuilder();
            for (int i = 0; i < maskLength; i++) {
                maskedString.append("*");
            }
            maskedString.append(endPart);
            return maskedString.toString();
        }
    }

}
