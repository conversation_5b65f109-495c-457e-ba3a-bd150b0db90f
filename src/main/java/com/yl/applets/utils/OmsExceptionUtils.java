package com.yl.applets.utils;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2020-04-19 17:46 <br>
 * @Author: <a href="<EMAIL>">qiang.hu</a>
 */
public class OmsExceptionUtils {

    private OmsExceptionUtils() {

    }

    public static String getStackTrace(Throwable throwable) {
        return (StringUtils.isNotBlank(throwable.getMessage()) ? throwable.getMessage() : "") + ExceptionUtils.getStackTrace(throwable);
    }

}
