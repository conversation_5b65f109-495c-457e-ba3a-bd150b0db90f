package com.yl.applets.utils;

import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.UnsupportedEncodingException;
import java.util.Map;
import java.util.TreeMap;

public class ParamSignUtils {

    private static final String CHARSET = "utf-8";

    public static String getDigest(TreeMap<String, Object> map, String key) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry entry : map.entrySet()) {
            sb = sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        sb.append("key").append("=").append(key);
//        System.out.println("拼接后的字符：" + sb.toString());
        String sign = DigestUtils.md5Hex(getContentBytes(sb.toString(), CHARSET));
//        System.out.println("加密后的签名：" + sign);
        return sign;
    }

    private static byte[] getContentBytes(String content, String charset) {
        if (charset == null || "".equals(charset)) {
            return content.getBytes();
        }
        try {
            return content.getBytes(charset);
        } catch (UnsupportedEncodingException e) {
            throw new BusinessException(ResultCodeEnum.BUSINESS_ERROR);
        }
    }

    public static void main(String[] args) {
        //treeMap默认是key升序排序 ,如果需要降序,可以使用Comparator中的compare方法
        TreeMap<String, Object> map = new TreeMap<String, Object>();
        map.put("channelCode", "MP");
        map.put("apiAccount","1001");
        getDigest(map, "abb9c413dc5a49fcb859bdac82aa93e3");
    }

}
