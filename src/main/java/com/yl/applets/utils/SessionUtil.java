package com.yl.applets.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yl.applets.entity.WxUser;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.service.IWxUserService;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;


/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-08-06 11:07 <br>
 * @Author: zhipeng.liu
 */

@Slf4j
@Component
public class SessionUtil {

    public final static String USER_ID_KEY = "_unid";

    public final static String USER_INFO = "_user";

    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private OrikaBeanMapper beanMapper;


    public WxUserVo getUser() {

        Object attribute = RequestContextHolder.getRequestAttributes().getAttribute(SessionUtil.USER_INFO, 0);
        if(attribute == null){
            return null;
        }
        String userJson = RequestContextHolder.getRequestAttributes().getAttribute(SessionUtil.USER_INFO, 0).toString();
        WxUserVo loginVO = JSONObject.parseObject(userJson, WxUserVo.class);
        if (loginVO == null || loginVO.getNumberId() ==null || StringUtils.isEmpty(loginVO.getMobile())) {
            log.warn("用户信息不全，重新登录");
            throw new BusinessException(ServiceErrCodeEnum.LOGIN_HAS_EXPIRED);
        }
        if(loginVO.getId()==null){
            //补丁，2021年3月25日01:03:29  迁移idc服务出现缓存ID为空的问题
            log.info("缓存ID为null的用户信息:{}",JsonUtils.toJson(loginVO));
            QueryWrapper<WxUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("openid", loginVO.getOpenid());
            queryWrapper.eq("is_delete",0);
            WxUser wxUser = wxUserService.getOne(queryWrapper);
            WxUserVo wxUserVo = beanMapper.map(wxUser, WxUserVo.class);
//            wxUserVo.setSessionKey(loginVO.getSessionKey());
            wxUserVo.setNickName(loginVO.getNickName());
            wxUserVo.setToken(loginVO.getToken());
            return wxUserVo;
        }
        log.info("当前用户信息：{}" , JSON.toJSONString(loginVO));
        return loginVO;
//        String openId = getOpenId();
//        Object obj = com.yl.redis.util.RedisUtil.get(BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(openId));
//        if (obj == null) {
//            throw new BusinessException(ServiceErrCodeEnum.OAUTH_NO_ACCESS);
//        }
//        WxUserVo loginVO = JSONObject.parseObject(obj.toString(), WxUserVo.class);
//        if (loginVO == null) {
//            throw new BusinessException(ServiceErrCodeEnum.OAUTH_NO_ACCESS);
//        }
//        return loginVO;
    }

    protected String getOpenId() {
        String unid = RequestContextHolder.getRequestAttributes().getAttribute(SessionUtil.USER_ID_KEY, 0).toString();
        if (null == unid) {
            log.error(" === 请在登录后调用此接口 ====");
            throw new BusinessException(ServiceErrCodeEnum.LOGIN_HAS_EXPIRED);
        }
        return unid;
    }

}
