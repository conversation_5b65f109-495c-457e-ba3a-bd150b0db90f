package com.yl.applets.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Decoder;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.io.IOException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-04-16 23:53
 * @Version 1.0
 */
@Slf4j
@Component
public class DESUtils {

    private final static String DES = "DES";

    public static void main(String[] args) throws Exception {
        String data = "yangdezhi";
        String key = "oijj@jkjlkjlk";
        System.err.println("加密:" + encrypt(data, key));
        System.err.println("解密:" + decrypt(encrypt(data, key), key));
    }

    /**
     * 功能： 根据键值进行加密
     *
     * @param data
     * @param key
     * @return java.lang.String
     **/
    public static String encrypt(String data, String key) {
        try {
            byte[] bt = encrypt(data.getBytes(), key.getBytes());
            return Base64.getMimeEncoder(Integer.MAX_VALUE, new byte[0]).encodeToString(bt);
        } catch (Exception e) {
            log.error("加解密异常，data:{} key:{}", data, key);
            return data;
        }
    }

    /**
     * 功能： 根据键值进行解密
     *
     * @param data
     * @param key
     * @return java.lang.String
     **/
    public static String decrypt(String data, String key) throws IOException,
            Exception {
        if (data == null)
            return null;
        BASE64Decoder decoder = new BASE64Decoder();
        byte[] buf = decoder.decodeBuffer(data);
        byte[] bt = decrypt(buf, key.getBytes());
        return new String(bt);
    }

    /**
     * 功能： 根据键值进行加密
     *
     * @param data
     * @param key
     * @return byte[]
     **/
    private static byte[] encrypt(byte[] data, byte[] key) throws Exception {
        // 生成一个可信任的随机数源
        SecureRandom sr = new SecureRandom();

        // 从原始密钥数据创建DESKeySpec对象
        DESKeySpec dks = new DESKeySpec(key);

        // 创建一个密钥工厂，然后用它把DESKeySpec转换成SecretKey对象
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(DES);
        SecretKey securekey = keyFactory.generateSecret(dks);

        // Cipher对象实际完成加密操作
        Cipher cipher = Cipher.getInstance(DES);

        // 用密钥初始化Cipher对象
        cipher.init(Cipher.ENCRYPT_MODE, securekey, sr);

        return cipher.doFinal(data);
    }


    /**
     * 功能： 根据键值进行解密
     *
     * @param data
     * @param key
     * @return byte[]
     **/
    private static byte[] decrypt(byte[] data, byte[] key) throws Exception {
        // 生成一个可信任的随机数源
        SecureRandom sr = new SecureRandom();

        // 从原始密钥数据创建DESKeySpec对象
        DESKeySpec dks = new DESKeySpec(key);

        // 创建一个密钥工厂，然后用它把DESKeySpec转换成SecretKey对象
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(DES);
        SecretKey securekey = keyFactory.generateSecret(dks);

        // Cipher对象实际完成解密操作
        Cipher cipher = Cipher.getInstance(DES);

        // 用密钥初始化Cipher对象
        cipher.init(Cipher.DECRYPT_MODE, securekey, sr);

        return cipher.doFinal(data);
    }

}
