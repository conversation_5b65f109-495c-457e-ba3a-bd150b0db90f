package com.yl.applets.utils;

import java.util.Base64;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: xiongweibin
 * @create: 2020-10-15 15:05
 */
public class Base64Utils {
    static final String salt = "hKja67KAhd123";
    /**
     * 加密
     * @param str
     * @return
     */
    public static String encode(String str) {
        byte[] bytes = (str + salt).getBytes();
        //Base64 加密
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * 解密
     * @param str
     */
    public static String decode(String str){
        //Base64 解密
        byte[] decoded = Base64.getDecoder().decode(str);
        return new String(decoded).replace(salt,"");
    }


    public static void main(String[] args) {
        String encode = Base64Utils.encode("2022-08-13 00:00:01");
        System.out.println(encode);
        String decode = Base64Utils.decode(encode);
        System.out.println(decode);
    }

}
