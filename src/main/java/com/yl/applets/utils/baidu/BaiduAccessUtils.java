package com.yl.applets.utils.baidu;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-09-18 20:51
 * @Version 1.0
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.*;

@Component
@Slf4j
public class BaiduAccessUtils {


    static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().build();

    public static String getToken() throws IOException {

        Object obj = RedisUtil.get(BcRedisKeyEnum.BAIDU_OCR_ACCESS_TOKEN.keyBuilder("aa"));
        log.info("查询百度OCR缓存：{}", JSON.toJSONString(obj));
        if (obj != null) return obj.toString();

        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "");
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/oauth/2.0/token?client_id=s6jwqB4p4CITFZDNn5G5WGzn&client_secret=ANKxUctdBR1uz0yKx0MTZ6eL2pweFklZ&grant_type=client_credentials")
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Accept", "application/json")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        String string = response.body().string();
        log.info("请求百度accessToken：{}", string);
        JSONObject jsonObject = JSON.parseObject(string);
        String token = jsonObject.getString("access_token");
        Integer expires = jsonObject.getInteger("expires_in");
        RedisUtil.setEx(BcRedisKeyEnum.BAIDU_OCR_ACCESS_TOKEN.keyBuilder("aa"), token, expires - 604800);
        log.info("缓存设置成功");
        return token;

    }

}
