package com.yl.applets.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.yl.applets.dto.AddressPCDDTO;
import com.yl.common.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.Arrays;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-11-08 15:26 <br>
 * @Author: zhipeng.liu
 */
@Slf4j
public class GdUtils {

    private GdUtils(){
        throw new IllegalStateException("Utility class");
    }

//    final static String URL = "https://restapi.amap.com/v3/geocode/geo?output=JSON&key=dbc9af58e1bf18b94c8892013a883f40&address=";
    final static String URL = "https://restapi.amap.com/v3/geocode/geo?output=JSON&key=b5f4ab5be6dcb94b76c9272fd4d4cf2b&address=";

    public static String getAddressDtail(String address) {
        String result = null;
        CloseableHttpClient httpClient = null;
        try {
            address = URLEncoder.encode(address, "UTF-8");

            result = "";
            // 创建httpclient对象
            httpClient = HttpClients.createDefault();
            // 创建get方式请求对象
            HttpGet httpGet = new HttpGet(URL + address);
            httpGet.addHeader("Content-type", "application/json");
            // 通过请求对象获取响应对象
            CloseableHttpResponse response = httpClient.execute(httpGet);
            // 获取结果实体
            // 判断网络连接状态码是否正常(0--200都数正常)
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                result = EntityUtils.toString(response.getEntity(), "utf-8");
            }
            // 释放链接
            response.close();
        } catch (IOException e) {
            log.error("getAddressDtail err:{}",e.getMessage());
        }finally {
            if(httpClient != null){
                try {
                    httpClient.close();
                } catch (IOException e) {
                    log.error("httpClient关闭失败：{}",e);
                }
            }
        }

        return result;
    }

    public static AddressPCDDTO getUserCardDTO(String address) {
        String resultStr = getAddressDtail(address);
        log.info("调用高德geo接口获取地址结果：{}",resultStr);
        if(StringUtils.isEmpty(resultStr)){
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(resultStr);
        if(ObjectUtils.isEmpty(jsonObject)){
            return null;
        }
        if(jsonObject.containsKey("status") && "1".equals(jsonObject.getString("status"))){
            JSONArray geocodes = jsonObject.getJSONArray("geocodes");
            if(geocodes!=null && !geocodes.isEmpty()){

                JSONObject geo = geocodes.getJSONObject(0);
                String province = geo.getString("province");
                String city = geo.getString("city");
                String district = geo.getString("district");
                //县级市高德返回city为"[]",2020年9月17日14:40:50  王耀如：city为[]时city取district
                city = "[]".equals(city)?district:city;
                district = "[]".equals(district)?city:district;
                AddressPCDDTO addressPCDDTO = new AddressPCDDTO();
                addressPCDDTO.setProvince(cleanProvinceName(province));
                addressPCDDTO.setCity(city);
                addressPCDDTO.setArea(district);
                return addressPCDDTO;
            }
        }
        return null;
    }

    /**
     * 高德清洗的省市区和我们数据库对不上，进行兼容处理
     * @return
     */
    public  static String cleanProvinceName(String province){
        String[] provinceArr = new String[]{"北京市","上海市","天津市","重庆市"};
        if(Arrays.asList(provinceArr).contains(province)){
            return province.replace("市","");
        }
        return province;
    }

}
