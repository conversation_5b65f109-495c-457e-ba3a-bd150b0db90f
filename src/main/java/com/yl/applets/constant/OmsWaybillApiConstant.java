package com.yl.applets.constant;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-08-21 11:44
 * @Author: <a href="<EMAIL>">chunlin.zhang</a>
 */
public interface OmsWaybillApiConstant {

    int NUMBER_ZERO = 0;

    int NUMBER_TEN = 10;

    int NUMBER_FIVE = 5;

    int NUMBER_TWO = 2;

    int NUMBER_EIGHTEEN = 18;

    int NUMBER_FIFTEEN = 15;

    int NUMBER_TWO_HUNDREAD = 200;

    int NUMBER_FIFTY = 50;

    int NUMBER_ONE_HUNDREAD = 100;

    int NUMBER_FIVE_THOUSAND = 5000;

    int NUMBER_THIRTY = 30;

    int NUMBER_SIXTY = 60;

    String SPLIT_SYMBOL = "-";

    //运单来源-JMS
    int WAYBILL_SOURCE_JMS = 1;

    //运单来源-巴枪
    int WAYBILL_SOURCE_BC = 2;

    //运单来源-订单
    int WAYBILL_SOURCE_ORDER = 3;

    //寄付现结
    String JFXJ = "PP_CASH";

    //寄付月结
    String JFYJ = "PP_PM";

    //到付现结
    String DFXJ = "CC_CASH";

    long ORDER_COLLECTION_KEY_EXPIRE = 7200L;

    String ORDER_COLLECTION_KEY_PREFEX = "OMSAPI:ORDERCOLLECTION:";


}
