package com.yl.applets.constant;

import java.util.ArrayList;
import java.util.List;

public class AppConstants  {

    private AppConstants(){
        throw new IllegalStateException("Utility class");
    }

    public static final List<String> waybillDetaiColumns = new ArrayList<>();

    static {
        waybillDetaiColumns.add("id");
        waybillDetaiColumns.add("waybillNo");
        waybillDetaiColumns.add("pickNetworkName");
        waybillDetaiColumns.add("deliveryTime");
        waybillDetaiColumns.add("collectStaffCode");
        waybillDetaiColumns.add("collectStaffName");
        waybillDetaiColumns.add("collectTime");
        waybillDetaiColumns.add("customerId");
        waybillDetaiColumns.add("customerCode");
        waybillDetaiColumns.add("customerName");
        waybillDetaiColumns.add("expressTypeId");
        waybillDetaiColumns.add("expressTypeCode");
        waybillDetaiColumns.add("dispatchCode");
        waybillDetaiColumns.add("dispatchName");
        waybillDetaiColumns.add("senderName");
        waybillDetaiColumns.add("senderMobilePhone");
        waybillDetaiColumns.add("senderTelphone");
        waybillDetaiColumns.add("senderPostalCode");
        waybillDetaiColumns.add("originId");
        waybillDetaiColumns.add("originCode");
        waybillDetaiColumns.add("originName");
        waybillDetaiColumns.add("isRealName");
        waybillDetaiColumns.add("receiverName");
        waybillDetaiColumns.add("receiverMobilePhone");
        waybillDetaiColumns.add("receiverTelphone");
        waybillDetaiColumns.add("receiverPostalCode");
        waybillDetaiColumns.add("receiverDetailedAddress");
        waybillDetaiColumns.add("destinationId");
        waybillDetaiColumns.add("destinationCode");
        waybillDetaiColumns.add("destinationName");
        waybillDetaiColumns.add("goodsTypeId");
        waybillDetaiColumns.add("goodsTypeCode");
        waybillDetaiColumns.add("goodsTypeName");
        waybillDetaiColumns.add("goodsName");
        waybillDetaiColumns.add("packageNumber");
        waybillDetaiColumns.add("packageTotalWeight");
        waybillDetaiColumns.add("settlementId");
        waybillDetaiColumns.add("settlementCode");
        waybillDetaiColumns.add("settlementName");
        waybillDetaiColumns.add("codNeed");
        waybillDetaiColumns.add("codMoney");
        waybillDetaiColumns.add("codFee");
        waybillDetaiColumns.add("insured");
        waybillDetaiColumns.add("insuredAmount");
        waybillDetaiColumns.add("insuredFee");
        waybillDetaiColumns.add("isNeedReceipt");
        waybillDetaiColumns.add("receiptNo");
        waybillDetaiColumns.add("freight");
        waybillDetaiColumns.add("remarks");
        waybillDetaiColumns.add("orderId");
        waybillDetaiColumns.add("senderFullAddress");
        waybillDetaiColumns.add("receiverFullAddress");
        waybillDetaiColumns.add("waybillStatusCode");
        waybillDetaiColumns.add("isRefund");
        waybillDetaiColumns.add("isSign");
        waybillDetaiColumns.add("dispatchStaffCode");
        waybillDetaiColumns.add("dispatchStaffName");
        waybillDetaiColumns.add("dispatchTime");
        waybillDetaiColumns.add("orderSourceName");
        waybillDetaiColumns.add("orderSourceCode");
        waybillDetaiColumns.add("receiverCityName");
        waybillDetaiColumns.add("receiverAreaName");
        waybillDetaiColumns.add("boxStandardName");
        waybillDetaiColumns.add("boxNumber");
        waybillDetaiColumns.add("boxPrice");
        waybillDetaiColumns.add("totalFreight");
        waybillDetaiColumns.add("senderCityName");
        waybillDetaiColumns.add("senderAreaName");
        waybillDetaiColumns.add("senderDetailedAddress");
        waybillDetaiColumns.add("receiverCountryName");
        waybillDetaiColumns.add("receiverProvinceName");
        waybillDetaiColumns.add("receiverTownship");
        waybillDetaiColumns.add("receiverStreet");
        waybillDetaiColumns.add("senderCountryName");
        waybillDetaiColumns.add("senderProvinceName");
        waybillDetaiColumns.add("senderTownship");
        waybillDetaiColumns.add("senderStreet");
        waybillDetaiColumns.add("signTime");
        waybillDetaiColumns.add("inputTime");
        waybillDetaiColumns.add("isPrivacy");
    }
}
