package com.yl.applets.constant;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 * 缓存常量类
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-08-17 15:41 <br>
 * @Author: zhoujw
 */
public class AppCacheConstant implements Serializable {

    public static final String SYSTEM_NAME = "APPLETS:";


    /* 订单运单状态 */
    public static final String SYS_WAYBILL_STATUS_LIST = SYSTEM_NAME + "SYS_WAYBILL_STATUS:LIST";

    /* 货币类型 */
    public static final String SYS_CURRENCY_TYPE_LIST = SYSTEM_NAME + "SYS_CURRENCY_TYPE:LIST";

    /* 结算方式 */
    public static final String SYS_PAYMENT_MANNER_LIST = SYSTEM_NAME + "SYS_PAYMENT_MANNER:LIST";

    /* 支付类型 */
    public static final String SYS_PAY_TYPE_LIST = SYSTEM_NAME + "SYS_PAY_TYPE:LIST";

    /* 费用类型 */
    public static final String SYS_SETTLEMENT_TYPE_LIST = SYSTEM_NAME + "SYS_SETTLEMENT_TYPE:LIST";

    /* 结算目的地 */
    public static final String SYS_SETTLEMENT_DESTINATION_LIST = SYSTEM_NAME + "SYS_SETTLEMENT_DESTINATION:LIST";

    /* 服务方式 */
    public static final String SYS_SERVICE_METHOD_LIST = SYSTEM_NAME + "SYS_SERVICE_METHOD:LIST";

    /* 产品类型 */
    public static final String SYS_PRODUCT_TYPE_LIST = SYSTEM_NAME + "SYS_PRODUCT_TYPE:LIST";

    /* 计量单位 */
    public static final String SYS_METERING_UNIT_LIST = SYSTEM_NAME + "SYS_METERING_UNIT:LIST";

    /* 物品类型 */
    public static final String SYS_ARTICLE_TYPE_LIST = SYSTEM_NAME + "SYS_ARTICLE_TYPE:LIST";

    /* 扫描类型 */
    public static final String SYS_SCANNING_TYPE_LIST = SYSTEM_NAME + "SYS_SCANNING_TYPE:LIST";

    /* 运输方式 */
    public static final String SYS_TRANSPORT_MANNER_LIST = SYSTEM_NAME + "SYS_TRANSPORT_MANNER:LIST";

    /* 异常件类型 */
    public static final String SYS_ABNORMAL_PRICE_LIST = SYSTEM_NAME + "SYS_ABNORMAL_PRICE:LIST";

    /* 数据字典 */
    public static final String SYS_DICTIONARY_LIST = SYSTEM_NAME + "SYS_DICTIONARY:LIST";

    /* 行政区划 */
    public static final String SYS_AREA_LIST = SYSTEM_NAME + "SYS_AREA:LIST_LIMIT";

    /* 寄件行政区划 */
    public static final String SYS_SENDER_AREA_LIST = SYSTEM_NAME + "SYS_SENDER_AREA:LIST_LIMIT";

    /* 收件行政区划 */
    public static final String SYS_RECEIVER_AREA_LIST = SYSTEM_NAME + "SYS_RECEIVER_AREA:LIST_LIMIT";

    /* 客户资料 */
    public static final String SYS_CUSTOMER_LIST = SYSTEM_NAME + "SYS_CUSTOMER:LIST";

    /* 网点资料 */
    public static final String SYS_NETWORK_LIST = SYSTEM_NAME + "SYS_NETWORK:LIST";

    /* 员工资料 */
    public static final String SYS_STAFF_LIST = SYSTEM_NAME + "SYS_STAFF:LIST";

    /* 车辆管理 */
    public static final String SYS_VEHICLE_LIST = SYSTEM_NAME + "SYS_VEHICLE:LIST";

    /* 基础数据综合数据 */
    public static final String SYS_BASE_DATA_DETAIL = SYSTEM_NAME + "SYS_BASE_DATA:DETAIL";

    /* 行政区划,树形结构 */
    public static final String AREA_TREE = SYSTEM_NAME + "AREA:TREE";

    /* 大区,所有*/
    public static final String REGIONAL_INFO_LIST = SYSTEM_NAME + "REGIONAL_INFO:LIST";

    /* 大区,详情*/
    public static final String REGIONAL_INFO_DETAIL = SYSTEM_NAME + "REGIONAL_INFO:DETAIL";

    /* 自动回复列表缓存*/
    public static final String WX_AUTO_REPLY_LIST = SYSTEM_NAME + "WX:AUTO:REPLY:LIST";

    public static final String WX_DISTRICT_LIST = SYSTEM_NAME + "WX:DISTRICT:LIST";
    public static final String WX_DISTRICT_LIST_FILTER = SYSTEM_NAME + "WX:DISTRICT:LIST:FILTER";

    /**
     * 回单金额
     */
    public static final BigDecimal RECEIPT_FREIGHT = new BigDecimal(5);


    public static Map<String, String> getMap() throws IllegalAccessException {
        Map<String, String> map = new HashMap<>();
        AppCacheConstant appCacheConstant = new AppCacheConstant();
        Class<? extends AppCacheConstant> cls = appCacheConstant.getClass();
        Field[] fields = cls.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            Field f = fields[i];
            f.setAccessible(true);
            if (f.get(appCacheConstant) != null) {
                map.put(f.get(appCacheConstant).toString().replace(SYSTEM_NAME, ""), f.get(appCacheConstant).toString());
            }
        }
        return map;
    }

}
