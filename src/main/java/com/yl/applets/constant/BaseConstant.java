package com.yl.applets.constant;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-08-17 15:41 <br>
 * @Author: zhoujw
 */

public class BaseConstant {
    public static final String APPLETS_FEIGN_CLIENT_PATH = "/applets";
    public static final String APPLETS_FEIGN_CLIENT_NAME = "yljmsapplets";

    public static final String FEIGN_CLIENT_PATH = "ylnetworkapi";
    private static final String APP_FCN = "/networkapi/app";
    private static final String WEB_FCN = "/networkapi/web";
    private static final String THIRD_FCN = "/networkapi/third";

    /*****************************WEB****************************/
    //企业银行定时任务
    public static final String PLATFORM_IBKSCHEDULE = WEB_FCN + "/ibkschedule";
    //企业银行
    public static final String PLATFORM_IBK = WEB_FCN + "/ibk";
    //物料平台
    public static final String PLATFORM_MSDM = WEB_FCN + "/msdm";
    //权限平台
    public static final String PLATFORM_OAUTH = WEB_FCN + "/oauth";
    //单证模块
    public static final String PLATFORM_OMS = WEB_FCN + "/oms";
    //操作模块
    public static final String PLATFORM_OPS = WEB_FCN + "/ops";
    //内部结算账单
    public static final String PLATFORM_SPMIBILL = WEB_FCN + "/spmibill";
    //内部结算报价
    public static final String PLATFORM_SPMI = WEB_FCN + "/spmi";
    //客户结算报价
    public static final String PLATFORM_SPMO = WEB_FCN + "/spmo";
    //第三方平台
    public static final String PLATFORM_THIRD = WEB_FCN + "/third";
    //通用edi
    public static final String PLATFORM_GENERAL_EDI = WEB_FCN + "/general/edi";
    //网点经营
    public static final String PLATFORM_NM = WEB_FCN + "/networkmanagement";
    //经营指标
    public static final String PLATFORM_BUSINESS_INDICATOR = WEB_FCN + "/businessindicator";
    //客户平台
    public static final String PLATFORM_CUSTOMER_PLATFORM = WEB_FCN + "/customerplatform";
    //订单模块
    public static final String PLATFORM_ORDER = WEB_FCN + "/order";
    //公告平台
    public static final String PLATFORM_NOTICE = WEB_FCN + "/notice";
    //短信平台
    public static final String PLATFORM_SMS = WEB_FCN + "/sms";
    //消息平台
    public static final String PLATFORM_SCM = WEB_FCN + "/scm";
    //智能设备
    public static final String PLATFORM_SMART_DEVICE = WEB_FCN + "/smartdevice";
    //isv
    public static final String PLATFORM_ISV = WEB_FCN + "/isv";
    //运力
    public static final String PLATFORM_TMS = WEB_FCN + "/tms";
    //服务质量
    public static final String PLATFORM_SQ = WEB_FCN + "/servicequality";
    //edi
    public static final String PLATFORM_EDI = WEB_FCN + "/edi";
    //VIP
    public static final String PLATFORM_VIP = WEB_FCN + "/vip";
    //客服系统
    public static final String PLATFORM_CSS = WEB_FCN + "/css";
    //实名制
    public static final String PLATFORM_OCR = WEB_FCN + "/ocr";




    /*****************************APP****************************/
    //巴枪APP
    public static final String PLATFORM_APPBC = APP_FCN + "/appbc";
    //delphi
    public static final String PLATFORM_DELPHI = APP_FCN + "/delphi";
    //小程序
    public static final String PLATFORM_APPLETS = APP_FCN + "/applets";



    /*****************************THIRD****************************/
    //丰巢
    public static final String PLATFORM_FCBOX = THIRD_FCN + "/fcbox";
    //循环贷
    public static final String PLATFORM_LOOPLOAN = THIRD_FCN + "/looploan";


    /**
     * 小程序下单打标
     */
    public static final String CCM_TERMINAL_ORDER_FROM = "CCM_TERMINAL_ORDER_FROM";

    /**
     * 一级预警客户标识
     */
    public static final String WARN_CUSTOMER_ONE_LEVEL = "1201";

    /**
     * 二级预警客户标识
     */
    public static final String WARN_CUSTOMER_TWO_LEVEL = "1202";

    /**
     * 预警客户标识value
     */
    public static final String WARN_CUSTOMER_VALUE = "预警客户";

    /**
     * 预警客户标识name
     */
    public static final String WARN_CUSTOMER_NAME = "YJKH";

    /**
     * 小程序网络退货标签
     */
    public static final String ONLINE_SHOPPING_RETURNS = "Online_Shopping_Returns";


    /**
     * 学生寄件状态0
     */
    public static final Integer STUDENT_SENDING_ZERO=0;


    /**
     * 学生寄件状态1
     */
    public static final Integer STUDENT_SENDING_ONE=1;

}
