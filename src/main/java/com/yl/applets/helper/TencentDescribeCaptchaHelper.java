package com.yl.applets.helper;

import com.alibaba.fastjson.JSON;
import com.tencentcloudapi.captcha.v20190722.CaptchaClient;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultRequest;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultResponse;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.yl.applets.config.tencent.TencentCloudCaptchaProperties;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.Inet4Address;
import java.net.InetAddress;

/**
 * <AUTHOR>
 * @date 2023/2/27 16:42
 * @description 腾讯滑块验证码helper
 */
@Slf4j
@Component
public class TencentDescribeCaptchaHelper {

    @Resource
    private TencentCloudCaptchaProperties tencentCloudCaptchaProperties;

    /**
     * 发送滑块验证码
     */
    public void sendCaptchaCode(String randStr, String ticket) {
        Credential cred = new Credential(tencentCloudCaptchaProperties.getSecretId(), tencentCloudCaptchaProperties.getSecretKey());
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint(tencentCloudCaptchaProperties.getEndpoint());
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        CaptchaClient client = new CaptchaClient(cred, "", clientProfile);
        DescribeCaptchaResultRequest req = new DescribeCaptchaResultRequest();
        req.setTicket(ticket);
        req.setRandstr(randStr);
        String ip = "127.0.0.1";
        try {
            InetAddress ip4 = Inet4Address.getLocalHost();
            ip = ip4.getHostAddress();
        } catch (Exception e) {
            log.warn("获取ip错误", e);
        }
        req.setUserIp(ip);
        req.setCaptchaType(9L);
        req.setAppSecretKey(tencentCloudCaptchaProperties.getAppSecretKey());
        req.setCaptchaAppId(Long.valueOf(tencentCloudCaptchaProperties.getCaptchaAppId()));
        DescribeCaptchaResultResponse resp;
        try {
            long start = System.currentTimeMillis();
            resp = client.DescribeCaptchaResult(req);
            long end = System.currentTimeMillis();
            log.info("发送滑块验证码==>入参{}，耗时==>{}", JSON.toJSONString(req), (end - start));
        } catch (TencentCloudSDKException e) {
            log.error("发送滑块验证码异常==>【randStr: {},ticket: {} 】\n异常信息===>{}", randStr, ticket, e.getMessage(), e);
            throw new BusinessException(ResultCodeEnum.TENCENT_CAPTCHA_ERROR);
        }
        if (1 != resp.getCaptchaCode()) {
            log.error("滑块验证码验证异常==>【randStr: {},ticket: {}】\n 腾讯滑块返回信息==>{}", randStr, ticket, JSON.toJSON(resp));
            throw new BusinessException(ResultCodeEnum.TENCENT_PARAMS_ERROR);
        }
    }
}
