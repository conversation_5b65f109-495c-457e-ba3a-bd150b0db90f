package com.yl.applets.config;


import com.alibaba.fastjson.JSONObject;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.utils.*;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.annotation.log.operation.ExcludeInterceptor;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.redis.util.RedisUtil;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

@Slf4j
public class LoginInterceptor extends HandlerInterceptorAdapter {


    @Autowired
    StringRedisTemplate redisTemplate;

    @Value("${jwt.expiration:2592000}")
    private Long expiration;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String token = request.getHeader("authToken");
        String requestURI = request.getRequestURI().replaceAll("/+", "/");

        log.info("requestURI:{}",requestURI);
        if(handler instanceof HandlerMethod){
            //获取到目标方法对象
            HandlerMethod method = (HandlerMethod) handler;
            ExcludeInterceptor methodAnnotation = method.getMethodAnnotation(ExcludeInterceptor.class);
            if(Objects.nonNull(methodAnnotation)){
                return true;
            }
        }

        if (StringUtils.isEmpty(token)) {
            return loginExpired(response);
        }

        Claims claim = JWTUtils.getClaim(token);
        if(claim == null){
            return loginExpired(response);
        }
        Integer userId = null;
        try {
            userId = JWTUtils.getUserId(token);
        } catch (Exception e) {
            return loginExpired(response);
        }
        if(userId==null){
            return loginExpired(response);
        }
        Object obj = RedisUtil.get(BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(userId.toString()));
        if (obj == null) {
            return loginExpired(response);
        }
        WxUserVo loginVO = JSONObject.parseObject(obj.toString(), WxUserVo.class);
        if (loginVO == null) {
            return loginExpired(response);
        }
        if(!token.equals(loginVO.getToken())){
            return loginExpired(response);
        }
        request.setAttribute(SessionUtil.USER_INFO, JsonUtils.toJson(loginVO));
        request.setAttribute(SessionUtil.USER_ID_KEY, userId);
        request.setAttribute(BcRedisKeyEnum.OAUTH_APP_TOKEN.name(), BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(userId.toString()));
        return true;
    }

    /***
     * 鉴权失败统一回写
     *
     * @param response
     * @param httpStatus
     * @param msg
     * @return
     */
    private void doFailed(HttpServletResponse response, int httpStatus, String msg) throws IOException {
//        response.setStatus(httpStatus);
//        response.setCharacterEncoding("UTF-8");
//        response.setContentType("application/json;charset=UTF-8");
////        response.setHeader("Access-Control-Allow-Origin", "*");
////        response.setHeader("Access-Control-Allow-Credentials", "true");
////        response.setHeader("Access-Control-Allow-Methods", "GET, HEAD, POST, PUT, PATCH, DELETE, OPTIONS");
////        response.setHeader("Access-Control-Max-Age", "86400");
////        response.setHeader("Access-Control-Allow-Headers", "*");
//        PrintWriter pw = response.getWriter();
//        Result result = new Result<>();
//        result.setCode(httpStatus);
//        result.setMsg(msg);
//        result.setData(false);
//        try {
//            pw.write(JSONObject.toJSONString(result));
//        } catch (Exception e) {
//        } finally {
//            pw.close();
//        }

    }


    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
    }



    private boolean loginExpired(HttpServletResponse response) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        Result result = Result.error(ServiceErrCodeEnum.LOGIN_HAS_EXPIRED);
        response.getWriter().write(JsonUtils.toJson(result));
        return false; // 阻止请求继续处理
    }

}