/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: WaybillDataSourceConfig
 * Author:   luhong
 * Date:     2020-10-21 17:21
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 〈一句话功能简述〉<br> 
 * 〈订单库dataSource-查运单〉
 *
 * <AUTHOR>
 * @create 2020-10-21
 * @since 1.0.0
 */
@Configuration
@MapperScan(basePackages = "com.yl.applets.mapper.oms", sqlSessionFactoryRef = "omsSqlSessionFactory")
public class OmsDataSourceConfig {

    /**
     * 分页插件
     */
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        return new PaginationInterceptor();
    }

    @Bean(name = "omsDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.oracle.oms", ignoreInvalidFields = true)
    public DataSource oracleDataSource() {
        return new DruidDataSource();
    }


    //会话工厂
    @Bean(name = "omsSqlSessionFactory")
    public SqlSessionFactory mybatisSqlSessionFactory(@Qualifier("omsDataSource") DataSource dataSource ) throws Exception {
        MybatisSqlSessionFactoryBean sf = new MybatisSqlSessionFactoryBean();
        sf.setDataSource(dataSource);
        sf.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/oms/*Mapper.xml"));
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setMapUnderscoreToCamelCase(true);
        //mybatisplus分页插件
        sf.setPlugins(new Interceptor[]{
                paginationInterceptor()
        });
        sf.setConfiguration(configuration);
        return sf.getObject();
    }


    //事务管理器
    @Bean(name = "omsTransactionManager")
    public DataSourceTransactionManager mybatisTransactionManager(@Qualifier("omsDataSource") DataSource dataSource) throws Exception {
        return new DataSourceTransactionManager(dataSource);
    }

}