package com.yl.applets.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/11/23 17:33
 */
@ConfigurationProperties(prefix = "consolidation.xinjiang")
@Configuration
@Data
public class ConsolidationSendProperties {

    /**
     * 新疆自治区id
     */
    private Integer provinceId;


    private String ast;

    /**
     * 云仓地址
     */
    private String address;

    /**
     * 云仓手机号
     */
    private String phone;

    /**
     * 发送人信息
     */
    private String senderName;

    /**
     * 发送人s手机号码
     */
    private String senderMobilePhone;

    /**
     * 发送人省id
     */
    private Integer senderProvinceId;

    /**
     * 发送人省名称
     */
    private String senderProvinceName;


    /**
     * 发送人城市名称
     */
    private String senderCityName;

    /**
     * 发送人城市id
     */
    private Integer senderCityId;

    /**
     * 发送人地区名称
     */
    private String senderAreaName;

    /**
     * 发送人地区id
     */
    private Integer senderAreaId;

    /**
     * 发送人详细地址
     */
    private String senderDetailedAddress;


    /**
     * 寄件网点编号
     */
    private String networkCode;


    /**
     * 寄件网点名称
     */
    private String networkName;


    /**
     * 寄件网点id
     */
    private Long networkId;
}
