package com.yl.applets.config.tencent;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/2/27 16:43
 * @description
 */
@Configuration
@ConfigurationProperties(prefix = "tencentcloudapi")
@Data
public class TencentCloudCaptchaProperties {

    private String secretId;

    private String secretKey;

    private String captchaAppId;

    private String appSecretKey;

    private String endpoint = "captcha.tencentcloudapi.com";
}
