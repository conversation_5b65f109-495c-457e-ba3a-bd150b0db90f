package com.yl.applets.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yl.applets.utils.SessionUtil;
import com.yl.applets.vo.WxUserVo;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2024-02-26 20:21
 * @Version 1.0
 */
@Configuration
@Slf4j
public class FeignHeaderConfiguration implements RequestInterceptor {

    @Autowired
    private SessionUtil sessionUtil;


    @Override
    public void apply(RequestTemplate template) {
        WxUserVo user = getUserFromRequestContext();
        log.info("开始设置请求头:{}", JSON.toJSONString(user));
        if (user != null) {
            template.header("phone", user.getMobile());
        }
        log.info("请求头设置成功:{}", user != null ? user.getMobile() : "null");
    }

    private WxUserVo getUserFromRequestContext() {
        try {
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (requestAttributes instanceof ServletRequestAttributes) {
                ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) requestAttributes;
                HttpServletRequest request = servletRequestAttributes.getRequest();

                // 这里假设你将用户信息存储在请求属性中
                Object userInfoObject = request.getAttribute(SessionUtil.USER_INFO);
                if (userInfoObject instanceof String) {
                    return JSONObject.parseObject((String) userInfoObject, WxUserVo.class);
                }
            }
        } catch (Exception e) {
            log.warn("feign接口获取用户信息失败");
        }

        // 如果不是ServletRequestAttributes，或者用户信息不存在，返回 null
        return null;
    }
}
