package com.yl.applets.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@ConfigurationProperties(prefix = "area")
@Configuration
public class HotCityProperties {

    private List<City> citys;

    @Data
    public static class City{

        /**
         * 省份
         */
        private String province;
        /**
         * 省份Id
         */
        private Integer provinceId;
        /**
         * 城市
         */
        private String city;
        /**
         * 城市Id
         */
        private Integer cityId;


    }





}
