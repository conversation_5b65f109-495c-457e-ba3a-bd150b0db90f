package com.yl.applets.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：  福利申领渠道来源
 * @Author： zhan<PERSON><PERSON>
 * @Date： 2022/07/13
 */
@Data
@ConfigurationProperties(prefix = "source")
@Configuration
public class SourceProperties {
    private List<source> list;

    @Data
    public static class source{
        /**
         * 来源code
         */
        private String code;

        /**
         * 来源name
         */
        private String name;

    }
}
