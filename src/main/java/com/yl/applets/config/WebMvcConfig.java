package com.yl.applets.config;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

@Configuration
public class WebMvcConfig extends WebMvcConfigurerAdapter implements ApplicationContextAware {


    private ApplicationContext applicationContext;

    public WebMvcConfig() {
        super();
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        super.addResourceHandlers(registry);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
    private CorsConfiguration corsConfig() {
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.addAllowedOrigin("*");
        corsConfiguration.addAllowedHeader("*");
        corsConfiguration.addAllowedMethod("*");
        corsConfiguration.setAllowCredentials(true);
        corsConfiguration.setMaxAge(3600L);
        return corsConfiguration;
    }
    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig());
        return new CorsFilter(source);
    }
    @Bean
    LoginInterceptor loginInterceptor() {
        return new LoginInterceptor();
    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        //拦截规则：除了login，其他都拦截判断
        registry.addInterceptor(loginInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("/version/list")
                .excludePathPatterns("/area/list")
                .excludePathPatterns("/area/v2/listChild")
                .excludePathPatterns("/area/listQuery")
                .excludePathPatterns("/baseData/list")
                .excludePathPatterns("/materialSpecification/page")
                .excludePathPatterns("/settlementDestination/list")
                .excludePathPatterns("/network/list")
                .excludePathPatterns("/costCalculation/insuranceFee/computationCost")
                .excludePathPatterns("/costCalculation/spmStandardShippingQuote/comCost")
                .excludePathPatterns("/network/getNetwork")
                .excludePathPatterns("/network/getNearbyOutlets")
                .excludePathPatterns("/network/getStaffMobile")
                .excludePathPatterns("/network/getStaffInfo")
                .excludePathPatterns("/login")
                .excludePathPatterns("/logisticsTracking/getDetailByWaybillNo")
                .excludePathPatterns("/logisticsTracking/v2/getDetailByWaybillNo")
                .excludePathPatterns("/gd/getArea")
                .excludePathPatterns("/gd/detail")
                .excludePathPatterns("/baseData/findDistrict")//省市区
                .excludePathPatterns("/gd/intelligentAddressRecognition")
                .excludePathPatterns("/gd/intelligentAddressRecognitionV2")
                .excludePathPatterns("/gd/intelligentAddressFile")
                .excludePathPatterns("/hotCity/list")
                .excludePathPatterns("/p2CQe1fR8a.txt")
                .excludePathPatterns("/network/YmZIEemK86.txt")
                .excludePathPatterns("/YmZIEemK86.txt")
                .excludePathPatterns("/portal")
                .excludePathPatterns("/customer-print-data/getData")
                .excludePathPatterns("/activity/list")
                .excludePathPatterns("/activity/uploadAccessRecord")
                .excludePathPatterns("/abnormalPiece/list")
                .excludePathPatterns("/user/shareOrder")//分享订单
                .excludePathPatterns("/api/**")//提供给小前台的接口
                .excludePathPatterns("/order/share/detail")  //分享订单-订单详情接口，加验签
                .excludePathPatterns("/waybillOrder/share/getDetailByNo") //分享订单-运单详情接口，加验签
                .excludePathPatterns("/swagger-ui.html/**")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/v2/api-docs")
                .excludePathPatterns("/activity/springActivity/isValid")//免登陆-春节不打烊
                .excludePathPatterns("/area/v3/listChild")//免登陆-省市区联动
                .excludePathPatterns("/network/v2/getNearbyOutlets")// 免登陆-附近网点
                .excludePathPatterns("/waybillOrder/v2/getVerifyDetail")// 免登陆-后四位运单详情
                .excludePathPatterns("/waybillOrder/v2/getDetailByNo")// 免登陆-运单详情
                .excludePathPatterns("/order/v2/detail")// 免登陆-订单详情
                .excludePathPatterns("/area/v2/getSettlementDestinationByProvinceCityAreaId")// 免登陆-结算目的地
                .excludePathPatterns("/user/sendVerificationCode") // 发送验证码
                .excludePathPatterns("/neighborhood/nearbySite") // 附近驿站
                .excludePathPatterns("/coupons/bindings")// 免登陆-结算目的地
                .excludePathPatterns("/newCustomerActivitySp/signUp") // 拉新活动报名
                .excludePathPatterns("/marketActivity/refreshPageViews") // 拉新活动报名
                .excludePathPatterns("/waybillOrder/checkWayBillNo")// 免登陆-校验运单是否存在
                .excludePathPatterns("/order/share/detail/checkOrder")// 免登陆-校验订单是否存在
                .excludePathPatterns("/ty/coupons/**")// 天翼优惠券
                .excludePathPatterns("/waybillOrder/getNumber")// 获取尾号
                .excludePathPatterns("/useCard/up")
                .excludePathPatterns("/waybillOrder/getWaybillDetail")
                .excludePathPatterns("/order/mark/resource")
                .excludePathPatterns("/baseData/findDistrict/filter")//省市区
                .excludePathPatterns("/gd/test")
        ;
        super.addInterceptors(registry);
    }

}