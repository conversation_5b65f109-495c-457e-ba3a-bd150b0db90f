package com.yl.applets.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * wechat mp properties
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "weixin.invoice")
public class WxInvoiceProperties {


    private String companyName = "极兔速递有限公司";


    private String taxNumber = "91310118667759488H";


    private String mobile = "021-********";


    private String bankAccount = "***************";
}
