package com.yl.applets.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-10-25 16:45
 * @Version 1.0
 */
@Data
@ConfigurationProperties(prefix = "applets.coupons")
@Configuration
public class WxCouponConfig {
    private List<WxCouponConfig.Config> configs;

    @Data
    public static class Config {
        /**
         * 优惠券领取活动类型   特别注意:这个type不能重复,只能累加
         */
        private Integer type;

        /**
         * 优惠券id
         */
        private List<String> couponId;
    }
}
