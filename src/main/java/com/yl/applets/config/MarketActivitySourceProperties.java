package com.yl.applets.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/18 14:54
 * @description
 */
@Data
@ConfigurationProperties(prefix = "market-activity-source")
@Configuration
public class MarketActivitySourceProperties {

    private List<Source> list;

    @Data
    public static class Source {
        private String code;
        private String name;
    }

    public boolean contains(final String curSource) {
        boolean contained = false;
        if (CollUtil.isNotEmpty(list)) {
            for (Source source : list) {
                if (StrUtil.equals(source.code, curSource)) contained = true;
            }
        }
        return contained;
    }
}
