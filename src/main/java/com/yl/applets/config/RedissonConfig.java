package com.yl.applets.config;

import com.yl.redis.exception.RedisException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-08-02
 */

@Configuration
@Slf4j
@ConfigurationProperties(prefix = "redis")
public class RedissonConfig {

    private List<Map<String, String>> nodesInfo;

    private String password;

    public String getPassword() {
        return this.password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public List<Map<String, String>> getNodesInfo() {
        return this.nodesInfo;
    }

    public void setNodesInfo(List<Map<String, String>> nodesInfo) {
        this.nodesInfo = nodesInfo;
    }

    public List<String> getNodesInfoList() {
        List<String> list = new ArrayList();
        if (CollectionUtils.isEmpty(this.nodesInfo)) {
            throw new RedisException("redis nodes is empty");
        } else {
            this.nodesInfo.forEach((map) -> {
                list.add((String)map.get("ip")+ ":" +Integer.parseInt((String)map.get("port")));
            });
            return list;
        }
    }

    @Bean
    public RedissonClient redissonClient(){
        Config config=new Config();
        ClusterServersConfig clusterServersConfig = config.useClusterServers();
        List<String> nodesInfoList = getNodesInfoList();
        for (String s : nodesInfoList) {
            log.info("redis:"+s);
            clusterServersConfig.addNodeAddress("redis://"+s).setPassword(password);
        }
        RedissonClient redissonClient = Redisson.create(config);
        return redissonClient;
    }
}
