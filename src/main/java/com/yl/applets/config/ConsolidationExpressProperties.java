package com.yl.applets.config;

import com.yl.applets.vo.consolidation.ConsolidationExpressCompanyVO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/11/22 16:33
 */
@ConfigurationProperties(prefix = "consolidation.express")
@Configuration
@Data
public class ConsolidationExpressProperties {
    private List<ConsolidationExpressCompanyVO> list;


}
