package com.yl.applets.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

import java.util.List;

/**
 * <AUTHOR> href="https://github.com/binarywang"><PERSON><PERSON></a>
 */
@Data
@ConfigurationProperties(prefix = "redis.wx")
@EnableConfigurationProperties(JedisProperties.class)
public class JedisProperties {

   private Integer maxTotal;
   private Integer maxIdle;
   private Integer minIdle;

}
