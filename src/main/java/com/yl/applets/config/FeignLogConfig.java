package com.yl.applets.config;

import feign.Logger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title FeignLogConfig
 * @Package com.log.demo.feign
 * @Description feign配置
 * @date 2021/6/2 7:40 下午
 */
@Configuration
public class FeignLogConfig {

    /**
     * @return : feign.Logger.Level
     * <AUTHOR> mz
     * @Description : feign打印级别
     * @Date : 5:06 下午 2021/8/8
     */
    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    /**
     * @return : feign.Logger
     * <AUTHOR> mz
     * @Description : 是否开启feign打印
     * @Date : 5:06 下午 2021/8/8
     */
    @Bean
    Logger feignInfoLogger() {
        return new FeignInfoLogger();
    }


}