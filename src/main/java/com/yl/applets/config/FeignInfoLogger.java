package com.yl.applets.config;

import feign.Request;
import feign.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title FeignInfoLogger
 * @Package com.yl.edi.jd.config
 * @Description feign info 日志配置
 * @date 2021/6/3 11:32 上午
 */
public class FeignInfoLogger extends feign.Logger {

    private final Logger logger;

    public FeignInfoLogger() {
        this(feign.Logger.class);
    }

    public FeignInfoLogger(Class<?> clazz) {
        this(LoggerFactory.getLogger(clazz));
    }

    public FeignInfoLogger(String name) {
        this(LoggerFactory.getLogger(name));
    }

    FeignInfoLogger(Logger logger) {
        this.logger = logger;
    }

    @Override
    protected void logRequest(String configKey, Level logLevel, Request request) {
        if (logger.isInfoEnabled()) {
            super.logRequest(configKey, logLevel, request);
        }
    }

    @Override
    protected Response logAndRebufferResponse(String configKey, Level logLevel, Response response, long elapsedTime)
            throws IOException {
        if (logger.isInfoEnabled()) {
            return super.logAndRebufferResponse(configKey, logLevel, response, elapsedTime);
        }
        return response;
    }

    @Override
    protected void log(String configKey, String format, Object... args) {
        // Not using SLF4J's support for parameterized messages (even though it
        // would be more efficient) because it would
        // require the incoming message formats to be SLF4J-specific.
        if (logger.isInfoEnabled()) {
            logger.info(String.format(methodTag(configKey) + format, args));
        }
    }
}