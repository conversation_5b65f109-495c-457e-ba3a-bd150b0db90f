//package com.yl.applets.handle;
//
///**
// * 云路供应链科技有限公司 版权所有 © Copyright 2020
// *
// * <AUTHOR>
// * @version 1.0
// * @Description: TODO
// * @date 2021-07-07 17:46
// */
//
////import com.yl.applets.annotation.SmsAccessLimit;
//import com.yl.applets.enums.exception.ServiceErrCodeEnum;
//import com.yl.common.base.exception.ServiceException;
//import com.yl.common.base.util.IpAddressUtil;
//import com.yl.redis.util.RedisUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//import org.springframework.web.method.HandlerMethod;
//import org.springframework.web.servlet.ModelAndView;
//import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.time.Duration;
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.time.LocalTime;
//import java.util.concurrent.TimeUnit;
//
///**
// * <AUTHOR>
// * @createDate :2020/7/18 6:54 下午
// * @desc : 接口拦截器
// */
//@Component
//@SuppressWarnings("all")
//@Slf4j
//public class APIInterceptor extends HandlerInterceptorAdapter {
//
//
//    @Resource
//    private RedisUtil redisUtil;
//    /**
//     * 同一个手机号码，同一个功能，一条最多发送多少调短信
//     */
//    @Value("${sms.phone.verify.max:10}")
//    private Integer smsPhoneVerifyMax;
//    /**
//     * 预处理回调方法，实现处理器的预处理(如登陆检查/判断同一对象短时间内是否重复调用接口等) 第三个参数为相应的处理器即controller
//     * f返回true表示流程继续，调用下一个拦截器或者处理器，返回false表示流程中断，通过response产生响应
//     */
//    @Override
//    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
//            throws Exception {
//
//        //判断同一用户短时间内是否重复请求接口
////        log.info("=======request path====="+ request.getRequestURI());
//        //判断请求是否属于方法的请求
//        if(handler instanceof HandlerMethod){
//            HandlerMethod hm = (HandlerMethod) handler;
//            //获取方法中的注解,看是否有该注解
//            SmsAccessLimit smsAccessLimit = hm.getMethodAnnotation(SmsAccessLimit.class);
//            if(smsAccessLimit == null){
//                return true;
//            }
//
//            String type="-1";
//            String phone = request.getParameter("mobile");
//            if(request.getParameter("type")!=null){
//                type = request.getParameter("type");
//            }
//
//            int seconds = smsAccessLimit.seconds();
//            int maxCount = smsAccessLimit.maxCount();
//            String ip= IpAddressUtil.getIpAdrress(request);
//            check(request.getContextPath(),ip,type,phone,maxCount,seconds);
//        }
//        return super.preHandle(request, response, handler);
//    }
//
//
//    /**
//     * 当请求进行处理之后，也就是controller方法调用之后执行，但是他会在DispatcherServlet进行视图渲染之前被调用
//     * 此时我们可以通过modelAndView对模型数据进行处理或对视图进行处理
//     */
//    @Override
//    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
//                           ModelAndView modelAndView) throws Exception {
//
//    }
//
//    /**
//     * 方法将在整个请求结束之后，也就是DispatcheServlet进行视图渲染之后执行，这个方法的主要作用是对资源的清理工作
//     */
//    @Override
//    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
//            throws Exception {
//
//    }
//
//    //获取当前时间，到凌晨 相差的秒数
//    private int getSecondNumber() {
//        LocalTime midnight = LocalTime.MIDNIGHT;
//        LocalDate today = LocalDate.now();
//        LocalDateTime todayMidnight = LocalDateTime.of(today, midnight);
//        LocalDateTime tomorrowMidnight = todayMidnight.plusDays(1);
//        long seconds = TimeUnit.NANOSECONDS.toSeconds(Duration.between(LocalDateTime.now(), tomorrowMidnight).toNanos());
//        return (int) seconds;
//    }
//
//    public void check(String path,String ip,String type,String phone,Integer maxCount,Integer seconds){
//
//        //统计该IP请求次数间隔时间
//        String timeKey ="APPLETS:VERIFYCODE:REQUEST:TIME:"+path+ ":"+type+":" + ip ;
//        //统计今天该IP请求了多少次
//        String countKey ="APPLETS:VERIFYCODE:REQUEST:COUNT:"+path + ":" +type+":" + ip ;
//        //统计今天该手机发送了多少次短信
//        String countPhoneKey ="APPLETS:VERIFYCODE:REQUEST:COUNT:"+path + ":" +type+":" + phone;
//
//        Object timeObj=redisUtil.get(timeKey);
//        Object countTObj=redisUtil.get(countKey);
//        Object countPhoneObj=redisUtil.get(countPhoneKey);
//        Integer count =0;
//        Integer countPhone =0;
//
//        if(timeObj != null){
//            throw new ServiceException(ServiceErrCodeEnum.OP_REPETITION_ERR);
//        }
//        if(countTObj != null){
//            count = Integer.valueOf(countTObj.toString());
//        }
//        if(countPhoneObj != null){
//            countPhone = Integer.valueOf(countPhoneObj.toString());
//        }
//        if (count < maxCount) {
//            count = count+1;
//            redisUtil.setEx(timeKey, "1",seconds);
//            redisUtil.setEx(countKey, count+"",getSecondNumber());
//            if (countPhone < smsPhoneVerifyMax && !"".equals(phone)) {
//                countPhone = countPhone+1;
//                redisUtil.setEx(countPhoneKey, countPhone+"",getSecondNumber());
//            }else{
//                if( !"".equals(phone)) {
//                    log.info("同一手机号频繁请求 手机号：{}", phone);
//                    throw new ServiceException(ServiceErrCodeEnum.OP_REPETITION_TIME_ERR);
//                }
//            }
//        }else{
//            log.info("同一IP地址频繁请求 ip:{}",ip);
//            throw new ServiceException(ServiceErrCodeEnum.OP_REPETITION_TIME_ERR);
//        }
//    }
//}