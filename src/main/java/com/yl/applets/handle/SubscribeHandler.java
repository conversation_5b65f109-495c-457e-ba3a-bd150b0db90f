package com.yl.applets.handle;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yl.applets.dto.AutoReplyDTO;
import com.yl.applets.entity.WxOfficialUser;
import com.yl.applets.mapper.applets.AutoReplyMapper;
import com.yl.applets.service.WxOfficialUserService;
import com.yl.applets.utils.JsonUtils;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.util.GenerationIdUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR> Wang(https://github.com/binarywang)
 */
@Component
@Slf4j
public class SubscribeHandler extends AbstractHandler {

    @Autowired
    private WxOfficialUserService wxOfficialUserService;

    @Autowired
    private AutoReplyMapper autoReplyMapper;

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    private static String content="终于等到你\nNice 兔 meet U\n\uD83D\uDC07\uD83D\uDC07\uD83D\uDC07\n\n菜单左下角“一键收寄”可跳转官方小程序下单、查件\n菜单右下角“在线客服”可接通客服兔喔~";

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage,
                                    Map<String, Object> context, WxMpService weixinService,
                                    WxSessionManager sessionManager) throws WxErrorException {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("requestId=>{},新关注用户 OPENID:{} " ,requestId,wxMessage.getFromUser());
        // 获取微信用户基本信息
        try {
            WxMpUser userWxInfo = weixinService.getUserService()
                .userInfo(wxMessage.getFromUser(), null);
            if (userWxInfo != null) {
                this.handleSubscribe(userWxInfo,requestId);
            }
        } catch (WxErrorException e) {
            if (e.getError().getErrorCode() == 48001) {
                log.info("requestId=>{},该公众号没有获取用户信息权限！",requestId,e);
            }
        }
        List<AutoReplyDTO> list = this.queryListAll(autoReplyMapper,orikaBeanMapper);
        if(list==null){
            log.warn("requestId=>{},没有配置任何回复信息",requestId);
            return null;
        }
        //关注回复
        Optional<AutoReplyDTO> optional = list.stream().filter(x -> 1==x.getReplyType()).findFirst();
        if(!optional.isPresent()){
            log.warn("requestId=>{},没有配置关注回复信息",requestId);
            return null;
        }
        AutoReplyDTO autoReplyDTO = optional.get();
        this.sendMessage(autoReplyDTO.getReplyContentList().get(0),requestId,weixinService,wxMessage.getFromUser());
        return null;
    }

    private void handleSubscribe(WxMpUser wxMpUser,String requestId) {
        LambdaQueryWrapper<WxOfficialUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WxOfficialUser::getUnionid,wxMpUser.getUnionId());
        WxOfficialUser wxOfficialUser = wxOfficialUserService.getOne(queryWrapper);
        log.info("");
        if(wxOfficialUser!=null){
            wxOfficialUser.setHeadImgUrl(wxMpUser.getHeadImgUrl());
            wxOfficialUser.setNickName(Base64.encodeBase64String(wxMpUser.getNickname().getBytes(StandardCharsets.UTF_8)));
            wxOfficialUser.setSex(wxMpUser.getSex());
            wxOfficialUser.setSubscribeScene(wxMpUser.getSubscribeScene());
            wxOfficialUser.setSubscribeTime(wxMpUser.getSubscribeTime());
            wxOfficialUser.setSubscribe(wxMpUser.getSubscribe()?1:0);
            wxOfficialUser.setProvince(wxMpUser.getProvince());
            wxOfficialUser.setCountry(wxMpUser.getCountry());
            wxOfficialUser.setCity(wxMpUser.getCity());
            wxOfficialUser.setUpdateTime(LocalDateTime.now());
            wxOfficialUserService.updateById(wxOfficialUser);
            log.info("requestId=>{},更新公众号用户信息成功：{}",requestId, JsonUtils.toJson(wxMpUser));
        }else{
            wxOfficialUser = new WxOfficialUser();
            wxOfficialUser.setId(GenerationIdUtil.getId());
            wxOfficialUser.setHeadImgUrl(wxMpUser.getHeadImgUrl());
            wxOfficialUser.setOpenid(wxMpUser.getOpenId());
            wxOfficialUser.setUnionid(wxMpUser.getUnionId());
            wxOfficialUser.setNickName(Base64.encodeBase64String(wxMpUser.getNickname().getBytes(StandardCharsets.UTF_8)));
            wxOfficialUser.setSex(wxMpUser.getSex());
            wxOfficialUser.setSubscribeScene(wxMpUser.getSubscribeScene());
            wxOfficialUser.setSubscribeTime(wxMpUser.getSubscribeTime());
            wxOfficialUser.setSubscribe(wxMpUser.getSubscribe()?1:0);
            wxOfficialUser.setCountry(wxMpUser.getCountry());
            wxOfficialUser.setProvince(wxMpUser.getProvince());
            wxOfficialUser.setCity(wxMpUser.getCity());
            LocalDateTime now = LocalDateTime.now();
            wxOfficialUser.setCreateTime(now);
            wxOfficialUser.setUpdateTime(LocalDateTime.now());
            wxOfficialUserService.save(wxOfficialUser);
            log.info("requestId=>{},新增公众号用户信息成功：{}",requestId,JsonUtils.toJson(wxOfficialUser));
        }
    }

    /**
     * 处理特殊请求，比如如果是扫码进来的，可以做相应处理
     */
    private WxMpXmlOutMessage handleSpecial(WxMpXmlMessage wxMessage)
        throws Exception {
        //TODO
        return null;
    }

}
