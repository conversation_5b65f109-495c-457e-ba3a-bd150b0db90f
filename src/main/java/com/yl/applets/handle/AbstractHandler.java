package com.yl.applets.handle;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yl.applets.constant.AppCacheConstant;
import com.yl.applets.dto.AutoReplyDTO;
import com.yl.applets.entity.AutoReply;
import com.yl.applets.mapper.applets.AutoReplyMapper;
import com.yl.applets.utils.JsonUtils;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpMessageHandler;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Wang(https://github.com/binarywang)
 */
@Slf4j
public abstract class AbstractHandler implements WxMpMessageHandler {

    protected Logger logger = LoggerFactory.getLogger(getClass());


    /**
     * 功能描述:
     * 自动回复列表ALL(缓存，增删改需要更新)
     * @param
     * @return:java.util.List<com.yl.applets.entity.AutoReply>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-05-25 9:53
     */
    public List<AutoReplyDTO> queryListAll(AutoReplyMapper autoReplyMapper,OrikaBeanMapper orikaBeanMapper) {
        List<AutoReplyDTO> list = null;
        Object obj = RedisUtil.get(AppCacheConstant.WX_AUTO_REPLY_LIST);
        if(obj!=null){
            list = JsonUtils.toList(obj.toString(),AutoReplyDTO.class);
        }else{
            List<AutoReply> autoReplies = autoReplyMapper.selectList(new LambdaQueryWrapper<AutoReply>());
            if(autoReplies==null){
                return null;
            }
            list = autoReplies.stream().map(autoReply -> {
                AutoReplyDTO autoReplyDTO = orikaBeanMapper.map(autoReply,AutoReplyDTO.class);
                autoReplyDTO.setKeyWordList(autoReply.getKeyWord()==null?null:JsonUtils.toList(autoReply.getKeyWord(), AutoReplyDTO.KeyWord.class));
                autoReplyDTO.setReplyContentList(JsonUtils.toList(autoReply.getReplyContent(), AutoReplyDTO.ReplyContentDTO.class));
                return autoReplyDTO;
            }).collect(Collectors.toList());
        }
        //设置缓存
        RedisUtil.set(AppCacheConstant.WX_AUTO_REPLY_LIST, JsonUtils.toJson(list));
        return list;
    }

    /**
     * 功能描述:
     * 根据条件发送信息
     * @param replyContentDTO
     * @param requestId
     * @return:void
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-05-24 18:15
     */
    public void sendMessage(AutoReplyDTO.ReplyContentDTO replyContentDTO, String requestId, WxMpService wxMpService,String openId) {
        WxMpKefuMessage wxMpKefuMessage = null;
        if("text".equals(replyContentDTO.getType())){
            wxMpKefuMessage = WxMpKefuMessage.TEXT().content(replyContentDTO.getText()).build();
        }else if("image".equals(replyContentDTO.getType())){
            wxMpKefuMessage = WxMpKefuMessage.IMAGE().mediaId(replyContentDTO.getMediaId()).build();
        }else if("voice".equals(replyContentDTO.getType())){
            wxMpKefuMessage = WxMpKefuMessage.VOICE().mediaId(replyContentDTO.getMediaId()).build();
        }else if("video".equals(replyContentDTO.getType())){
            wxMpKefuMessage = WxMpKefuMessage.VIDEO().mediaId(replyContentDTO.getMediaId()).build();
        }else if("news".equals(replyContentDTO.getType())){
            wxMpKefuMessage = WxMpKefuMessage.MPNEWS().mediaId(replyContentDTO.getMediaId()).build();
        }else{
            log.warn("requestId=>{},暂不支持回复类型：{}",requestId,replyContentDTO.getType());
            return;
        }
        try {
            wxMpKefuMessage.setToUser(openId);
            wxMpService.getKefuService().sendKefuMessage(wxMpKefuMessage);
        } catch (WxErrorException e) {
            log.warn("微信客户自动回复失败",e);
        }
    }


    public void queryContentAndsendMessage(String content,WxMpService wxMpService,String openId) {
        WxMpKefuMessage wxMpKefuMessage = WxMpKefuMessage.TEXT().content(content).build();;
        try {
            wxMpKefuMessage.setToUser(openId);
            wxMpService.getKefuService().sendKefuMessage(wxMpKefuMessage);
        } catch (WxErrorException e) {
            log.warn("自定义菜单click回复失败",e);
        }
    }
}
