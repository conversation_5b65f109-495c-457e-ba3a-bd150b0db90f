package com.yl.applets.handle;

import com.yl.applets.dto.AutoReplyDTO;
import com.yl.applets.mapper.applets.AutoReplyMapper;
import com.yl.applets.utils.JsonUtils;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class Msg<PERSON>and<PERSON> extends AbstractHandler {

    @Autowired
    private AutoReplyMapper autoReplyMapper;

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage,
                                    Map<String, Object> context, WxMpService weixinService,
                                    WxSessionManager sessionManager) {
     String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("requestId=>{},收到用户消息 OPENID: {}" ,requestId,wxMessage.getFromUser());
        String content = wxMessage.getContent();
        List<AutoReplyDTO> list = this.queryListAll(autoReplyMapper,orikaBeanMapper);
        log.info("requestId=>{},获取自动回复列表为：{}",requestId, JsonUtils.toJson(list));
        if(list==null){
            log.warn("requestId=>{},没有配置任何回复信息",requestId);
            return null;
        }
        //关键字回复
        if(StringUtils.isNotEmpty(content)){
            List<AutoReplyDTO> keyWordList = list.stream().filter(autoReplyDTO -> 3==autoReplyDTO.getReplyType()).collect(Collectors.toList());
            for(AutoReplyDTO autoReplyDTO:keyWordList){
                for(AutoReplyDTO.KeyWord keyWord:autoReplyDTO.getKeyWordList()){
                    //回复规则：1:全匹配,2半匹配
                    if((1==keyWord.getRuleType()&&content.equals(keyWord.getKeyWord()))||(2==keyWord.getRuleType()&&content.contains(keyWord.getKeyWord()))){
                        //回复方式：1:回复全部
                        if(1==autoReplyDTO.getReplyMode()){
                            for(AutoReplyDTO.ReplyContentDTO replyContentDTO:autoReplyDTO.getReplyContentList()){
                                this.sendMessage(replyContentDTO,requestId,weixinService,wxMessage.getFromUser());
                            }
                            return null;
                        }
                        //随机回复一条
                        if(2==autoReplyDTO.getReplyMode()){
                            Random random = new Random();
                            List<AutoReplyDTO.ReplyContentDTO> replyContentDTOS = autoReplyDTO.getReplyContentList();
                            if(replyContentDTOS!=null&&replyContentDTOS.size()>0){
                                AutoReplyDTO.ReplyContentDTO replyContentDTO = replyContentDTOS.get(random.nextInt(replyContentDTOS.size()));
                                this.sendMessage(replyContentDTO,requestId,weixinService,wxMessage.getFromUser());
                                return null;
                            }
                        }
                    }
                }
            }
        }

        //自动回复
        Optional<AutoReplyDTO> optional = list.stream().filter(x -> 2==x.getReplyType()).findFirst();
        if(!optional.isPresent()){
            log.warn("requestId=>{},没有配置自动回复信息",requestId);
            return null;
        }
        AutoReplyDTO autoReplyDTO = optional.get();
        this.sendMessage(autoReplyDTO.getReplyContentList().get(0),requestId,weixinService,wxMessage.getFromUser());
        return null;
    }

}
