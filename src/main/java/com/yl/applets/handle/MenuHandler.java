package com.yl.applets.handle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yl.applets.entity.AppletsMpMenu;
import com.yl.applets.service.IAppletsMpMenuService;
import com.yl.common.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static me.chanjar.weixin.common.api.WxConsts.EventType;

/**
 * //点击菜单事件
 * <AUTHOR> <PERSON>(https://github.com/binarywang)
 */
@Component
@Slf4j
public class MenuHandler extends AbstractHandler {

    @Autowired
    private IAppletsMpMenuService appletsMpMenuService;

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage,
                                    Map<String, Object> context, WxMpService weixinService,
                                    WxSessionManager sessionManager) {
        log.info("收到菜单点击事件{}",JSON.toJSONString(wxMessage));
        String msg = String.format("type:%s, event:%s, key:%s",
            wxMessage.getMsgType(), wxMessage.getEvent(),
            wxMessage.getEventKey());
        if (EventType.VIEW.equals(wxMessage.getEvent())) {
            return null;
        }
        if(EventType.CLICK.equals(wxMessage.getEvent())){
            //查询key对应的content
            AppletsMpMenu one = appletsMpMenuService.getOne(new LambdaQueryWrapper<AppletsMpMenu>().eq(AppletsMpMenu::getIsDelete, 0).orderByDesc(AppletsMpMenu::getCreateTime));
            if(one==null){
                log.info("菜单配置为空");
                return null;
            }
            log.info("查询到的click消息：{}", JSON.toJSONString(one.getTextContent()));
            String content = one.getTextContent();
            if(StringUtils.isNotEmpty(content)){
                List<Map<String,String>> lists = JSONArray.parseObject(content,List.class);
                for (Map<String, String> keys : lists) {
                    for (String key : keys.keySet()) {
                        if(key.equals(wxMessage.getEventKey())){
                            log.info("对应的自定义消息：{}",JSON.toJSONString(keys));
                            queryContentAndsendMessage(keys.get(key)+"",weixinService,wxMessage.getFromUser());
                            return null;
                        }
                    }
                }
            }
        }
//        return WxMpXmlOutMessage.TEXT().content(msg)
//            .fromUser(wxMessage.getToUser()).toUser(wxMessage.getFromUser())
//            .build();
        return null;
    }

}
