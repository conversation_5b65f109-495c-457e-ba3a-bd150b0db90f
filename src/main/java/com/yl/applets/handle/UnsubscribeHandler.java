package com.yl.applets.handle;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yl.applets.entity.WxOfficialUser;
import com.yl.applets.service.WxOfficialUserService;
import com.yl.applets.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class UnsubscribeHandler extends AbstractHandler {

    @Autowired
    private WxOfficialUserService wxOfficialUserService;

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage,
                                    Map<String, Object> context, WxMpService wxMpService,
                                    WxSessionManager sessionManager) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        String openId = wxMessage.getFromUser();
        this.logger.info("requestId=>{},取消关注用户 OPENID:{} " ,requestId, openId);
        WxMpUser wxMpUser = null;
        try {
            wxMpUser = wxMpService.getUserService().userInfo(openId);
        } catch (WxErrorException e) {
            log.warn("requestId=>{},调用微信接口获取用户信息失败：openId:{}",requestId,openId,e);
        }

        if(wxMpUser==null){
            log.info("requestId=>{},获取用户信息失败：{}",requestId,openId);
            return null;
        }
        this.handleUnsubscribe(wxMpUser,requestId);
        return null;
    }

    private void handleUnsubscribe(WxMpUser wxMpUser, String requestId) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("通过unionID获取用户信息");
        LambdaQueryWrapper<WxOfficialUser> queryWrapper = new LambdaQueryWrapper<WxOfficialUser>().eq(WxOfficialUser::getOpenid,wxMpUser.getOpenId());
        WxOfficialUser wxOfficialUser = wxOfficialUserService.getOne(queryWrapper);
        stopWatch.stop();
        log.info("requestId=>{}，查询数据库获取用户信息耗时：{}，结果：{}",requestId,stopWatch.getLastTaskTimeMillis(), JsonUtils.toJson(wxOfficialUser));
        if(wxOfficialUser==null){
            return;
        }
        stopWatch.start("更新用户信息");
        wxOfficialUser.setSubscribe(0);
        wxOfficialUser.setUpdateTime(LocalDateTime.now());
        wxOfficialUser.setUnsubscribeTime(LocalDateTime.now());
        wxOfficialUserService.updateById(wxOfficialUser);
        stopWatch.stop();
        log.info("requestId=>{},更新用户信息耗时：{}",requestId,stopWatch.getLastTaskTimeMillis());

    }

}
