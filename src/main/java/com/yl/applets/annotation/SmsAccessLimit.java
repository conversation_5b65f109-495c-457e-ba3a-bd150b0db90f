package com.yl.applets.annotation;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 * 定义接口恶意请求多次注解
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-07-07 17:42
 */

@Retention(RUNTIME)//表示它在运行时
@Target(METHOD) //表示它只能放在方法上
@SuppressWarnings("all")
public @interface SmsAccessLimit {

    int seconds();//规定几秒

    int permission();//最大错误数

    long lockTime();//锁定时间

    String keyWord();



}