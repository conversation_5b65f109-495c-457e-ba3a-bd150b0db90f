package com.yl.applets.holder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.Semaphore;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: RedisTemplate 装饰器
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-06-11 14:35
 */
@Component
public class RedisTemplateHolder {
    /**
     * 最大有20个redis连接被使用，其他的连接要等待令牌释放
     * 令牌数量自己定义，这个令牌是为了避免高并发下，获取redis连接数时，抛出的异常
     * 在压力测试下，性能也很可观
     */
    private static Semaphore semaphore = new Semaphore(20);

    private RedisTemplateHolder() {
    }

    public static RedisTemplate getRedisTemplate(RedisTemplate redisTemplate) {
        try {
            semaphore.acquire();
            return redisTemplate;
        } catch (Exception e) {
            throw new IllegalStateException(e);
        }
    }

    public static void release() {
        semaphore.release();
    }

    public static Object execute(Statement statement, RedisTemplate<String, Object> redisTemplate) {
        try {
            return statement.prepare(getRedisTemplate(redisTemplate));
        } finally {
            RedisTemplateHolder.release();
        }
    }

}
