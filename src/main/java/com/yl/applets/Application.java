package com.yl.applets;


import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.client.RestTemplate;

@ComponentScan(basePackages = {"com.yl.*"})
@MapperScan({"com.yl.applets.mapper"})
@EnableDiscoveryClient
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class,RabbitAutoConfiguration.class})
@EnableApolloConfig(value = {"dbConnection.yml","common.bootstrap","common.mybatis-plus","application", "redis.yml", "rabbit.yml","applets.yml"})
@EnableAsync
@EnableFeignClients(basePackages = {"com.yl.*"})
public class Application {

    @Bean
    public Encoder multipartFormEncoder() {
        return new SpringFormEncoder(new SpringEncoder(() -> new HttpMessageConverters(new RestTemplate().getMessageConverters())));
    }

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);

    }

}
