//package com.yl.applets.aspect;
//
//import com.yl.applets.annotation.SmsAccessLimit;
//import com.yl.applets.utils.BcRedisKeyEnum;
//import com.yl.applets.vo.WxUserVo;
//import lombok.extern.slf4j.Slf4j;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.Signature;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Pointcut;
//import org.aspectj.lang.reflect.CodeSignature;
//import org.aspectj.lang.reflect.MethodSignature;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.data.redis.support.atomic.RedisAtomicLong;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.lang.reflect.Method;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.Objects;
//import java.util.concurrent.TimeUnit;
//
///**
// * 云路科技有限公司 版权所有 Copyright 2023<br>
// *
// * @Description
// * <AUTHOR>
// * @Date 2023-04-16 0:31
// * @Version 1.0
// */
//@Component
//@Aspect
//@Slf4j
//public class SmsAccessLimitAspect {
//
//    @Resource
//    private RedisTemplate<String, Integer> redisTemplate;
//
//
//    public void SmsAccessLimit() {
//    }
//
//    @Pointcut("execution(* com.yl.applets.controller..*(..))")
//    public void annotationPoint() {
//    }
//
//
//    @Around(value = "annotationPoint()")
//    public Object process(ProceedingJoinPoint joinPoint) throws Throwable {
//
//        Object proceed;
//
//        //参数列表
//        Object[] args = joinPoint.getArgs();
//        //封装参数名和参数值
//        Map<String, Object> map = new HashMap<>();
//        String[] names = ((CodeSignature) joinPoint.getSignature()).getParameterNames();
//        for (int i = 0; i < names.length; i++) {
//            map.put(names[i], args[i]);
//        }
//
//        //1.1获取目标对象对应的字节码对象
//        Class<?> targetCls = joinPoint.getTarget().getClass();
//        //1.2获取目标方法对象
//
//        //1.2.1 获取方法签名信息从而获取方法名和参数类型
//        Signature signature = joinPoint.getSignature();
//
//        //1.2.1.1将方法签名强转成MethodSignature类型，方便调用
//        MethodSignature ms = (MethodSignature) signature;
//
//        //1.2.2通过字节码对象以及方法签名获取目标方法对象
//        Method targetMethod = targetCls.getDeclaredMethod(ms.getName(), ms.getParameterTypes());
//
//        //1.3获取目标方法对象上注解中的属性值
//
//        //1.2.3 获取方法上的自定义requiredLog注解
//        SmsAccessLimit requiredLog = targetMethod.getAnnotation(SmsAccessLimit.class);
//
//        //注解值
//        int seconds = requiredLog.seconds();
//        int permission = requiredLog.permission();
//        long time = requiredLog.lockTime();
//        String keyWord = requiredLog.keyWord();
//
//        //通过反射拿到需要加锁的字段
//        String key = BcRedisKeyEnum.WAYBILL_QUERY_COUNT_PER_MINUTER.keyBuilder(map.get(keyWord).toString());
//        log.info("查询当前运单号：{} key为：{}", keyWord, key);
//
//
//        //每分钟查询10次，都错误后，禁止查询十分钟
//        RedisAtomicLong atomicLong = this.getAtomicLong(key);
//
//        proceed = joinPoint.proceed();
//
//        // 不存在这个key
//        long invokeCount = atomicLong.get();
//        log.info("[切面限流]==>初次获取执行次数: {}", invokeCount);
//        if (invokeCount == 0) {
//            log.info("[切面限流]==>初次执行");
//            atomicLong.incrementAndGet();
//            atomicLong.expire(60, TimeUnit.SECONDS);
//
//            return proceed;
//        }
//
//
//        return proceed;
//    }
//
//
//    private RedisAtomicLong getAtomicLong(final String key) {
//        return new RedisAtomicLong(key, Objects.requireNonNull(redisTemplate.getConnectionFactory()));
//    }
//
//}
