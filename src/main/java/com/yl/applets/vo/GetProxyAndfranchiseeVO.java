package com.yl.applets.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/17 14:28
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetProxyAndfranchiseeVO implements Serializable {

    private static final long serialVersionUID = -8898657941701529904L;
    private Long id;
    private String code;
    private String name;
    private Long franchiseeId;
    private String franchiseeCode;
    private String franchiseeName;
    private Long proxyId;
    private String proxyCode;
    private String proxyName;
}
