package com.yl.applets.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OmsOrderRescheduleVO implements Serializable {
    private static final long serialVersionUID = 4676127717293849884L;

    /**
     * orderId
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 订单来源编码
     */
    private String orderSourceCode;

    /**
     * 订单来源名称
     */
    private String orderSourceName;

    /**
     * 改约上限
     */
    private Integer rescheduleLimit;

    /**
     * 是否允许改约（0 否，1 是）
     */
    private Integer isOvertimeReschedule;

    /**
     * 剩余次数
     */
    private Integer remaindTimes;

    /**
     * 是否存在配置，默认false
     */
    private Boolean existRule;

}
