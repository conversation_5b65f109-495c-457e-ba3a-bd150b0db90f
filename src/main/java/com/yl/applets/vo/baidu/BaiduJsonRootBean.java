package com.yl.applets.vo.baidu;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-09-21 21:22
 * @Version 1.0
 */
@Data
public class BaiduJsonRootBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @JSONField(name = "姓名")
    private Item name;
    
    @JSONField(name = "出生")
    private Item birth;

    @JsonProperty("民族")
    @JSONField(name = "民族")
    private Item nation;

    @JsonProperty("住址")
    @JSONField(name = "住址")
    private Item address;

    @JsonProperty("公民身份号码")
    @JSONField(name = "公民身份号码")
    private Item idNum;

    @JsonProperty("性别")
    @JSONField(name = "性别")
    private Item sex;

    @Data
    public static class Item {
        private String words;
        private Location location;
    }

    @Data
    public static class Location {
        private int top;
        private int left;
        private int width;
        private int height;
    }
}
