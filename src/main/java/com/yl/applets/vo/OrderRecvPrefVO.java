package com.yl.applets.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-04-22 20:58
 */
@Data
public class OrderRecvPrefVO implements Serializable {

    private Long id;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 运单号
     */
    private String waybillId;

    /**
     * 订单来源
     */
    private String orderSourceCode;


    /**
     * 工作日一级偏好编码
     */
    private String weekdayOneLevelCode;

    /**
     * 工作日一级偏好名称
     */
    private String weekdayOneLevelName;

    /**
     * 工作日二级偏好编码
     */
    private String weekdayTwoLevelCode;

    /**
     * 工作日二级偏好名称
     */
    private String weekdayTwoLevelName;

    /**
     * 休息日一级偏好编码
     */
    private String weekendOneLevelCode;

    /**
     * 休息日一级偏好名称
     */
    private String weekendOneLevelName;

    /**
     * 休息日二级偏好编码
     */
    private String weekendTwoLevelCode;

    /**
     * 休息日二级偏好名称
     */
    private String weekendTwoLevelName;
}
