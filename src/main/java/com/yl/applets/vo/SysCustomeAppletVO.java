package com.yl.applets.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class SysCustomeAppletVO implements Serializable {

    private Integer id;

    /**
     * 客户编码
     */
    private String code;

    /**
     * 客户名称
     */
    private String name;

    /**
     * 客户是否启用:1启用,2不启用
     */
    private Integer isEnable;

    /**
     * 网点ID
     */
    private Integer networkId;

    /**
     * 网点CODE
     */
    private String networkCode;

    /**
     * 网点名称
     */
    private String networkName;

    /**
     * 网点是否启用:1启用,2不启用
     */
    private Integer networkIsEnable;

    /**
     * 打印机设备名称
     */
    private String printerEquipmentName;

    /**
     * 打印机设备编码
     */
    private String printerEquipmentNumber;

    /**
     * 打印机设备厂商
     */
    private Integer printerEquipmentManufacturer;

    /**
     * 设备id
     */
    private Integer printerEquipmentId;

}
