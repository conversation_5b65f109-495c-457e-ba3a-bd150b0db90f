package com.yl.applets.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-09-20 14:46
 * @Author: <a href="<EMAIL>">chunlin.zhang</a>
 */
@Data
public class ThirdExpressListVO {

    /**
     * 主键id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 会员id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer customerId;

    /**
     * 会员编号
     */
    private String customerCode;

    /**
     * 会员名称
     */
    private String customerName;

    /**
     * 订单编号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 运单id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long waybillId;

    /**
     * 运单编号
     */
    private String waybillNo;

    /**
     * 寄件人名称
     */
    private String senderName;
    /**
     * 寄件人手机号码
     */
    private String senderMobilePhone;
    /**
     * 寄件城市名称
     */
    private String senderCityName;
    /**
     * 收件人名称
     */
    private String receiverName;
    /**
     * 收件城市名称
     */
    private String receiverCityName;
    /**
     * 收件人手机号码
     */
    private String receiverMobilePhone;
    /**
     * 状态编码
     */
    private Integer statusCode;
    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 录入时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inputTime;

    /**
     * 订单取件网点id
     */
    private Integer orderNetworkId;

    /**
     * 订单取件网点编码
     */
    private String orderNetworkCode;

    /**
     * 订单取件网点名称
     */
    private String orderNetworkName;


    /**
     * 状态时间：当前状态对应的时间
     */
    private LocalDateTime statusTime;

    /**
     * 状态时间名
     */
    private String statusTimeName;

    /** 云打印订单打印状态 */
    private String printStatusCode;
    /** 打印次数(用于控制重复打印) */
    private Integer printsNumber;

    /** 订单来源 */
    private String orderSourceCode;


    /** 签回单 0否   1是  2回单标记*/
    private Integer signReceipt;

    /** 回单金额 */
    private BigDecimal receiptFreight;
    /** 回单运单号 */
    private String receiptWaybillNo;

    /**
     * 总运费（金额）
     */
    private BigDecimal totalFreight;

    /**
     * 订单支付状态   0、支付成功   2、未支付 3、已关闭（支付取消）  6、支付失败
     */
    private Integer orderPayStatus;

    /**
     * 订单支付状态名称
     */
    private String orderPayStatusName;

    /**
     * 运单寄件网点id
     */
    private Integer waybillNetworkId;

    /**
     * 运单寄件网点编码
     */
    private String waybillNetworkCode;

    /**
     * 运单寄件网点名称
     */
    private String waybillNetworkName;

    /**
     * 寄件网点id
     */
    private String realPickNetworkId;



    /** 修改人 */
    private Integer updateBy;
    /** 修改人 */
    private String updateByName;
    /** 修改人 1,可以修改，2不可以修改 */
    private Integer isUpdate;

    /**
     * 小程序memberId
     */
    private Integer memberId;

    private Boolean isShowPay = false;

    private String realPickCityName;

    /**
     * * 1 网购退货
     * * 2 学生寄件
     * * 3 内部员工寄件
     */
    private Integer orderChannelType;

}