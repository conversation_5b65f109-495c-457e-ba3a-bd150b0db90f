package com.yl.applets.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 优惠劵配置信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustPromotionConfigVo对象", description = "优惠劵配置信息")
public class CustPromotionConfigVo implements Serializable {


    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "优惠券CODE")
    private String code;

    @ApiModelProperty(value = "申请编码")
    private String applyCode;

    @ApiModelProperty(value = "优惠券标题")
    private String couponTitle;

    @ApiModelProperty(value = "发放主体1.市场部2.品牌部0.其他")
    private Integer grantSubject;

    @ApiModelProperty(value = "发放平台1.小程序2.网点")
    private Integer grantPlatform;

    @ApiModelProperty(value = "代理区名字")
    private String agencyName;

    @ApiModelProperty(value = "代理区ID")
    private Long agencyId;

    @ApiModelProperty(value = "领取条件类型0.无门槛1.数字2.仅一张")
    private Integer getCondition;

    @ApiModelProperty(value = "领取条件参数")
    private Integer getValue;

    @ApiModelProperty(value = "使用条件0.无门槛1.数字2.仅一张")
    private Integer useCondition;

    @ApiModelProperty(value = "使用条件参数")
    private Integer useValue;

    @ApiModelProperty(value = "优惠类型1.直减2.折扣")
    private Integer couponType;

    @ApiModelProperty(value = "优惠类型参数或内容")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "优惠限制")
    private BigDecimal couponLimit;

    @ApiModelProperty(value = "优惠券总数")
    private Integer couponCount;

    @ApiModelProperty(value = "领取数量")
    private Integer beReceiveCount;

    @ApiModelProperty(value = "使用数量")
    private Integer beUsedCount;

    @ApiModelProperty(value = "核销数量")
    private Integer beFinishCount;

    @ApiModelProperty(value = "失效数量")
    private Integer beInvalidCount;

    @ApiModelProperty(value = "发放时间：启用时间+10分钟")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime grantTime;

    @ApiModelProperty(value = "发放截止时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime grantEndTime;

    @ApiModelProperty(value = "优惠券使用生效时间类型")
    private Integer couponTimeType;

    @ApiModelProperty(value = "优惠券生效开始时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime couponValidTime;

    @ApiModelProperty(value = "优惠券生效截止时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime couponInvalidTime;

    @ApiModelProperty(value = "优惠券领取之后有效天数")
    private Integer couponTimeValue;

    @ApiModelProperty(value = "用途")
    private String purpose;

    @ApiModelProperty(value = "支付方式1.微信支付")
    private Integer payMode;

    @ApiModelProperty(value = "启用时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime enableTime;

    @ApiModelProperty(value = "启用操作人ID")
    private Long enableUserid;

    @ApiModelProperty(value = "启用操作人名字")
    private String enableUsername;

    @ApiModelProperty(value = "作废原因")
    private String unllifyReason;

    @ApiModelProperty(value = "作废人ID")
    private Long unllifyUserid;

    @ApiModelProperty(value = "作废人名字")
    private String unllifyUsername;

    @ApiModelProperty(value = "作废时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime unllifyTime;

    @ApiModelProperty(value = "增发次数")
    private Integer addNum;

    @ApiModelProperty(value = "增发合计量")
    private Integer addSum;

    @ApiModelProperty(value = "增发人ID")
    private Long addUserid;

    @ApiModelProperty(value = "增发人名字")
    private String addUsername;

    @ApiModelProperty(value = "增发时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime addTime;

    @ApiModelProperty(value = "创建人ID")
    private Long createUserid;

    @ApiModelProperty(value = "创建人名字")
    private String createUsername;

    @ApiModelProperty(value = "创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人ID")
    private Long updateUserid;

    @ApiModelProperty(value = "更新人名字")
    private String updateUsername;

    @ApiModelProperty(value = "更新时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "代理区code")
    private String agencyCode;

    @ApiModelProperty(value = "状态 1未启用 ，2作废 ，3已启用 ，4终止 ，5回收")
    private Integer status;

    @ApiModelProperty(value = "分发数量")
    private Integer beDistributeCount;

    @ApiModelProperty(value = "申请时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "申请优惠券总量")
    private Integer applyCount;

    @ApiModelProperty(value = "申请优惠券单价")
    private BigDecimal applyPriceAmount;

    @ApiModelProperty(value = "申请优惠券总金额")
    private BigDecimal applySumAmount;

    @ApiModelProperty(value = "券实际总金额")
    private BigDecimal couponSumAmount;

    @ApiModelProperty(value = "终止时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime terminateTime;

    @ApiModelProperty(value = "终止人员id")
    private Long terminateUserid;

    @ApiModelProperty(value = "终止人员姓名")
    private String terminateUsername;

    @ApiModelProperty(value = "回收时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recycleTime;

    @ApiModelProperty(value = "回收人员id")
    private Long recycleUserid;

    @ApiModelProperty(value = "回收人员姓名")
    private String recycleUsername;

    @ApiModelProperty(value = "过期数量")
    private BigDecimal overdueCount;

    @ApiModelProperty(value = "优惠卷领取之后多久生效")
    private Long couponStartTimeValue;

    @ApiModelProperty(value = "是否停止发放优惠劵，触发了风控 1 是 2否")
    private Integer isStop;

    @ApiModelProperty(value = "是否新人 1是 2不是")
    private Integer isNew;

    @ApiModelProperty(value = "是否全国  1是 2不是")
    private Integer isNationwide;


}
