package com.yl.applets.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CustomerQueryByPhoneVO implements Serializable {

    private String mobilePhone;

    /**
     * 是否预警客户 1是 2否
     */
    private Integer isWarningCustomer;

    /**
     * 二级问题类型名称
     */
    private String levelTwoTypeName;

    /**
     * 二级问题类型编码
     * 1201：预警1,允许下单，仅打标提示，1202：预警2，不允许下单
     */
    private String levelTwoTypeCode;

    /**
     * 请求来源
     */
    private String querySource;

    private List<String> mobilePhones;
}
