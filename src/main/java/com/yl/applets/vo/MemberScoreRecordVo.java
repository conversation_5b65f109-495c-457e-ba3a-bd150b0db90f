package com.yl.applets.vo;

import com.yl.applets.enums.ChannelMemberEnum;
import com.yl.applets.enums.MemberUserActionSourceEnum;
import com.yl.common.base.util.StringUtils;
import com.yl.redis.util.RedisUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-08-03
 */
@Data
@Slf4j
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "成长值记录vo", description = "成长值记录vo")
public class MemberScoreRecordVo {

    /**
     * 下单使用的id
     */
    private Integer memberId;


    //用户行为
    private String memberAction;

    private String memberActionName;

    /**
     * 用户端-渠道
     */
    private Integer type;

    //成长记录
    private Integer growRecord;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    public void setMemberAction(String memberAction) {
        this.memberAction = memberAction;
        if(!StringUtils.isEmpty(memberAction)){
            for (MemberUserActionSourceEnum value : MemberUserActionSourceEnum.values()) {
                if(value.getCode().equals(memberAction)){
                    this.memberActionName = value.getName();
                }
            }
        }
    }


}
