package com.yl.applets.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OmsWaybill响应对象", description = "运单详情")
public class OmsWaybillDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "运单号")
    private String waybillNo;

    @ApiModelProperty(value = "寄件网点名称")
    private String pickNetworkName;

    @ApiModelProperty(value = "寄件时间")
    private LocalDateTime deliveryTime;

    private Integer memberId;

    @ApiModelProperty(value = "揽件业务员code")
    private String collectStaffCode;

    @ApiModelProperty(value = "揽件业务员名称")
    private String collectStaffName;

    @ApiModelProperty(value = "揽件时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime collectTime;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "客户编号code")
    private String customerCode;

    @ApiModelProperty(value = "客户编号名称")
    private String customerName;

    @ApiModelProperty(value = "产品类型id")
    private Integer expressTypeId;

    @ApiModelProperty(value = "产品类型code")
    private String expressTypeCode;

    @ApiModelProperty(value = "产品类型名称")
    private String expressTypeName;

    @ApiModelProperty(value = "派件方式code")
    private String dispatchCode;

    @ApiModelProperty(value = "派件方式名称")
    private String dispatchName;

    @ApiModelProperty(value = "签收时间")
    private LocalDateTime signTime;

    @ApiModelProperty(value = "寄件人姓名")
    private String senderName;

    @ApiModelProperty(value = "寄件人手机号")
    private String senderMobilePhone;

    @ApiModelProperty(value = "寄件人座机")
    private String senderTelphone;

    @ApiModelProperty(value = "寄件邮编")
    private String senderPostalCode;

    @ApiModelProperty(value = "寄件国家Id")
    private Integer senderCountryId;

    @ApiModelProperty(value = "寄件国家名称")
    private String senderCountryName;

    @ApiModelProperty(value = "寄件省份id")
    private Integer senderProvinceId;

    @ApiModelProperty(value = "寄件省份名称")
    private String senderProvinceName;

    @ApiModelProperty(value = "寄件城市id")
    private Integer senderCityId;

    @ApiModelProperty(value = "寄件城市名称")
    private String senderCityName;

    @ApiModelProperty(value = "寄件区域Id")
    private Integer senderAreaId;

    @ApiModelProperty(value = "寄件区域名称")
    private String senderAreaName;

    @ApiModelProperty(value = "寄件乡镇")
    private String senderTownship;

    @ApiModelProperty(value = "寄件街道")
    private String senderStreet;

    @ApiModelProperty(value = "寄件详细地址")
    private String senderDetailedAddress;

    @ApiModelProperty(value = "始发地id")
    private Integer originId;

    @ApiModelProperty(value = "始发地code")
    private String originCode;

    @ApiModelProperty(value = "始发地名称")
    private String originName;

    @ApiModelProperty(value = "是否实名制编码,1是，0否")
    private Integer isRealName;

    @ApiModelProperty(value = "是否实名制")
    private String isRealCnName;

    @ApiModelProperty(value = "实名姓名")
    private String realName;

    @ApiModelProperty(value = "证件类型")
    private Integer idNoType;

    @ApiModelProperty(value = "证件类型名称")
    private String idNoTypeName;

    @ApiModelProperty(value = "证件号码")
    private String idNo;

    @ApiModelProperty(value = "性别")
    private Integer sex;

    @ApiModelProperty(value = "性别名称")
    private String sexName;

    @ApiModelProperty(value = "收件人姓名")
    private String receiverName;

    @ApiModelProperty(value = "收件人手机号")
    private String receiverMobilePhone;

    @ApiModelProperty(value = "收件人座机")
    private String receiverTelphone;

    @ApiModelProperty(value = "收件邮编")
    private String receiverPostalCode;

    @ApiModelProperty(value = "收件国家id")
    private Integer receiverCountryId;

    @ApiModelProperty(value = "收件国家名称")
    private String receiverCountryName;

    @ApiModelProperty(value = "收件省份id")
    private Integer receiverProvinceId;

    @ApiModelProperty(value = "收件省份名称")
    private String receiverProvinceName;

    @ApiModelProperty(value = "收件城市id")
    private Integer receiverCityId;

    @ApiModelProperty(value = "收件城市名称")
    private String receiverCityName;

    @ApiModelProperty(value = "收件区域id")
    private Integer receiverAreaId;

    @ApiModelProperty(value = "收件区域名称")
    private String receiverAreaName;

    @ApiModelProperty(value = "收件乡镇")
    private String receiverTownship;

    @ApiModelProperty(value = "收件街道")
    private String receiverStreet;

    @ApiModelProperty(value = "收件详细地址")
    private String receiverDetailedAddress;

    @ApiModelProperty(value = "目的地id")
    private Integer destinationId;

    @ApiModelProperty(value = "目的地code")
    private String destinationCode;

    @ApiModelProperty(value = "目的地名称")
    private String destinationName;

    @ApiModelProperty(value = "物品类型id")
    private Integer goodsTypeId;

    @ApiModelProperty(value = "物品类型code")
    private String goodsTypeCode;

    @ApiModelProperty(value = "物品类型名称")
    private String goodsTypeName;

    @ApiModelProperty(value = "物品名称")
    private String goodsName;

    @ApiModelProperty(value = "件数,＞1表示子母件，如果业务上不支持子母件，则前端限制不显示，并默认为1")
    private Integer packageNumber;

    @ApiModelProperty(value = "包裹总长,单位厘米")
    private BigDecimal packageLength;

    @ApiModelProperty(value = "包裹总宽,单位厘米")
    private BigDecimal packageWide;

    @ApiModelProperty(value = "包裹总高,单位厘米")
    private BigDecimal packageHigh;

    @ApiModelProperty(value = "包裹计费重量,单位千克")
    private BigDecimal packageChargeWeight;

    @ApiModelProperty(value = "包裹总重量,单位千克")
    private BigDecimal packageTotalWeight;

    @ApiModelProperty(value = "包裹总体积,单位立方厘米")
    private BigDecimal packageTotalVolume;

    @ApiModelProperty(value = "包裹体积重,单位立方厘米")
    private BigDecimal packageVolume;

    @ApiModelProperty(value = "包裹收件重量,单位千克")
    private BigDecimal packageCollectWeight;

    @ApiModelProperty(value = "包裹入仓重量,单位千克")
    private BigDecimal packageReceiptWeight;

    @ApiModelProperty(value = "包裹集散到件重量,单位千克")
    private BigDecimal packageArrivalWeight;

    @ApiModelProperty(value = "包裹内部计费重量,单位千克")
    private BigDecimal packageInsideChargeWeight;

    @ApiModelProperty(value = "运单重量")
    private BigDecimal waybillWeight;

    @ApiModelProperty(value = "包材规格code")
    private String boxStandardCode;

    @ApiModelProperty(value = "包材规格名称")
    private String boxStandardName;

    @ApiModelProperty(value = "包材数量")
    private Integer boxNumber;

    @ApiModelProperty(value = "箱子价格")
    private BigDecimal boxPrice;

    @ApiModelProperty(value = "结算方式id")
    private Integer settlementId;

    @ApiModelProperty(value = "结算方式编码")
    private String settlementCode;

    @ApiModelProperty(value = "结算方式名称")
    private String settlementName;

    @ApiModelProperty(value = "是否需要代收货款,1是，0否")
    private Integer codNeed;

    @ApiModelProperty(value = "代收货款金额")
    private BigDecimal codMoney;

    @ApiModelProperty(value = "代收货款手续费")
    private BigDecimal codFee;

    @ApiModelProperty(value = "COD收款标识,1是，0否")
    private Integer isCodReceive;

    @ApiModelProperty(value = "货币币别编码")
    private String currencyCode;

    @ApiModelProperty(value = "货币币别名称")
    private String currencyName;

    @ApiModelProperty(value = "是否需要保价,1是，0否")
    private Integer insured;

    @ApiModelProperty(value = "保价金额")
    private BigDecimal insuredAmount;

    @ApiModelProperty(value = "保价费")
    private BigDecimal insuredFee;

    @ApiModelProperty(value = "是否需要需要签回单,1是，0否")
    private Integer isNeedReceipt;

    @ApiModelProperty(value = "关联单号")
    private String receiptNo;

    @ApiModelProperty(value = "优惠券编号")
    private String couponCode;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal couponAmount;

    @ApiModelProperty(value = "包材费")
    private BigDecimal packageCost;

    @ApiModelProperty(value = "运费")
    private BigDecimal freight;

    @ApiModelProperty(value = "税金")
    private BigDecimal tax;

    @ApiModelProperty(value = "手工运费")
    private BigDecimal handicraftFee;

    @ApiModelProperty(value = "其他费")
    private BigDecimal otherFee;

    @ApiModelProperty(value = "税后总运费")
    private BigDecimal afterTaxFreight;

    @ApiModelProperty(value = "总运费")
    private BigDecimal totalFreight;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "运单来源code")
    private String waybillSourceCode;

    @ApiModelProperty(value = "运单来源名称")
    private String waybillSourceName;

    @ApiModelProperty(value = "'内部订单编号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    @ApiModelProperty(value = "客户订单编号")
    private String customerOrderId;

    @ApiModelProperty(value = "寄件拼接地址")
    private String senderFullAddress;

    @ApiModelProperty(value = "收件拼接地址")
    private String receiverFullAddress;

    @ApiModelProperty(value = "运单状态")
    private String waybillStatusCode;

    @ApiModelProperty(value = "派件网点id")
    private Integer dispatchNetworkId;

    @ApiModelProperty(value = "派件网点code")
    private String dispatchNetworkCode;

    @ApiModelProperty(value = "派件网点名称")
    private String dispatchNetworkName;

    @ApiModelProperty(value = "寄件网点code")
    private String pickNetworkCode;

    @ApiModelProperty(value = "签收网点编码")
    private String signNetworkCode;

    @ApiModelProperty(value = "签收网点名称")
    private String signNetworkName;

    @ApiModelProperty(value = "寄件网点id")
    private Integer pickNetworkId;

    @ApiModelProperty(value = "寄件网点财务网点id")
    private Integer pickFinanceId;

    @ApiModelProperty(value = "是否退件转寄，0否，1退件，2转寄")
    private Integer isRefund;

    @ApiModelProperty(value = "是否签收,签收标识,1正常签收，0否,2退件签收")
    private Integer isSign;

    @ApiModelProperty(value = "支付方式编码")
    private String paidModeCode;

    @ApiModelProperty(value = "支付方式名称")
    private String paidModeName;

    /**
     * 寄件财务中心code
     */
    @ApiModelProperty(value = "寄件财务中心code")
    private String pickFinanceCode;

    /**
     * 寄件财务中心名称
     */
    @ApiModelProperty(value = "寄件财务中心名称")
    private String pickFinanceName;

    /**
     * 派件财务中心code
     */
    @ApiModelProperty(value = "派件财务中心code")
    private String dispatchFinanceCode;

    /**
     * 派件财务中心名称
     */
    @ApiModelProperty(value = "派件财务中心名称")
    private String dispatchFinanceName;


    /**
     * 转运中心财务中心id
     */
    private Integer distributeFinanceId;

    /**
     * 转运中心财务中心code
     */
    private String distributeFinanceCode;

    /**
     * 转运中心财务中心名称
     */
    private String distributeFinanceName;

    /**
     * 派件业务员code
     */

    private String dispatchStaffCode;

    /**
     * 派件业务员名称
     */

    private String dispatchStaffName;

    /**
     * 派件时间
     */

    private LocalDateTime dispatchTime;

    /**
     * 问题件标识,1是，0否
     */

    private Integer isAbnormal;

    /**问题件登记时间**/
    private LocalDateTime abnormalRegTime;

    private BigDecimal receivePayFee;

    /**
     * 签收图片
     */
    @ApiModelProperty(value = "签收图片")
    private String sigPicUrl;
    /**
     * 电子签名图片
     */
    @ApiModelProperty(value = "电子签名图片")
    private String electronicSignaturePicUrl;

    /**
     * 签收图片
     */
    @ApiModelProperty(value = "签收图片")
    private List<String> sigPicUrlList;
    /**
     * 电子签名图片
     */
    @ApiModelProperty(value = "电子签名图片")
    private List<String> electronicSignaturePicUrlList;

    /**
     * 签收类型id
     */
    @ApiModelProperty(value = "签收类型id")
    private Integer signId;

    /**
     * 签收类型编码
     */
    @ApiModelProperty(value = "签收类型编码")
    private String signCode;

    /**
     * 签收类型名称
     */
    @ApiModelProperty(value = "签收类型名称")
    private String signName;

    @ApiModelProperty(value = "录入时间")
    private LocalDateTime inputTime;

    /**
     * 收件分拣码
     */

    private String receiverSortingCode;

    @ApiModelProperty(value = "是否作废件,1是，0否")
    private Integer isVoid;

    @ApiModelProperty(value = "签收财务网点编码")
    private String signFinanceCode;

    @ApiModelProperty(value = "签收财务网点名称")
    private String signFinanceName;

    @ApiModelProperty(value = "订单来源code")
    private String orderSourceCode;

    private LocalDateTime refundTime;

    @ApiModelProperty(value = "录入人ID")
    private Integer inputStaffBy;

    @ApiModelProperty(value = "录入人code")
    private String inputStaffCode;

    @ApiModelProperty(value = "录入人名称")
    private String inputStaffName;

    /**
     * 三段码
     **/
    @ApiModelProperty(value = "三段码")
    private String terminalDispatchCode;

    /**
     * 一段码名称
     */
    @ApiModelProperty(value = "一段码名称")
    private String centerName;

    @ApiModelProperty(value = "是否已收款 1已收款 2未收款 3待支付-锁定")
    private Integer isReceive;

    @ApiModelProperty(value = "转运中心扫描标识:1已转运,0未转运,默认0")
    private Integer isDistributeScan;

    @ApiModelProperty(value = "集散到件标识:1是,0否,默认0")
    private Integer isArrivalScan;

    @ApiModelProperty(value = "寄件方式code")
    private String sendCode;

    @ApiModelProperty(value = "寄件方式名称")
    private String sendName;

    @ApiModelProperty(name ="是否打印客户存根 0否，1是")
    private int printerCounterfoil;

    @ApiModelProperty(name ="是否残缺 1残缺、0完整")
    private Integer missFlag;

    @ApiModelProperty(name ="是否隐私面单 0:否 1:是")
    private Integer isPrivacy;

    @ApiModelProperty(value = "最佳取件开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime bestPickTimeStart;

    @ApiModelProperty(value = "最佳取件结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime bestPickTimeEnd;

    private String realPickCityName;//实际取件网点城市

}
