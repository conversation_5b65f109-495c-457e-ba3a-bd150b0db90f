package com.yl.applets.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SysCustomerSameMallGroupVO extends SysCustomerGroupByMallVO implements Serializable {

    private static final long serialVersionUID = -3606110583803448359L;

    private List<SysCustomerGroupByMallVO> sameMallCustomers;
}

