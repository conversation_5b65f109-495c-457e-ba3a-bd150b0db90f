package com.yl.applets.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 优惠劵领取使用信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "", description = "优惠劵领取使用信息表")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustPromotionInfoVo implements Serializable {

    public CustPromotionInfoVo(Long id, Long proId, String proCode, String freight) {
        this.id = id;
        this.proId = proId;
        this.proCode = proCode;
        this.freight = new BigDecimal(freight);
    }

    public CustPromotionInfoVo() {}

    @ApiModelProperty(value = "当前优惠劵id")
    private Long id;

    @ApiModelProperty(value = "主优惠劵id")
    private Long proId;

    @ApiModelProperty(value = "优惠劵名称")
    private String proName;

    @ApiModelProperty(value = "优惠劵CODE")
    private String proCode;

    @ApiModelProperty(value = "优惠劵类型 1.直减券2.折扣券 ")
    private Integer couponType;

    @ApiModelProperty(value = "优惠类型参数")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "单笔最高抵扣金额")
    private BigDecimal couponLimit;

    @ApiModelProperty(value = "优惠劵有效开始时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime couponValidTime;

    @ApiModelProperty(value = "优惠劵有效结束时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime couponInvalidTime;

    @ApiModelProperty(value = "订单原价")
    private BigDecimal freight;

    @ApiModelProperty(value = "订单优惠金额")
    private BigDecimal orderPreAmount;

    @ApiModelProperty(value = "收件城市id,如果是空，就是目的全网适用")
    private List<Long> receiveCityIds;

    @ApiModelProperty(value = "寄件城市id,如果是空，就是始发全网适用")
    private List<Long> sendCityIds;


}
