package com.yl.applets.vo;

import com.yl.applets.dto.WaybillInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AppOmsWaybillDetailVO extends OmsWaybillDetailVO {

    private String staffMobile;

    @ApiModelProperty(value = "运费费用")
    private WaybillInfoDto waybillFee;

    private String orderReceiveMobile;
    private String orderSenderMobile;

    private String paymentModeName;

    /**
     * 订单是否小程序来源 1 是 0 否
     */
    private Integer isApplets;

    /**
     * 派件业务员电话
     */
    private String dispatchStaffMobile;

    /**
     * 揽件业务员电话
     */
    private String collectStaffMobile;
}
