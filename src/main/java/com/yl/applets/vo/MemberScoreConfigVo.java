package com.yl.applets.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.yl.applets.enums.ChannelMemberEnum;
import com.yl.applets.enums.MemberUserActionSourceEnum;
import com.yl.common.base.util.StringUtils;
import com.yl.redis.util.RedisUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-08-03
 */
@Data
@Slf4j
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "积分配置vo", description = "积分配置vo")
public class MemberScoreConfigVo {

//    public MemberScoreConfigVo(Integer memberId){
//        this.memberId=memberId;
//    }

    private Integer memberId;

    /**
     * 用户端-渠道
     */
    @ApiModelProperty(value = "渠道")
    private Integer type;

    /**
     * 成长分值/次
     */
    @ApiModelProperty(value = "成长值(兔粮)")
    private Integer growValue;


    /**
     * 成长限制/日
     */
    @ApiModelProperty(value = "限制次数")
    private Integer growLimit;

    @ApiModelProperty(value = "当前次数")
    private Long currentNum;

    @ApiModelProperty(value = "是否达到限制")
    private Boolean isLimit;


    /**
     * 用户行为
     */
    @ApiModelProperty(value = "用户行为")
    private String memberAction;

    @ApiModelProperty(value = "展示文案")
    private String showText;



    public void setGrowLimit(Integer growLimit) {
        this.growLimit = growLimit;
        if (growLimit!=null){
            String key=ChannelMemberEnum.MemberUserRedis.CHANNEL_PRE + ":" + this.type + ":" + this.memberAction;
            String memberId=ChannelMemberEnum.MemberUserRedis.GROW_LIMIT + this.memberId;
            log.info("设置isLimit：key{}",key+memberId);
            Object o = RedisUtil.hGet(key, memberId);
            if(o!=null){
                long l = Long.parseLong(o + "");
                //当前次数
                this.currentNum = l;
                log.info("成长值限制：{},当前成长值：{}",growLimit,l);
                if(l>=growLimit){
                    this.isLimit=true;
                }else {
                    this.isLimit=false;
                }
            }else {
                this.currentNum = 0L;
                this.isLimit=false;
            }
        }
    }


    public void setMemberAction(String memberAction) {
        this.memberAction = memberAction;
        if(!StringUtils.isEmpty(memberAction)){
            for (MemberUserActionSourceEnum value : MemberUserActionSourceEnum.values()) {
                if(value.getCode().equals(memberAction)){
                    this.showText = value.getShow();
                }
            }
        }
    }

}
