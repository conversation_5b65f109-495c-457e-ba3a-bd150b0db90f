package com.yl.applets.vo.quickoperationticke;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/4/14 16:28
 */
@Data
public class CcmWorkOrderVO {
    /**
     * 工单ID
     */
    Long workOrderId;

    /**
     * 工单编号
     */
    String workOrderNo;

    /**
     * 运单号
     */
    String waybillNo;

    /**
     * 工单状态
     */
    Integer workOrderStatus;

    /**
     * 一级问题类型编码
     */
    String firstTypeCode;

    /**
     * 二级问题类型编码
     */
    String secondTypeCode;

    /**
     * 一级选项name
     */
    String optionOneName;

    /**
     * 二级选项name
     */
    String optionTwoName;

    /**
     * 三级选项name
     */
    String optionThreeName;

    /**
     * 问题描述
     */
    String problemDescription;

    /**
     * 催单次数
     */
    Integer reminderCount;

    /**
     * 登记时间
     */
    LocalDateTime createTime;

    /**
     * 附件
     */
    List<String> filePaths = new ArrayList<>();

    /**
     * 处理记录列表
     */
    List<CcmWorkOrderProcessRecordVO> processRecords = new ArrayList<>();
}
