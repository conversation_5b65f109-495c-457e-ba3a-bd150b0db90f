package com.yl.applets.vo.quickoperationticke;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/4/14 16:31
 */
@Data
public class CcmWorkOrderProcessRecordVO {

    /**
     * 处理类型
     */
    Integer processType;

    /**
     * 处理时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime processTime;
}
