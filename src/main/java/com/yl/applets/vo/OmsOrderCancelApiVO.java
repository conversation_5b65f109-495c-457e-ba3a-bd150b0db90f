/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: OmsOrderCancelApiVO
 * Author:   luhong
 * Date:     2020-10-15 14:42
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-15
 * @since 1.0.0
 */
@ApiModel(
        value = "OmsOrderApi请求对象",
        description = "订单Api请求实体"
)
@Data
public class OmsOrderCancelApiVO implements Serializable {
    private Boolean success;
    private String reason;
    private Long orderId;
    private String customerOrderId;
    private String waybillId;
}