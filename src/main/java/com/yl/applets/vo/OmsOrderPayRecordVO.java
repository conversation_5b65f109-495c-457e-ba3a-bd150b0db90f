package com.yl.applets.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单流水VO
 *
 * <AUTHOR>
 * @date 2021-12-09
 */
@Data
public class OmsOrderPayRecordVO implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 订单金额，保留两位小数
     */
    private BigDecimal orderAmount;

    /**
     * 运单号
     */
    private String waybillNo;

    /**
     * 订单支付状态 0、"支付成功"   2、"未支付" 3、"已关闭"（支付取消）  6、"支付失败"
     */
    private Integer orderPayStatus;

    /**
     * 1.微信支付 2、现金支付
     */
    private Integer paymentSource;

    /**
     * 服务商应用id
     */
    private String spAppid;

    /**
     * 服务商户号
     */
    private String spMchid;

    /**
     * 二级商户应用ID
     */
    private String subAppid;

    /**
     * 二级商户号
     */
    private String subMchid;

    /**
     * 商户订单号
     */
    private Long orderNo;

    /**
     * 用户服务标识
     */
    private String spOpenid;

    /**
     * 用户终端ip
     */
    private String payerClientIp;

    /**
     * 支付流水号
     */
    private String paymentNo;

    /**
     * 商品描述
     */
    private String goodsDesc;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人，线下操作，线上微信
     */
    private String createByName;

    /**
     * 更新名称
     */
    private String updateByName;

    /**
     * 支付时间
     */
    private LocalDateTime payCreateTime;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 用户code
     */
    private String userCode;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 优惠券code
     */
    private String couponCode;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 优惠券优惠金额
     */
    private BigDecimal couponAmount;

    /**
     * 原订单金额
     */
    private BigDecimal originalAmount;
}
