package com.yl.applets.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 优惠劵分发表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "优惠券列表对象", description = "优惠券列表对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustUserPromotionVo implements Serializable {


    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "主键")
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "优惠劵id")
    private Long proId;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "分发id")
    private Long disId;

    @ApiModelProperty(value = "优惠券CODE")
    private String proCode;

    @ApiModelProperty(value = "申请编码")
    private String applyCode;

    @ApiModelProperty(value = "优惠券标题")
    private String couponTitle;

    @ApiModelProperty(value = "优惠类型1.直减2.折扣")
    private Integer couponType;

    @ApiModelProperty(value = "优惠类型参数或内容")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "优惠限制")
    private BigDecimal couponLimit;

    @ApiModelProperty(value = "发放截止时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime grantEndTime;

    @ApiModelProperty(value = "网点id")
    private Long networkId;

    @ApiModelProperty(value = "网点名称")
    private String networkName;

    @ApiModelProperty(value = "网点code")
    private String networkCode;

    @ApiModelProperty(value = "业务员code")
    private String pickStaffCode;


    @ApiModelProperty(value = "收件城市id,如果是空，就是目的全网适用")
    private List<Long> receiveCityIds;

    @ApiModelProperty(value = "寄件城市id,如果是空，就是始发全网适用")
    private List<Long> sendCityIds;

    private Integer current;

    private Integer size;


    @ApiModelProperty(value = "优惠券规则")
    private String purpose;

    //领劵时，校验当前用户是否可领
    @ApiModelProperty(value = "用户所在城市的id")
    private String cityId;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户id")
    private Long numberId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "用户手机号")
    private String userPhone;

    @ApiModelProperty(value = "用户昵称")
    private String userAlias;

    @ApiModelProperty(value = "用户uuid")
    private String userUuid;

    /**
     * 是否专属
     * 1.是 0.否
     */
    private Integer isExclusive;

}
