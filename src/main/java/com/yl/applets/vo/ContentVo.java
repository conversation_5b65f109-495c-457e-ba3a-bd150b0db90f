package com.yl.applets.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @ClassName ContentVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2022-01-17
 * @Version 1.0
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ContentVo {
    @JSONField(name="news_item")
    @JsonProperty("news_item")
    private List<NewsItemVo> newsItem;
}
