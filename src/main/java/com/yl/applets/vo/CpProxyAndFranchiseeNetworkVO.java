package com.yl.applets.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: CpProxyAndFranchiseeNetworkVO
 * @description: 根据编码查询代理区和加盟商
 * @author: zhuzheng<PERSON>e
 * @create: 2019-06-28 16:47
 **/
@Data
@ApiModel(description = "根据编码查询代理区和加盟商VO")
public class CpProxyAndFranchiseeNetworkVO implements Serializable {


    @ApiModelProperty(name = "proxyId", value = "代理区id")
    private String proxyId;

    @ApiModelProperty(name = "proxyCode", value = "代理区编码")
    private String proxyCode;

    @ApiModelProperty(name = "proxyName", value = "代理区名称")
    private String proxyName;

    @ApiModelProperty(name = "franchiseeId", value = "加盟商Id")
    private String franchiseeId;

    @ApiModelProperty(name = "franchiseeCode", value = "加盟商编码")
    private String franchiseeCode;

    @ApiModelProperty(name = "franchiseeName", value = "加盟商名称")
    private String franchiseeName;


}
