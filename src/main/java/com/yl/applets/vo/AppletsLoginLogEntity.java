package com.yl.applets.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description 微信小程序登录日志
 * <AUTHOR>
 * @Date 2022/10/13 9:35
 * @Version 1.0
 */

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("YL_JMSAPPLETS_LOG_INFO")
@Accessors(chain = true)
public class AppletsLoginLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ID")
    private Long id;

    @TableField("MEMBER_ID")
    private Long memberId;

    @TableField("MOBILE")
    private String mobile;

    @TableField("NICK_NAME")
    private String nickName;

    @TableField("GENDER")
    private Integer gender;

    @TableField("LOGIN_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss") // 表示返回时间类型
    private LocalDateTime loginTime;

    @TableField("RESOURCE_TYPE")
    private Integer resourceType;

    @TableField("IP")
    private String ip;

    @TableField("PROVINCE")
    private String province;

    @TableField("CITY")
    private String city;

    @TableField("ZONE")
    private String zone;

    @TableField("EQUIPMENT_BRAND")
    @ApiModelProperty(value = "签到时的设备品牌", notes = "签到时的设备品牌")
    private String equipmentBrand;

    @TableField("EQUIPMENT_TYPE")
    @ApiModelProperty(value = "android/ios", notes = "android/ios")
    private String equipmentType;

    @TableField("CREATE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")                    // 表示返回时间类型
    private LocalDateTime createTime;

    @TableField("EQUIPMENT_MODEL")
    private String equipmentModel;

    @TableField("EQUIPMENT_CODE")
    private String equipmentCode;

    @TableField("RESOURCE_POLICE")
    private String resourcePolice;
}
