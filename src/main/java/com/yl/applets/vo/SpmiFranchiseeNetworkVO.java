package com.yl.applets.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "SqFranchiseeNetworkVO", description = "服务质量平台网点响应对象")
public class SpmiFranchiseeNetworkVO implements Serializable {
    @ApiModelProperty(name = "id", value = "网点id")
    private Integer id;

    @ApiModelProperty(name = "name", value = "名称")
    private String name;

    @ApiModelProperty(name = "code", value = "编号")
    private String code;

    @ApiModelProperty(name = "franchiseeId", value = "所属加盟商id")
    private Integer franchiseeId;

    @ApiModelProperty(name = "franchiseeName", value = "所属加盟商名称")
    private String franchiseeName;

    @ApiModelProperty(name = "franchiseeCode", value = "所属加盟商编号")
    private String franchiseeCode;

    /**
     * 寄件网点财务中心信息
     */
    private Integer financialCenterId;

    private String financialCenterCode;

    private String financialCenterDesc;
}
