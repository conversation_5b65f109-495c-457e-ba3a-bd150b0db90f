package com.yl.applets.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SpmCommonCostVO implements Serializable {

    @ApiModelProperty(name = "cost", value = "费用")
    private BigDecimal cost;
    @ApiModelProperty(name = "setWeight", value = "结算重量")
    private BigDecimal setWeight;

    @ApiModelProperty(name = "firstWeightPrice", value = "首重单价")
    private BigDecimal firstWeightPrice;

    @ApiModelProperty(name = "laterWeightPrice", value = "续重单价")
    private BigDecimal laterWeightPrice;

    @ApiModelProperty(name = "laterWeightPrice", value = "续重费用")
    private BigDecimal laterCost;

    @ApiModelProperty(value = "折扣后费用")
    private BigDecimal discountedCost;

    @ApiModelProperty(value = "折扣后首重费用")
    private BigDecimal discountedFirstWeightPrice;


    @ApiModelProperty(value = "折扣费用")
    private BigDecimal feeCost;


    @ApiModelProperty(value = "折扣后续重价格")
    private BigDecimal discountedLaterWeightPrice;


    @ApiModelProperty(value = "折扣率")
    private BigDecimal discountRate;


}
