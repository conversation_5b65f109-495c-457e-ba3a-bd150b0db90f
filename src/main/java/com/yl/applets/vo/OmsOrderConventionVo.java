package com.yl.applets.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021-07-20
 *
 * @Description
 * @Author: gaoxuewne
 * @Date: 2021-07-20 9:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OmsOrderConventionVo  implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 订单编号
     */
    private Long orderId;

    /***
     * 运单号
     */
    private String waybillId;

    /**
     * 订单来源名称
     */
    private String orderSourceName;

    /**
     * 订单来源code
     */
    private String orderSourceCode;

    /**
     * 订单录入时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inputTime;

    /**
     * 调度代理区时间
     */
    private LocalDateTime dispatchProxyAreaTime;

    /**
     * 代理区code
     */
    private String proxyAreaCode;

    /**
     * 代理区name
     */
    private String proxyAreaName;

    /**
     * 调度网点时间
     */

    private LocalDateTime dispatchNetworkTime;

    /**
     * 调度业务员时间
     */

    private LocalDateTime dispatchStaffTime;

    /***
     * 调度业务员编码
     */
    private String dispatchStaffCode;

    /***
     * 调度业务员姓名
     */
    private String dispatchStaffName;

    /**
     * 订单预约时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderConventionTime;

    /**
     * 预约取件开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime conventionPickStartTime;


    /**
     * 预约取件结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime conventionPickEndTime;

    /**
     * 预约人姓名
     */
    private String conventionPersonName;

    /**
     * 预约人ID
     */
    private Integer conventionPersonId;

    /**
     * 取件时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pickTime;

    /**
     * 取件网点名称
     */

    private String pickNetworkName;

    /**
     * 取件网点code
     */

    private String pickNetworkCode;


    /**
     * 取件业务员名称
     */

    private String pickStaffName;

    /**
     * 取件业务员code
     */
    private String pickStaffCode;

    /**
     * 超时未预约:0否1是
     */
    private Integer overtimeNoConvention;

    /**
     * 超时未取件:0否1是
     */
    private Integer overtimeNoPick;

    /**
     * 即将超时未取件:0否1是
     */
    private Integer soonOvertimeNoPick;

    /***
     * 0表示调度网点，1表示调度业务员，2.表示已收件3.表示已取消 , 4.被打回代理区
     */
    private Integer orderStatus;

    /***
     * 预约状态 0否1是
     */
    private Integer conventionStatus;

    /**
     * 二次订单预约时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime secondOrderConventionTime;

    /**
     * 二次预约上门取件开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime secondConventionStartTime;

    /**
     * 二次预约上门取件结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime secondConventionEndTime;

    /**
     * 取件截止时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pickEndTime;

    /**
     * 预约截止时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime conventionEndTime;
}