package com.yl.applets.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/12/12 20:13
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "公共算费VO类")
public class CiticSpmCommonCostVO  implements Serializable {
    private static final long serialVersionUID = 5554965962713621005L;

    @ApiModelProperty(name = "cost", value = "费用")
    private BigDecimal cost;

    @ApiModelProperty(name = "setWeight", value = "结算重量")
    private BigDecimal setWeight;

    @ApiModelProperty(value = "到付比例, 1-100区间, 保留2位小数, [非必填, 默认为1]")
    private BigDecimal codScale;

    @ApiModelProperty(value = "算费失败原因")
    private String calCostDesc;
}
