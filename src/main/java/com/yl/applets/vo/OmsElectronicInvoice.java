package com.yl.applets.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "小程序电子发票响应对象", description = "小程序电子发票响应对象")
public class OmsElectronicInvoice implements Serializable {

    @ApiModelProperty(value = "运单号")
    private String waybillId;

    @ApiModelProperty(value = "寄件时间")
    private LocalDateTime inputTime;

    @ApiModelProperty(value = "寄件城市名称")
    private String senderCityName;

    @ApiModelProperty(value = "寄件人姓名")
    private String senderName;

    @ApiModelProperty(value = "收件城市名称")
    private String receiverCityName;

    @ApiModelProperty(value = "收件人姓名")
    private String receiverName;

    @ApiModelProperty(value = "结算方式名称")
    private String settlementName;

    @ApiModelProperty(value = "总运费")
    private BigDecimal totalFreight;

    @ApiModelProperty("运费")
    private BigDecimal freight;

    @ApiModelProperty("保价费")
    private BigDecimal insuredFee;

}
