package com.yl.applets.thread;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yl.applets.entity.WxOfficialUser;
import com.yl.applets.service.WxOfficialUserService;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.util.GenerationIdUtil;
import com.yl.common.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.apache.commons.codec.binary.Base64;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: xiongweibin
 * @create: 2020-12-02 14:41
 */
@Slf4j
public class WxHistoryUserHandleThread implements Runnable {
    private List<String> secList;
    private WxMpService wxMpService;
    private WxOfficialUserService wxOfficialUserService;
    private AtomicInteger count;
    private CountDownLatch latch;
    private int startNum;

    public WxHistoryUserHandleThread(List<String> secList, WxMpService wxMpService, CountDownLatch latch, WxOfficialUserService wxOfficialUserService, AtomicInteger count,int startNum) {
        this.secList = secList;
        this.wxMpService = wxMpService;
        this.wxOfficialUserService = wxOfficialUserService;
        this.latch = latch;
        this.count = count;
        this.startNum = startNum;
    }

    @Override
    public void run() {
        try {
            handerHistoryUser(secList);
        } catch (Exception e) {
            log.info("处理第{}-{}批次失败",count,startNum,e);
            throw new BusinessException(ResultCodeEnum.INTERFACE_INNER_INVOKE_ERROR);
        } finally {
            latch.countDown();
        }

    }

    private void handerHistoryUser(List<String> secList) {
        AtomicInteger i = new AtomicInteger(0);
        AtomicInteger successCount = new AtomicInteger(0);
        secList.forEach(openId->{
            try {
                i.getAndIncrement();
                WxMpUser wxMpUser = null;
                try {
                    wxMpUser = wxMpService.getUserService().userInfo(openId);
                } catch (WxErrorException e) {
                    log.warn("第{}-{}批次，第{}个调用微信接口获取用户信息失败：openId:{}",openId,e);
                }

                if(wxMpUser==null){
                    log.info("第{}-{}批次，第{}个调用微信接口获取用户信息null:{}", count,startNum,i,openId);
                    return;
                }
                LambdaQueryWrapper<WxOfficialUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(WxOfficialUser::getUnionid,wxMpUser.getUnionId());
                WxOfficialUser wxOfficialUser = wxOfficialUserService.getOne(queryWrapper);
                if(wxOfficialUser!=null){
                    log.info("第{}-{}批次，第{}个用户已存在:{}", count,startNum,i,openId);
                    return;
                }
                wxOfficialUser = new WxOfficialUser();
                wxOfficialUser.setId(GenerationIdUtil.getId());
                wxOfficialUser.setHeadImgUrl(wxMpUser.getHeadImgUrl());
                wxOfficialUser.setOpenid(wxMpUser.getOpenId());
                wxOfficialUser.setUnionid(wxMpUser.getUnionId());
                String nickName = wxMpUser.getNickname();
                wxOfficialUser.setNickName(StringUtils.isEmpty(nickName)?"":Base64.encodeBase64String(wxMpUser.getNickname().getBytes(StandardCharsets.UTF_8)));
                wxOfficialUser.setSex(wxMpUser.getSex());
                wxOfficialUser.setSubscribeScene(wxMpUser.getSubscribeScene());
                wxOfficialUser.setSubscribeTime(wxMpUser.getSubscribeTime());
                wxOfficialUser.setSubscribe(wxMpUser.getSubscribe()?1:0);
                wxOfficialUser.setCountry(wxMpUser.getCountry());
                wxOfficialUser.setProvince(wxMpUser.getProvince());
                wxOfficialUser.setCity(wxMpUser.getCity());
                LocalDateTime now = LocalDateTime.now();
                wxOfficialUser.setCreateTime(now);
                wxOfficialUser.setUpdateTime(LocalDateTime.now());
                wxOfficialUserService.save(wxOfficialUser);
                log.info("第{}-{}批次新增第{}个用户成功",count,startNum,i);
                successCount.getAndIncrement();
            } catch (Exception e) {
                log.warn("第{}-{}批次新增第{}个用户失败:{}",count,startNum,i,openId,e);
            }
        });
        log.info("第{}-{}批次成功新增用户数：{}",count,startNum,successCount);
    }
}
