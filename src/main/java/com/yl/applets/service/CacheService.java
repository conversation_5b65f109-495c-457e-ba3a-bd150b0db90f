package com.yl.applets.service;

import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 功能描述:
 * redis 分布式锁
 * @since: 1.0.0
 * @Author:luhong
 * @Date: 2021-01-25 11:28
 */
@Service
@Slf4j
public class CacheService {

    @Autowired
    private IRedisLockService redisLockService;

    /**
     * @author: luhong
     * @Description redis分布式缓存锁 (存在返回fasle  ,不存在设置值并返回true)
     * @Date 18:43 2018/6/27
     * @Param [lockName]
     * @return:boolean
     */
    public boolean lock(String lockName){
        Boolean boo = true;
        try {
            boo = redisLockService.setAndExpireIfAbsent(lockName, 10L);
        } catch (Exception e) {
            log.error("加锁失败",e);
            unlock(lockName);
        }
        return boo;
    }

    /**
     * @author: luhong
     * @Description 释放锁
     * @Date 18:43 2018/6/27
     * @Param [lockName]
     * @return:boolean
     */
    public void unlock(String lockName) {
        try {
            RedisUtil.delete(lockName);
        } catch (Exception e) {
            log.error("释放锁失败",e);
        }
    }
}
