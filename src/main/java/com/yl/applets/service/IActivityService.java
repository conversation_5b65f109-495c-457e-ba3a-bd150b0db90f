package com.yl.applets.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.ActivityConfigDTO;
import com.yl.applets.dto.ActivityConfigQueryDTO;
import com.yl.applets.dto.ActivityVisitRecordDTO;
import com.yl.applets.entity.ActivityConfig;
import com.yl.applets.vo.ActivityConfigVO;
import com.yl.applets.vo.ActivityVisitRecordStatisticsVO;
import com.yl.common.base.model.vo.Result;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动管理
 * @author: xiongweibin
 * @create: 2020-08-24 11:46
 */
public interface IActivityService {

    /**
     * 活动配置新增
     * @param queryDTO
     * @return
     */
    Page<ActivityConfigVO> getPages(ActivityConfigQueryDTO queryDTO);

    /**
     * 活动配置集合查询
     * @param queryDTO
     * @return
     */
    List<ActivityConfigVO> list(ActivityConfigQueryDTO queryDTO);

    /**
     * 新增活动配置
     * @param dto
     * @return
     */
    Result<ActivityConfig> add(ActivityConfigDTO dto);

    /**
     * 删除活动配置
     * @param dto
     * @return
     */
    Result delete(ActivityConfigDTO dto);

    /**
     * 下线
     * @param dto
     * @return
     */
    Result offline(ActivityConfigDTO dto);

    /**
     * 更新活动配置
     * @param dto
     * @return
     */
    Result<ActivityConfig> update(ActivityConfigDTO dto);

    /**
     * 统计活动访问次数
     * @param id
     * @return
     */
    Result<List<ActivityVisitRecordStatisticsVO>> statisticsActivityVisitQty(Long id);

    /**
     * 查询详情
     * @param id
     * @return
     */
    Result<ActivityConfigVO> getDetailById(Long id);

    /**
     * 上传访问曝光记录
     * @param dto
     * @return
     */
    Result uploadAccessRecord(ActivityVisitRecordDTO dto);

    Boolean springActivityIsValid();
}
