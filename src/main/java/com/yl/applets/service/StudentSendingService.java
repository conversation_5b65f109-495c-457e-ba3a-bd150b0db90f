package com.yl.applets.service;

import com.yl.applets.dto.StudentSendingDTO;
import com.yl.applets.vo.studentsending.WXStudentSendingResultVO;
import com.yl.common.base.model.vo.Result;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/4/18 10:28
 */
public interface StudentSendingService {

    /**
     * 获取学生认证状态
     * @param numberId
     * @return
     */
    Integer getAuthenticationStatus(Integer numberId);


    /**
     * 腾讯认证
     * @param dto
     * @return
     */
    Result<WXStudentSendingResultVO> authentication(StudentSendingDTO dto);

}
