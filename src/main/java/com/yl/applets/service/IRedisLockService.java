package com.yl.applets.service;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: xiongweibin
 * @create: 2020-06-11 15:27
 */
public interface IRedisLockService {
    /**
     *
     * @param key key
     * @param expire key失效时间 单位 秒
     * @return
     */
    boolean setAndExpireIfAbsent(String key, final long expire);

    /**
     * 删除KEY
     * @param key
     * @return
     */
    boolean delete(String key);

    boolean existsKey(String key);
}
