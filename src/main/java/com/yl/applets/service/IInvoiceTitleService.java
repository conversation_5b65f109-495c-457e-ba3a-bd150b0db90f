package com.yl.applets.service;

import com.yl.applets.dto.InvoiceTitleDTO;
import com.yl.applets.entity.InvoiceTitle;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.applets.vo.WxUserVo;

/**
 * <p>
 * 开票抬头表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-21
 */
public interface IInvoiceTitleService extends IService<InvoiceTitle> {

    Boolean save(InvoiceTitleDTO invoiceTitleDTO, WxUserVo user);
}
