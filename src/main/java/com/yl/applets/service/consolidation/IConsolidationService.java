package com.yl.applets.service.consolidation;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.consolidation.ConsolidationCancelDTO;
import com.yl.applets.dto.consolidation.ConsolidationDTO;
import com.yl.applets.dto.consolidation.ConsolidationPageDTO;
import com.yl.applets.vo.WxUserVo;
import com.yl.applets.vo.consolidation.ConsolidationExpressCompanyVO;
import com.yl.applets.vo.consolidation.ConsolidationInfoVO;
import com.yl.applets.vo.consolidation.ConsolidationPageVO;
import com.yl.common.base.model.vo.Result;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/11/21 13:42
 */
public interface IConsolidationService {
    /**
     * 新增新建集运信息
     * @param dto
     */
    String consolidationProOrderSave(ConsolidationDTO dto);

    /**
     * 查询所有的快递公司列表
     */
    List<ConsolidationExpressCompanyVO> consolidationExpressCompanyList();


    /**
     * 判断是否存在待支付订单
     * @param memberId
     */
    Integer judgePendingPayOrder(Long memberId);


    /**
     * 判断是否已经登记过
     * @param preOrderNo
     */
     Boolean judgeRepeatOrder(String preOrderNo);


    /**
     * 更新新建集运信息
     * @param dto
     */
    Boolean consolidationProOrderUpdate(ConsolidationDTO dto);



    /**
     * 取消新建集运信息
     * @param dto
     */
    Boolean consolidationProOrderCancel(ConsolidationCancelDTO dto);

    /**
     * 删除新建集运信息
     * @param dto
     */
    Boolean consolidationProOrderDelete(ConsolidationCancelDTO dto);


    /**
     * 列表分页查询
     * @param dto
     */
    Result<Page<ConsolidationPageVO>> consolidationProOrderPages(ConsolidationPageDTO dto);


    /**
     * 通过主键id查询集运详情
     * @param id
     * @return
     */
    Result<ConsolidationInfoVO> consolidationProOrderInfoById(Long id, WxUserVo user);


    /**
     * 通过运单号查询集运详情
     * @param waybillNo
     * @return
     */
    Result<ConsolidationInfoVO> consolidationProOrderInfoByNo(String waybillNo,WxUserVo user);


    /**
     * 通过主键id查询集运详情
     * @param id
     * @return
     */
    Result<ConsolidationInfoVO> consolidationAbnormalInfo(Long id,String phone);
}
