package com.yl.applets.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.applets.dto.CustomerPrintDataDTO;
import com.yl.applets.entity.CustomerPrintData;
import com.yl.applets.vo.CloudPrintStatusVo;
import com.yl.common.base.model.vo.Result;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-23
 */
public interface ICustomerPrintDataService extends IService<CustomerPrintData> {

    /**
     * 绑定设备
     * @param customerPrintDataDTO
     * @return
     */
    CustomerPrintData bind(CustomerPrintDataDTO customerPrintDataDTO);

    /**
     * 打印
     * @param orderId
     * @return
     */
    Result print(Long orderId, Integer userId);

    /**
     * 查看打印状态
     * @param orderIds
     * @return
     */
    List<CloudPrintStatusVo> printStatus(List<Long> orderIds);
}
