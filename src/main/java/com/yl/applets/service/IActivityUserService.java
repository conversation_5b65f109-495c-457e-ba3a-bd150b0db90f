package com.yl.applets.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.applets.dto.ActivityConfigDTO;
import com.yl.applets.dto.ActivityConfigQueryDTO;
import com.yl.applets.dto.ActivityPrizeRecordDto;
import com.yl.applets.dto.ActivityVisitRecordDTO;
import com.yl.applets.entity.ActivityConfig;
import com.yl.applets.entity.ActivityUserRecord;
import com.yl.applets.vo.ActivityConfigVO;
import com.yl.applets.vo.ActivityVisitRecordStatisticsVO;
import com.yl.common.base.model.vo.Result;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动管理
 * @author: xiongweibin
 * @create: 2020-08-24 11:46
 */
public interface IActivityUserService extends IService<ActivityUserRecord> {


    ActivityPrizeRecordDto getPrizeInfo(Integer numberId,Integer type);

}
