package com.yl.applets.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.*;
import com.yl.applets.vo.PandaNearbyVO;
import com.yl.applets.vo.PandaOrderDetailVO;
import com.yl.applets.vo.QueryPandaOrderVO;
import com.yl.common.base.model.vo.Result;

import java.util.List;

public interface IPandaPostOrderService {

    //查单
    public Result<Page<PandaOrderDetailVO>> queryPage(ThirdExpressApiDTO dto, String requestId);

    //取消订单
    public Result<Boolean> cancelOrder(String orderId,Integer numberId,String requestId);

    //取消订单
    public Result<Boolean> deleteCancelOrder(String orderId,Integer numberId,String requestId);

    //改单
    public Result<Boolean> updateOrder(PandaOrderDTO dto, String requestId);

    //下单
    public Result<PandaOrderDetailVO> addOrder(PandaOrderDTO dto, String requestId);

    //获取附近5公里熊猫门店
    Result<PandaOrderDetailVO> detail(PandadetailDTO dto, String requestId);

    //查询附近驿站
    public Result<Page<Object>> queryNearStation(PandaNearStationDto dto);

    //查询运费
    public Result<Object> queryFreight(SpmApiTrialDTO spmApiTrialDTO);

    //获取附近5公里熊猫门店
    Result<List<PandaNearbyVO>> nearby(PandaNearbyDTO pandaNearbyDTO, String requestId);
}
