package com.yl.applets.service;

import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.redis.util.RedisUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
public class CaptchaService {

    @Value("${server.session.timeout:300}")
    private Integer timeout;

    public Map<String,Object> createToken(String captcha){
        //生成一个token
        String cToken = UUID.randomUUID().toString();
        //生成验证码对应的token  以token为key  验证码为value存在redis中
        RedisUtil.setEx(BcRedisKeyEnum.APPLETS_CAPTCHA.keyBuilder(cToken),captcha,timeout);
        Map<String, Object> map = new HashMap<>();
        map.put("ctoken", cToken);
        map.put("expire", timeout);
        return map;
    }

}
