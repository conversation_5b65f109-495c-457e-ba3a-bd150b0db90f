package com.yl.applets.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.AutoReplyDTO;
import com.yl.applets.dto.AutoReplyQueryDto;
import com.yl.applets.dto.WxMaterialDto;
import com.yl.applets.vo.AutoReplyVO;
import com.yl.applets.vo.WxFreePublishVo;
import com.yl.common.base.model.vo.Result;
import me.chanjar.weixin.mp.bean.material.WxMpMaterialFileBatchGetResult;
import me.chanjar.weixin.mp.bean.material.WxMpMaterialNewsBatchGetResult;
import me.chanjar.weixin.mp.bean.material.WxMpMaterialUploadResult;
import org.springframework.web.multipart.MultipartFile;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021<br>
 *
 * @description: 小程序自动回复规则
 * @author: caiyao
 * @create: 2021-05-20 10:11
 */
public interface IAutoReplyService {

    /**
     * 新增、更新(1:关注回复 2:收到消息回复)自动回复规则
     *
     * @param dto
     * @return
     */
    Result add(AutoReplyDTO dto);

    /**
     * 新增关键词自动回复规则
     *
     * @param dto
     * @return
     */
    Result addKeyWordReply(AutoReplyDTO dto);

    /**
     * 更新(关键词回复)自动回复规则
     *
     * @param dto
     * @return
     */
    Result updateKeyWordReply(AutoReplyDTO dto);

    /**
     * 删除单条关键词自动回复规则
     *
     * @param id
     * @return
     */
    Result delete(Long id);

    /**
     * 查看单条关键词自动回复规则
     *
     * @param id
     * @return
     */
    AutoReplyVO selectKeyWordReply(Long id);

    /**
     * 查看(1:关注回复 2:收到消息回复)自动回复
     *
     * @param replyType
     * @return
     */
    AutoReplyVO select(Integer replyType);

    /**
     * 查看关键词自动回复规则列表
     *
     * @param dto
     * @return
     */
    Page<AutoReplyVO> getPages(AutoReplyQueryDto dto);

    /**
     * 功能描述:
     * 微信文件上传
     * @param file
     * @param type
     * @return:com.yl.common.base.model.vo.Result<me.chanjar.weixin.common.bean.result.WxMediaUploadResult>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-05-20 15:16
     */
    Result<WxMpMaterialUploadResult> upload(MultipartFile file, String type);

    Result<WxMpMaterialFileBatchGetResult> wxMaterialPage(WxMaterialDto wxMaterialDto);

    Result<WxFreePublishVo> wxMaterialNewsPage(WxMaterialDto wxMaterialDto);
}
