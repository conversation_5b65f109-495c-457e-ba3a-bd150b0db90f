package com.yl.applets.service;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import com.yl.applets.dto.BindPhoneDTO;
import com.yl.applets.dto.MemberUserOffDto;
import com.yl.applets.dto.PhoneLoginDTO;
import com.yl.applets.dto.WxUserDTO;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.model.vo.Result;

import javax.servlet.http.HttpServletRequest;

/**
 * 登录
 *
 * <AUTHOR>
 * @since 2019-08-05
 */
public interface IAppLoginService {

    /**
     * 登出
     *
     * @return
     */
    Boolean logout();

    /**
     * 功能描述:
     * 绑定手机号
     * @param bindPhoneDTO
     * @param user
     * @return:com.yl.applets.vo.WxUserVo
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2020-11-09 14:24
     */
    WxUserVo bindPhone(BindPhoneD<PERSON> bindPhoneDTO, WxUserVo user, Boolean flag);

    WxUserVo wxLogin(WxUserDTO wxUserDTO, HttpServletRequest request);

    WxUserVo wxLoginV2(WxUserDTO wxUserDTO, HttpServletRequest request);

    WxUserVo update(WxUserDTO wxUserDTO,WxUserVo user);

    WxUserVo phoneLogin(PhoneLoginDTO phoneLoginDTO);

    /**
     * 注销
     *
     * @return
     */
    Boolean logoff(WxUserVo user);

    Result<MemberUserOffDto> isOff(String mobile);

    WxMaJscode2SessionResult getWxUserInfo(WxUserDTO wxUserDTO, String requestId);
}
