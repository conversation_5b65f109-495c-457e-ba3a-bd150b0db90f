package com.yl.applets.service;


import com.yl.applets.dto.NearbyOutletsDTO;
import com.yl.applets.vo.EsSearchResult;
import com.yl.applets.vo.lmdm.AppNetworkVO;
import com.yl.applets.vo.lmdm.SysNetworkSafeVO;
import com.yl.applets.vo.lmdm.SysNetworkVO;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019-08-17
 */
public interface IAppNetworkService {

    /**
     * 获取App网点资料列表
     *
     * @return java.util.List<com.yl.lmdmapi.vo.app.AppAbnormalPieceVO>
     * <AUTHOR>
     * @date 2019-08-17
     */
    List<AppNetworkVO> getAppList();

    /**
     *
     * @param networkId
     * @return
     */
    SysNetworkSafeVO getDetail(Integer networkId);

    EsSearchResult getNearbyOutlets(NearbyOutletsDTO nearbyOutletsDTO);
}
