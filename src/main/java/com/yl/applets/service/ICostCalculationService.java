package com.yl.applets.service;


import com.yl.applets.dto.SpmApiInsuranceTrialDTO;
import com.yl.applets.dto.SpmApiTrialDTO;
import com.yl.applets.vo.SpmCommonCostVO;

import java.math.BigDecimal;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description: 计费相关接口
 * @Project:
 * @CreateDate: Created in 2019-08-31 13:56 <br>
 * @Author: zhipeng.liu
 */
public interface ICostCalculationService {
    /**
     * 保价费计算
     * @param spmApiInsuranceTrialDTO
     * @return
     */
    BigDecimal computationCost(SpmApiInsuranceTrialDTO spmApiInsuranceTrialDTO);

    /**
     * 运费计算
     * @param spmApiTrialDTO
     * @return
     */
    SpmCommonCostVO comCost(SpmApiTrialDTO spmApiTrialDTO);
}
