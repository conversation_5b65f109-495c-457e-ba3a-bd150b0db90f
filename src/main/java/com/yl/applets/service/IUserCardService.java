package com.yl.applets.service;

import com.yl.applets.dto.UserCardDTO;
import com.yl.applets.entity.UserCard;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.applets.vo.UserCardVo;

/**
 * <p>
 * 用户实名认证表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-22
 */
public interface IUserCardService extends IService<UserCard> {

    /**
     * 添加实名信息
     * @param dto
     * @return
     */
    UserCardVo saveUserCard(UserCardDTO dto,String openId);
}
