package com.yl.applets.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.applets.dto.ReceiveBenefitDTO;
import com.yl.applets.dto.ReceiveBenefitQueryDTO;
import com.yl.applets.entity.ReceiveBenefit;
import com.yl.applets.vo.ReceiveBenefitVo;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： zhan<PERSON>hong
 * @Date： 2022/07/12
 */
public interface IReceiveBenefitService extends IService<ReceiveBenefit> {
    /**
     * 添加福利申领记录
     * @param receiveBenefitDTO
     * @param requestId
     * @return
     */
    Boolean addReceiveBenefit(ReceiveBenefitDTO receiveBenefitDTO, String requestId);


    /**
     * 圣诞报名活动
     * @param receiveBenefitDTO
     * @return
     */
    Boolean christmasRegistrationActivity(ReceiveBenefitDTO receiveBenefitDTO);
}
