package com.yl.applets.service;


import com.yl.applets.dto.AppAreaDTO;
import com.yl.applets.vo.AreaSearchVO;
import com.yl.applets.vo.lmdm.AppAreaVO;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019-08-17
 */
public interface IAppAreaService {

    /**
     * 获取App行政区域列表
     *
     * @return java.util.List<com.yl.lmdmapi.vo.app.AppAbnormalPieceVO>
     * <AUTHOR>
     * @date 2019-08-17
     */
    List<AppAreaVO> getAppList();

    /**
     *
     * @param parentId
     * @param type
     * @return
     */
    List<AppAreaVO> getAppList(Integer parentId, Integer type);

    /**
     * 模糊匹配
     * @param keyWord
     * @return
     */
    List<AreaSearchVO> getAppList(String keyWord);

    /**
     * 功能描述:
     * v2区域查询
     * @param appAreaDTO
     * @return:java.util.List<com.yl.applets.vo.lmdm.AppAreaVO>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-03-03 15:27
     */
    List<AppAreaVO> getAppListV2(AppAreaDTO appAreaDTO);

    /**
     * 功能描述:
     * v2区域层级查询
     * @param appAreaDTO
     * @return:java.util.List<com.yl.applets.vo.lmdm.AppAreaVO>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-03-03 15:27
     */
    List<AppAreaVO> listChildV2(AppAreaDTO appAreaDTO);

    /**
     * 功能描述:
     * v2区域模糊查询
     * @param appAreaDTO
     * @return:java.util.List<com.yl.applets.vo.AreaSearchVO>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-03-03 15:27
     */
    List<AreaSearchVO> listQueryV2(AppAreaDTO appAreaDTO);
}
