package com.yl.applets.service;

import com.yl.applets.dto.DispatchCodeRequestDTO;
import com.yl.applets.dto.OmsOrderApiDTO;
import com.yl.applets.dto.OrderDTO;
import com.yl.applets.vo.DispatchCodeResponseVO;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-08-09 15:56
 */
public interface INetworkAbnormalService {

   /*
     对象转换
    */
   DispatchCodeRequestDTO orderDTOToDispatchCodeRequestDTO(OrderDTO dto);
   DispatchCodeRequestDTO omsOrderApiDTOOToDispatchCodeRequestDTO(OmsOrderApiDTO dto);

   /**
    * 获取三段码
    * @return
    */
   DispatchCodeResponseVO getNetworkByFetch(DispatchCodeRequestDTO dto);
}
