package com.yl.applets.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yl.applets.entity.CommonlyUsedNetwork;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 常用网点表 服务类
 * </p>
 *
 * <AUTHOR> @since 2020-03-14
 */
public interface ICommonlyUsedNetworkService extends IService<CommonlyUsedNetwork> {

    IPage<CommonlyUsedNetwork> getPage(String longitude, String latitude,  List<Long> ids);
}
