package com.yl.applets.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.applets.entity.MemberScoreRecord;
import com.yl.applets.vo.MemberScoreRecordVo;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.model.vo.Result;

/**
 * <p>
 *  会员用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-7-7 17:28:33
 */
public interface IMemberScoreRecordService extends IService<MemberScoreRecord> {


    Result<IPage<MemberScoreRecordVo>> queryScoreRecord(WxUserVo user, Page page);
}
