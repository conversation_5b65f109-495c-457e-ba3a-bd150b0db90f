package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.config.ConsolidationExpressProperties;
import com.yl.applets.config.ConsolidationSendProperties;
import com.yl.applets.dto.consolidation.ConsolidationCancelDTO;
import com.yl.applets.dto.consolidation.ConsolidationDTO;
import com.yl.applets.dto.consolidation.ConsolidationPageDTO;
import com.yl.applets.dto.consolidation.ConsolidationUnPayDTO;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.ConsolidationFeignClient;
import com.yl.applets.feign.OrderFeigntClient;
import com.yl.applets.service.consolidation.IConsolidationService;
import com.yl.applets.vo.OmsOrderApiVO;
import com.yl.applets.vo.WxUserVo;
import com.yl.applets.vo.consolidation.ConsolidationExpressCompanyVO;
import com.yl.applets.vo.consolidation.ConsolidationInfoVO;
import com.yl.applets.vo.consolidation.ConsolidationPageVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.StringUtils;
import com.yl.common.base.util.YlPreconditions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static org.springframework.messaging.simp.SimpMessageHeaderAccessor.getUser;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/11/21 13:43
 */
@Slf4j
@Service
public class IConsolidationServiceImpl implements IConsolidationService {


    @Autowired
    private ConsolidationExpressProperties consolidationExpressProperties;


    @Autowired
    private ConsolidationFeignClient consolidationFeignClient;

    @Resource
    private ConsolidationSendProperties consolidationSendProperties;

    @Autowired
    private OrderFeigntClient orderFeigntClient;


    @Override
    public List<ConsolidationExpressCompanyVO> consolidationExpressCompanyList() {
        return consolidationExpressProperties.getList();
    }

    @Override
    public String consolidationProOrderSave(ConsolidationDTO dto) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        //1.判断新疆地区
        log.info("新增新疆集运==> requestId:[{}],请求参数：[{}]", requestId, JSON.toJSONString(dto));
        Integer receiverProvinceId = dto.getReceiverProvinceId();
        YlPreconditions.checkArgument(Objects.equals(consolidationSendProperties.getProvinceId(), receiverProvinceId), new ServiceException(ServiceErrCodeEnum.NO_XINJIANG_AREA_ERROR));

        //2.判断是否存在待支付订单
        Long memberId = dto.getMemberId();
        int unPayNum = judgePendingPayOrder(memberId);
        ServiceErrCodeEnum unPayOrderError = ServiceErrCodeEnum.UN_PAY_ORDER_ERROR;
        String msg = unPayOrderError.getMsg();
        if (0 != unPayNum) {
            msg = StringUtils.format(msg, unPayNum);
            unPayOrderError.setMsg(msg);
            throw new ServiceException(unPayOrderError);
        }
        //3.判断是否重复下单
        String preOrderNo = dto.getPreOrderNo();
        Boolean repeat = judgeRepeatOrder(preOrderNo);
        if (repeat) {
            throw new ServiceException(ServiceErrCodeEnum.REPEAT_ORDER_ERROR);
        }
        Result<String> result = consolidationFeignClient.consolidationProOrderSave(dto);
        log.info("新增新疆集运==>feign_requestId:[{}],result：[{}]", requestId, JSON.toJSONString(result));
        return result.result();
    }

    /**
     * 判断是否存在待支付订单
     */
    @Override
    public Integer judgePendingPayOrder(Long memberId) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("新增新疆集运:判断是否存在待支付订单==> requestId:[{}],请求参数：[{}]", requestId, memberId);
        ConsolidationUnPayDTO query = new ConsolidationUnPayDTO();
        query.setMemberId(memberId);
        Result<Integer> result = consolidationFeignClient.getCountUnpaidOrders(query);
        log.info("新增新疆集运:判断是否存在待支付订单==> requestId:[{}],result：[{}]", requestId, JSON.toJSONString(result));
        Integer data = result.result();
        //根据返回的数量判定是否存在待支付的订单
        int unPayNum = data == null ? 0 : data;
        return unPayNum;
    }


    /**
     * 判断是否已经登记过
     */
    @Override
    public Boolean judgeRepeatOrder(String preOrderNo) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("新增新疆集运:判断是否已经登记过==> requestId:[{}],请求参数：[{}]", requestId, preOrderNo);
        Result<Boolean> result = consolidationFeignClient.isExistPreOrderNo(preOrderNo);
        log.info("新增新疆集运:判断是否已经登记过==> requestId:[{}],result：[{}]", requestId, JSON.toJSONString(result));
        //根据返回的数量判定是否重复订单 //true -已登记，false-未登记
        return result.result();
    }


    @Override
    public Boolean consolidationProOrderUpdate(ConsolidationDTO dto) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("新疆集运:修改==> requestId:[{}],请求参数：[{}]", requestId, JSON.toJSONString(dto));

        //1.判断当前订单是否当前用户
        Result<ConsolidationInfoVO> updateInfo = consolidationFeignClient.consolidationProOrderInfoById(dto.getId());
        log.info("新疆集运:修改:获取订单信息==> requestId:[{}],请求参数：[{}]", requestId, JSON.toJSONString(updateInfo));
        ConsolidationInfoVO dataInfo = updateInfo.result();
        if (null == dataInfo || !Objects.equals(dataInfo.getMemberId(), dto.getMemberId())) {
            throw new ServiceException(ResultCodeEnum.DATA_NOT_FOUND);
        }
        if (!org.apache.commons.lang3.StringUtils.equals(dto.getPreOrderNo(), dataInfo.getPreOrderNo())) {
            throw new ServiceException(ServiceErrCodeEnum.QUERY_ORDER_EQUALS_PRE_ERROR);
        }
        //2.判断新疆地区
        Integer receiverProvinceId = dto.getReceiverProvinceId();
        YlPreconditions.checkArgument(Objects.equals(consolidationSendProperties.getProvinceId(), receiverProvinceId), new ServiceException(ServiceErrCodeEnum.NO_XINJIANG_AREA_ERROR));

//        //3.判断是否存在待支付订单
//        Long memberId = dto.getMemberId();
//        int unPayNum=judgePendingPayOrder(memberId);
//
//        if (0 != unPayNum) {
//            ServiceErrCodeEnum unPayOrderError = ServiceErrCodeEnum.UN_PAY_ORDER_ERROR;
//            String msg = unPayOrderError.getMsg();
//            msg = StringUtils.format(msg, unPayNum);
//            unPayOrderError.setMsg(msg);
//            YlPreconditions.checkArgument(false, new ServiceException(unPayOrderError));
//        }

        Result<Boolean> result = consolidationFeignClient.consolidationProOrderUpdate(dto);
        log.info("新增新疆集运:修改==> requestId:[{}],result：[{}]", requestId, JSON.toJSONString(result));
        return result.result();
    }


    @Override
    public Boolean consolidationProOrderCancel(ConsolidationCancelDTO dto) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("新疆集运:取消==> requestId:[{}],请求参数：[{}]", requestId, JSON.toJSONString(dto));
        //1.判断当前订单是否当前用户
        Result<ConsolidationInfoVO> info = consolidationFeignClient.consolidationProOrderInfoById(Long.parseLong(dto.getId()));
        log.info("集运:取消:获取订单信息==> requestId:[{}],请求参数：[{}]", requestId, JSON.toJSONString(info));
        ConsolidationInfoVO dataInfo = info.result();
        if (null == dataInfo || !Objects.equals(dataInfo.getMemberId(), dto.getMemberId())) {
            throw new ServiceException(ResultCodeEnum.DATA_NOT_FOUND);
        }
        Result<Boolean> result = consolidationFeignClient.consolidationProOrderCancel(dto);
        log.info("新疆集运:取消==> requestId:[{}],result：[{}]", requestId, JSON.toJSONString(result));
        return result.result();
    }

    @Override
    public Boolean consolidationProOrderDelete(ConsolidationCancelDTO dto) {

        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("新疆集运:删除==> requestId:[{}],请求参数：[{}]", requestId, JSON.toJSONString(dto));
        //1.判断当前订单是否当前用户
        Result<ConsolidationInfoVO> info = consolidationFeignClient.consolidationProOrderInfoById(Long.parseLong(dto.getId()));
        log.info("集运:删除:获取订单信息==> requestId:[{}],请求参数：[{}]", requestId, JSON.toJSONString(info));
        ConsolidationInfoVO dataInfo = info.result();
        if (null == dataInfo || !Objects.equals(dataInfo.getMemberId(), dto.getMemberId())) {
            throw new ServiceException(ResultCodeEnum.DATA_NOT_FOUND);
        }
        Result<Boolean> result = consolidationFeignClient.consolidationProOrderDelete(dto);
        log.info("新疆集运:删除==> requestId:[{}],result：[{}]", requestId, JSON.toJSONString(result));
        return result.result();
    }

    @Override
    public Result<Page<ConsolidationPageVO>> consolidationProOrderPages(ConsolidationPageDTO dto) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("新增新疆集运:分页查询==> requestId:[{}],请求参数：[{}]", requestId, JSON.toJSONString(dto));
        Result<Page<ConsolidationPageVO>> result = consolidationFeignClient.consolidationProOrderPages(dto);
        log.info("新增新疆集运:分页查询==> requestId:[{}],result：[{}]", requestId, JSON.toJSONString(result));
        result.result();
        return result;
    }

    @Override
    public Result<ConsolidationInfoVO> consolidationProOrderInfoById(Long id, WxUserVo user) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("通过主键id查询集运详情:id查询==> requestId:[{}],请求参数：[{}]", requestId, id);
        Result<ConsolidationInfoVO> result = consolidationFeignClient.consolidationProOrderInfoById(id);
        log.info("通过主键id查询集运详情:id查询==> requestId:[{}],result：[{}]", requestId, JSON.toJSONString(result));
        ConsolidationInfoVO data = result.result();
        if (null != data) {
            //获取运单号
            String waybillNo = data.getWaybillId();
            Long memberId = data.getMemberId();
            if(!Objects.equals(memberId, Long.parseLong(user.getNumberId()+""))){
                throw new ServiceException(ResultCodeEnum.DATA_NOT_FOUND);
            }
            OmsOrderApiVO detail = getOrderDetailByWaybillNo(waybillNo);
            if (null != detail) {
                data.setOrderId(detail.getId() + "");
            }
        }
        return result;
    }

    @Override
    public Result<ConsolidationInfoVO> consolidationProOrderInfoByNo(String waybillNo,WxUserVo user) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("通过运单号查询集运详情:运单号查询==> requestId:[{}],请求参数：[{}]", requestId, waybillNo);
        Result<ConsolidationInfoVO> result = consolidationFeignClient.consolidationProOrderInfoByNo(waybillNo);
        log.info("通过运单号查询集运详情:运单号查询==> requestId:[{}],result：[{}]", requestId, JSON.toJSONString(result));
        ConsolidationInfoVO data = result.result();
        if(!Objects.isNull(data) && !Objects.equals(data.getMemberId(), Long.parseLong(user.getNumberId()+""))){
            throw new ServiceException(ResultCodeEnum.DATA_NOT_FOUND);
        }
        OmsOrderApiVO detail = getOrderDetailByWaybillNo(waybillNo);
        if (null != data && null != detail) {
            data.setOrderId(detail.getId() + "");
        }
        return result;
    }


    @Override
    public Result<ConsolidationInfoVO> consolidationAbnormalInfo(Long id, String phone) {
        log.info("通过主键id查询集运详情:id查询==>,请求参数：[{}]", id);
        Result<ConsolidationInfoVO> result = consolidationFeignClient.consolidationProOrderInfoById(id);
        log.info("通过主键id查询集运详情:id查询==>result：[{}]", JSON.toJSONString(result));
        ConsolidationInfoVO data = result.result();
        if (null != data) {
            //1.查询手机号信息是否跟订单手机号匹配
            String receiverMobilePhone = data.getReceiverMobilePhone();
            receiverMobilePhone = receiverMobilePhone.substring(7, 11);
            YlPreconditions.checkArgument(Objects.equals(phone, receiverMobilePhone), new ServiceException(ServiceErrCodeEnum.QUERY_ORDER_ABNORMAL_ERROR));
            //获取运单号
            String waybillNo = data.getWaybillId();
            OmsOrderApiVO detail = getOrderDetailByWaybillNo(waybillNo);
            if (null != detail) {
                data.setOrderId(detail.getId() + "");
            }
        } else {
            throw new BusinessException(ServiceErrCodeEnum.YD_NO_EXEITS);
        }
        return result;
    }

    private OmsOrderApiVO getOrderDetailByWaybillNo(String waybillNo) {
        if (StringUtils.isEmpty(waybillNo)) {
            log.info("通过运单号查询集运详情 getOrderDetailByWaybillNo :==>查询集运订单信息运单号为空");
            return null;
        }
        OmsOrderApiVO detail = orderFeigntClient.getOrderInfoByWaybillId(waybillNo).result();
        log.info("通过运单号查询集运详情:==>查询集运订单信息 ,waybillNo:[{}] ;result[{}]", waybillNo, JSON.toJSONString(detail));
        return detail;
    }


}
