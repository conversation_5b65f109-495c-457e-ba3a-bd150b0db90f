package com.yl.applets.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.yl.applets.constant.BaseConstant;
import com.yl.applets.dto.*;
import com.yl.applets.dto.lmdm.SysStaffDTO;
import com.yl.applets.entity.*;
import com.yl.applets.enums.*;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.*;
import com.yl.applets.feign.dto.CouponStaffBuryingRecordDTO;
import com.yl.applets.feign.dto.citicmonthlysettlement.MonthlySettlementCustomerDTO;
import com.yl.applets.mapper.applets.MemberUserMapper;
import com.yl.applets.mapper.applets.UserStaffMapper;
import com.yl.applets.service.*;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.utils.OrderMarkingHelper;
import com.yl.applets.utils.YlStringUtils;
import com.yl.applets.vo.*;
import com.yl.applets.vo.lmdm.AppAreaVO;
import com.yl.applets.vo.lmdm.SysNetworkVO;
import com.yl.applets.vo.lmdm.SysStaffVO;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.EnableEnum;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.CollectionUtils;
import com.yl.common.base.util.GenerationIdUtil;
import com.yl.common.base.util.StringUtils;
import com.yl.common.base.valid.enums.FromEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import com.yl.applets.enums.monthlysettlement.MonthlySettlementEnum.MonthlySettlementAutoSchedulEnum;
import com.yl.applets.enums.monthlysettlement.MonthlySettlementEnum.MonthlySettlementCusLabelEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-08-31 12:01 <br>
 * @Author: zhipeng.liu
 */
@Service
@Slf4j
public class OrderServiceImpl extends BaseServiceImpl implements IOrderService {

    @Autowired
    private OmsOrderClient omsOrderGateWayFeign;

    @Autowired
    private OmsMiniGateWayFeign omsMiniGateWayFeign;

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;


    @Autowired
    private OldLmdmFeignClient sysNetworkFeignClient;

    @Autowired
    private IMyStaffService myStaffService;

    @Autowired
    private IUserCardService userCardService;

    @Autowired
    private IAppAreaService appAreaService;

    @Autowired
    private ICommonlyUsedNetworkService commonlyUsedNetworkService;

    @Autowired
    private OldLmdmFeignClient sysCustomerExtFeignClient;

    @Autowired
    private OrderFeigntClient orderFeigntClient;


    @Autowired
    private PromotionFeighClient promotionFeighClient;

    @Autowired
    private MemberUserMapper memberUserMapper;

    @Autowired
    private ICustomerPrintDataService customerPrintDataService;

    @Autowired
    private UserStaffMapper userStaffMapper;

    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;


    @Value("${isInterceptOrder:1}")
    private String isInterceptOrder;

    @Value("#{'${hide.orderSource:}'.split(',')}")
    private List<String> hideOrderSourceList;


    @Value("#{'${monthly.orderSource:D61,D13,D908,D1133,D14}'.split(',')}")
    private List<String> monthlySettlementCode;


    @Autowired
    private OrderMarkingHelper orderMarkingHelper;

    @Value("${waybill.realPick.phone:18855571447,18868082246}")
    private String waybillRealpickPhone;

    @Autowired
    private  CiticMonthlySettlementClient citicMonthlySettlementClient;

    @Autowired
    private CustomerPlatformNetworkFeignClient customerPlatformNetworkFeignClient;


    @Autowired
    private  CouponStaffBuryingFeignClient couponStaffBuryingFeignClient;

    @Autowired
    private Map<String,OrderChannelCheck> orderChannelCheckMap;

    @Override
    public OmsOrderApiVO detail(Long orderId) {
        OmsOrderDetailApiQueryDto omsOrderDetailApiQueryDto = new OmsOrderDetailApiQueryDto();
        omsOrderDetailApiQueryDto.setId(orderId);
        omsOrderDetailApiQueryDto.setFrom(FromEnum.MINIAPP.getCode());
        log.info("查看订单详情,入参===>{}", JSON.toJSONString(omsOrderDetailApiQueryDto));
        OmsOrderApiVO result = orderFeigntClient.detailApp(orderId).result();
        log.info("查看订单详情,出参===>{}", JSON.toJSONString(result));
        if (result.getOmsOrderThirdExtVO() != null) {
            String params = result.getOmsOrderThirdExtVO().getParams();
            if (StringUtils.isEmpty(params)) {
                log.info("查看订单详情,出参【优惠券信息】===>{}", params);
            }
        }

        if (StrUtil.equals(waybillRealpickPhone, "all") || (result.getSenderMobilePhone() != null && waybillRealpickPhone.contains(result.getSenderMobilePhone()))) {
            try {
                if (StrUtil.isNotBlank(result.getRealPickNetworkCode())) {
                    Result<List<CpAreaByNetworkCodeVO>> listResult = customerPlatformNetworkFeignClient.getAreaByNetworkCode(Collections.singletonList(result.getRealPickNetworkCode()));
                    List<CpAreaByNetworkCodeVO> cpAreaByNetworkCodeVOList = listResult.result();
                    log.info("订单实际取件网点不为空,入参:{} 查询结果==>{}", listResult, JSON.toJSONString(cpAreaByNetworkCodeVOList));
                    if (CollectionUtil.isNotEmpty(cpAreaByNetworkCodeVOList)) {
                        result.setRealPickCityName(cpAreaByNetworkCodeVOList.get(0).getCityDesc());
                    }
                }
            } catch (Exception e) {
                log.error("中台转换订单实际取件网点异常:{}", e);
            }
        }

        if (Objects.nonNull(result) && hideOrderSourceList.contains(result.getOrderSourceCode())) {
            //隐藏区域信息
            result.setSenderAreaName("***");
            result.setReceiverAreaName("***");
            //隐藏详细地址
            result.setSenderDetailedAddress("***");
            result.setReceiverDetailedAddress("***");
        }


        return result;
    }

    @Override
    public OmsOrderApiVO save(OrderDTO dto, WxUserVo wxUserVo) {
        StopWatch stopWatch = new StopWatch();
        String requestId = dto.getMyRequestId();
        log.info("requestId==>{},下单入参==>{}", requestId, JSON.toJSONString(dto));
        //设置保价标识
        if (null != dto.getDeclaredValue() && dto.getDeclaredValue().compareTo(BigDecimal.ZERO) > 0) {
            dto.setInsured(1);
        } else {
            dto.setInsured(0);
        }
        if (org.apache.commons.lang.StringUtils.equals("0", isInterceptOrder)) {
            //校验收/寄件城市
            validataCity(dto.getReceiverAreaId(), AddressType.RES_ADDR_TYPE);
            validataCity(dto.getSenderAreaId(), AddressType.SEND_ADDR_TYPE);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        //设置默认费用为0
        dto.setSettlementWeight(dto.getSettlementWeight() == null ? new BigDecimal(0) : dto.getSettlementWeight());
        dto.setStandardValue(dto.getStandardValue() == null ? new BigDecimal(0) : dto.getStandardValue());
        dto.setInsuredValue(dto.getInsuredValue() == null ? new BigDecimal(0) : dto.getInsuredValue());
        //文件类型不能报价
        checkInsuredPrice(dto.getGoodsTypeCode(), dto.getInsuredValue());
        //减去优惠券金额
        if (dto.getCouponValue() != null && dto.getCouponValue().compareTo(new BigDecimal("0")) != 0) {
            if (dto.getStandardValue().compareTo(dto.getCouponValue()) != 1) {
                log.warn("requestId==>{}标准运费==>{},优惠金额==>{}", requestId, dto.getStandardValue(), dto.getCouponValue());
                throw new BusinessException(ServiceErrCodeEnum.COUPON_ORDER_FRIEGHT_ERROR);
            }
        }
        //特殊类型 总运费=折扣后的标准运费+保价费 （网络退货）
        //总运费 = 标准运费+保价费
        if( OrderChannelTypeEnum.getOrderChannelTypeList().contains(dto.getOrderChannelType())){
            BigDecimal discountFreight = dto.getDiscountFreight();
            discountFreight=Objects.isNull(discountFreight)?new BigDecimal(0):discountFreight;
            dto.setTotalFreight(dto.getStandardValue().subtract(discountFreight).add(dto.getInsuredValue()));
        }else{
            dto.setTotalFreight(dto.getStandardValue().add(dto.getInsuredValue()));
        }
        OmsOrderApiDTO omsOrderApiDTO = orikaBeanMapper.map(dto, OmsOrderApiDTO.class);
        //处理优惠券相关字段
        boolean isUserCoupon = false;
        if (!StringUtils.isEmpty(dto.getCouponId()) && !StringUtils.isEmpty(dto.getCouponCode())
                && dto.getCouponValue() != null && !StringUtils.isEmpty(dto.getUseProId())
        ) {
            isUserCoupon = true;
            OrderCouponDto couponDto = new OrderCouponDto(dto.getCouponCode(), dto.getCouponId(), dto.getCouponValue(), dto.getUseProId());
            String jsonString = JSON.toJSONString(couponDto);
            log.info("requestId==>{}优惠券信息==>{}", requestId, jsonString);
            omsOrderApiDTO.setParams(jsonString);
        }

        log.info("下单获取用户实名信息");
//        UserCard userCard = userCardService.getOne(new LambdaQueryWrapper<UserCard>().eq(UserCard::getUserId,wxUserVo.getId()));
        List<Long> ids = wxUserService.getIdsByIdAndNumberId(wxUserVo.getNumberId());
        log.info("根据numberId查询用户ids:{},请求id==>{}", ids, requestId);
        List<UserCard> userCards = userCardService.list(new LambdaQueryWrapper<UserCard>().in(UserCard::getUserId, ids));
        UserCard userCard = null;
        if (userCards != null && userCards.size() > 0) {
            userCard = userCards.get(0);
        } else {
            log.warn("请求id==>{}当前用户未实名==>{}", requestId, JSON.toJSONString(wxUserVo));
            throw new BusinessException(ServiceErrCodeEnum.ID_CARD_NULL);
        }
        //设置订单来源
        this.setOrderSourceCode(omsOrderApiDTO, userCard, dto);
        //如果是网点扫码的，把网点信息都传进去
        SysNetworkVO sysNetworkVO = null;
        if (dto.getPickNetworkId() != null) {
            log.info("requestId==>{},获取基础数据网点信息入参:{}", requestId, dto.getPickNetworkId());
            stopWatch.start("获取到网点扫码信息");
//            sysNetworkVO = sysNetworkFeignClient.getDetailById(dto.getPickNetworkId().intValue()).result();
            ForwardRequest requests = new ForwardRequest();
            requests.setRequestId(requestId);
            requests.setRequestUri("/lmdmapi/network/detail");
            requests.setBody(dto.getPickNetworkId() + "");
            //调接口
            log.info("请求id==>{}接口入参==>{}", requestId, JSON.toJSONString(requests));
            Result<?> forwards = channelApiFeignClient.forward(requests);
            log.info("请求id==>{}接口返回==>{}", requestId, JSON.toJSONString(forwards));
            Object results = forwards.result();
            sysNetworkVO = JSON.parseObject(JSON.toJSONString(results), SysNetworkVO.class);
            stopWatch.stop();
            log.info("requestId==>{},获取到网点扫码信息耗时：{},结果：【{}】", requestId, stopWatch.getLastTaskTimeMillis(), JSON.toJSONString(sysNetworkVO));
            if (sysNetworkVO != null && sysNetworkVO.getIsEnable().equals(EnableEnum.NORMAL.getCode())) {
                omsOrderApiDTO.setHasNetWorkInfo(Boolean.TRUE);
                omsOrderApiDTO.setPickNetworkCode(sysNetworkVO.getCode());
                omsOrderApiDTO.setPickNetworkName(sysNetworkVO.getName());
                validataSendAddrNetWork(dto, sysNetworkVO);
            } else {
                dto.setPickNetworkId(null);
            }
        }
        //网点寄件已网点二维码和业务员二维码不处理，其它寄件走绑定业务逻辑
        if (dto.getPickNetworkId() == null && StringUtils.isEmpty(dto.getPickStaffCode())) {
            stopWatch.start("获取用户绑定业务员");
            UserStaff userStaff = userStaffMapper.selectOne(new LambdaQueryWrapper<UserStaff>().eq(UserStaff::getUserId, wxUserVo.getId()).eq(UserStaff::getFullAddress, dto.getSenderAddressFull()));
            stopWatch.stop();
            log.info("requestId==>{},获取用户绑定业务员耗时：{},结果：{}", requestId, stopWatch.getLastTaskTimeMillis(), JsonUtils.toJson(userStaff));
            if (userStaff != null) {
                dto.setPickStaffCode(userStaff.getStaffCode());
            }
        }

        MyStaff myStaff = null;

        //如果是扫码业务员下单的，把业务员信息都传进去
        if (StringUtils.isNotEmpty(dto.getPickStaffCode()) && dto.getPickNetworkId() == null) {
            SysStaffDTO sysStaffDTO = new SysStaffDTO();
            sysStaffDTO.setCode(dto.getPickStaffCode());
            log.info("requestId==>{},获取基础数据业务员信息入参：{}", requestId, JsonUtils.toJson(sysStaffDTO));
            stopWatch.start("获取基础数据业务员信息");
//            SysStaffVO sysStaffVO = sysNetworkFeignClient.getStaffDetail(sysStaffDTO).result();
            ForwardRequest request = new ForwardRequest();
            request.setRequestId(requestId);
            request.setRequestUri("/lmdmapi/sysStaff/detail");
            request.setBody(JSON.toJSONString(sysStaffDTO));
            log.info("请求id==>{}接口入参==>{}", requestId, JSON.toJSONString(request));
            Result<?> forward = channelApiFeignClient.forward(request);
            log.info("请求id==>{}接口返回==>{}", requestId, JSON.toJSONString(forward));
            Object resultO = forward.result();
            SysStaffVO sysStaffVO = JSON.parseObject(JSON.toJSONString(resultO), SysStaffVO.class);
            stopWatch.stop();
            log.info("requestId==>{},获取基础数据业务员信息耗时：{},结果：{}", requestId, stopWatch.getLastTaskTimeMillis(), JsonUtils.toJson(sysStaffVO));
            if (sysStaffVO != null && sysStaffVO.getIsEnable().equals(EnableEnum.NORMAL.getCode())) {
                omsOrderApiDTO.setPickStaffCode(dto.getPickStaffCode());
                omsOrderApiDTO.setPickStaffName(sysStaffVO.getName());
                omsOrderApiDTO.setHasPickStaffInfo(Boolean.TRUE);
                log.info("requestId==>{},业务员二维码获取基础数据网点信息入参：{}", requestId, sysStaffVO.getNetworkId());
                stopWatch.start("业务员二维码获取基础数据网点信息");
//                SysNetworkVO sysNetworkVO1 = sysNetworkFeignClient.getDetailById(sysStaffVO.getNetworkId()).result();
                ForwardRequest requests = new ForwardRequest();
                requests.setRequestId(requestId);
                requests.setRequestUri("/lmdmapi/network/detail");
                requests.setBody(sysStaffVO.getNetworkId() + "");
                //调接口
                log.info("请求id==>{}接口入参==>{}", requestId, JSON.toJSONString(requests));
                Result<?> forwards = channelApiFeignClient.forward(requests);
                log.info("请求id==>{}接口返回==>{}", requestId, JSON.toJSONString(forwards));
                Object results = forwards.result();
                SysNetworkVO sysNetworkVO1 = JSON.parseObject(JSON.toJSONString(results), SysNetworkVO.class);
                stopWatch.stop();
                log.info("requestId==>{},业务员二维码获取基础数据网点信息耗时：{},结果：{}", requestId, stopWatch.getLastTaskTimeMillis(), JsonUtils.toJson(sysNetworkVO1));
                validataSendAddr(dto, sysNetworkVO1);
                omsOrderApiDTO.setPickNetworkId(sysStaffVO.getNetworkId().longValue());
                omsOrderApiDTO.setPickNetworkCode(sysNetworkVO1.getCode());
                omsOrderApiDTO.setPickNetworkName(sysNetworkVO1.getName());
                myStaff = new MyStaff();
                myStaff.setId(GenerationIdUtil.getId());
                myStaff.setMobile(sysStaffVO.getMobile());
                myStaff.setCreateTime(LocalDateTime.now());
                myStaff.setName(sysStaffVO.getName());
                myStaff.setNetworkCode(sysNetworkVO1.getCode());
                myStaff.setNetworkId(sysNetworkVO1.getId());
                myStaff.setNetworkName(sysNetworkVO1.getName());
                myStaff.setStaffNo(sysStaffVO.getCode());
                myStaff.setUserId(wxUserVo.getId().longValue());
                myStaff.setUpdateTime(LocalDateTime.now());
                myStaff.setIsExclusive(dto.getIsExclusive());
                myStaff.setUserChannel(StaffChannelTypeEnum.WX.getCode());
                if (sysNetworkVO1.getStartBusinessTime() != null) {
                    myStaff.setStartBusinessTime(formatter.format(sysNetworkVO1.getStartBusinessTime()));
                }
                if (sysNetworkVO1.getEndBusinessTime() != null) {
                    myStaff.setEndBusinessTime(formatter.format(sysNetworkVO1.getEndBusinessTime()));
                }
            } else {
                dto.setPickStaffCode(null);
                //业务员未启用,不能下专属寄。
                omsOrderApiDTO.setOrderSourceCode(OrderSourceEnum.WX_MINIPROGRAM.getCode());
                omsOrderApiDTO.setOrderSourceName(OrderSourceEnum.WX_MINIPROGRAM.getName());
                omsOrderApiDTO.setBestPickTimeStart(dto.getBestPickTimeStart());
                omsOrderApiDTO.setBestPickTimeEnd(dto.getBestPickTimeEnd());
            }
        }

        //判断是否实名寄件（如果有实名登记信息并且寄件人手机号和实名登记的手机号一致就认为是实名寄件)
        log.info("requestId==>{},下单获取用户实名信息耗时：{}，结果：{}", requestId, stopWatch.getLastTaskTimeMillis(), JsonUtils.toJson(userCard));
        if (userCard != null) {
            omsOrderApiDTO.setIsRealName(1);
            omsOrderApiDTO.setRealName(userCard.getName());
            omsOrderApiDTO.setIdNo(userCard.getCardNum());
            omsOrderApiDTO.setIdNoType(userCard.getCardType());
        }
       // omsOrderApiDTO.setCustomerId(wxUserVo.getNumberId());//下单时，多个渠道的账号，同一个手机号使用同一个numberId，不再使用主键Id
        omsOrderApiDTO.setMemberId(wxUserVo.getNumberId());//下单时，多个渠道的账号，同一个手机号使用同一个numberId，不再使用主键Id
        omsOrderApiDTO.setCustomerName(wxUserVo.getNickName());
        omsOrderApiDTO.setFrom(FromEnum.MINIAPP.getCode());
        omsOrderApiDTO.setNeedDispatch(1);//需要调度
        omsOrderApiDTO.setAutoScheduling("1");
        //如果是签回单 赋值回单金额
//        if(omsOrderApiDTO.getSignReceipt() != null && omsOrderApiDTO.getSignReceipt() == 1){
//            omsOrderApiDTO.setReceiptFreight(AppCacheConstant.RECEIPT_FREIGHT);
//            omsOrderApiDTO.setTotalFreight(omsOrderApiDTO.getTotalFreight().add(AppCacheConstant.RECEIPT_FREIGHT));
//        }
        //如果是网点二维码，置空业务员编码
        if (omsOrderApiDTO.getOrderSourceCode().equals(OrderSourceEnum.WX_QR_NETWORK.getCode())) {
            log.error("requestId==>{},下订单,前端入参有误，手动修复", requestId);
            omsOrderApiDTO.setPickStaffCode(null);
        }
        //使用了优惠券
        if (isUserCoupon) {
            PromotionUserUseDto promotionUserUseDto = new PromotionUserUseDto();
            promotionUserUseDto.setId(Long.parseLong(dto.getUseProId()));
            promotionUserUseDto.setProId(Long.parseLong(dto.getCouponId()));
            promotionUserUseDto.setNumberId(wxUserVo.getNumberId().longValue());
            promotionUserUseDto.setUserId(wxUserVo.getNumberId().longValue());
            promotionUserUseDto.setSendNetworkId(dto.getPickNetworkId() == null ? dto.getFetchNetworkId() : dto.getPickNetworkId());//寄件网点为空，取三段码网点
            promotionUserUseDto.setRecCityId(dto.getSenderCityId().longValue());
            if (promotionUserUseDto.getSendNetworkId() == null) {
                log.info("requestId==>{},下单使用优惠券,但寄件地址没有解析到寄件网点", requestId);
                throw new BusinessException(ServiceErrCodeEnum.COUPON_NETWORK_ADDRESS_ERROR);
            }
            log.info("requestId==>{},下单,锁定优惠券,远程入参：【{}】", requestId, JSON.toJSONString(promotionUserUseDto));
            Result<Integer> integerResult = promotionFeighClient.useLockPromotion(promotionUserUseDto);
            log.info("requestId==>{},下单,锁定优惠券,接口返回：【{}】", requestId, JSON.toJSONString(integerResult));
            if (integerResult.getCode() != 1) {//锁定失败
                throw new BusinessException(integerResult.getCode(), integerResult.getMsg());
            }
        }
        //查询会员等级
        QueryWrapper<MemberUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("member_id", getUser().getNumberId());
        MemberUser memberUser = memberUserMapper.selectOne(queryWrapper);
        log.info("requestId==>{},下单,当前用户的等级==>{}", requestId, JSON.toJSONString(memberUser));
        if (memberUser != null) {
            omsOrderApiDTO.setMemberLevel(memberUser.getMemberLevel());
        }
        //处理下收寄件人名称长度
        omsOrderApiDTO.setSenderName(YlStringUtils.subText(omsOrderApiDTO.getSenderName()));
        omsOrderApiDTO.setReceiverName(YlStringUtils.subText(omsOrderApiDTO.getReceiverName()));
        //处理订单打标
        OrderRecvPrefDTO orderRecvPrefDTO = new OrderRecvPrefDTO();
        List<PreferencesCommonDto.OrderMarkingStatus> markingStatusList = dto.getMarkingStatusList();
        if (CollectionUtils.isNotEmpty(markingStatusList)) {
            //校验打标code是否合法
            orderMarkingHelper.checkMarking(dto);
            BeanUtils.copyProperties(dto, orderRecvPrefDTO);
            orderRecvPrefDTO.setDataSource("XD_WXXCX");
            log.info("下单标识转换完成==>{}", JSON.toJSONString(orderRecvPrefDTO));
            omsOrderApiDTO.setOrderRecvPrefDTO(orderRecvPrefDTO);
        }
        //微信下单,打标
        List<OrderMarkExpandVO> resourceList = Lists.newArrayList();
        OrderMarkExpandVO expandVO = new OrderMarkExpandVO();
        //构建参数
        expandVO.setFieldName(BaseConstant.CCM_TERMINAL_ORDER_FROM);
        expandVO.setFieldValue("1");//微信
        expandVO.setEffective(true);

        resourceList.add(expandVO);
        //判断特殊类型的渠道下单
        if( OrderChannelTypeEnum.getOrderChannelTypeList().contains(dto.getOrderChannelType())){
            OrderChannelTypeEnum mappingValue = OrderChannelTypeEnum.getMappingValue(getUser().getNumberId(),dto.getOrderChannelType(),orderChannelCheckMap);
            OrderMarkExpandVO expandReturnVO = new OrderMarkExpandVO();
            expandReturnVO.setFieldName(mappingValue.getValue());//微信
            expandReturnVO.setFieldValue(mappingValue.getFieldValue());
            expandReturnVO.setEffective(true);
            resourceList.add(expandReturnVO);
        }


        // 打标数据
        if (CollectionUtil.isNotEmpty(omsOrderApiDTO.getMarkFields())) {
            omsOrderApiDTO.getMarkFields().addAll(resourceList);
        } else {
            omsOrderApiDTO.setMarkFields(resourceList);
        }
        //设置月结的信息
        buildCiticMonthlySettlementParam(dto.getPaymentModeCode(),dto.getAccount(),myStaff,omsOrderApiDTO.getExpressTypeCode(),omsOrderApiDTO);
        log.info("requestId==>{},下订单,远程入参==>【{}】",requestId, JSON.toJSONString(omsOrderApiDTO));
        stopWatch.start("下单调用订单");
        OmsOrderApiVO omsOrderApiVO = orderFeigntClient.saveOrder(omsOrderApiDTO).result();
        stopWatch.stop();
        log.info("requestId==>{},下订单耗时==>{}结果==>【{}】", requestId, stopWatch.getLastTaskTimeMillis(), JSON.toJSONString(omsOrderApiVO));
        //下单成功/失败，解锁并更新状态
        if (isUserCoupon) {
            if (omsOrderApiVO != null && omsOrderApiVO.getId() != null) {//返回了订单号，下单成功
                PromotionFinishDto promotionFinishDto = new PromotionFinishDto();
                promotionFinishDto.setDisId(StringUtils.isEmpty(dto.getDisId()) ? null : Long.parseLong(dto.getDisId()));
                promotionFinishDto.setId(Long.parseLong(dto.getUseProId()));
                promotionFinishDto.setProId(Long.parseLong(dto.getCouponId()));
                promotionFinishDto.setOrderId(omsOrderApiVO.getId());
                promotionFinishDto.setVerificationNetworkId(dto.getPickNetworkId() == null ? dto.getFetchNetworkId() : dto.getPickNetworkId());
                promotionFinishDto.setStaffCode(omsOrderApiDTO.getPickStaffCode());
                log.info("requestId==>{},下单成功,提交优惠券,远程入参：{}", requestId, JSON.toJSONString(promotionFinishDto));
                Result<Integer> usePromotion = promotionFeighClient.usePromotion(Long.parseLong(dto.getUseProId()), omsOrderApiVO.getId(),
                        Long.parseLong(dto.getCouponId()), StringUtils.isEmpty(dto.getDisId()) ? null : Long.parseLong(dto.getDisId()), promotionFinishDto.getVerificationNetworkId(), omsOrderApiDTO.getPickStaffCode());
                log.info("requestId==>{},下单成功,提交优惠券,远程出参：{}", requestId, JSON.toJSONString(usePromotion));
                if (usePromotion.getCode() != 1) {//优惠券提交失败
                    log.warn("requestId==>{},下单成功,优惠券使用失败,远程出参：{}", requestId, JSON.toJSONString(usePromotion));
                    throw new BusinessException(usePromotion.getCode(), usePromotion.getMsg());
                }
                CouponStaffBuryingRecordDTO couponStaffBuryingRecordDTO=new CouponStaffBuryingRecordDTO();
                try {
                    log.info("requestId==>[{}],优惠券埋点,远程入参id：[{}]",requestId, dto.getUseProId());
                    if(StringUtils.isNotEmpty(dto.getUseProId())){
                        couponStaffBuryingRecordDTO.setCouponId(Long.parseLong(dto.getUseProId()));
                        couponStaffBuryingRecordDTO.setType(CouponStaffBuryingRecordEnum.USEINFO_CODE.code);
                        couponStaffBuryingFeignClient.saveCouponStaffBurying(couponStaffBuryingRecordDTO);
                    }
                } catch (Exception e) {
                    log.error("员工优惠券埋点使用 :[{}] 异常:", JSON.toJSONString(couponStaffBuryingRecordDTO),e);

                }

            } else {
                log.info("requestId==>{},下单成功,解锁优惠券,远程入参：{}", requestId, dto.getUseProId());
                Result<Boolean> unlockResult = promotionFeighClient.unlockUserPromotion(Long.parseLong(dto.getUseProId()));
                log.info("requestId==>{},下单成功,解锁优惠券,远程出参：{}", requestId, JSON.toJSONString(unlockResult));
            }
        }
        stopWatch.start("处理我得专属业务员");
        //处理我得专属业务员
        handleMyStaff(wxUserVo, myStaff, omsOrderApiVO, requestId);
        stopWatch.stop();
        //处理客户常用地址信息
        handleFrequentAddress(wxUserVo, omsOrderApiDTO);
        //处理绑定业务员
        stopWatch.start("处理绑定业务员");
        handleUserStaff(wxUserVo, myStaff, omsOrderApiVO, dto, requestId);
        stopWatch.stop();
        //处理我得常用网点
        stopWatch.start("处理我得常用网点");
        handleCommonlyUsedNetwork(wxUserVo, sysNetworkVO, omsOrderApiVO, requestId);
        stopWatch.stop();
        log.info("requestId==>{},下单成功总耗时：{}，结果：{}", requestId, stopWatch.prettyPrint(), JsonUtils.toJson(omsOrderApiVO));
        return omsOrderApiVO;
    }

    private void handleOrderSign(OmsOrderApiVO omsOrderApiVO, List<OrderMarkingSignDto.OrderMarkingStatus> orderMarkingStatusList) {
        if (CollectionUtils.isNotEmpty(orderMarkingStatusList)) {
            //订单id
            Long id = omsOrderApiVO.getId();
            //调用打标接口
            OrderMarkingSignDto orderMarkingSignDto = new OrderMarkingSignDto();
            orderMarkingSignDto.setOrderId(id + "");
            orderMarkingSignDto.setWaybillId(omsOrderApiVO.getWaybillId());
            orderMarkingSignDto.setOrderMarkingStatusList(orderMarkingStatusList);
            List<OrderMarkingSignDto> orderMarkingSignDtos = Lists.newArrayList();
            orderMarkingSignDtos.add(orderMarkingSignDto);
            Result<Boolean> signRecord = channelApiFeignClient.signRecord(orderMarkingSignDtos);
            log.info("下单,记录打标记录==>{}", JSON.toJSONString(orderMarkingSignDto), JSON.toJSONString(signRecord));
        }
    }

    /**
     * 处理月结账号信息
     */
    private void buildCiticMonthlySettlementParam(String paymentModeCode,String account,MyStaff myStaff,String expressTypeCode,OmsOrderApiDTO omsOrderApiDTO){

        log.info("处理月结账号信息,前置入参==>【{}】", JSON.toJSONString(omsOrderApiDTO));
        //处理月结的特殊参数
        if(StringUtils.isNotEmpty(paymentModeCode) && Objects.equals(paymentModeCode, MonthlySettlementEnum.MonthlySettlementPayEnumPay.PP_PM.code)){
            //获取下单id
            if(StringUtils.isEmpty(account)){
                throw new BusinessException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_ACCOUNT_ERROR);
            }

            MonthlySettlementCustomerDTO result = citicMonthlySettlementClient.findCustomerAccount(account).result();
            if(Objects.isNull(result)){
                throw new BusinessException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_CUSTOMER_INVALID_ERROR);
            }
            log.info("处理月结账号信息,客户信息结果==>【{}】", JSON.toJSONString(result));
            //判断账号是否可用
            if(Objects.equals(result.getIsEnable(),MonthlySettlementEnum.MonthlySettlementEnableEnum.NO_ENABLE.code) ){
                throw new BusinessException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_ACCOUNT_INVALID_ERROR);
            }

            //判断兔优达
            if(Objects.equals(expressTypeCode,MonthlySettlementEnum.MonthlySettlementRabbitDeliveryEnum.TYD.name)){
                if(!Objects.equals(result.getIsRabbitDelivery(),MonthlySettlementEnum.MonthlySettlementRabbitDeliveryEnum.TYD.code)){
                    throw new BusinessException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_RABBITDELIVERY_INVALID_ERROR);
                }
            }
            //优先级：网点二维码下单=业务员二维码下单=纸质二维码下单=专属寄>全网共享>非全网共享
            //非当前业务的则进行月结的共享逻辑
            //判断是否非共享客户
            if(Objects.equals(result.getCustomerShareId(),MonthlySettlementEnum.MonthlySettlementShareEnum.NO_SHARE.code)
                    && !monthlySettlementCode.contains(omsOrderApiDTO.getOrderSourceCode()) && Objects.isNull(myStaff)){
                omsOrderApiDTO.setPickNetworkId(Long.parseLong(result.getNetworkId()+""));
                omsOrderApiDTO.setPickNetworkCode(result.getNetworkCode());
                omsOrderApiDTO .setPickNetworkName(result.getNetworkName());
                omsOrderApiDTO.setAutoScheduling(MonthlySettlementAutoSchedulEnum.AUTOSCHEDUL_NOT.code);
                omsOrderApiDTO.setOrderStatusCode(OmsOrderStatusEnum.dispatched_network.getCode());
            }else{
                omsOrderApiDTO.setAutoScheduling(MonthlySettlementAutoSchedulEnum.AUTOSCHEDUL.code);
                omsOrderApiDTO.setCustomerLabel(MonthlySettlementCusLabelEnum.NUM_06.code);
            }
            omsOrderApiDTO.setCustomerCode(result.getCode());
            omsOrderApiDTO.setCustomerId(Integer.parseInt(result.getId()));
            omsOrderApiDTO.setCustomerName(result.getName());
            omsOrderApiDTO.setAppletsReCalculate(true);
            omsOrderApiDTO.setOrderTypeCode(OrderTypeEnum.MONTHLYSETTLEMENT.getCode());
        }
    }

    /**
     * 处理月结账号信息-修改
     */
    private void updateCiticMonthlySettlementParam(String paymentModeCode,String account,String myStaff,String expressTypeCode,OmsOrderUpdateApiDTO omsOrderUpdateApiDTO){
        //处理月结的特殊参数
        if(StringUtils.isNotEmpty(paymentModeCode) && Objects.equals(paymentModeCode, MonthlySettlementEnum.MonthlySettlementPayEnumPay.PP_PM.code)){
            //获取下单id
            if(StringUtils.isEmpty(account)){
                throw new BusinessException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_ACCOUNT_ERROR);
            }

            MonthlySettlementCustomerDTO result = citicMonthlySettlementClient.findCustomerAccount(account).result();
            log.info("处理月结账号信息,客户信息结果==>【{}】", JSON.toJSONString(result));
            if(Objects.isNull(result)){
                throw new BusinessException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_CUSTOMER_INVALID_ERROR);
            }
            //判断账号是否可用
            if(Objects.equals(result.getIsEnable(),MonthlySettlementEnum.MonthlySettlementEnableEnum.NO_ENABLE.code) ){
                throw new BusinessException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_ACCOUNT_INVALID_ERROR);
            }

            //判断兔优达
            if(Objects.equals(expressTypeCode,MonthlySettlementEnum.MonthlySettlementRabbitDeliveryEnum.TYD.name)){
                if(!Objects.equals(result.getIsRabbitDelivery(),MonthlySettlementEnum.MonthlySettlementRabbitDeliveryEnum.TYD.code)){
                    throw new BusinessException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_RABBITDELIVERY_INVALID_ERROR);
                }
            }
            //优先级：网点二维码下单=业务员二维码下单=纸质二维码下单=专属寄=微信小程序下单(绑定业务员>全网共享>非全网共享)
            //非当前业务的则进行月结的共享逻辑
            //判断是否非共享客户
            if (Objects.equals(result.getCustomerShareId(), MonthlySettlementEnum.MonthlySettlementShareEnum.NO_SHARE.code)
                    && !monthlySettlementCode.contains(omsOrderUpdateApiDTO.getOrderSourceCode()) && StringUtils.isBlank(myStaff)) {
                omsOrderUpdateApiDTO.setPickNetworkId(Long.parseLong(result.getNetworkId() + ""));
                omsOrderUpdateApiDTO.setPickNetworkCode(result.getNetworkCode());
                omsOrderUpdateApiDTO.setPickNetworkName(result.getNetworkName());
                omsOrderUpdateApiDTO.setAutoScheduling(MonthlySettlementAutoSchedulEnum.AUTOSCHEDUL_NOT.code);
            } else {
                omsOrderUpdateApiDTO.setAutoScheduling(MonthlySettlementAutoSchedulEnum.AUTOSCHEDUL.code);
                omsOrderUpdateApiDTO.setCustomerLabel(MonthlySettlementCusLabelEnum.NUM_06.code);
            }
            omsOrderUpdateApiDTO.setCustomerCode(result.getCode());
            omsOrderUpdateApiDTO.setCustomerId(Integer.parseInt(result.getId()));
            omsOrderUpdateApiDTO.setCustomerName(result.getName());
            omsOrderUpdateApiDTO.setOrderTypeCode(OrderTypeEnum.MONTHLYSETTLEMENT.getCode());

        }



    }


    /**
     * 功能描述:
     * 初始化订单来源
     *
     * @param omsOrderApiDTO
     * @return:void
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-02-24 10:43
     */
    private void setOrderSourceCode(OmsOrderApiDTO omsOrderApiDTO, UserCard userCard, OrderDTO dto) {
        if (StringUtils.isNotEmpty(omsOrderApiDTO.getWaybillId()) && (omsOrderApiDTO.getWaybillId().startsWith("JT") || omsOrderApiDTO.getWaybillId().startsWith("UT"))) {
            //纸质二维码
            omsOrderApiDTO.setOrderSourceCode(OrderSourceEnum.WX_QR_PAPER_CODE.getCode());
            omsOrderApiDTO.setOrderSourceName(OrderSourceEnum.WX_QR_PAPER_CODE.getName());
            //纸质二维码需要带实名信息下单
            omsOrderApiDTO.setIsRealName(1);
            omsOrderApiDTO.setRealName(userCard.getName());
            omsOrderApiDTO.setIdNo(userCard.getCardNum());
            omsOrderApiDTO.setIdNoType(userCard.getCardType());
        } else if (omsOrderApiDTO.getPickNetworkId() != null) {
            //网点二维码
            omsOrderApiDTO.setOrderSourceCode(OrderSourceEnum.WX_QR_NETWORK.getCode());
            omsOrderApiDTO.setOrderSourceName(OrderSourceEnum.WX_QR_NETWORK.getName());
        } else if (StringUtils.isNotEmpty(omsOrderApiDTO.getPickStaffCode())) {
            if (StrUtil.isNotBlank(dto.getOrderSourceCode()) && StrUtil.equals(dto.getOrderSourceCode(), OrderSourceEnum.BYJ.getCode())) {
                //毕业季
                omsOrderApiDTO.setOrderSourceCode(OrderSourceEnum.BYJ.getCode());
                omsOrderApiDTO.setOrderSourceName(OrderSourceEnum.BYJ.getName());
            } else {
                if (Objects.equals(1, dto.getIsExclusive())) {//业务员新码下单
                    omsOrderApiDTO.setOrderSourceCode(OrderSourceEnum.ZSJ.getCode());
                    omsOrderApiDTO.setOrderSourceName(OrderSourceEnum.ZSJ.getName());
                    omsOrderApiDTO.setBestPickTimeStart(null);
                    omsOrderApiDTO.setBestPickTimeEnd(null);
                } else {
                    //业务员二维码
                    omsOrderApiDTO.setOrderSourceCode(OrderSourceEnum.WX_QR_SALEMAN.getCode());
                    omsOrderApiDTO.setOrderSourceName(OrderSourceEnum.WX_QR_SALEMAN.getName());
                }
            }
        } else {
            //小程序普通下单
            omsOrderApiDTO.setOrderSourceCode(OrderSourceEnum.WX_MINIPROGRAM.getCode());
            omsOrderApiDTO.setOrderSourceName(OrderSourceEnum.WX_MINIPROGRAM.getName());
        }

        //2023-03-29 解决微信小程序来源  最佳取件时间时间为空的异常
        if (StrUtil.equals(omsOrderApiDTO.getOrderSourceCode(), OrderSourceEnum.WX_MINIPROGRAM.getCode())) {
            if (Objects.isNull(dto.getBestPickTimeStart()) || Objects.isNull(dto.getBestPickTimeEnd())) {
                throw new BusinessException(ServiceErrCodeEnum.BESTPICK_TIME_IS_MISS);
            }
        }

    }

    /**
     * 功能描述:
     * 处理绑定业务员
     * 地址绑定业务员-优化（微信单独逻辑/未打通）
     *
     * @param wxUserVo
     * @param myStaff
     * @param omsOrderApiVO
     * @param requestId
     * @return:void
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-02-02 11:08
     */
    private void handleUserStaff(WxUserVo wxUserVo, MyStaff myStaff, OmsOrderApiVO omsOrderApiVO, OrderDTO orderDTO, String requestId) {
        try {
            if (omsOrderApiVO != null && myStaff != null) {
                StopWatch stopWatch = new StopWatch();
                stopWatch.start("查询绑定业务员");
                UserStaff userStaff = userStaffMapper.selectOne(new LambdaQueryWrapper<UserStaff>().eq(UserStaff::getUserId, wxUserVo.getId()).eq(UserStaff::getFullAddress, orderDTO.getSenderAddressFull()));
                stopWatch.stop();
                log.info("requestId:{},查询绑定业务员耗时:{},结果:{}", requestId, stopWatch.getLastTaskTimeMillis(), JsonUtils.toJson(userStaff));

                //插入常用业务员
                if (userStaff != null && !userStaff.getStaffCode().equals(myStaff.getStaffNo())) {
                    userStaff.setStaffCode(myStaff.getStaffNo());
                    userStaff.setUpdateTime(LocalDateTime.now());
                    stopWatch.start("修改绑定业务员");
                    userStaffMapper.updateById(userStaff);
                    stopWatch.stop();
                    log.info("requestId:{},修改绑定业务员耗时:{}", requestId, stopWatch.getLastTaskTimeMillis());
                } else if (userStaff == null) {
                    userStaff = new UserStaff();
                    userStaff.setId(GenerationIdUtil.getId());
                    userStaff.setCreateTime(LocalDateTime.now());
                    userStaff.setFullAddress(orderDTO.getSenderAddressFull());
                    userStaff.setStaffCode(myStaff.getStaffNo());
                    userStaff.setUserId(Long.valueOf(wxUserVo.getId()));
                    stopWatch.start("新增绑定业务员");
                    userStaffMapper.insert(userStaff);
                    stopWatch.stop();
                    log.info("requestId:{},新增绑定业务员耗时:{}", requestId, stopWatch.getLastTaskTimeMillis());
                }
            }
        } catch (Exception e) {
            log.warn("requestId:{},处理绑定业务员异常", requestId, e);
        }
    }

    /**
     * 文件类型不能报价
     *
     * @param goodssTypeCode
     * @param insuredValue
     */
    private void checkInsuredPrice(String goodssTypeCode, BigDecimal insuredValue) {
        if ("bm000001".equals(goodssTypeCode) && insuredValue != null && insuredValue.compareTo(new BigDecimal(0)) > 0) {
            throw new BusinessException(ServiceErrCodeEnum.CAN_NOT_INSURED);
        }
    }

    /**
     * 处理我得常用网点
     *
     * @param wxUserVo
     * @param sysNetworkVO
     * @param omsOrderApiVO
     */
    private void handleCommonlyUsedNetwork(WxUserVo wxUserVo, SysNetworkVO sysNetworkVO, OmsOrderApiVO omsOrderApiVO, String requestId) {

        try {
            DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("HH:mm");
            if (omsOrderApiVO != null && sysNetworkVO != null) {
                //根据常用网点id和用户Id查找记录，没有就插入，有就更新
                StopWatch stopWatch = new StopWatch();
                List<Long> ids = wxUserService.getIdsByIdAndNumberId(wxUserVo.getNumberId());
                stopWatch.start("常用网点查询");
                List<CommonlyUsedNetwork> commonlyUsedNetworks = commonlyUsedNetworkService.list(new LambdaQueryWrapper<CommonlyUsedNetwork>().eq(CommonlyUsedNetwork::getNetworkId, sysNetworkVO.getId()).in(CommonlyUsedNetwork::getUserId, ids));
                stopWatch.stop();
                log.info("requestId:{},常用网点查询耗时：{}，结果：{}", requestId, stopWatch.getLastTaskTimeMillis(), JsonUtils.toJson(commonlyUsedNetworks));
                CommonlyUsedNetwork commonlyUsedNetwork = new CommonlyUsedNetwork();
                if (commonlyUsedNetworks != null && !commonlyUsedNetworks.isEmpty()) {
                    commonlyUsedNetworks = commonlyUsedNetworks.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getNetworkId()))), ArrayList::new));
                    commonlyUsedNetwork = commonlyUsedNetworks.get(0);
                    commonlyUsedNetwork.setUpdateTime(LocalDateTime.now());
                    commonlyUsedNetwork.setNetworkName(sysNetworkVO.getName());
                    commonlyUsedNetwork.setMobile(sysNetworkVO.getMobile());
                    commonlyUsedNetwork.setTelephone(sysNetworkVO.getTelephone());
                    commonlyUsedNetwork.setLatitude(sysNetworkVO.getLatitude());
                    commonlyUsedNetwork.setLongitude(sysNetworkVO.getLongitude());
                    commonlyUsedNetwork.setAddress(sysNetworkVO.getAddress());
                    commonlyUsedNetwork.setUserId(wxUserVo.getId().longValue());
                    if (sysNetworkVO.getStartBusinessTime() != null) {
                        commonlyUsedNetwork.setStartBusinessTime(dtf2.format(sysNetworkVO.getStartBusinessTime()));
                    }
                    if (sysNetworkVO.getEndBusinessTime() != null) {
                        commonlyUsedNetwork.setEndBusinessTime(dtf2.format(sysNetworkVO.getEndBusinessTime()));
                    }
                    stopWatch.start("常用网点修改");
                    commonlyUsedNetworkService.updateById(commonlyUsedNetwork);
                    stopWatch.stop();
                    log.info("requestId:{},常用网点修改耗时：{}", requestId, stopWatch.getLastTaskTimeMillis());
                } else {
                    commonlyUsedNetwork = new CommonlyUsedNetwork();
                    commonlyUsedNetwork.setId(GenerationIdUtil.getId());
                    commonlyUsedNetwork.setUpdateTime(LocalDateTime.now());
                    commonlyUsedNetwork.setNetworkName(sysNetworkVO.getName());
                    commonlyUsedNetwork.setMobile(sysNetworkVO.getMobile());
                    commonlyUsedNetwork.setTelephone(sysNetworkVO.getTelephone());
                    commonlyUsedNetwork.setLatitude(sysNetworkVO.getLatitude());
                    commonlyUsedNetwork.setLongitude(sysNetworkVO.getLongitude());
                    commonlyUsedNetwork.setCreateTime(LocalDateTime.now());
                    commonlyUsedNetwork.setNetworkId(sysNetworkVO.getId().longValue());
                    commonlyUsedNetwork.setUserId(wxUserVo.getId().longValue());
                    commonlyUsedNetwork.setAddress(sysNetworkVO.getAddress());
                    if (sysNetworkVO.getStartBusinessTime() != null) {
                        commonlyUsedNetwork.setStartBusinessTime(dtf2.format(sysNetworkVO.getStartBusinessTime()));
                    }
                    if (sysNetworkVO.getEndBusinessTime() != null) {
                        commonlyUsedNetwork.setEndBusinessTime(dtf2.format(sysNetworkVO.getEndBusinessTime()));
                    }
                    stopWatch.start("常用网点新增");
                    commonlyUsedNetworkService.save(commonlyUsedNetwork);
                    stopWatch.stop();
                    log.info("requestId:{},常用网点新增耗时：{}", requestId, stopWatch.getLastTaskTimeMillis());
                }
            }
        } catch (Exception e) {
            log.warn("requestId:{},处理我的常用网点异常", requestId, e);
        }
    }

    /**
     * 处理我的专属业务员
     */
    private void handleMyStaff(WxUserVo wxUserVo, MyStaff myStaff, OmsOrderApiVO omsOrderApiVO, String requestId) {
        try {
            if (omsOrderApiVO != null && myStaff != null) {
                StopWatch stopWatch = new StopWatch();
                List<Long> ids = wxUserService.getIdsByIdAndNumberId(wxUserVo.getNumberId());
                stopWatch.start("查询我的专属业务员");
                List<MyStaff> myStaff1s = myStaffService.list(new LambdaQueryWrapper<MyStaff>().in(MyStaff::getUserId, ids).eq(MyStaff::getStaffNo, myStaff.getStaffNo()));
                stopWatch.stop();
                log.info("requestId:{},查询我的专属业务员耗时:{},结果:{}", requestId, stopWatch.getLastTaskTimeMillis(), JsonUtils.toJson(myStaff1s));
                MyStaff myStaff1 = new MyStaff();
                //插入常用业务员
                if (myStaff1s != null && !myStaff1s.isEmpty()) {
                    myStaff1s = myStaff1s.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getStaffNo()))), ArrayList::new));
                    myStaff1 = myStaff1s.get(0);
                    myStaff1.setUpdateTime(LocalDateTime.now());
                    //不再修改userId，会影响到微信小程序-绑定业务员逻辑中，业务员的查找
//                    myStaff1.setUserId(wxUserVo.getId().longValue());
                    stopWatch.start("修改我的专属业务员");
                    myStaffService.updateById(myStaff1);
                    stopWatch.stop();
                    log.info("requestId:{},修改我的专属业务员耗时:{}", requestId, stopWatch.getLastTaskTimeMillis());
                } else {
                    stopWatch.start("新增我的专属业务员");
                    myStaffService.save(myStaff);
                    stopWatch.stop();
                    log.info("requestId:{},新增我的专属业务员耗时:{}", requestId, stopWatch.getLastTaskTimeMillis());
                }
            }
        } catch (Exception e) {
            log.warn("requestId:{},处理我的专属业务员异常", requestId, e);
        }
    }




    private void handleFrequentAddress(WxUserVo wxUserVo, OmsOrderApiDTO omsOrderApiDTO) {
        try {
            FrequentAddressDTO dto = new FrequentAddressDTO();
            dto.setCreateTime(LocalDateTime.now());
            dto.setUpdateTime(LocalDateTime.now());
            dto.setCreateBy(wxUserVo.getNumberId());
            dto.setUpdateBy(wxUserVo.getNumberId());
            dto.setCreateByName(wxUserVo.getNickName());
            dto.setUpdateByName(wxUserVo.getNickName());
            dto.setUserId(wxUserVo.getNumberId());

            dto.setMobile(omsOrderApiDTO.getReceiverMobilePhone());
            dto.setName(omsOrderApiDTO.getReceiverName());
            dto.setProvinceId(omsOrderApiDTO.getReceiverProvinceId());
            dto.setProvinceName(omsOrderApiDTO.getReceiverProvinceName());
            dto.setCityId(omsOrderApiDTO.getReceiverCityId());
            dto.setCityName(omsOrderApiDTO.getReceiverCityName());
            dto.setAreaId(omsOrderApiDTO.getReceiverAreaId());
            dto.setAreaName(omsOrderApiDTO.getReceiverAreaName());
            dto.setDetailedAddress(omsOrderApiDTO.getReceiverDetailedAddress());
            dto.setCompanyName(omsOrderApiDTO.getReceiverCompany());
            channelApiFeignClient.saveFrequentAddressInfo(dto);
        } catch (Exception e) {
            log.error("处理客户常用地址信息异常", e);
        }

    }

    /**
     * 校验type为4的城市id是否在开发范围内
     *
     * @param cityId
     */
    private void validataCity(Integer cityId, Integer type) {
        AppAreaDTO appAreaDTO = new AppAreaDTO();
        appAreaDTO.setAddressType(type);
        List<AppAreaVO> appList = appAreaService.getAppListV2(appAreaDTO);
        Integer cityType = 4;
        boolean isCan = false;

        for (AppAreaVO appAreaVO :
                appList) {
            if (appAreaVO.getType().equals(cityType) && appAreaVO.getId().equals(cityId)) {
                isCan = true;
                break;
            }
        }
        if (!isCan) {
            throw new BusinessException(type.equals(AddressType.SEND_ADDR_TYPE) ? ServiceErrCodeEnum.NOT_OPEN_SEND_ADDR : ServiceErrCodeEnum.NOT_OPEN_RES_ADDR);
        }

    }

    /**
     * 效验寄件地址-业务员
     *
     * @param dto
     * @param sysNetworkVO
     */
    private void validataSendAddr(OrderDTO dto, SysNetworkVO sysNetworkVO) {
        if (!sysNetworkVO.getCityId().equals(dto.getSenderCityId())) {
            throw new BusinessException(ServiceErrCodeEnum.SEND_ADDR_ERR);
        }
    }

    /**
     * 效验寄件地址-网点
     *
     * @param dto
     * @param sysNetworkVO
     */
    private void validataSendAddrNetWork(OrderDTO dto, SysNetworkVO sysNetworkVO) {
        if (!sysNetworkVO.getCityId().equals(dto.getSenderCityId())) {
            throw new BusinessException(ServiceErrCodeEnum.SEND_ADDR_ERR_NETWORK);
        }
    }

    @Override
    public OmsOrderUpdateApiVO update(OrderDTO dto, WxUserVo wxUserVo) {
        String requestId = dto.getMyRequestId();
        //重新计算费用
        log.info("请求id==>{}计算总费==>{}", requestId, JSON.toJSONString(dto));
        dto.setSettlementWeight(dto.getSettlementWeight() == null ? new BigDecimal(0) : dto.getSettlementWeight());
        dto.setStandardValue(dto.getStandardValue() == null ? new BigDecimal(0) : dto.getStandardValue());
        dto.setInsuredValue(dto.getInsuredValue() == null ? new BigDecimal(0) : dto.getInsuredValue());
        //文件类型不能报价
        checkInsuredPrice(dto.getGoodsTypeCode(), dto.getInsuredValue());
        //特殊类型 总运费=折扣后的标准运费+保价费 （网络退货）
        //总运费 = 标准运费+保价费
        if( OrderChannelTypeEnum.getOrderChannelTypeList().contains(dto.getOrderChannelType())){
            BigDecimal discountFreight = dto.getDiscountFreight();
            discountFreight=Objects.isNull(discountFreight)?new BigDecimal(0):discountFreight;
            dto.setTotalFreight(dto.getStandardValue().subtract(discountFreight).add(dto.getInsuredValue()));
        }else{
            dto.setTotalFreight(dto.getStandardValue().add(dto.getInsuredValue()));
        }

        //校验打标
        Boolean orderMarkingFlag = false;
        OrderRecvPrefDTO orderRecvPrefVO = new OrderRecvPrefDTO();
        List<PreferencesCommonDto.OrderMarkingStatus> markingStatusList = dto.getMarkingStatusList();
        if (CollectionUtils.isNotEmpty(markingStatusList)) {
            //校验打标code是否合法
            orderMarkingHelper.checkMarking(dto);
            BeanUtils.copyProperties(dto, orderRecvPrefVO);
            orderRecvPrefVO.setDataSource("XG_WXXCX");
            log.info("更新订单,打标处理完成==>{}", JSON.toJSONString(orderRecvPrefVO));
            orderMarkingFlag = true;
        }
        log.info("请求id==>{}总费计算完成==>{}", requestId, JSON.toJSONString(dto));
        OmsOrderUpdateApiDTO omsOrderUpdateApiDTO = orikaBeanMapper.map(dto, OmsOrderUpdateApiDTO.class);
        omsOrderUpdateApiDTO.setFrom(FromEnum.MINIAPP.getCode());
        //omsOrderUpdateApiDTO.setCustomerId(wxUserVo.getNumberId());
        omsOrderUpdateApiDTO.setMemberId(wxUserVo.getNumberId());
        omsOrderUpdateApiDTO.setCustomerName("");
        omsOrderUpdateApiDTO.setUpdateBy(wxUserVo.getId());
        omsOrderUpdateApiDTO.setUpdateByName(wxUserVo.getNumberId() + "");
        if (omsOrderUpdateApiDTO.getRemarks() == null) {
            omsOrderUpdateApiDTO.setRemarks("");
        }
        if (orderMarkingFlag) {
            omsOrderUpdateApiDTO.setOrderRecvPrefDTO(orderRecvPrefVO);
        }
        //处理收寄件人长度
        omsOrderUpdateApiDTO.setReceiverName(YlStringUtils.subText(omsOrderUpdateApiDTO.getReceiverName()));
        omsOrderUpdateApiDTO.setSenderName(YlStringUtils.subText(omsOrderUpdateApiDTO.getSenderName()));
        updateCiticMonthlySettlementParam(dto.getPaymentModeCode(),dto.getAccount(),dto.getPickStaffCodeUpdate(),dto.getExpressTypeCode(),omsOrderUpdateApiDTO);
        log.info("请求id==>{}修改订单，调用order接口updateAppletsOrder，入参===>{}",requestId,JSON.toJSONString(omsOrderUpdateApiDTO));
        Result<OmsOrderUpdateApiVO> omsOrderUpdateApiVOResult = omsOrderGateWayFeign.updateOrder(omsOrderUpdateApiDTO);
        OmsOrderUpdateApiVO data = omsOrderUpdateApiVOResult.getData();
        log.info("请求id==>{}修改订单，调用order接口updateAppletsOrder，出参===>{}", requestId, JSON.toJSONString(data));
        return data;
    }

    @Override
    public Boolean cancel(List<Long> orderIds, String requestId) {
        OmsOrderCancelApiDTO dto = new OmsOrderCancelApiDTO();
        dto.setOrderIds(orderIds).setFrom(FromEnum.MINIAPP.getCode());
        dto.setOperateSource("微信小程序");
        OmsOrderCancelApiVO result = omsOrderGateWayFeign.cancelOrder(dto).result();
        List<String> orders = Lists.newArrayList();
        for (Long orderId : orderIds) {
            orders.add(orderId + "");
        }
        if (result.getSuccess()) {//取消成功，优惠券变为未使用
            PromotionUserUseDto dto1 = new PromotionUserUseDto();
            dto1.setOrderNos(orders);
            log.info("请求id==>{}取消订单,优惠券回退==>{}", requestId, JSON.toJSONString(dto1));
            promotionFeighClient.rollbackPromotionByOrderNo(dto1);
        }
        return Objects.nonNull(result);
    }

    @Override
    public Boolean cancelReason(OrderCancelDTO orderCancelDTO, String requestId) {
        OmsOrderCancelApiDTO dto = new OmsOrderCancelApiDTO();
        dto.setOrderIds(orderCancelDTO.getOrderIds()).setFrom(FromEnum.MINIAPP.getCode());
        dto.setOperateSource("微信小程序");
        dto.setReason(orderCancelDTO.getReason());
        dto.setCancelReasonCode(orderCancelDTO.getCancelReasonCode());
        OmsOrderCancelApiVO result = omsOrderGateWayFeign.cancelOrder(dto).result();
        List<String> orders = Lists.newArrayList();
        for (Long orderId : orderCancelDTO.getOrderIds()) {
            orders.add(orderId + "");
        }
        if (result.getSuccess()) {//取消成功，优惠券变为未使用
            PromotionUserUseDto dto1 = new PromotionUserUseDto();
            dto1.setOrderNos(orders);
            log.info("请求id==>{}取消订单,优惠券回退==>{}", requestId, JSON.toJSONString(dto1));
            promotionFeighClient.rollbackPromotionByOrderNo(dto1);
        }
        return Objects.nonNull(result);
    }

    @Override
    public Boolean delete(Long orderId) {
        return omsMiniGateWayFeign.delete(orderId).result();
    }

    @Override
    public OmsOrderBatchApiVo cloudPrintOrder(BatchOrderDTO dto) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        List<OrderDTO> orderDTOs = dto.getOrders();
        if (orderDTOs.size() > 10) {
            //同一寄件人可以一对多批量寄件，一次最多寄10票
            throw new BusinessException(ServiceErrCodeEnum.EXCEED_MAXIMUM_ERROR);
        }
        OmsCloudPrintApiDto omsCloudPrintApiDto = new OmsCloudPrintApiDto();
        omsCloudPrintApiDto.setPrintDeviceId(dto.getPrintDeviceId());
        omsCloudPrintApiDto.setPrintDeviceTypeCode(dto.getPrintDeviceTypeCode());
        omsCloudPrintApiDto.setPrintCustomerId(dto.getPrintCustomerId());
        omsCloudPrintApiDto.setPrinterEquipmentId(dto.getPrinterEquipmentId());
        List<OmsOrderApiDTO> orders = Lists.newArrayList();

        WxUserVo wxUserVo = getUser();
        //判断是否实名寄件（如果有实名登记信息并且寄件人手机号和实名登记的手机号一致就认为是实名寄件)
        String name = "";
        String cardNum = "";
        String mobile = "";
        Integer cardType = 0;
        UserCard userCard = userCardService.getOne(new LambdaQueryWrapper<UserCard>().eq(UserCard::getUserId, wxUserVo.getId()));
        if (userCard != null) {
            name = userCard.getName();
            cardNum = userCard.getCardNum();
            mobile = userCard.getMobile();
            cardType = userCard.getCardType();
        }
        Long pickNetWorkId = null;
        String pickNetWorkCode = null;
        String pickNetWorkName = null;

        //根据大客户id,获取网点信息
//        SysCustomeAppletVO sysCustomeApplet = sysCustomerExtFeignClient.getSysCustomerAppletInfo(dto.getPrinterEquipmentId()).result();
        ForwardRequest forwardRequest = new ForwardRequest();
        forwardRequest.setRequestId(requestId);
        forwardRequest.setBody(dto.getPrinterEquipmentId() + "");
        forwardRequest.setRequestUri("/lmdmapi/sysCustomerPrinterEquipment/getSysCustomerAppletInfo");
        log.info("请求id==>{}channel,入参==>{}", requestId, JSON.toJSONString(forwardRequest));
        Result<?> forward = channelApiFeignClient.forward(forwardRequest);
        log.info("请求id==>{}channel,出参==>{}", requestId, JSON.toJSONString(forward));
        Object result1 = forward.result();
        SysCustomeAppletVO sysCustomeApplet = JSON.parseObject(JSON.toJSONString(result1), SysCustomeAppletVO.class);
        if (sysCustomeApplet == null) {
            throw new BusinessException(ServiceErrCodeEnum.STOPMESSAGE_ISNULL);
        }
        CustomerPrintData one = customerPrintDataService.getOne(new LambdaQueryWrapper<CustomerPrintData>().eq(CustomerPrintData::getPrinterEquipmentId, dto.getPrinterEquipmentId()).eq(CustomerPrintData::getUserId, wxUserVo.getId()));

        if (one == null) {
            throw new BusinessException(ServiceErrCodeEnum.PLEASE_BIND);
        }

        pickNetWorkId = sysCustomeApplet.getNetworkId().longValue();
        pickNetWorkCode = sysCustomeApplet.getNetworkCode();
        pickNetWorkName = sysCustomeApplet.getNetworkName();

        for (OrderDTO orderDTO : orderDTOs) {
            //文件类型不能报价
            checkInsuredPrice(orderDTO.getGoodsTypeCode(), orderDTO.getInsuredValue());

            if (orderDTO.getSenderAreaId().equals(orderDTO.getReceiverAreaId()) && orderDTO.getSenderDetailedAddress().equals(orderDTO.getReceiverDetailedAddress())) {
                throw new BusinessException(ServiceErrCodeEnum.DETAILEDADDRESS_EQUALS);
            }
            if (org.apache.commons.lang.StringUtils.equals("0", isInterceptOrder)) {
                validataCity(orderDTO.getSenderAreaId(), AddressType.SEND_ADDR_TYPE);
                validataCity(orderDTO.getReceiverAreaId(), AddressType.RES_ADDR_TYPE);
            } else {
                //筛单接口校验
                InterceptOrderDto interceptOrderDto = new InterceptOrderDto();
                interceptOrderDto.setSource("appletWechat");
                InterceptOrderDto.SendAddress sendAddress = new InterceptOrderDto.SendAddress();
                sendAddress.setProvince(orderDTO.getSenderProvinceName());
                sendAddress.setCity(orderDTO.getSenderCityName());
                sendAddress.setArea(orderDTO.getSenderAreaName());
                sendAddress.setDetail(orderDTO.getSenderDetailedAddress());
                interceptOrderDto.setSendAddress(sendAddress);
                InterceptOrderDto.ReceiverAddress receiverAddress = new InterceptOrderDto.ReceiverAddress();
                receiverAddress.setProvince(orderDTO.getReceiverProvinceName());
                receiverAddress.setCity(orderDTO.getReceiverCityName());
                receiverAddress.setArea(orderDTO.getReceiverAreaName());
                receiverAddress.setDetail(orderDTO.getReceiverDetailedAddress());
                interceptOrderDto.setReceiverAddress(receiverAddress);
                log.info("请求id==>{}云打印调用筛单接口,入参==>{}", requestId, JSON.toJSONString(interceptOrderDto));
                Result<InterceptOrderVo> interceptOrderVoResult = channelApiFeignClient.orderIntercept(interceptOrderDto);
                log.info("请求id==>{}云打印调用筛单接口,出参==>{}", requestId, JSON.toJSONString(interceptOrderVoResult));
                InterceptOrderVo result = interceptOrderVoResult.result();
                if (result.getHit()) {
                    log.info("请求id==>{}筛单拦截==>{}", requestId, JSON.toJSONString(result));
                    throw new BusinessException(result.getReason());
                }
            }
            if (orderDTO.getSettlementWeight() == null) {
                orderDTO.setSettlementWeight(new BigDecimal(0));
            }
            if (orderDTO.getStandardValue() == null) {
                orderDTO.setStandardValue(new BigDecimal(0));
            }
            if (orderDTO.getInsuredValue() == null) {
                orderDTO.setInsuredValue(new BigDecimal(0));
            }
            //总运费 = 标准运费+保价费
            orderDTO.setTotalFreight(orderDTO.getStandardValue().add(orderDTO.getInsuredValue()));
            OmsOrderApiDTO omsOrderApiDTO = orikaBeanMapper.map(orderDTO, OmsOrderApiDTO.class);

            if (orderDTO.getSenderMobilePhone().equals(mobile)) {
                omsOrderApiDTO.setIsRealName(1);
                omsOrderApiDTO.setRealName(name);
                omsOrderApiDTO.setIdNo(cardNum);
                omsOrderApiDTO.setIdNoType(cardType);
            }
            omsOrderApiDTO.setFrom(FromEnum.CLOUDPRINT_KD100.getCode());
            omsOrderApiDTO.setPickNetworkId(pickNetWorkId);
            omsOrderApiDTO.setPickNetworkName(pickNetWorkName);
            omsOrderApiDTO.setPickNetworkCode(pickNetWorkCode);
            omsOrderApiDTO.setCustomerCode(sysCustomeApplet.getCode());
            omsOrderApiDTO.setCustomerName(sysCustomeApplet.getName());
            omsOrderApiDTO.setCustomerId(sysCustomeApplet.getId());
            omsOrderApiDTO.setMemberId(wxUserVo.getNumberId());

            omsOrderApiDTO.setNeedDispatch(2);//不需要调度
            omsOrderApiDTO.setAutoScheduling("0");//不需要调度

            //脱敏手机号
//            omsOrderApiDTO.setReceiverMobilePhone(DesensitizationUtils.desensitizationPhone(omsOrderApiDTO.getReceiverMobilePhone()));
//            omsOrderApiDTO.setSenderMobilePhone(DesensitizationUtils.desensitizationPhone(omsOrderApiDTO.getSenderMobilePhone()));
            omsOrderApiDTO.setReceiverMobilePhone(omsOrderApiDTO.getReceiverMobilePhone());
            omsOrderApiDTO.setSenderMobilePhone(omsOrderApiDTO.getSenderMobilePhone());
            //脱敏地址
//            omsOrderApiDTO.setReceiverDetailedAddress(DesensitizationUtils.desensitizationAddress(omsOrderApiDTO.getReceiverDetailedAddress()));
//            omsOrderApiDTO.setSenderDetailedAddress(DesensitizationUtils.desensitizationAddress(omsOrderApiDTO.getSenderDetailedAddress()));
            omsOrderApiDTO.setReceiverDetailedAddress(omsOrderApiDTO.getReceiverDetailedAddress());
            omsOrderApiDTO.setSenderDetailedAddress(omsOrderApiDTO.getSenderDetailedAddress());
            //脱敏姓名
//            omsOrderApiDTO.setReceiverName(DesensitizationUtils.desensitizationName(omsOrderApiDTO.getReceiverName()));
//            omsOrderApiDTO.setSendName(DesensitizationUtils.desensitizationName(omsOrderApiDTO.getSendName()));
            omsOrderApiDTO.setReceiverName(omsOrderApiDTO.getReceiverName());
            omsOrderApiDTO.setSendName(omsOrderApiDTO.getSendName());

            orders.add(omsOrderApiDTO);
        }

        //更新打印机时间
        one.setUpdateTime(LocalDateTime.now());
        customerPrintDataService.updateById(one);

        omsCloudPrintApiDto.setOrders(orders);
        omsCloudPrintApiDto.setFrom(FromEnum.CLOUDPRINT_KD100.getCode());
        log.info("请求id==>{}云打印-批量订单,远程入参：【{}】", requestId, JSON.toJSONString(omsCloudPrintApiDto));
        OmsOrderBatchApiVo result = orderFeigntClient.cloudPrintOrder(omsCloudPrintApiDto).result();
        log.info("请求id==>{}云打印-批量订单,远程出参：【{}】", requestId, JSON.toJSONString(result));
        return result;
    }

    @Override
    public OmsOrderBatchApiVo batchOrder(OmsOrderBatchApiDto omsOrderBatchApiDto) {
        WxUserVo wxUserVo = getUser();

        if (omsOrderBatchApiDto.getOrders().size() > 10) {
            //同一寄件人可以一对多批量寄件，一次最多寄10票
            throw new BusinessException(ServiceErrCodeEnum.EXCEED_MAXIMUM_ERROR);
        }

        List<OmsOrderApiDTO> list = new ArrayList<>();

        //判断是否实名寄件（如果有实名登记信息并且寄件人手机号和实名登记的手机号一致就认为是实名寄件)
        List<Long> ids = wxUserService.getIdsByIdAndNumberId(wxUserVo.getNumberId());
        log.info("根据numberId查询用户ids:{}", ids);
        List<UserCard> userCards = userCardService.list(new LambdaQueryWrapper<UserCard>().in(UserCard::getUserId, ids));
        UserCard userCard = null;
        if (userCards != null && userCards.size() > 0) {
            userCard = userCards.get(0);
        }
        UserCard userCarda = userCard == null ? null : orikaBeanMapper.map(userCard, UserCard.class);

        omsOrderBatchApiDto.getOrders().forEach(dto -> {
            //设置保价标识
            if (null != dto.getDeclaredValue() && dto.getDeclaredValue().compareTo(BigDecimal.ZERO) > 0) {
                dto.setInsured(1);
            } else {
                dto.setInsured(0);
            }
            //文件类型不能报价
            checkInsuredPrice(dto.getGoodsTypeCode(), dto.getInsuredValue());
            if (org.apache.commons.lang.StringUtils.equals("0", isInterceptOrder)) {
                validataCity(dto.getReceiverAreaId(), AddressType.RES_ADDR_TYPE);
                validataCity(dto.getSenderAreaId(), AddressType.SEND_ADDR_TYPE);
            }

            if (dto.getSettlementWeight() == null) {
                dto.setSettlementWeight(new BigDecimal(0));
            }
            if (dto.getStandardValue() == null) {
                dto.setStandardValue(new BigDecimal(0));
            }
            if (dto.getInsuredValue() == null) {
                dto.setInsuredValue(new BigDecimal(0));
            }
            //总运费 = 标准运费+保价费
            dto.setTotalFreight(dto.getStandardValue().add(dto.getInsuredValue()));
            OmsOrderApiDTO omsOrderApiDTO = orikaBeanMapper.map(dto, OmsOrderApiDTO.class);
            if (userCarda != null) {
                omsOrderApiDTO.setIsRealName(1);
                omsOrderApiDTO.setRealName(userCarda.getName());
                omsOrderApiDTO.setIdNo(userCarda.getCardNum());
                omsOrderApiDTO.setIdNoType(userCarda.getCardType());
            }
         //   omsOrderApiDTO.setCustomerId(wxUserVo.getNumberId());
            omsOrderApiDTO.setMemberId(wxUserVo.getNumberId());
//            omsOrderApiDTO.setCustomerId(wxUserVo.getId());
            omsOrderApiDTO.setCustomerName(wxUserVo.getNickName());
            omsOrderApiDTO.setFrom(FromEnum.MINIAPP.getCode());
            omsOrderApiDTO.setNeedDispatch(1);//需要调度
            omsOrderApiDTO.setAutoScheduling("1");//需要调度

            //处理寄件人、收件人长度
            omsOrderApiDTO.setSenderName(YlStringUtils.subText(omsOrderApiDTO.getSenderName()));
            omsOrderApiDTO.setReceiverName(YlStringUtils.subText(omsOrderApiDTO.getReceiverName()));
            list.add(omsOrderApiDTO);
        });
        omsOrderBatchApiDto.setOrders(list);
        omsOrderBatchApiDto.setFrom(FromEnum.MINIAPP.getCode());
        //查询会员等级
        QueryWrapper<MemberUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("member_id", getUser().getNumberId());
        MemberUser memberUser = memberUserMapper.selectOne(queryWrapper);
        log.info("requestId==>{},下单,当前用户的等级==>{}", omsOrderBatchApiDto.getMyRequestId(), JSON.toJSONString(memberUser));
        if (memberUser != null) {
            for (OmsOrderApiDTO order : omsOrderBatchApiDto.getOrders()) {
                //处理订单打标
                OrderRecvPrefDTO orderRecvPrefDTO = new OrderRecvPrefDTO();
                List<PreferencesCommonDto.OrderMarkingStatus> markingStatusList = order.getMarkingStatusList();
                if (CollectionUtils.isNotEmpty(markingStatusList)) {
                    orderMarkingHelper.checkMarking(order);
                    BeanUtils.copyProperties(order, orderRecvPrefDTO);
                    orderRecvPrefDTO.setDataSource("XD_WXXCX");
                    order.setOrderRecvPrefDTO(orderRecvPrefDTO);
                }
                buildCiticMonthlySettlementParam(order.getPaymentModeCode(),order.getAccount(),null,order.getExpressTypeCode(),order);

                //微信下单,打标
                List<OrderMarkExpandVO> resourceList = Lists.newArrayList();
                OrderMarkExpandVO expandVO = new OrderMarkExpandVO();
                //构建参数
                expandVO.setFieldName(BaseConstant.CCM_TERMINAL_ORDER_FROM);
                expandVO.setFieldValue("1");//微信
                expandVO.setEffective(true);
                resourceList.add(expandVO);

                order.setMarkFields(resourceList);
                order.setMemberLevel(memberUser.getMemberLevel());
            }
        }
        log.info("请求id==>{}正常渠道-批量订单,远程入参：【{}】", omsOrderBatchApiDto.getMyRequestId(), JSON.toJSONString(list));
        OmsOrderBatchApiVo result = orderFeigntClient.batchSave(omsOrderBatchApiDto).result();
        log.info("请求id==>{}正常渠道-批量订单,远程出参：【{}】", omsOrderBatchApiDto.getMyRequestId(), JSON.toJSONString(result));
        return result;
    }

    public static class AddressType {
        /**
         * 寄件地址类型
         */
        public static final Integer SEND_ADDR_TYPE = 1;
        /**
         * 收件地址类型
         */
        public static final Integer RES_ADDR_TYPE = 2;
    }


}
