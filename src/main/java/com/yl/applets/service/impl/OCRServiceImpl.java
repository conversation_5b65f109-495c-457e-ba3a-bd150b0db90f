package com.yl.applets.service.impl;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRResponse;
import com.yl.applets.service.IOCRService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

@Service
@Slf4j
public class OCRServiceImpl implements IOCRService {

    @Value("${tx.ocr.secretId:AKIDttlSyRVFWfcOzqm60tpgPyPgqQJoaTcc}")
    private String secretId;

    @Value("${tx.ocr.secretKey:kS1S8pRxRuyuDcyCHKGifhcL681u0yqi}")
    private String secretKey;



    @Override
    public IDCardOCRResponse getIDCardInfo(MultipartFile file) {

        try {
            String base64Str = getBase64FromInputStream(file.getInputStream());
            // 实例化一个认证对象，入参需要传入腾讯云账户secretId，secretKey
            Credential cred = new Credential(secretId,secretKey );

            // 实例化要请求产品(以cvm为例)的client对象
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setSignMethod(ClientProfile.SIGN_TC3_256);

            OcrClient ocrClient = new OcrClient(cred,"ap-beijing",clientProfile);

            IDCardOCRRequest idCardOCRRequest = new IDCardOCRRequest();
            //FRONT正面BACK反面
            idCardOCRRequest.setCardSide("FRONT");
            idCardOCRRequest.setImageBase64(base64Str);
            IDCardOCRResponse resp =  ocrClient.IDCardOCR(idCardOCRRequest);
            if(resp!=null){
                return resp;
            }
        } catch (IOException e) {
            log.error("getIDCardInfo异常",e);
        } catch (TencentCloudSDKException e) {
            log.error("getIDCardInfo异常TencentCloudSDKException",e);
        }
        return null;
    }


    public static String getBase64FromInputStream(InputStream in) {
        // 将图片文件转化为字节数组字符串，并对其进行Base64编码处理
        byte[] data = null;
        // 读取图片字节数组
        //Java 7 的编译器和运行环境支持新的 try-with-resources 语句，
        try(ByteArrayOutputStream swapStream = new ByteArrayOutputStream()){
            byte[] buff = new byte[100];
            int rc = 0;
            while ((rc = in.read(buff, 0, 100)) > 0) {
                swapStream.write(buff, 0, rc);
            }
            data = swapStream.toByteArray();
        }catch (Exception e){
            log.error("读取图片字节数组失败",e);
        }
        return new String(Base64.encodeBase64(data));
    }

}
