package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.SpmApiInsuranceTrialDTO;
import com.yl.applets.feign.*;
import com.yl.applets.feign.dto.SpmApiCustomerShippingQuoteTryCalcDTO;
import com.yl.applets.feign.dto.SysCustomerDTO;
import com.yl.applets.feign.dto.citicmonthlysettlement.*;
import com.yl.applets.service.CiticMonthlySettlementService;
import com.yl.applets.vo.CiticSpmCommonCostVO;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/12/12 10:40
 */
@Slf4j
@Service
public class CiticMonthlySettlementServiceImpl implements CiticMonthlySettlementService {

    @Autowired
    public SpmoBusinessInsuranceFeeFeignClient spmoBusinessInsuranceFeeFeignClient;

    @Autowired
    private CiticMonthlySettlementClient citicMonthlySettlementClient;
    @Autowired
    private SpmoCustomerShippingQuoteFeignClient spmoCustomerShippingQuoteFeignClient;
    @Autowired
    private CpCustomerFeignClient cpCustomerFeignClient;

    @Override
    public Result<Integer> bindInfo(CiticMonthlySettlementDTO dto) {
        log.info("月结账号绑定====>dto:[{}]", JSON.toJSONString(dto));
        //1.校验必填
        Integer memberId = dto.getMemberId();
        String account = dto.getAccount();
        if (Objects.isNull(memberId) || StringUtils.isBlank(account) || StringUtils.isBlank(dto.getPassword())) {
            throw new ServiceException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_MUST_PARAM_ERROR);
        }
        return citicMonthlySettlementClient.bindInfo(dto);
    }

    @Override
    public Result<Integer> unBindInfo(CiticMonthlySettlementUpdateDTO dto) {
        log.info("月结账号解绑====>dto:[{}]", JSON.toJSONString(dto));
        //1.校验必填
        Integer memberId = dto.getMemberId();
        if (Objects.isNull(memberId) || StringUtils.isBlank(dto.getId())) {
            throw new ServiceException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_MUST_PARAM_ERROR);
        }
        return citicMonthlySettlementClient.unBindInfo(dto);
    }

    @Override
    public Result<Integer> updateDefaultAccount(CiticMonthlySettlementUpdateDTO dto) {
        log.info("月结账号修改默认账号====>dto:[{}]", JSON.toJSONString(dto));
        //1.校验必填
        Integer memberId = dto.getMemberId();
        if (Objects.isNull(memberId) || StringUtils.isBlank(dto.getId())) {
            throw new ServiceException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_MUST_PARAM_ERROR);
        }
        return citicMonthlySettlementClient.updateDefaultAccount(dto);
    }

    @Override
    public Result<Page<CiticMonthlySettlementResultDTO>> getMonthlySettlementList(CiticMonthlySettlementQueryDTO dto) {

        log.info("月结账号查询列表====>dto:[{}]", JSON.toJSONString(dto));
        Integer size = dto.getSize();
        //保证接口的最大请求数据大小
        if(!Objects.isNull(size) && dto.getSize() > 10){
            dto.setSize(10);
        }
        return citicMonthlySettlementClient.getMonthlySettlementList(dto);
    }

    @Override
    public Result<Boolean> judgeRabbitDelivery(String account) {
        log.info("月结账号判断兔优达====>account:[{}]", account);
        //1.校验必填
        if ( StringUtils.isBlank(account)) {
            throw new ServiceException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_MUST_PARAM_ERROR);
        }
        return  citicMonthlySettlementClient.judgeRabbitDelivery(account);
    }

    @Override
    public Result<Boolean> judgeAccount(String account) {
        log.info("月结账号判断账号是否可用====>account:[{}]", account);
        //1.校验必填
        if ( StringUtils.isBlank(account)) {
            throw new ServiceException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_MUST_PARAM_ERROR);
        }
        return citicMonthlySettlementClient.judgeAccount(account);
    }

    @Override
    public  Result<MonthlySettlementShareCustomerDTO> findShareCustomer(String account) {
        log.info("月结账号查询共享客户信息====>account:[{}]", account);
        //1.校验必填
        if ( StringUtils.isBlank(account)) {
            throw new ServiceException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_MUST_PARAM_ERROR);
        }
        return citicMonthlySettlementClient.findShareCustomer(account);
    }

    @Override
    public Result<CiticMonthlySettlementResultDTO> findDefaultAccount(String memberId) {
        log.info("月结账号查询默认客户信息====>memberId:[{}]", memberId);
        return  citicMonthlySettlementClient.findDefaultAccount(memberId);
    }


    @Override
    public Result<CiticMonthlySettlementResultDTO> findAccountByCusCode(String memberId,String customerCode) {
        log.info("月结账号信息查询客户信息通过客户code====>customerCode:[{}]", customerCode);
        //1.校验必填
        if ( StringUtils.isBlank(customerCode)) {
            throw new ServiceException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_MUST_PARAM_ERROR);
        }
        return citicMonthlySettlementClient.findAccountByCusCode(memberId,customerCode);
    }

    @Override
    public CiticSpmCommonCostVO spmCustomerShippingQuoteCost(SpmApiCustomerShippingQuoteTryCalcDTO spmApiCustomerShippingQuoteTryCalcDTO) {
        String customerCode = spmApiCustomerShippingQuoteTryCalcDTO.getCustomerCode();
        if(StringUtils.isBlank(customerCode)){
            throw new ServiceException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_MUST_CUSTOMERCODE_ERROR);
        }
        SysCustomerDTO result = cpCustomerFeignClient.getSysCustomerByCode(customerCode).result();
        if(Objects.isNull(result) || Objects.isNull(result.getNetworkId())){
            throw new  ServiceException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_MUST_CUSTOMERCODE_ERROR);
        }
        spmApiCustomerShippingQuoteTryCalcDTO.setNetworkId(result.getNetworkId());
        return spmoCustomerShippingQuoteFeignClient.comCostAndWeight(spmApiCustomerShippingQuoteTryCalcDTO).result();
    }
}
