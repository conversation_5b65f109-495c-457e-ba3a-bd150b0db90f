package com.yl.applets.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yl.applets.constant.AppConstants;
import com.yl.applets.dto.CommonSearchApiDTO;
import com.yl.applets.entity.OmsWaybill;
import com.yl.applets.enums.BooleanStatusEnum;
import com.yl.applets.enums.DocumentTypeEnum;
import com.yl.applets.feign.CommonApiClient;
import com.yl.applets.mapper.oms.OmsWaybillMapper;
import com.yl.applets.service.IWaybillService;
import com.yl.applets.utils.ReflectUtil;
import com.yl.applets.vo.OmsWaybillDetailVO;
import com.yl.common.base.enums.SexEnum;
import com.yl.common.base.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-08-06 14:00 <br>
 * @Author: zhipeng.liu
 */
@Service
public class WaybillServiceImpl extends BaseServiceImpl implements IWaybillService {

    //为了实现指定列查询返回,并加快查询速度,设置运单表字段缓存,实属无赖之举..
    private static final Map<String, String> PROPERTYSMAP = propertyMap();

    @Autowired
    private CommonApiClient commonApiClient;

    @Autowired
    private OmsWaybillMapper omsWaybillMapper;


    private void setStatusName(OmsWaybillDetailVO omsWaybillDetailVO) {
        if (omsWaybillDetailVO == null) {
            return;
        }
        if (omsWaybillDetailVO.getIsRealName() != null) {
            omsWaybillDetailVO.setIsRealCnName(BooleanStatusEnum.of(omsWaybillDetailVO.getIsRealName()).getName());
        }
        if (omsWaybillDetailVO.getIdNoType() != null) {
            omsWaybillDetailVO.setIdNoTypeName(DocumentTypeEnum.getNameByCode(omsWaybillDetailVO.getIdNoType()));
        }
        if (omsWaybillDetailVO.getSex() != null) {
            omsWaybillDetailVO.setSexName(SexEnum.getNameByCode(omsWaybillDetailVO.getSex()));
        }
    }

    /**
     * 解析OmsWaybill实体类和YL_OMS_OMS_WAYBILL表字段映射
     * @return
     */
    private static Map<String, String> propertyMap() {
        Map<String, String> map = new HashMap<>(280);
        List<String> allFields = ReflectUtil.getAllFields(OmsWaybill.class);
        allFields.stream().forEach(x -> map.put(x, StringUtils.transToCamel(x)));
        return map;
    }
}
