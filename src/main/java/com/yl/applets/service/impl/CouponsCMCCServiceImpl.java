package com.yl.applets.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.CouponsCMCCDTO;
import com.yl.applets.dto.PromotionUserGetAppletsDto;
import com.yl.applets.entity.WxUser;
import com.yl.applets.enums.ChannelSourceEnum;
import com.yl.applets.feign.PromotionFeighClient;
import com.yl.applets.mapper.applets.MemberUserMapper;
import com.yl.applets.mapper.applets.WxUserMapper;
import com.yl.applets.service.CouponsCMCCService;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-03-14 18:29
 * @Version 1.0
 */
@Service
@Slf4j
public class CouponsCMCCServiceImpl implements CouponsCMCCService {

    @Value("${cmcc.secret.key:jt585VcP45e@uioLhn}")
    private String secretKey;

    @Value("#{'${cmcc.secret.couponIds:}'.split(',')}")
    private List<String> couponIds;

    @Resource
    protected RedissonClient redissonClient;

    @Autowired
    private PromotionFeighClient promotionFeighClient;

    @Autowired
    private MemberUserMapper memberUserMapper;

    @Autowired
    private WxUserMapper wxUserMapper;

    @Autowired
    private com.yl.applets.service.IWxUserService iWxUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> bindCouponsById(CouponsCMCCDTO dto, HttpServletRequest request) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");

        //根据消息头拿到时间戳和签名
        String timeStamp = request.getHeader("timeStamp");
        String sign = request.getHeader("sign");
        log.info("【对接河南移动优惠券】时间戳：{} sign：{}", timeStamp, sign);
        if (StrUtil.hasBlank(timeStamp, sign)) {
            throw new BusinessException(0, ResultCodeEnum.PARAMS_IS_INVALID.getMsg());
        }
        //验签
        String signNew = DigestUtils.md5Hex(DigestUtils.md5Hex(JSON.toJSONString(dto) + timeStamp + secretKey));
        log.info("【对接河南移动优惠券】后端加密参数之后，sign：{}", signNew);

        if (!StringUtils.equals(signNew, sign)) {
            throw new BusinessException(0, ResultCodeEnum.IS_NO_ACCESS.getMsg());
        }

        for (String id : dto.getCouponIds()) {
            if (!couponIds.contains(id)) {
                throw new BusinessException(0, String.format(ResultCodeEnum.PARAM_COUPONS_IDS_NOT_EXIST.getMsg(), id));
            }
        }

        RLock lock = null;

        try {
            //加锁
            lock = redissonClient.getLock(BcRedisKeyEnum.OUTSIDE_CMCC_COUPONS.keyBuilder(dto.getPhone()));
            lock.lock(2, TimeUnit.MINUTES);

            //处理河南移动用户新增
            WxUser wxUser = iWxUserService.handleCMCCBindPhone(new WxUserVo().setOpenid(dto.getOpenId()), dto.getPhone(), requestId, ChannelSourceEnum.CMCC.getKey());
            log.info("处理CMCC用户信息注册，返回user：{}", JSON.toJSONString(wxUser));

            //优惠券中台领取优惠券DTO
            List<PromotionUserGetAppletsDto> list = new ArrayList<>(2);
            dto.getCouponIds().forEach(item -> {
                PromotionUserGetAppletsDto appletsDto = new PromotionUserGetAppletsDto();
                appletsDto.setUserAlias("极兔用户");
                appletsDto.setUserId(wxUser.getId().longValue());
                appletsDto.setUserUuid(wxUser.getOpenid());
                appletsDto.setUserPhone(wxUser.getMobile());
                appletsDto.setNumberId(wxUser.getNumberId().longValue());
                appletsDto.setProId(Long.parseLong(item));
                list.add(appletsDto);
            });

            log.info("请求id==>{},领取优惠券,入参==>{}", requestId, JSON.toJSONString(list));
            Result<List<Long>> result = promotionFeighClient.getCouponByCMCC(list);
            log.info("请求id==>{},领取优惠券,出参==>{}", requestId, JSON.toJSONString(result));
            if (result.getCode() != 1) {
                throw new BusinessException(0, result.getMsg());
            }
            return Result.success(Boolean.TRUE);

        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
