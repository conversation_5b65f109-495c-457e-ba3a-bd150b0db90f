package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.dto.ForwardRequest;
import com.yl.applets.dto.MyStaffCheckAddressDTO;
import com.yl.applets.dto.MyStaffPageDTO;
import com.yl.applets.dto.MyStaffUpdateDefaultDTO;
import com.yl.applets.dto.lmdm.SysStaffDTO;
import com.yl.applets.entity.MyStaff;
import com.yl.applets.entity.MyStaffScanRecord;
import com.yl.applets.enums.StaffChannelTypeEnum;
import com.yl.applets.enums.StaffExclusiveTypeEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.EdiFeignClient;
import com.yl.applets.feign.OldLmdmFeignClient;
import com.yl.applets.mapper.applets.MyStaffMapper;
import com.yl.applets.mapper.applets.WxUserMapper;
import com.yl.applets.service.IMyStaffScanRecordService;
import com.yl.applets.service.IMyStaffService;
import com.yl.applets.service.IWxUserService;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.FrequentAddressVO;
import com.yl.applets.vo.MyStaffVO;
import com.yl.applets.vo.RandomStaffVo;
import com.yl.applets.vo.WxUserVo;
import com.yl.applets.vo.lmdm.SysNetworkVO;
import com.yl.applets.vo.lmdm.SysStaffVO;
import com.yl.common.base.enums.EnableEnum;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.CollectionUtils;
import com.yl.common.base.util.GenerationIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 专属会员 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2020-01-07
 */
@Service
@Slf4j
public class MyStaffServiceImpl extends ServiceImpl<MyStaffMapper, MyStaff> implements IMyStaffService {


    @Autowired
    private IWxUserService wxUserService;


    @Autowired
    private OldLmdmFeignClient sysNetworkFeignClient;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Autowired
    private IMyStaffScanRecordService myStaffScanRecordService;

    @Autowired
    private EdiFeignClient ediFeignClient;

    @Autowired
    private MyStaffMapper myStaffMapper;
    @Autowired
    private WxUserMapper wxUserMapper;


    @Override
    public Boolean bindStaff(String staffCode, WxUserVo user,Integer isExclusive) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        StopWatch stopWatch = new StopWatch();
        List<Long> ids = wxUserService.getIdsByIdAndNumberId(user.getNumberId());
        stopWatch.start("查询我的专属业务员");
        List<MyStaff> myStaff1s = this.list(new LambdaQueryWrapper<MyStaff>().in(MyStaff::getUserId, ids).eq(MyStaff::getStaffNo, staffCode));
        stopWatch.stop();
        log.info("requestId:{},查询我的专属业务员耗时:{},结果:{}",requestId,stopWatch.getLastTaskTimeMillis(), JsonUtils.toJson(myStaff1s));
        MyStaff myStaff1=new MyStaff();
        //插入常用业务员
        if (myStaff1s != null && !myStaff1s.isEmpty()) {
            myStaff1s = myStaff1s.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getStaffNo()))), ArrayList::new));
            myStaff1 = myStaff1s.get(0);
            myStaff1.setUpdateTime(LocalDateTime.now());
            stopWatch.start("修改我的专属业务员");
            this.updateById(myStaff1);
            stopWatch.stop();
            log.info("requestId:{},修改我的专属业务员耗时:{}",requestId,stopWatch.getLastTaskTimeMillis());
        } else {
            MyStaff myStaff = saveStaff(staffCode, user, requestId, stopWatch);
            log.info("requestId:{},保存数据==>{}",requestId,JSON.toJSONString(myStaff));
            if(myStaff!=null){
                myStaff.setIsExclusive(isExclusive);
                myStaff.setUserChannel(StaffChannelTypeEnum.WX.getCode());
                this.save(myStaff);
            }
            log.info("requestId:{},新增我的专属业务员耗时:{}",requestId,stopWatch.getLastTaskTimeMillis());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RandomStaffVo getStaffAndBinding(String randomCode,WxUserVo userVo) {
        Result<RandomStaffVo> infoByRandomCode = channelApiFeignClient.getInfoByRandomCode(randomCode);
        log.info("根据随机码查询业务员,入参==>{},出参==>{}",randomCode,JSON.toJSONString(infoByRandomCode));
        RandomStaffVo result = infoByRandomCode.result();
        if(result!=null){
            //2.校验专属小哥生效
            String requestId = UUID.randomUUID().toString().replace("-", "");
            SysStaffDTO sysStaffDTO = new SysStaffDTO();
            sysStaffDTO.setCode(result.getCode());
            ForwardRequest request=new ForwardRequest();
            request.setRequestId(requestId);
            request.setRequestUri("/lmdmapi/sysStaff/detail");
            request.setBody(JSON.toJSONString(sysStaffDTO));
            log.info("请求id==>{}接口入参==>{}",requestId,JSON.toJSONString(request));
            Result<?> forward = channelApiFeignClient.forward(request);
            log.info("请求id==>{}接口返回==>{}",requestId,JSON.toJSONString(forward));
            Object resultO = forward.result();
            SysStaffVO sysStaffVO = JSON.parseObject(JSON.toJSONString(resultO), SysStaffVO.class);
            if(sysStaffVO != null && sysStaffVO.getIsEnable().equals(EnableEnum.NORMAL.getCode())){
                result.setIsEnable(1);//启用
            }else {
                result.setIsEnable(2);//禁用
            }
            //2. 绑定业务员信息
            bindStaff(result.getCode(),userVo, StaffExclusiveTypeEnum.NEW.getCode());
            //3. 保存用户扫新码的记录
            saveStaffScanRecord(randomCode,userVo);
            return result;
        }
        throw new BusinessException("未绑定业务员！");
    }

    @Override
    public Boolean queryStaff(String staffCode, WxUserVo user) {
        //1.是否是我的专属小哥
        List<Long> ids = wxUserService.getIdsByIdAndNumberId(user.getNumberId());
        List<MyStaff> myStaff1s = this.list(new LambdaQueryWrapper<MyStaff>().in(MyStaff::getUserId, ids).eq(MyStaff::getStaffNo, staffCode));
        if(CollectionUtils.isEmpty(myStaff1s)){
            throw new BusinessException(ResultCodeEnum.DATA_NOT_FOUND);
        }
        //2.校验专属小哥生效
        String requestId = UUID.randomUUID().toString().replace("-", "");
        SysStaffDTO sysStaffDTO = new SysStaffDTO();
        sysStaffDTO.setCode(staffCode);
        ForwardRequest request=new ForwardRequest();
        request.setRequestId(requestId);
        request.setRequestUri("/lmdmapi/sysStaff/detail");
        request.setBody(JSON.toJSONString(sysStaffDTO));
        log.info("请求id==>{}接口入参==>{}",requestId,JSON.toJSONString(request));
        Result<?> forward = channelApiFeignClient.forward(request);
        log.info("请求id==>{}接口返回==>{}",requestId,JSON.toJSONString(forward));
        Object resultO = forward.result();
        SysStaffVO sysStaffVO = JSON.parseObject(JSON.toJSONString(resultO), SysStaffVO.class);
        if(sysStaffVO != null && sysStaffVO.getIsEnable().equals(EnableEnum.NORMAL.getCode())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    public void saveStaffScanRecord(String randomCode, WxUserVo userVo) {
        MyStaffScanRecord record=new MyStaffScanRecord();
        record.setId(GenerationIdUtil.getId());
        record.setRandomCode(randomCode);
        record.setUserName(userVo.getNickName());
        record.setMobile(userVo.getMobile());
        record.setUserId(userVo.getNumberId().longValue());
        record.setUserChannel(StaffChannelTypeEnum.WX.getCode());
        record.setCreateTime(LocalDateTime.now());
        myStaffScanRecordService.save(record);
    }

    private MyStaff saveStaff(String staffCode,WxUserVo user,String requestId,StopWatch stopWatch){
        MyStaff myStaff=null;
        SysStaffDTO sysStaffDTO = new SysStaffDTO();
        sysStaffDTO.setCode(staffCode);
        log.info("requestId==>{},获取基础数据业务员信息入参：{}",requestId,JsonUtils.toJson(sysStaffDTO));
        stopWatch.start("获取基础数据业务员信息");
//        SysStaffVO sysStaffVO = sysNetworkFeignClient.getStaffDetail(sysStaffDTO).result();
        ForwardRequest request=new ForwardRequest();
        request.setRequestId(requestId);
        request.setRequestUri("/lmdmapi/sysStaff/detail");
        request.setBody(JSON.toJSONString(sysStaffDTO));
        log.info("请求id==>{}接口入参==>{}",requestId,JSON.toJSONString(request));
        Result<?> forward = channelApiFeignClient.forward(request);
        log.info("请求id==>{}接口返回==>{}",requestId,JSON.toJSONString(forward));
        Object resultO = forward.result();
        SysStaffVO sysStaffVO= JSON.parseObject(JSON.toJSONString(resultO), SysStaffVO.class);
        stopWatch.stop();
        log.info("requestId==>{},获取基础数据业务员信息耗时：{},结果：{}",requestId,stopWatch.getLastTaskTimeMillis(), JsonUtils.toJson(sysStaffVO));
        if (sysStaffVO != null && sysStaffVO.getIsEnable().equals(EnableEnum.NORMAL.getCode())) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            log.info("requestId==>{},业务员二维码获取基础数据网点信息入参：{}",requestId,sysStaffVO.getNetworkId());
            stopWatch.start("业务员二维码获取基础数据网点信息");
//            SysNetworkVO sysNetworkVO1 = sysNetworkFeignClient.getDetailById(sysStaffVO.getNetworkId()).result();
            String requestIds = UUID.randomUUID().toString().replaceAll("-", "");
            ForwardRequest requests=new ForwardRequest();
            requests.setRequestId(requestIds);
            requests.setRequestUri("/lmdmapi/network/detail");
            requests.setBody(sysStaffVO.getNetworkId()+"");
            //调接口
            log.info("请求id==>{}接口入参==>{}",requestIds, JSON.toJSONString(requests));
            Result<?> forwards = channelApiFeignClient.forward(requests);
            log.info("请求id==>{}接口返回==>{}",requestIds,JSON.toJSONString(forwards));
            Object results = forwards.result();
            SysNetworkVO sysNetworkVO1= JSON.parseObject(JSON.toJSONString(results), SysNetworkVO.class);
            stopWatch.stop();
            log.info("requestId==>{},业务员二维码获取基础数据网点信息耗时：{},结果：{}",requestId,stopWatch.getLastTaskTimeMillis(),JsonUtils.toJson(sysNetworkVO1));
            myStaff=new MyStaff();
            myStaff.setId(GenerationIdUtil.getId());
            myStaff.setMobile(sysStaffVO.getMobile());
            myStaff.setCreateTime(LocalDateTime.now());
            myStaff.setName(sysStaffVO.getName());
            myStaff.setNetworkCode(sysNetworkVO1.getCode());
            myStaff.setNetworkId(sysNetworkVO1.getId());
            myStaff.setNetworkName(sysNetworkVO1.getName());
            myStaff.setStaffNo(sysStaffVO.getCode());
            myStaff.setUserId(user.getId().longValue());
            myStaff.setUpdateTime(LocalDateTime.now());
            if (sysNetworkVO1.getStartBusinessTime() != null) {
                myStaff.setStartBusinessTime(formatter.format(sysNetworkVO1.getStartBusinessTime()));
            }
            if (sysNetworkVO1.getEndBusinessTime() != null) {
                myStaff.setEndBusinessTime(formatter.format(sysNetworkVO1.getEndBusinessTime()));
            }
        }
        return myStaff;
    }

    @Override
    public  Result<Page<MyStaffVO>> getMyStaffList(MyStaffPageDTO page) {
        List<Long> userIds = wxUserService.getIdsByIdAndNumberId(page.getNumberId());
        page.setUserIds(userIds);
        return channelApiFeignClient.list(page);
    }

    @Override
    public Result<String> updateDefaultStaff(MyStaffUpdateDefaultDTO dto) {
        return channelApiFeignClient.updateDefaultStaff(dto);
    }

    @Override
    public Result<Boolean> checkSendAddress(MyStaffCheckAddressDTO dto) {
        return channelApiFeignClient.checkSendAddress(dto);
    }

    @Override
    public Result<List<FrequentAddressVO>> getFrequentAddressByUserId(Integer userId) {
        return  channelApiFeignClient.getFrequentAddressByUserId(userId);
    }
}
