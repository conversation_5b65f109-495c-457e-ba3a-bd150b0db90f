package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yl.applets.constant.AppCacheConstant;
import com.yl.applets.dto.ForwardRequest;
import com.yl.applets.enums.AppQueryTypeEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.OldLmdmFeignClient;
import com.yl.applets.service.IAppBaseDataService;
import com.yl.applets.vo.lmdm.AppBaseDataVO;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019-08-23
 */
@Service
@Slf4j
public class AppBaseDataServiceImpl implements IAppBaseDataService {
    @Autowired
    private OldLmdmFeignClient appBaseDataFeignClient;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Override
    public AppBaseDataVO getAppList() {
        //查询缓存，如果没有，则查库

        try {
            Object redisObj = com.yl.redis.util.RedisUtil.get(AppCacheConstant.SYSTEM_NAME + AppCacheConstant.SYS_BASE_DATA_DETAIL);
            if (Objects.nonNull(redisObj)) {
                return (AppBaseDataVO) redisObj;
            }
        } catch (Exception e) {
            log.warn("获取redis失败,走查库",e);
        }

        //如果没有的话，查询数据库
//        AppBaseDataVO baseDataVO = appBaseDataFeignClient.getAppList(AppQueryTypeEnum.ALL.getCode()).result();
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        ForwardRequest request=new ForwardRequest();
        request.setRequestId(requestId);
        request.setRequestUri("/lmdmapi/app/baseData/list");
        request.setBody(AppQueryTypeEnum.ALL.getCode()+"");
        log.info("请求id==>{}接口入参==>{}",requestId,JSON.toJSONString(request));
        Result<?> forward = channelApiFeignClient.forward(request);
        log.info("请求id==>{}接口返回==>{}",requestId,JSON.toJSONString(forward));
        Object resultO = forward.result();
        AppBaseDataVO baseDataVO= JSON.parseObject(JSON.toJSONString(resultO), AppBaseDataVO.class);
        //放进缓存
        com.yl.redis.util.RedisUtil.set(AppCacheConstant.SYSTEM_NAME + AppCacheConstant.SYS_BASE_DATA_DETAIL, baseDataVO);
        return baseDataVO;
    }
}
