package com.yl.applets.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yl.applets.config.WxMaConfiguration;
import com.yl.applets.config.WxMaProperties;
import com.yl.applets.dto.BindPhoneDTO;
import com.yl.applets.dto.MemberUserOffDto;
import com.yl.applets.dto.PhoneLoginDTO;
import com.yl.applets.dto.WxUserDTO;
import com.yl.applets.entity.MemberUser;
import com.yl.applets.entity.WxUser;
import com.yl.applets.enums.ChannelSourceEnum;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.OssApi;
import com.yl.applets.mapper.applets.MemberUserMapper;
import com.yl.applets.service.CacheService;
import com.yl.applets.service.IAppLoginService;
import com.yl.applets.service.IWxUserService;
import com.yl.applets.stream.MemberUserPushOutputProcessor;
import com.yl.applets.stream.dto.MemberUserMqDto;
import com.yl.applets.utils.*;
import com.yl.applets.vo.WxUserVo;
import com.yl.applets.vo.wx.WxGetnumResult;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.YlPreconditions;
import com.yl.redis.util.RedisUtil;
import jodd.net.URLDecoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.TreeMap;
import java.util.UUID;

/**
 * 登录
 *
 * <AUTHOR>
 * @since 2019-06-19
 */
@Service
@Slf4j
public class AppLoginServiceImpl extends BaseServiceImpl implements IAppLoginService {

    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private WxMaProperties wxMaProperties;

    @Autowired
    private OrikaBeanMapper beanMapper;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Autowired
    private MemberUserMapper memberUserMapper;

    @Value("${jwt.expiration:2592000}")
    private Long expiration;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private MemberUserPushOutputProcessor memberUserPushOutputProcessor;

    @Autowired
    private OssApi ossApi;

    @Override
    public Boolean logout() {
        try {
            String token = RequestContextHolder.getRequestAttributes().getAttribute(BcRedisKeyEnum.OAUTH_APP_TOKEN.name(), 0).toString();
            if(StringUtils.isNotEmpty(token)){
                RedisUtil.delete(token);
            }
        } catch (Exception e) {
            log.error("登出错误:{}",e.getMessage());
        }
        return true;
    }

    @Override
    public WxUserVo bindPhone(BindPhoneDTO bindPhoneDTO, WxUserVo user, Boolean flag) {
        //本次请求id
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        //解密
        String appId = wxMaProperties.getConfigs().get(0).getAppid();
        final WxMaService wxService = WxMaConfiguration.getMaService(appId);
        String phoneNumber = "";
        try {
            if (flag) {
                Result<String> result = channelApiFeignClient.getToken(bindPhoneDTO.getCode());
                log.info("当前用户调用中台返回手机号:{}", JSON.toJSONString(result));
                phoneNumber = result.result();
            } else {
                WxMaPhoneNumberInfo phoneNoInfo = wxService.getUserService().getPhoneNoInfo(Base64Utils.decode(bindPhoneDTO.getUuid()), URLDecoder.decode(bindPhoneDTO.getEncryptedData()), bindPhoneDTO.getIv());
                phoneNumber = phoneNoInfo.getPhoneNumber();
            }
        } catch (Exception e) {
            log.warn("requestId：{},绑定手机号解密失败", requestId, e);
            throw new ServiceException(ServiceErrCodeEnum.MOBILE_BIND_FAIL);
        }
        if (StrUtil.isBlank(phoneNumber)) {
            log.warn("requestId：{},绑定手机号解密为null", requestId);
            throw new ServiceException(ServiceErrCodeEnum.MOBILE_BIND_FAIL);
        }
        return wxUserService.handleBindPhone(user, phoneNumber, requestId);
    }


    @Override
    public WxUserVo wxLogin(WxUserDTO wxUserDTO, HttpServletRequest request) {
        StopWatch stopWatch = new StopWatch();
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        log.info("登录-requestId==>{},入参==>{}", requestId, JSON.toJSONString(wxUserDTO));
        if (org.apache.commons.lang3.StringUtils.isBlank(wxUserDTO.getCode())) {
            throw new BusinessException(ResultCodeEnum.FAIL);
        }

        WxMaJscode2SessionResult session = this.getWxUserInfo(wxUserDTO,requestId);
        //加锁，存在抛异常,不存在缓存加锁
        String lockKey = BcRedisKeyEnum.APPLETS_USER_LOGIN_LOCK.keyBuilder(session.getOpenid());
        if(!cacheService.lock(lockKey)){
            log.info("登录操作频繁，requestID:{},openId:{}", requestId,session.getOpenid());
            throw new BusinessException(ResultCodeEnum.LOGIN_LOCK_ERROR);
        }

        WxUser wxUser = null;
        try {
            QueryWrapper<WxUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("openid", session.getOpenid());
            queryWrapper.eq("is_delete",0);
            stopWatch.start("查询用户是否存在");
            log.info("查询用户-requestId==>{},入参==>{}", requestId, JsonUtils.toJson(queryWrapper));
            wxUser = wxUserService.getOne(queryWrapper);
            stopWatch.stop();
            log.info("查询用户-requestId==>{},耗时：{},结果==>{}", requestId,stopWatch.getLastTaskTimeMillis(),JsonUtils.toJson(wxUser));
            if (wxUser == null) {
                //用户表插入记录
                wxUser = new WxUser();
                wxUser.setCreateTime(LocalDateTime.now());
                wxUser.setUpdateTime(LocalDateTime.now());
                wxUser.setGender(wxUserDTO.getGender());
                wxUser.setAvatarUrl(wxUserDTO.getAvatarUrl());
                wxUser.setCity(wxUserDTO.getCity());
                wxUser.setProvince(wxUserDTO.getProvince());
                wxUser.setNickName(Base64.encodeBase64String(wxUserDTO.getNickName().getBytes(StandardCharsets.UTF_8)));
                wxUser.setOpenid(session.getOpenid());
                wxUser.setUnionid(session.getUnionid() == null ? session.getOpenid() : session.getUnionid());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(wxUserDTO.getMobile())) {
                    wxUser.setMobile(wxUser.getMobile());
                }
                wxUser.setIsDelete(0);
                stopWatch.start("保存用户");
                wxUserService.save(wxUser);
                //补丁，迁移后没有返回用户id,oracle自增id由序列和触发器生成
                wxUser = wxUserService.getOne(queryWrapper);
                stopWatch.stop();
                log.info("用户入库成功-requestId==>{},耗时：{}", requestId,stopWatch.getLastTaskTimeMillis());
            }else{
                wxUser.setUpdateTime(LocalDateTime.now());
                wxUser.setGender(wxUserDTO.getGender());
                wxUser.setAvatarUrl(wxUserDTO.getAvatarUrl());
                wxUser.setCity(wxUserDTO.getCity());
                wxUser.setProvince(wxUserDTO.getProvince());
                wxUser.setNickName(Base64.encodeBase64String(wxUserDTO.getNickName().getBytes(StandardCharsets.UTF_8)));
                wxUser.setOpenid(session.getOpenid());
                wxUser.setUnionid(session.getUnionid() == null ? session.getOpenid() : session.getUnionid());
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(wxUserDTO.getMobile())) {
                    wxUser.setMobile(wxUser.getMobile());
                }
                wxUser.setIsDelete(0);
                stopWatch.start("更新用户");
                wxUserService.updateById(wxUser);
                stopWatch.stop();
                log.info("用户更新成功-requestId==>{},耗时：{}", requestId,stopWatch.getLastTaskTimeMillis());
            }
        } catch (IllegalStateException e) {
            log.error("requestId==>{},登录失败：{}", requestId,e);
            throw new BusinessException(ResultCodeEnum.FAIL);
        }finally {
            //释放锁
            cacheService.unlock(lockKey);
        }
        WxUserVo wxUserVo = beanMapper.map(wxUser, WxUserVo.class);
//        wxUserVo.setSessionKey(session.getSessionKey());
        wxUserVo.setNickName( new String(Base64.decodeBase64(wxUser.getNickName()), StandardCharsets.UTF_8));
        wxUserVo.setToken(JWTUtils.generateById(wxUser.getId(), expiration));
        //将注册时间更新到当前渠道的用户缓存中
        MemberUser memberUser = memberUserMapper.selectOne(new QueryWrapper<MemberUser>().eq("member_id", wxUserVo.getNumberId()));
        if(memberUser!=null){
            wxUserVo.setCreateTime(memberUser.getCreateTime());
        }
        RedisUtil.setEx(BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(wxUserVo.getId().toString()), JSON.toJSONString(wxUserVo), expiration);
        log.info("requestId==>{},登录成功,总耗时：{},结果：{}",requestId,stopWatch.getTotalTimeMillis(),JsonUtils.toJson(wxUserVo));


        //手机号加密
        if(StringUtils.isNotBlank(wxUserVo.getMobile())){
            wxUserVo.setMobile(wxUserVo.getMobile().replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2"));
        }

        return wxUserVo;
    }



    @Override
    public WxUserVo wxLoginV2(WxUserDTO wxUserDTO, HttpServletRequest request) {
        StopWatch stopWatch = new StopWatch();
        //本次请求id
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        WxMaJscode2SessionResult session = this.getWxUserInfo(wxUserDTO,requestId);
        log.info("requestId==>{},获取sessionKey==>{}",requestId,JSON.toJSONString(session));
        WxUserVo wxUserVo=new WxUserVo();
        wxUserVo.setOpenid(session.getOpenid());
        wxUserVo.setUnionid(session.getUnionid() == null ? session.getOpenid() : session.getUnionid());
        wxUserVo.setNickName("极兔用户");
        wxUserVo.setUuid(Base64Utils.encode(session.getSessionKey()));//加密sessionKey
        log.info("requestId==>{},登录成功,总耗时：{},耗时详情：{}结果：{}",requestId,stopWatch.getTotalTimeMillis(),stopWatch.prettyPrint(),JsonUtils.toJson(wxUserVo));

        //手机号加密
        if(StringUtils.isNotBlank(wxUserVo.getMobile())){
            wxUserVo.setMobile(wxUserVo.getMobile().replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2"));
        }
        return wxUserVo;
    }

    /**
     * 功能描述:
     * 更新微信用户信息（头像、昵称）——
     * @param wxUserDTO
     * @return:com.yl.applets.vo.WxUserVo
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-04-19 15:42
     */
    @Override
    public WxUserVo update(WxUserDTO wxUserDTO,WxUserVo user) {
        StopWatch stopWatch = new StopWatch();
        String requestId = UUID.randomUUID().toString().replace("-","");
        log.info("requestId:{},更新微信用户信息入参",requestId);
        stopWatch.start("更新微信用户信息查库用户");
        QueryWrapper<WxUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("openid", user.getOpenid());
        queryWrapper.eq("type", ChannelSourceEnum.WX.getKey());
        queryWrapper.eq("is_delete",0);
        log.info("requestId:{},更新微信用户信息查库用户：入参{}",requestId, JsonUtils.toJson(queryWrapper));
        WxUser wxUser = wxUserService.getOne(queryWrapper);
        stopWatch.stop();
        log.info("requestId:{},更新微信用户信息查库用户结果：{},耗时：{}",requestId,JsonUtils.toJson(wxUser),stopWatch.getLastTaskTimeMillis());
        if (wxUser == null) {
            throw new ServiceException(ResultCodeEnum.USER_NOT_EXIST);
        }
//        WxMaJscode2SessionResult session = this.getWxUserInfo(wxUserDTO,requestId);
        wxUser.setUpdateTime(LocalDateTime.now());
        wxUser.setGender(wxUserDTO.getGender());
        wxUser.setAvatarUrl(wxUserDTO.getAvatarUrl());
        wxUser.setCity(wxUserDTO.getCity());
        wxUser.setProvince(wxUserDTO.getProvince());
        wxUser.setNickName(Base64.encodeBase64String(wxUserDTO.getNickName().getBytes(StandardCharsets.UTF_8)));
//        if(StringUtils.isNotEmpty(session.getUnionid())&&!session.getUnionid().equals(wxUser.getUnionid())){
//            wxUser.setUnionid(session.getUnionid());
//        }
        wxUser.setZone(wxUserDTO.getZone());
        wxUser.setEquipmentInfo(wxUserDTO.getEquipmentInfo());
        stopWatch.start("更新用户");
        if (wxUserService.updateById(wxUser)) {
            WxUser byId = wxUserService.getById(wxUser.getId());
            byId.setNickName(byId.getNickName()=="极兔用户"?byId.getNickName():new String(Base64.decodeBase64(byId.getNickName()), StandardCharsets.UTF_8));
            MemberUserMqDto dto = beanMapper.map(byId, MemberUserMqDto.class);
            dto.setMqType("update_info");
            memberUserPushOutputProcessor.sendMemberUserPushMessage(dto);
        }
        stopWatch.stop();
        WxUserVo wxUserVo = beanMapper.map(wxUser,WxUserVo.class);
        wxUserVo.setSessionKey(null);
        wxUserVo.setNickName(wxUserVo.getNickName()=="极兔用户"?wxUserVo.getNickName():new String(Base64.decodeBase64(wxUser.getNickName()), StandardCharsets.UTF_8));
        wxUserVo.setToken(user.getToken());
        //将注册时间更新到当前渠道的用户缓存中
        MemberUser memberUser = memberUserMapper.selectOne(new QueryWrapper<MemberUser>().eq("member_id", wxUserVo.getNumberId()));
        if(memberUser!=null){
            wxUserVo.setCreateTime(memberUser.getCreateTime());
        }
        RedisUtil.setEx(BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(wxUserVo.getId().toString()), JSON.toJSONString(wxUserVo), expiration);
        log.info("requestId==>{},更新微信用户信息成功：耗时：{},结果：{}",requestId,stopWatch.prettyPrint(),JsonUtils.toJson(wxUserVo));

        //处理下头像的问题，兼容老版本，以https来的数据就是头像地址,否则则是去oss查询
        try {
            if (StringUtils.isNotBlank(wxUserVo.getAvatarUrl())&&!wxUserVo.getAvatarUrl().startsWith("http")) {
                long start =System.currentTimeMillis();
                Result<List<String>> result = ossApi.getDownloadSignedUrl(Collections.singletonList(wxUserVo.getAvatarUrl()));
                long end =System.currentTimeMillis();
                log.info("update用户===》{}，上传微信头像，调用oss解析长链接，入参：{}，出参{}，耗时：{}",JSON.toJSONString(wxUserVo.getNumberId()),
                        JSON.toJSONString(wxUserVo.getAvatarUrl()), JSON.toJSONString(result),(end-start));
                if (result != null && ResultCodeEnum.SUCCESS.getCode() == result.getCode()){
                    String url = result.getData().get(0);
                    wxUserVo.setAvatarUrl(url);
                }
            }
        } catch (Exception e) {
            log.warn("update用户===》{}，上传微信头像失败，调用oss解析长链接，入参：{}",JSON.toJSONString(wxUserVo.getNumberId()),
                    JSON.toJSONString(wxUserVo.getAvatarUrl()));
        }
        return wxUserVo;
    }

    @Override
    public WxUserVo phoneLogin(PhoneLoginDTO phoneLoginDTO) {
        //校验验证码
        String key = BcRedisKeyEnum.APPLETS_VERIFYCODE_BINDMOBILE.keyBuilder(phoneLoginDTO.getMobile());
        YlPreconditions.checkArgument(phoneLoginDTO.getVerificationCode().equals(RedisUtil.get(key)), new ServiceException(ServiceErrCodeEnum.VERIFICATIONCODE_ERROR));
        RedisUtil.delete(key);
        //查询存在就登录/不存在注册并登录
        QueryWrapper<WxUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mobile", phoneLoginDTO.getMobile());
        queryWrapper.eq("is_delete",0);
        WxUser wxUser = wxUserService.getOne(queryWrapper);
        if(wxUser == null){
            //用户表插入记录
            wxUser = new WxUser();
            wxUser.setOpenid("");
            wxUser.setUnionid("");
            wxUser.setNickName("极兔用户");
            wxUser.setCreateTime(LocalDateTime.now());
            wxUser.setUpdateTime(LocalDateTime.now());
            wxUser.setMobile(phoneLoginDTO.getMobile());
            wxUser.setIsDelete(0);
            wxUserService.save(wxUser);
            //补丁  2021年3月25日01:49:49  数据迁移后新用户没有ID，再查一次，后续优化
            wxUser = wxUserService.getOne(queryWrapper);
        }
        WxUserVo wxUserVo = beanMapper.map(wxUser,WxUserVo.class);
        wxUserVo.setToken(JWTUtils.generateById(wxUser.getId(), expiration));
        //将注册时间更新到当前渠道的用户缓存中
        MemberUser memberUser = memberUserMapper.selectOne(new QueryWrapper<MemberUser>().eq("member_id", wxUserVo.getNumberId()));
        if(memberUser!=null){
            wxUserVo.setCreateTime(memberUser.getCreateTime());
        }
        RedisUtil.setEx(BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(wxUserVo.getId().toString()), JSON.toJSONString(wxUserVo), expiration);
        return wxUserVo;
    }

    @Override
    public Boolean logoff(WxUserVo user) {
        try {
            StopWatch stopWatch = new StopWatch();
            String requestId = UUID.randomUUID().toString().replace("-","");
            log.info("requestId:{},注销用户信息入参",requestId);
            stopWatch.start("注销用户信息查库用户");
            user.setType(ChannelSourceEnum.WX.getKey());
            Boolean logoff = channelApiFeignClient.logoff(user).result();
            stopWatch.stop();
            log.info("requestId==>{},注销微信用户信息成功：耗时：{},结果：{}",requestId,stopWatch.prettyPrint(),logoff);
            if(logoff){
                //清除用户登陆缓存信息
                String token = RequestContextHolder.getRequestAttributes().getAttribute(BcRedisKeyEnum.OAUTH_APP_TOKEN.name(), 0).toString();
                if(StringUtils.isNotEmpty(token)){
                    RedisUtil.delete(token);
                }
            }else {
                return false;
            }
        } catch (Exception e) {
            log.error("注销错误:{}",e.getMessage());
            return false;
        }
        return true;
    }

    @Override
    public Result<MemberUserOffDto> isOff(String mobile) {
        return channelApiFeignClient.isOff(mobile);
    }


    /**
     * 功能描述:
     * 获取微信用户openid、unionid、sessionkey
     * @param wxUserDTO
     * @param requestId
     * @return:cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-04-19 14:24
     */
    @Override
    public WxMaJscode2SessionResult getWxUserInfo(WxUserDTO wxUserDTO,String requestId) {
        StopWatch stopWatch = new StopWatch();
        String appId = wxMaProperties.getConfigs().get(0).getAppid();
        final WxMaService wxService = WxMaConfiguration.getMaService(appId);
        WxMaJscode2SessionResult session = null;
        try {
            stopWatch.start("调用微信");
            log.info("调用微信-requestId==>{},入参==>{}", requestId, wxUserDTO.getCode());
            session = wxService.getUserService().getSessionInfo(wxUserDTO.getCode());
            stopWatch.stop();
            log.info("调用微信获取session-requestId==>{},耗时==>{}", requestId,stopWatch.getLastTaskTimeMillis());
        } catch (Exception e) {
            log.error("登录失败：{}", e);
        }
        if(session==null){
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
        return session;
    }


}
