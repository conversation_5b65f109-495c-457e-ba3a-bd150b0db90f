package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.ForwardRequest;
import com.yl.applets.entity.lmdm.SysSettlementDestination;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.OldLmdmFeignClient;
import com.yl.applets.service.IAppSettlementDestinationService;
import com.yl.applets.vo.lmdm.AppSettlementDestinationVO;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019-08-23
 */
@Service
@Slf4j
public class AppSettlementDestinationServiceImpl implements IAppSettlementDestinationService {

    @Autowired
    private OldLmdmFeignClient sysSettlementDestinationFeignClient;
    @Autowired
    private OrikaBeanMapper orikaBeanMapper;


    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Override
    public List<AppSettlementDestinationVO> getAppList() {
//        List<SysSettlementDestination> list = sysSettlementDestinationFeignClient.redisAll().result();
        String requestId = UUID.randomUUID().toString().replace("-", "");
        ForwardRequest requests=new ForwardRequest();
        requests.setRequestId(requestId);
        requests.setRequestUri("/lmdmapi/settlementDestination/redisAll");
        requests.setBody("");
        //调接口
        log.info("请求id==>{}接口入参==>{}",requestId,JSON.toJSONString(requests));
        Result<?> forwards = channelApiFeignClient.forward(requests);
        log.info("请求id==>{}接口出参==>{}",requestId,JSON.toJSONString(forwards));
        Object results = forwards.result();
        List<SysSettlementDestination> list = JSON.parseArray(JSON.toJSONString(results), SysSettlementDestination.class);
        return orikaBeanMapper.mapAsList(list, AppSettlementDestinationVO.class);
    }

}
