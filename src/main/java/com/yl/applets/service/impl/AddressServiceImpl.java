package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yl.applets.dto.AddressDTO;
import com.yl.applets.dto.AppletsUserSignQueryDto;
import com.yl.applets.entity.Address;
import com.yl.applets.enums.AddressTypeEnum;
import com.yl.applets.enums.DefaultEnum;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.UserSignChannelApiFeignClient;
import com.yl.applets.mapper.applets.AddressMapper;
import com.yl.applets.service.IAddressService;
import com.yl.applets.service.IWxUserService;
import com.yl.applets.utils.SessionUtil;
import com.yl.applets.utils.YlStringUtils;
import com.yl.applets.vo.AddressVo;
import com.yl.applets.vo.AppletsUserSignVo;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.GenerationIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 地址管理
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-20
 */
@Service
@Slf4j
public class AddressServiceImpl extends ServiceImpl<AddressMapper, Address> implements IAddressService {

    @Autowired
    private AddressMapper addressMapper;

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Autowired
    private SessionUtil sessionUtil;

    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private UserSignChannelApiFeignClient signChannelApiFeignClient;

    @Override
    public Page<AddressVo> getPages(Page<Object> page, String keyword, Integer userId,Integer type,List<Long> signs) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id==>{}查询地址薄,入参==>keyword={},userId={},type={},signs={}",requestId,keyword,userId,type,signs);
        Page<AddressVo> result=new Page<>();
        Page<AddressVo> pages = addressMapper.getPages(page, keyword, userId, type,signs);
        BeanUtils.copyProperties(pages,result);
        List<AddressVo> records = pages.getRecords();
        if(records.isEmpty()){
            log.info("请求id==>{}地址为空",requestId);
            return result;
        }
        int i=0;
        for (AddressVo record : records) {
            if(i!=0){
                record.setIsDefault(2);
            }
            i++;
        }
        Map<Integer, List<AddressVo>> groups = records.stream().collect(Collectors.groupingBy(AddressVo::getType));
        List<AddressVo> sendAddress = groups.get(1);
        log.info("请求id==>{}寄件地址==>{}", requestId,JSON.toJSONString(sendAddress));
        List<AddressVo> receiveAddress = groups.get(2);
        log.info("请求id==>{}收件地址==>{}",requestId,JSON.toJSONString(receiveAddress));
        List<AddressVo> list= Lists.newArrayList();
        Set<AddressVo> set= Sets.newHashSet();
        if(sendAddress!=null&&!sendAddress.isEmpty()){
            list.addAll(sendAddress);
            set.addAll(sendAddress);
        }
        List<Long> ids=Lists.newArrayList();
        if(receiveAddress!=null&&!receiveAddress.isEmpty()){
            for (AddressVo receive : receiveAddress) {
                if(set.contains(receive)){
                    ids.add(receive.getId());
                }else {
                    list.add(receive);
                }
            }
        }
        log.info("请求id==>{}去重之后的数据==>{}", requestId,JSON.toJSONString(list));
        result.setRecords(list);
        //清洗数据、删除重复
        if(!ids.isEmpty()){
            disableRepeatData(ids,requestId);
        }
        result.setTotal(pages.getTotal()-ids.size());
        return result;
    }

    @Async
    public void disableRepeatData(List<Long> ids,String requestId) {
        addressMapper.disabledAddress(ids);
        log.info("请求id==>{}清洗数据,删除重复的收件地址",requestId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AddressVo save(AddressDTO dto) {
        WxUserVo wxUserVo = sessionUtil.getUser();
        //查询打标是否正确
        if(dto.getAddressSign()!=null){
            checkAddressSign(dto,wxUserVo);
        }
        //根据numberId查询ids
        List<Long> ids=getIdsById(wxUserVo.getNumberId());
        List<Long> addressIds= Lists.newArrayList();
        AddressVo addressVo = this.getDefault(ids,addressIds);
        Address address = orikaBeanMapper.map(dto, Address.class);
        if (DefaultEnum.YES.getCode() == dto.getIsDefault() && addressVo != null) {
            addressMapper.updateToNonDefault(addressIds);
        }
        //处理收件人名称问题
        address.setName(YlStringUtils.subText(address.getName()));

        address.setId(GenerationIdUtil.getId()).setUserId(wxUserVo.getId()).setCreateBy(wxUserVo.getId())
                .setCreateByName("").setUpdateBy(wxUserVo.getId()).setUpdateByName("").setType(1);
        super.save(address);
        return orikaBeanMapper.map(address, AddressVo.class);
    }

    private void checkAddressSign(AddressDTO dto, WxUserVo wxUserVo) {
        AppletsUserSignQueryDto queryDto=new AppletsUserSignQueryDto();
        queryDto.setMemberId(wxUserVo.getNumberId());
        queryDto.setMemberName(wxUserVo.getNickName());
        queryDto.setMobile(wxUserVo.getMobile());
        Result<List<AppletsUserSignVo>> list = signChannelApiFeignClient.getList(queryDto);
        log.info("查询用户地址标识,入参==>{},出参==>{}",JSON.toJSONString(queryDto),JSON.toJSONString(list));
        List<AppletsUserSignVo> result = list.getData();
        if(CollectionUtils.isEmpty(result)){
            throw new BusinessException(ResultCodeEnum.USER_ADDRESS_SIGN_NULL);
        }
        List<Integer> collect = result.stream().map(AppletsUserSignVo::getSignCode).collect(Collectors.toList());
        if (!collect.contains(dto.getAddressSign())) {
            log.warn("用户标签不存在==>{}",JSON.toJSONString(dto));
            throw new BusinessException(ResultCodeEnum.USER_ADDRESS_SIGN_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(AddressDTO dto) {
        WxUserVo wxUserVo = sessionUtil.getUser();
        Address a = getById(dto.getId());
        if(a==null){
            throw new BusinessException(ServiceErrCodeEnum.OAUTH_NO_ACCESS);
        }
        //查询打标是否正确
        if(dto.getAddressSign()!=null){
            checkAddressSign(dto,wxUserVo);
        }
        //根据numberId查询ids
        List<Long> ids=getIdsById(wxUserVo.getNumberId());
        if(!ids.contains(a.getUserId().longValue())){
            throw new BusinessException(ServiceErrCodeEnum.OAUTH_NO_ACCESS);
        }
        List<Long> addressIds= Lists.newArrayList();
        AddressVo addressVo = this.getDefault(ids,addressIds);
        Address address = orikaBeanMapper.map(dto, Address.class);
        if (DefaultEnum.YES.getCode() == dto.getIsDefault() && addressVo != null) {
            addressMapper.updateToNonDefault(addressIds);
        }
        address.setUpdateBy(wxUserVo.getId()).setUpdateByName("");
        //处理收件人名称长度
        address.setName(YlStringUtils.subText(address.getName()));
        return updateById(address);
    }

    @Override
    public AddressVo getDefault(List<Long> ids,List<Long> addressIds) {
        List<Address> list = addressMapper.selectList(new LambdaQueryWrapper<Address>()
                .in(Address::getUserId, ids).eq(Address::getType, AddressTypeEnum.SEND.getCode())
                .eq(Address::getIsDefault, DefaultEnum.YES.getCode()).orderByDesc(Address::getCreateTime));
        addressIds.addAll(list.stream().map(Address::getId).collect(Collectors.toList()));
        return CollectionUtils.isEmpty(list) ? null : orikaBeanMapper.map(list.get(0), AddressVo.class);
    }

    @Override
    public AddressVo getDefaultByMobile(String mobile) {
        long start = System.currentTimeMillis();
        List<Address> list = addressMapper.getDefaultByMobile(mobile);
        if (System.currentTimeMillis() - start > 3000)
            log.info("查询默认地址耗时{}ms", System.currentTimeMillis() - start);
        return CollectionUtils.isEmpty(list) ? null : orikaBeanMapper.map(list.get(0), AddressVo.class);
    }

    /**
     * 根据numberId查询所有的ids
     * @param numberId
     * @return
     */
    private List<Long> getIdsById(Integer numberId) {
        return wxUserService.getIdsByIdAndNumberId(numberId);
    }
}
