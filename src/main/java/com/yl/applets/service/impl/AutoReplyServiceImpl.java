package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.constant.AppCacheConstant;
import com.yl.applets.dto.AutoReplyDTO;
import com.yl.applets.dto.AutoReplyQueryDto;
import com.yl.applets.dto.WxMaterialDto;
import com.yl.applets.entity.AutoReply;
import com.yl.applets.mapper.applets.AutoReplyMapper;
import com.yl.applets.service.IAutoReplyService;
import com.yl.applets.vo.AutoReplyVO;
import com.yl.applets.vo.WxFreePublishVo;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.GenerationIdUtil;
import com.yl.common.base.util.StringUtils;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.WxType;
import me.chanjar.weixin.common.error.WxError;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.util.json.GsonHelper;
import me.chanjar.weixin.common.util.json.WxGsonBuilder;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.material.WxMpMaterial;
import me.chanjar.weixin.mp.bean.material.WxMpMaterialFileBatchGetResult;
import me.chanjar.weixin.mp.bean.material.WxMpMaterialNewsBatchGetResult;
import me.chanjar.weixin.mp.bean.material.WxMpMaterialUploadResult;
import me.chanjar.weixin.mp.enums.WxMpApiUrl;
import me.chanjar.weixin.mp.util.json.WxMpGsonBuilder;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021<br>
 *
 * @description: 小程序自动回复规则
 * @author: caiyao
 * @create: 2021-05-20 10:11
 */
@Service
@Slf4j
public class AutoReplyServiceImpl implements IAutoReplyService {

    @Autowired
    private AutoReplyMapper autoReplyMapper;

    @Autowired
    private WxMpService wxMpService;

    @Autowired
    public RestTemplate restTemplate;

    private static final String FREE_PUBLISH_URL ="https://api.weixin.qq.com/cgi-bin/draft/batchget";

    /**
     * 新增、更新(1:关注回复 2:收到消息回复)自动回复规则
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result add(AutoReplyDTO dto) {
        AutoReply autoReply = new AutoReply();
        autoReply.setReplyType(dto.getReplyType());
        //1:关注回复 2:收到消息回复
        if (StringUtils.isNotEmpty(dto.getReplyType())) {
            if (dto.getReplyType() == 1 || dto.getReplyType() == 2) {
                QueryWrapper qw = new QueryWrapper();
                qw.eq("reply_type", dto.getReplyType());
                AutoReply autoReply1 = autoReplyMapper.selectOne(qw);
                if (autoReply1 == null) {
                    autoReply.setId(GenerationIdUtil.getId());
                    autoReply.setCreateBy(dto.getCreateBy());
                    autoReply.setCreateTime(LocalDateTime.now());
                    autoReply.setUpdateTime(LocalDateTime.now());
                    List<AutoReplyDTO.ReplyContentDTO> replyContentList = dto.getReplyContentList();
                    for (AutoReplyDTO.ReplyContentDTO replyContentDTO : replyContentList) {
                        if ("text".equals(replyContentDTO.getType()) && StringUtils.isEmpty(replyContentDTO.getText())) {
                            throw new BusinessException("回复内容不能为空");
                        }
                    }
                    autoReply.setReplyContent(JSON.toJSONString(replyContentList));
                    RedisUtil.delete(AppCacheConstant.WX_AUTO_REPLY_LIST);
                    autoReplyMapper.insert(autoReply);
                } else {
                    autoReply.setUpdateBy(dto.getUpdateBy());
                    autoReply.setUpdateTime(LocalDateTime.now());
                    autoReply.setId(autoReply1.getId());
                    List<AutoReplyDTO.ReplyContentDTO> replyContentList = dto.getReplyContentList();
                    for (AutoReplyDTO.ReplyContentDTO replyContentDTO : replyContentList) {
                        if ("text".equals(replyContentDTO.getType()) && StringUtils.isEmpty(replyContentDTO.getText())) {
                            throw new BusinessException("回复内容不能为空");
                        }
                    }
                    autoReply.setReplyContent(JSON.toJSONString(replyContentList));
                    RedisUtil.delete(AppCacheConstant.WX_AUTO_REPLY_LIST);
                    autoReplyMapper.updateById(autoReply);
                }

            }
        }
        return Result.success();
    }

    /**
     * 新增(关键词回复)自动回复规则
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result addKeyWordReply(AutoReplyDTO dto) {
        AutoReply autoReply = new AutoReply();
        autoReply.setReplyType(dto.getReplyType());
        autoReply.setId(GenerationIdUtil.getId());
        if (StringUtils.isNotEmpty(dto.getReplyType())) {
            //3:关键词回复
            if (dto.getReplyType() == 3) {
                LambdaQueryWrapper<AutoReply> wrapper = new LambdaQueryWrapper();
                wrapper.eq(AutoReply::getReplyType, dto.getReplyType());
                int count = autoReplyMapper.selectCount(wrapper);
                if (count >= 200) {
                    throw new BusinessException("规则最多200条");
                } else {
                    autoReply.setRuleName(dto.getRuleName());
                    autoReply.setKeyWord(JSON.toJSONString(dto.getKeyWordList()));
                    autoReply.setReplyMode(dto.getReplyMode());
                    autoReply.setCreateBy(dto.getCreateBy());
                    autoReply.setCreateTime(LocalDateTime.now());
                    autoReply.setUpdateTime(LocalDateTime.now());
                    List<AutoReplyDTO.ReplyContentDTO> replyContentList = dto.getReplyContentList();
                    for (AutoReplyDTO.ReplyContentDTO replyContentDTO : replyContentList) {
                        if ("text".equals(replyContentDTO.getType()) && StringUtils.isEmpty(replyContentDTO.getText())) {
                            throw new BusinessException("回复内容不能为空");
                        }
                    }
                    autoReply.setReplyContent(JSON.toJSONString(replyContentList));
                    RedisUtil.delete(AppCacheConstant.WX_AUTO_REPLY_LIST);
                    autoReplyMapper.insert(autoReply);
                }
            }
        }
        return Result.success();
    }

    /**
     * 更新(关键词回复)自动回复规则
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateKeyWordReply(AutoReplyDTO dto) {
        AutoReply autoReply = new AutoReply();
        autoReply.setReplyType(dto.getReplyType());
        if (StringUtils.isNotEmpty(dto.getReplyType())) {
            //3:关键词回复
            if (dto.getReplyType() == 3) {
                autoReply.setRuleName(dto.getRuleName());
                autoReply.setKeyWord(JSON.toJSONString(dto.getKeyWordList()));
                autoReply.setReplyMode(dto.getReplyMode());
                autoReply.setUpdateBy(dto.getUpdateBy());
                autoReply.setUpdateTime(LocalDateTime.now());
                autoReply.setId(dto.getId());
                List<AutoReplyDTO.ReplyContentDTO> replyContentList = dto.getReplyContentList();
                for (AutoReplyDTO.ReplyContentDTO replyContentDTO : replyContentList) {
                    if ("text".equals(replyContentDTO.getType()) && StringUtils.isEmpty(replyContentDTO.getText())) {
                        throw new BusinessException("回复内容不能为空");
                    }
                }
                autoReply.setReplyContent(JSON.toJSONString(replyContentList));
                RedisUtil.delete(AppCacheConstant.WX_AUTO_REPLY_LIST);
                autoReplyMapper.updateById(autoReply);
            }
        }
        return Result.success();
    }

    /**
     * 删除单条关键词自动回复规则
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result delete(Long id) {
        RedisUtil.delete(AppCacheConstant.WX_AUTO_REPLY_LIST);
        autoReplyMapper.deleteById(id);
        return Result.success();
    }

    /**
     * 查看单条关键词自动回复规则
     *
     * @param id
     * @return
     */
    @Override
    public AutoReplyVO selectKeyWordReply(Long id) {
        AutoReply autoReply = autoReplyMapper.selectById(id);
        AutoReplyVO autoReplyVO = new AutoReplyVO();
        if (autoReply != null) {
            BeanUtils.copyProperties(autoReply, autoReplyVO);
        }
        return autoReplyVO;
    }

    /**
     * 查看(1:关注回复 2:收到消息回复)自动回复
     *
     * @param replyType
     * @return
     */
    @Override
    public AutoReplyVO select(Integer replyType) {
        return autoReplyMapper.selectAutoReply(replyType);
    }

    /**
     * 查看关键词自动回复列表
     *
     * @param dto
     * @return
     */
    @Override
    public Page<AutoReplyVO> getPages(AutoReplyQueryDto dto) {
        return autoReplyMapper.selectAutoReplyList(new Page<>(dto.getCurrent(), dto.getSize()), dto);
    }

    @Override
    public Result<WxMpMaterialUploadResult> upload(MultipartFile multipartFile, String type) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        WxMpMaterialUploadResult wxMediaUploadResult = null;
        String response = "";
        try {
            File file = new File(multipartFile.getOriginalFilename());
            FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), file);
            WxMpMaterial wxMpMaterial = new WxMpMaterial();
            wxMpMaterial.setFile(file);
            wxMpMaterial.setName(file.getName());
            if ("video".equals(type)) {
                wxMpMaterial.setVideoIntroduction(file.getName());
                wxMpMaterial.setName(file.getName());
            }
            wxMediaUploadResult = wxMpService.getMaterialService().materialFileUpload(type, wxMpMaterial);
            log.info("requestId==>{},微信上传结果：{}", requestId, response);
        } catch (Exception e) {
            log.warn("微信上传失败", e);
            throw new BusinessException("微信上传失败");
        }
        return Result.success(wxMediaUploadResult);
    }

    @Override
    public Result<WxMpMaterialFileBatchGetResult> wxMaterialPage(WxMaterialDto wxMaterialDto) {
        WxMpMaterialFileBatchGetResult wxMpMaterialFileBatchGetResult = null;
        try {
            wxMpMaterialFileBatchGetResult = wxMpService.getMaterialService().materialFileBatchGet(wxMaterialDto.getType(), (wxMaterialDto.getCurrent() - 1) * wxMaterialDto.getSize(), wxMaterialDto.getSize());
        } catch (WxErrorException e) {
            log.info("获取微信素材列表失败", e);
            throw new BusinessException(ResultCodeEnum.INTERFACE_OUTER_INVOKE_ERROR);
        }
        return Result.success(wxMpMaterialFileBatchGetResult);
    }

    @Override
    public Result<WxFreePublishVo> wxMaterialNewsPage(WxMaterialDto wxMaterialDto) {
        WxFreePublishVo wxFreePublishVo=null;
        try {
            Map<String, Object> params = new HashMap(4);
            params.put("no_content", 1);
            params.put("offset", (wxMaterialDto.getCurrent() - 1) * wxMaterialDto.getSize());
            params.put("count", wxMaterialDto.getSize());
            String responseText = this.wxMpService.post(FREE_PUBLISH_URL, WxGsonBuilder.create().toJson(params));
            log.info("获取微信素材草稿列表响应原始报文:{}",responseText);
            WxError wxError = WxError.fromJson(responseText, WxType.MP);
            if (wxError.getErrorCode() == 0) {
                wxFreePublishVo= JSON.parseObject(wxError.getJson(), WxFreePublishVo.class);
                log.info("获取微信素材草稿列表返回信息:{}",JSON.toJSONString(wxFreePublishVo));
                return Result.success(wxFreePublishVo);
            } else {
                throw new WxErrorException(wxError);
            }
        } catch (WxErrorException e) {
            log.info("获取微信素材草稿列表失败", e);
            throw new BusinessException(ResultCodeEnum.INTERFACE_OUTER_INVOKE_ERROR);
        }
    }
}
