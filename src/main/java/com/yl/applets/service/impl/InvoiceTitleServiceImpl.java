package com.yl.applets.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.dto.InvoiceTitleDTO;
import com.yl.applets.entity.InvoiceTitle;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.mapper.applets.InvoiceTitleMapper;
import com.yl.applets.service.IInvoiceTitleService;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <p>
 * 开票抬头表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-21
 */
@Slf4j
@Service
public class InvoiceTitleServiceImpl extends ServiceImpl<InvoiceTitleMapper, InvoiceTitle> implements IInvoiceTitleService {

    @Resource
    private OrikaBeanMapper orikaBeanMapper;

    @Override
    public Boolean save(InvoiceTitleDTO invoiceTitleDTO, WxUserVo user) {
        log.info("新增开票抬头入参：{}", JsonUtils.toJson(invoiceTitleDTO));
        //校验重复
        checkRepeat(invoiceTitleDTO,user.getId());

        InvoiceTitle invoiceTitle = orikaBeanMapper.map(invoiceTitleDTO,InvoiceTitle.class);
        invoiceTitle.setUserId(user.getId());
        invoiceTitle.setCreateTime(LocalDateTime.now());
        invoiceTitle.setUpdateTime(LocalDateTime.now());
        if(invoiceTitleDTO.getTaxNumber()==null){
            invoiceTitle.setTaxNumber("");
        }
        log.info("新增开票抬头入库：{}", JsonUtils.toJson(invoiceTitle));
        return this.save(invoiceTitle);
    }

    /**
     * 判断重复  发票类型//抬头名称//税号//手机号//电子邮箱
     * @return
     */
    private void checkRepeat(InvoiceTitleDTO invoiceTitleDTO,Integer userId){

        LambdaQueryWrapper<InvoiceTitle> lamSql = new LambdaQueryWrapper<InvoiceTitle>()
                .eq(InvoiceTitle::getUserId, userId)
                .eq(InvoiceTitle::getType, invoiceTitleDTO.getType())
                .eq(InvoiceTitle::getMobile, invoiceTitleDTO.getMobile())
                .eq(InvoiceTitle::getTaxNumber, invoiceTitleDTO.getTaxNumber())
                .eq(InvoiceTitle::getEmail, invoiceTitleDTO.getEmail())
                .eq(InvoiceTitle::getName, invoiceTitleDTO.getName());

        if(this.count(lamSql) > 0){
            throw new BusinessException(ServiceErrCodeEnum.INVOICE_REPEAT);
        }
    }
}
