package com.yl.applets.service.impl;


import com.yl.applets.service.IAppAbnormalPieceService;
import com.yl.applets.vo.lmdm.AppAbnormalPieceVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;


/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019-08-17
 */
@Service
@Slf4j
public class AppAbnormalPieceServiceImpl implements IAppAbnormalPieceService {


    @Override
    public List<AppAbnormalPieceVO> getAppList() {

        return Collections.emptyList();
    }

}
