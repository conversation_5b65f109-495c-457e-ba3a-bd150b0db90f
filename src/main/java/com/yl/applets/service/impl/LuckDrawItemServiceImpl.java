package com.yl.applets.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.entity.LuckDrawItem;
import com.yl.applets.mapper.applets.LuckDrawItemMapper;
import com.yl.applets.service.ILuckDrawItemService;
import org.springframework.stereotype.Service;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： <PERSON><PERSON><PERSON><PERSON>
 * @Date： 2022/08/08
 */
@Service
public class LuckDrawItemServiceImpl extends ServiceImpl<LuckDrawItemMapper, LuckDrawItem> implements ILuckDrawItemService {

}
