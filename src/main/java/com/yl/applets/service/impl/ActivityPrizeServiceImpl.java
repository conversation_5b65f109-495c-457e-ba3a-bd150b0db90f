package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.dto.ActivityUserRecordDto;
import com.yl.applets.dto.PrizeDto;
import com.yl.applets.entity.*;
import com.yl.applets.feign.OssApi;
import com.yl.applets.mapper.applets.ActivityPrizeRecordMapper;
import com.yl.applets.mapper.applets.LuckDrawItemMapper;
import com.yl.applets.mapper.applets.LuckDrawPoolMapper;
import com.yl.applets.service.IActivityPrizeService;
import com.yl.applets.service.IActivityUserService;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.utils.ResultUtil;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.CollectionUtils;
import com.yl.common.base.util.GenerationIdUtil;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动管理
 * @author: xiongweibin
 * @create: 2020-08-24 11:46
 */
@Service
@Slf4j
public class ActivityPrizeServiceImpl extends ServiceImpl<ActivityPrizeRecordMapper, ActivityPrizeRecord> implements IActivityPrizeService {


    @Autowired
    private LuckDrawPoolMapper luckDrawPoolMapper;

    @Autowired
    private LuckDrawItemMapper luckDrawItemMapper;

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Autowired
    private OssApi ossApi;

    @Autowired
    private IActivityUserService activityUserService;

    @Autowired
    private IActivityPrizeService activityPrizeService;



    @Transactional(rollbackFor = Exception.class)
    public Result<ActivityUserRecordDto> updatePoolAndSaveRecord(LuckDraw luckDraw,LuckDrawPool luckDrawPool, LuckDrawPool prize, PrizeDto paramDto, WxUserVo user, LocalDateTime now, String requestId) {
        if (luckDrawPoolMapper.updateByPrize(prize)>0) {//5.抢成功
            //中奖了
            //查询奖品详情,回显
            Long itemId = luckDrawPool.getItemId();
            LuckDrawItem luckDrawItem = luckDrawItemMapper.selectById(itemId);
            ActivityUserRecordDto dto = activityPrizeService.yesPrize(luckDraw,luckDrawItem, paramDto, user, now, requestId);
            return Result.success(dto);
        }else {//5.抢失败
            ActivityUserRecordDto dto = activityPrizeService.noPrize(luckDraw,paramDto, user, now, requestId);
            return Result.success(dto);
        }
    }

    /**
     *
     * 未中奖
     * @param user
     * @param now
     * @return
     */
    public ActivityUserRecordDto noPrize(LuckDraw luckDraw,PrizeDto paramDto, WxUserVo user, LocalDateTime now, String requestId) {
        ActivityUserRecord activityUserRecord=new ActivityUserRecord();
        activityUserRecord.setActivityId(paramDto.getId());
        activityUserRecord.setActivityName(luckDraw.getActivityName());
        activityUserRecord.setActivityCode(luckDraw.getActivityCode());
        activityUserRecord.setType(1);//类型默认1
        activityUserRecord.setIsWin(0);//未中奖
        activityUserRecord.setId(GenerationIdUtil.getId());
        activityUserRecord.setNumberId(user.getNumberId());
        activityUserRecord.setOpenId(user.getOpenid());
        activityUserRecord.setWinTime(now);
        activityUserRecord.setWinMobile(user.getMobile());
        if (activityUserService.save(activityUserRecord)) {
            log.info("请求id==>{},用户{},抽奖流水保存成功",requestId, JSON.toJSONString(user));
        }
        ActivityUserRecordDto map = orikaBeanMapper.map(activityUserRecord, ActivityUserRecordDto.class);
        map.setPrizeId(activityUserRecord.getId());
        return map;
    }

    /**
     *
     * 中奖
     * @param user
     * @param now
     * @return
     */
    public ActivityUserRecordDto yesPrize(LuckDraw luckDraw,LuckDrawItem luckDrawItem,PrizeDto paramDto,WxUserVo user, LocalDateTime now,String requestId) {
        ActivityUserRecord activityUserRecord=new ActivityUserRecord();
        activityUserRecord.setItemId(luckDrawItem.getId());
        activityUserRecord.setWinLevel(luckDrawItem.getItemLevel());
        activityUserRecord.setActivityName(luckDraw.getActivityName());
        activityUserRecord.setActivityCode(luckDraw.getActivityCode());
        activityUserRecord.setWinName(luckDrawItem.getName());
        activityUserRecord.setType(1);
        activityUserRecord.setIsWin(1);
        activityUserRecord.setId(GenerationIdUtil.getId());
        activityUserRecord.setNumberId(user.getNumberId());
        activityUserRecord.setOpenId(user.getOpenid());
        activityUserRecord.setWinTime(now);
        activityUserRecord.setWinMobile(user.getMobile());
        activityUserRecord.setActivityId(paramDto.getId());
        if (activityUserService.save(activityUserRecord)) {
            log.info("请求id==>{},用户{}中奖,抽奖流水保存成功",requestId,JSON.toJSONString(user));
            ActivityUserRecordDto map = orikaBeanMapper.map(activityUserRecord, ActivityUserRecordDto.class);
            map.setPrizeId(activityUserRecord.getId());
            String key= BcRedisKeyEnum.PRIZE_CONFIG_ITEMSURL_KEY.keyBuilder(luckDraw.getId()+"");
            Map<Object, Object> objectObjectMap = RedisUtil.hGetAll(key);
            if(objectObjectMap!=null){
                log.info("请求id==>{},中奖,奖项的url使用缓存==>{}",requestId,JSON.toJSONString(objectObjectMap));
                if(objectObjectMap.get(luckDrawItem.getWinImgUrl())!=null){
                    luckDrawItem.setWinImgUrl(objectObjectMap.get(luckDrawItem.getWinImgUrl())+"");
                }else {
                    supplementOssWinPath(luckDrawItem,luckDrawItem.getWinImgUrl());
                    log.info("请求id==>{},中奖,redis失效,奖项的url请求oss",requestId);
                }
            }else {
                supplementOssWinPath(luckDrawItem,luckDrawItem.getWinImgUrl());
                log.info("请求id==>{},中奖,奖项的url请求oss",requestId);
            }
            map.setWinImgUrl(luckDrawItem.getWinImgUrl());
            map.setWinName(luckDrawItem.getName());
            return map;
        }
        throw new BusinessException(ResultCodeEnum.FAIL);
    }


    private void supplementOssWinPath(LuckDrawItem drawDto,String path) {
        if (Objects.nonNull(path)) {
            log.info("请求oss,入参==>{}",path);
            Result<Map<String, String>> ossResult = ossApi.getDownloadSignedUrlDetail(Arrays.asList(path));
            log.info("请求oss,出参==>{}",JSON.toJSONString(ossResult));
            ResultUtil.checkFeignResult(ossResult);
            Map<String, String> ossPathMap = ossResult.getData();
            if (CollectionUtils.isNotEmpty(ossPathMap.values())) {
                drawDto.setWinImgUrl(ossPathMap.get(path));
            }
        }
    }


}
