package com.yl.applets.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yl.applets.config.WxMaConfiguration;
import com.yl.applets.config.WxMaProperties;
import com.yl.applets.dto.*;
import com.yl.applets.entity.*;
import com.yl.applets.enums.*;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.OssApi;
import com.yl.applets.feign.PromotionFeighClient;
import com.yl.applets.mapper.applets.*;
import com.yl.applets.service.*;
import com.yl.applets.stream.MemberScoreOutputProcessor;
import com.yl.applets.stream.MemberUserPushOutputProcessor;
import com.yl.applets.stream.dto.MemberScoreMqDto;
import com.yl.applets.stream.dto.MemberUserMqDto;
import com.yl.applets.utils.*;
import com.yl.applets.vo.MyStaffVO;
import com.yl.applets.vo.SubscribeVO;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.CollectionUtils;
import com.yl.common.base.util.GenerationIdUtil;
import com.yl.common.base.util.YlPreconditions;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 小程序用户 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2019-09-21
 */
@Service
@Slf4j
public class WxUserServiceImpl extends ServiceImpl<WxUserMapper, WxUser> implements IWxUserService {

    @Autowired
    private SessionUtil sessionUtil;

    @Value("${jwt.expiration:2592000}")
    private Long expiration;

    @Value("${wx.white:136736}")
    private String whiteMobile;

    @Autowired
    private WxUserMapper wxUserMapper;

    @Autowired
    private OrikaBeanMapper beanMapper;

    @Autowired
    private MemberUserMapper memberUserMapper;

    @Autowired
    private WxMaProperties wxMaProperties;

    @Autowired
    private WxOfficialUserService wxOfficialUserService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private NumberIdUtils numberIdUtils;

    @Autowired
    private MemberUserPushOutputProcessor memberUserPushOutputProcessor;

    @Autowired
    private MemberScoreOutputProcessor memberScoreOutputProcessor;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Autowired
    private UserActionRecordMapper userActionRecordMapper;

    @Autowired
    private IMemberUserService memberUserService;

    @Autowired
    private MemberRechargeRecordMapper memberRechargeRecordMapper;

    @Autowired
    private PromotionFeighClient promotionFeighClient;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private OssApi ossApi;

    @Autowired
    private IMemberUserService iMemberUserService;

    @Autowired
    private IAppLoginService iAppLoginService;

    @Autowired
    private HttpServletRequest request;



    //获取线程数量
    private static final int AVAILABLE_PROCESSORS = Runtime.getRuntime().availableProcessors();
    //创建线程池
    private static ThreadPoolExecutor executor = new ThreadPoolExecutor(AVAILABLE_PROCESSORS, AVAILABLE_PROCESSORS, 60, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(10), new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WxUserVo bindMobile(String mobile, String verificationCode, Integer type, String openid, String unionid) {
        //本次请求id
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        WxUserVo user = sessionUtil.getUser();
        if (user == null || org.apache.commons.lang.StringUtils.isEmpty(user.getOpenid())) {
            user = new WxUserVo();
            user.setOpenid(openid);
            user.setUnionid(unionid);
        }
        if (org.apache.commons.lang.StringUtils.isEmpty(user.getOpenid()) || org.apache.commons.lang.StringUtils.isEmpty(user.getUnionid())) {
            throw new ServiceException(ServiceErrCodeEnum.LOGIN_HAS_EXPIRED);
        }
        String key;
        switch (type) {
            case 1:
                //绑定手机号
                key = BcRedisKeyEnum.APPLETS_VERIFYCODE_BINDMOBILE.keyBuilder(mobile);
                break;
            case 3:
                //修改手机号时获取新手机号的验证码
                key = BcRedisKeyEnum.APPLETS_VERIFYCODE_MODIFYMOBILENEW.keyBuilder(mobile);
                break;
            default:
                throw new ServiceException(ServiceErrCodeEnum.VERIFICATIONCODE_TYPE_ERROR);
        }
        YlPreconditions.checkArgument(verificationCode.equals(RedisUtil.get(key)), new ServiceException(ServiceErrCodeEnum.VERIFICATIONCODE_ERROR));
        RedisUtil.delete(key);
//        YlPreconditions.checkArgument(!StringUtils.equals(mobile, user.getMobile()), new ServiceException(ServiceErrCodeEnum.MOBILE_EQUALS));
        return this.handleBindPhone(user, mobile, requestId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WxUserVo handleBindPhone(WxUserVo user, String phoneNumber, String requestId) {
        //判断手机号是否已注销，注销的不允许登录（临时2022-6-7 09:11:38）
        if (isAlreadyOff(phoneNumber)) {
            log.info("该手机号已经注销,无法登录==>{}", phoneNumber);
            throw new BusinessException(ResultCodeEnum.IS_NOT_LOGIN_MOBILE_ERROR);
        }
        WxUser reslut = new WxUser();
        StopWatch stopWatch = new StopWatch();
        log.info("requestId:{},绑定手机号入参{}", requestId, phoneNumber);
        phoneNumber = RegexUtils.handlePhone(phoneNumber);
        log.info("requestId:{},绑定手机号正则处理结果：{}", requestId, phoneNumber);
        stopWatch.start("查询用户是否存在");
        QueryWrapper<WxUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mobile", phoneNumber);
        queryWrapper.eq("is_delete", 0);
        log.info("requestId:{},绑定手机号查库用户1：{}", requestId, JsonUtils.toJson(queryWrapper));
        List<WxUser> userPhone = this.list(queryWrapper);
        stopWatch.stop();
        log.info("requestId:{},绑定手机号查库用户结果：{},耗时：{}", requestId, JsonUtils.toJson(userPhone), stopWatch.getLastTaskTimeMillis());
        stopWatch.start("查询openid是否存在");
        //查询渠道
        QueryWrapper<WxUser> queryOpenid = new QueryWrapper<>();
        queryOpenid.eq("openid", user.getOpenid());
        queryOpenid.eq("is_delete", 0);
        queryOpenid.eq("type", ChannelSourceEnum.WX.getKey());
        List<WxUser> openids = list(queryOpenid);
        stopWatch.stop();
        log.info("requestId:{},绑定手机号查用户结果：{},耗时：{}", requestId, JsonUtils.toJson(openids), stopWatch.getLastTaskTimeMillis());
        //加锁，保证同渠道中的同一个openid同时只有一个可操作
        String lockKey = BcRedisKeyEnum.APPLETS_USER_LOGIN_LOCK.keyBuilder(user.getOpenid());

        try {
            if (!cacheService.lock(lockKey)) {
                log.info("登录操作频繁，requestID:{},openId:{}", requestId, user.getOpenid());
                throw new BusinessException(ResultCodeEnum.LOGIN_LOCK_ERROR);
            }
            //1.手机号查不到
            if (userPhone == null || userPhone.isEmpty()) {
                stopWatch.start("保存新用户");
                WxUser wxUser = new WxUser();
                Object numberId = RedisUtil.get(NumberIdUtils.CHANNEL_NUMBER_ID_LOCK + phoneNumber);
                if (numberId == null) {
                    RedisUtil.setEx(NumberIdUtils.CHANNEL_NUMBER_ID_LOCK + phoneNumber, numberIdUtils.getNumberId() + "", 10);
                    numberId = RedisUtil.get(NumberIdUtils.CHANNEL_NUMBER_ID_LOCK + phoneNumber);
                }
                wxUser.setNumberId(Integer.valueOf(numberId + ""));
                reslut = saveUserByType(wxUser, user, phoneNumber, openids, ChannelSourceEnum.WX.getKey());
                stopWatch.stop();
                log.info("用户入库成功-requestId==>{}，入参：{},耗时：{}", requestId, JSON.toJSONString(wxUser), stopWatch.getLastTaskTimeMillis());
                //发送mq，同步新增的用户信息,并发送注册+登录+更新活跃时间mq
//                sendThisUserToMemberUser(reslut, ChannelSourceEnum.WX.getKey());
            } else {
                List<WxUser> isNTSS = userPhone.stream().filter(o -> ChannelSourceEnum.HTSS.getKey().equals(o.getType())).collect(Collectors.toList());
                //查出来的只有农腾盛世,需要注册C端用户
                boolean isOnlyNtss = CollectionUtils.isNotEmpty(isNTSS) && isNTSS.size() == userPhone.size();
                if (isOnlyNtss) {
                    //2.手机号查到了,但是只有农腾盛世的账号。
                    stopWatch.start("保存新用户");
                    WxUser wxUser = new WxUser();
                    wxUser.setNumberId(userPhone.get(0).getNumberId());
                    reslut = saveUserByType(wxUser, user, phoneNumber, openids, ChannelSourceEnum.WX.getKey());
                    stopWatch.stop();
                    log.info("用户入库成功-requestId==>{}，入参：{},耗时：{}", requestId, JSON.toJSONString(wxUser), stopWatch.getLastTaskTimeMillis());
                    //发送mq，同步新增的用户信息,并发送注册+登录+更新活跃时间mq
//                    sendThisUserToMemberUser(reslut, ChannelSourceEnum.WX.getKey());
                } else {
                    //2.手机号查到了,同一渠道->更新此渠道的openid
                    List<WxUser> isType = userPhone.stream().filter(o -> ChannelSourceEnum.WX.getKey().equals(o.getType())).collect(Collectors.toList());
                    if (isType != null && !isType.isEmpty()) {
                        WxUser wxUser = isType.get(0);
                        if (user.getOpenid().equals(wxUser.getOpenid()) && StringUtils.isNotBlank(user.getUnionid()) && user.getUnionid().equals(wxUser.getUnionid())) {
                            reslut = wxUser;
                        } else {
                            wxUser.setOpenid(user.getOpenid()).setUnionid(user.getUnionid()).setUpdateTime(LocalDateTime.now());
                            if (updateById(wxUser)) {
                                RedisUtil.delete(ChannelSourceEnum.WX.getRedisKey() + wxUser.getId());
                                reslut = wxUser;
                            }
                        }
                        // 埋点：用户登录
                        sendScoreDLMq(reslut);
                    } else {
                        //2.手机号查到了,不同渠道->新增此渠道的数据
                        stopWatch.start("保存新用户");
                        WxUser wxUser = new WxUser();
                        wxUser.setNumberId(userPhone.get(0).getNumberId());
                        reslut = saveUserByType(wxUser, user, phoneNumber, openids, ChannelSourceEnum.WX.getKey());
                        stopWatch.stop();
                        log.info("用户入库成功-requestId==>{}，入参：{},耗时：{}", requestId, JSON.toJSONString(wxUser), stopWatch.getLastTaskTimeMillis());
                        //埋点：注册成功
                        sendScoreZCMq(reslut, ChannelSourceEnum.WX.getKey());
                    }
                }
            }

            //发送mq，同步新增的用户信息,并发送注册+登录+更新活跃时间mq
            sendThisUserToMemberUser(reslut, ChannelSourceEnum.WX.getKey());

            //2025-04-01 变更需求，用户登录成功后没有申请过VIP服务的直接开通vip权限信息
            try {
                MemberUser memberUser = memberUserMapper.getOneMemberUser(reslut.getNumberId());
                if(Objects.isNull(memberUser) || Objects.isNull(memberUser.getMemberExpiresTime())){
                    AppletsMemberDTO appletsMemberDTO = beanMapper.map(user, AppletsMemberDTO.class);
                    appletsMemberDTO.setNumberId(reslut.getNumberId());
                    appletsMemberDTO.setId(reslut.getId());
                    appletsMemberDTO.setNickName(reslut.getNickName());
                    appletsMemberDTO.setOpenid(reslut.getOpenid());
                    appletsMemberDTO.setMobile(reslut.getMobile());
                    channelApiFeignClient.applyMember(appletsMemberDTO);
                }
            } catch (Exception e) {
                log.error("微信登录开通vip权限失败 e[{}]",e);
            }

            //过滤掉当前数据,清掉非当前数据的openid和unionid
            WxUser finalReslut = reslut;
            List<WxUser> isDeleteOpenIds = openids.stream().filter(i -> !finalReslut.getId().equals(i.getId())).collect(Collectors.toList());
            if (isDeleteOpenIds != null && !isDeleteOpenIds.isEmpty()) {
                for (WxUser openid : isDeleteOpenIds) {
                    openid.setOpenid("").setUnionid("").setUpdateTime(LocalDateTime.now());
                    ;
                    if (updateById(openid)) {//退登百度
                        RedisUtil.delete(ChannelSourceEnum.WX.getRedisKey() + openid.getId());
                    }
                }
            }
            return setTokenAndRedis(reslut);
        } finally {
            //释放锁
            cacheService.unlock(lockKey);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WxUser handleCMCCBindPhone(WxUserVo user, String phoneNumber, String requestId, Integer key) {
        //判断手机号是否已注销，注销的不允许登录（临时2022-6-7 09:11:38）
        if (isAlreadyOff(phoneNumber)) {
            log.info("该手机号已经注销,无法登录==>{}", phoneNumber);
            throw new BusinessException(ResultCodeEnum.IS_NOT_LOGIN_MOBILE_ERROR);
        }
        WxUser reslut;
        StopWatch stopWatch = new StopWatch();
        log.info("requestId:{},绑定手机号入参{}", requestId, phoneNumber);
        phoneNumber = RegexUtils.handlePhone(phoneNumber);
        log.info("requestId:{},绑定手机号正则处理结果：{}", requestId, phoneNumber);
        stopWatch.start("查询用户是否存在");
        QueryWrapper<WxUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mobile", phoneNumber);
        queryWrapper.eq("is_delete", 0);
        log.info("requestId:{},绑定手机号查库用户1：{}", requestId, JsonUtils.toJson(queryWrapper));
        List<WxUser> userPhone = this.list(queryWrapper);
        stopWatch.stop();
        log.info("requestId:{},绑定手机号查库用户结果：{},耗时：{}", requestId, JsonUtils.toJson(userPhone), stopWatch.getLastTaskTimeMillis());
        stopWatch.start("查询openid是否存在");
        //查询渠道
        QueryWrapper<WxUser> queryOpenid = new QueryWrapper<>();
        queryOpenid.eq("openid", user.getOpenid());
        queryOpenid.eq("is_delete", 0);
        queryOpenid.eq("type", ChannelSourceEnum.WX.getKey());
        List<WxUser> openids = list(queryOpenid);
        stopWatch.stop();
        log.info("requestId:{},绑定手机号查用户结果：{},耗时：{}", requestId, JsonUtils.toJson(openids), stopWatch.getLastTaskTimeMillis());

        //1.手机号查不到
        if (userPhone == null || userPhone.isEmpty()) {
            stopWatch.start("保存CMCC新用户");
            WxUser wxUser = new WxUser();
            Object numberId = RedisUtil.get(NumberIdUtils.CHANNEL_NUMBER_ID_LOCK + phoneNumber);
            if (numberId == null) {
                RedisUtil.setEx(NumberIdUtils.CHANNEL_NUMBER_ID_LOCK + phoneNumber, numberIdUtils.getNumberId() + "", 10);
                numberId = RedisUtil.get(NumberIdUtils.CHANNEL_NUMBER_ID_LOCK + phoneNumber);
            }
            wxUser.setNumberId(Integer.valueOf(numberId + ""));
            reslut = saveUserByType(wxUser, user, phoneNumber, openids, key);
            stopWatch.stop();
            log.info("用户入库成功-requestId==>{}，入参：{},耗时：{}", requestId, JSON.toJSONString(reslut), stopWatch.getLastTaskTimeMillis());
//            //发送mq，同步新增的用户信息,并发送注册+登录+更新活跃时间mq
//            sendThisUserToMemberUser(reslut, key);
        } else {
            List<WxUser> isNTSS = userPhone.stream().filter(o -> ChannelSourceEnum.HTSS.getKey().equals(o.getType())).collect(Collectors.toList());
            //查出来的只有农腾盛世,需要注册C端用户
            boolean isOnlyNtss = CollectionUtils.isNotEmpty(isNTSS) && isNTSS.size() == userPhone.size();
            if (isOnlyNtss) {
                //2.手机号查到了,但是只有农腾盛世的账号。
                stopWatch.start("保存CMCC新用户");
                WxUser wxUser = new WxUser();
                wxUser.setNumberId(userPhone.get(0).getNumberId());
                reslut = saveUserByType(wxUser, user, phoneNumber, openids, key);
                stopWatch.stop();
                log.info("用户入库成功-requestId==>{}，入参：{},耗时：{}", requestId, JSON.toJSONString(reslut), stopWatch.getLastTaskTimeMillis());
                //发送mq，同步新增的用户信息,并发送注册+登录+更新活跃时间mq
//                sendThisUserToMemberUser(reslut, key);
            } else {
                //2.手机号查到了,同一渠道->更新此渠道的openid
                List<WxUser> isType = userPhone.stream().filter(o -> key.equals(o.getType())).collect(Collectors.toList());
                if (isType != null && !isType.isEmpty()) {
                    WxUser wxUser = isType.get(0);
                    if (user.getOpenid().equals(wxUser.getOpenid())) {
                        reslut = wxUser;
                    } else {
                        wxUser.setOpenid(user.getOpenid()).setUpdateTime(LocalDateTime.now());
                        updateById(wxUser);
                        reslut = wxUser;
                    }
                } else {
                    //2.手机号查到了,不同渠道->新增此渠道的数据
                    stopWatch.start("保存新用户");
                    WxUser wxUser = new WxUser();
                    wxUser.setNumberId(userPhone.get(0).getNumberId());
                    reslut = saveUserByType(wxUser, user, phoneNumber, openids, key);
                    stopWatch.stop();
                    log.info("用户入库成功-requestId==>{}，入参：{},耗时：{}", requestId, JSON.toJSONString(reslut), stopWatch.getLastTaskTimeMillis());
                    //埋点：注册成功
                    sendScoreZCMq(reslut, key);
                }
            }
        }
        //发送mq，同步新增的用户信息,并发送注册+登录+更新活跃时间mq
        sendThisUserToMemberUser(reslut, key);

        //过滤掉当前数据,清掉非当前数据的openid和unionid
        WxUser finalReslut = reslut;
        List<WxUser> isDeleteOpenIds = openids.stream().filter(i -> !finalReslut.getId().equals(i.getId())).collect(Collectors.toList());
        if (isDeleteOpenIds != null && !isDeleteOpenIds.isEmpty()) {
            log.info("result用户信息：{} 需要清掉的用户List:{}", JSON.toJSONString(finalReslut), JSON.toJSONString(isDeleteOpenIds));
            for (WxUser openid : isDeleteOpenIds) {
                openid.setOpenid("").setUnionid("").setUpdateTime(LocalDateTime.now());
                updateById(openid);
            }
        }
        return reslut;
    }


    private boolean isAlreadyOff(String phoneNumber) {
        return Lists.newArrayList(whiteMobile.split(",")).contains(phoneNumber);
    }


    /**
     * mq，发送消息
     *
     * @param reslut
     */
    private void sendThisUserToMemberUser(WxUser reslut, Integer key) {
        log.info("新增用户成功，推送MQ消息：{}", JSON.toJSONString(reslut));
        MemberUserMqDto mqDto = beanMapper.map(reslut, MemberUserMqDto.class);
        mqDto.setMqType("save");
        MemberScoreMqDto dto = new MemberScoreMqDto();
        dto.setId(reslut.getId().longValue());
        dto.setMemberId(reslut.getNumberId());
        dto.setMemberAction(MemberUserActionSourceEnum.ZC.getCode());
        dto.setType(key);
        mqDto.setScoreMqDto(dto);
        mqDto.setIp(IpUtils.getIpAddr(request));
        mqDto.setPort(IpUtils.getLocalPort(request));
        mqDto.setGender(reslut.getGender());
        memberUserPushOutputProcessor.sendMemberUserPushMessage(mqDto);
    }

    /**
     * 注册+分
     *
     * @param reslut
     */
    private void sendScoreZCMq(WxUser reslut, Integer key) {
        WxUserVo user = beanMapper.map(reslut, WxUserVo.class);
        log.info("当前渠道用户注册成功：{}", JSON.toJSONString(user));
        MemberScoreMqDto dto = new MemberScoreMqDto();
        dto.setId(user.getId().longValue());
        dto.setMemberId(user.getNumberId());
        dto.setMemberAction(MemberUserActionSourceEnum.ZC.getCode());
        dto.setType(key);
        memberScoreOutputProcessor.sendScorePushMessage(dto);
    }

    /**
     * 登录+分
     *
     * @param reslut
     */
    private void sendScoreDLMq(WxUser reslut) {
        WxUserVo user = beanMapper.map(reslut, WxUserVo.class);
        log.info("当前渠道用户登录成功：{}", JSON.toJSONString(user));
        MemberScoreMqDto dto = new MemberScoreMqDto();
        dto.setId(user.getId().longValue());
        dto.setMemberId(user.getNumberId());
        dto.setMemberAction(MemberUserActionSourceEnum.DL.getCode());
        dto.setType(ChannelSourceEnum.WX.getKey());
        memberScoreOutputProcessor.sendScorePushMessage(dto);
    }

    private WxUser saveUserByType(WxUser wxUser, WxUserVo user, String phoneNumber, List<WxUser> openids, Integer key) {
        wxUser.setCreateTime(LocalDateTime.now());
        wxUser.setUpdateTime(LocalDateTime.now());
        wxUser.setNickName("极兔用户");
        wxUser.setType(key);
        wxUser.setIsDelete(0);
        wxUser.setOpenid(user.getOpenid());
        wxUser.setUnionid(user.getUnionid());
        wxUser.setMobile(phoneNumber);
        wxUser.setGender(0);
        log.info("wx_user用户入库前数据：{}", JSON.toJSONString(wxUser));
        save(wxUser);
        QueryWrapper<WxUser> result = new QueryWrapper<>();
        result.eq("openid", user.getOpenid()).eq("type", key);
        //获取openid查到的旧的id
        List<Integer> ids = openids.stream().map(WxUser::getId).collect(Collectors.toList());
        if (ids != null && !ids.isEmpty()) {
            result.notIn("id", ids);
        }
        return getOne(result);
    }


    private WxUserVo setTokenAndRedis(WxUser wxUser) {
        WxUserVo wxUserVo = beanMapper.map(wxUser, WxUserVo.class);
        wxUserVo.setNickName(Objects.equals(wxUserVo.getNickName(), "极兔用户") ? wxUserVo.getNickName() : new String(Base64.decodeBase64(wxUserVo.getNickName()), StandardCharsets.UTF_8));
        wxUserVo.setToken(JWTUtils.generateById(wxUserVo.getId(), expiration));
        //将注册时间更新到当前渠道的用户缓存中
        MemberUser memberUser = memberUserMapper.selectOne(new QueryWrapper<MemberUser>().eq("member_id", wxUserVo.getNumberId()));
        if(memberUser!=null){
            wxUserVo.setCreateTime(memberUser.getCreateTime());
            //增加过期时间
            wxUserVo.setMemberExpiresTime(memberUser.getMemberExpiresTime());
        }
        //处理下头像的问题，兼容老版本，以https来的数据就是头像地址,否则则是去oss查询
        try {
            if (StringUtils.isNotEmpty(wxUserVo.getAvatarUrl())&&!wxUserVo.getAvatarUrl().startsWith("http")) {
                long start =System.currentTimeMillis();
                Result<List<String>> result = ossApi.getDownloadSignedUrl(Collections.singletonList(wxUserVo.getAvatarUrl()));
                long end =System.currentTimeMillis();
                log.info("用户===》{}，上传微信头像，调用oss解析长链接，入参：{}，出参{}，耗时：{}",JSON.toJSONString(wxUserVo.getNumberId()),
                        JSON.toJSONString(wxUserVo.getAvatarUrl()), JSON.toJSONString(result),(end-start));
                if (result != null && ResultCodeEnum.SUCCESS.getCode() == result.getCode()){
                    String url = result.getData().get(0);
                    wxUserVo.setAvatarUrl(url);
                }
            }
        } catch (Exception e) {
            log.warn("用户===》{}，上传微信头像失败，调用oss解析长链接，入参：{}",JSON.toJSONString(wxUserVo.getNumberId()),
                    JSON.toJSONString(wxUserVo.getAvatarUrl()));
        }
        RedisUtil.setEx(BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(wxUserVo.getId().toString()), JSON.toJSONString(wxUserVo), expiration);
        log.info("登录成功,user：{}", JsonUtils.toJson(wxUserVo));
        //登录成功，更新活跃时间
        sendThisUserToUpdateTimeUser(wxUser);
        //手机号加密
        if (org.apache.commons.lang.StringUtils.isNotBlank(wxUserVo.getMobile())) {
            wxUserVo.setMobile(wxUserVo.getMobile().replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2"));
        }
        return wxUserVo;
    }

    /**
     * mq，发送消息,活跃时间
     *
     * @param reslut
     */
    private void sendThisUserToUpdateTimeUser(WxUser reslut) {
        MemberUserMqDto mqDto = new MemberUserMqDto();
        mqDto.setNumberId(reslut.getNumberId());
        mqDto.setMqType("update_time");
        memberUserPushOutputProcessor.sendMemberUserPushMessage(mqDto);
    }

    @Override
    public Integer isFirstTYD() {
        WxUserVo wxUserVo = sessionUtil.getUser();
        WxUser wxUser = wxUserMapper.selectOne(new LambdaQueryWrapper<WxUser>().eq(WxUser::getId, wxUserVo.getId()));
        Integer firstRabbitDelivery = wxUser.getIsFirstRabbitDelivery();
        return Objects.isNull(firstRabbitDelivery) ? 1 : firstRabbitDelivery;
    }

    @Override
    public void updateFirstTYD() {
        WxUserVo wxUserVo = sessionUtil.getUser();
        WxUser wxUser = wxUserMapper.selectOne(new LambdaQueryWrapper<WxUser>().eq(WxUser::getId, wxUserVo.getId()));

        Integer isFirstTYD = Objects.isNull(wxUser.getIsFirstRabbitDelivery()) ? 1 : wxUser.getIsFirstRabbitDelivery();

        if (isFirstTYD.equals(1)) {
            WxUser user = new WxUser();
            user.setIsFirstRabbitDelivery(2);
            UpdateWrapper<WxUser> wrapper = new UpdateWrapper<WxUser>().eq("ID", wxUserVo.getId());

            wxUserMapper.update(user, wrapper);
        }
    }


    /**
     * 功能描述:
     * 是否关注公众号
     *
     * @param wxUserDTO
     * @return:com.yl.applets.vo.SubscribeVO
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-04-20 9:38
     */
    @Override
    public SubscribeVO isSubscribeV2(WxUserDTO wxUserDTO) {
        //本次请求id
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        if (StringUtils.isBlank(wxUserDTO.getCode())) {
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
        String appId = wxMaProperties.getConfigs().get(0).getAppid();
        final WxMaService wxService = WxMaConfiguration.getMaService(appId);
        WxMaJscode2SessionResult session = null;
        try {
            session = wxService.getUserService().getSessionInfo(wxUserDTO.getCode());
            log.info("调用微信登录-requestId==>{},结果==>{}", requestId, JsonUtils.toJson(session));
        } catch (WxErrorException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
        String unionid = session.getUnionid();
        QueryWrapper<WxUser> wxUserQueryWrapper = new QueryWrapper<>();
        wxUserQueryWrapper.eq("openid", session.getOpenid());
        wxUserQueryWrapper.eq("is_delete", 0);
        log.info("查询用户-requestId==>{},入参==>{}", requestId, JsonUtils.toJson(wxUserQueryWrapper));
        WxUser wxUser = this.getOne(wxUserQueryWrapper);
        log.info("查询用户-requestId==>{},结果==>{}", requestId, JsonUtils.toJson(wxUser));
        this.updateUnionId(wxUser, unionid, requestId);
        SubscribeVO subscribeVO = new SubscribeVO();
//        subscribeVO.setSessionKey(session.getSessionKey());
        if (StringUtils.isEmpty(unionid)) {
            subscribeVO.setIsSubscribe(false);
            return subscribeVO;
        }
        LambdaQueryWrapper<WxOfficialUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WxOfficialUser::getUnionid, session.getUnionid());
        WxOfficialUser wxOfficialUser = wxOfficialUserService.getOne(queryWrapper);
        if (wxOfficialUser == null || wxOfficialUser.getSubscribe() != 1) {
            subscribeVO.setIsSubscribe(false);
        } else {
            subscribeVO.setIsSubscribe(true);
        }
        return subscribeVO;
    }

    /**
     * 功能描述:
     * 小程序订阅上报
     *
     * @param miniSubscribeDTO
     * @return:void
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-04-22 18:23
     */
    @Override
    public void miniSubscribeReport(MiniSubscribeDTO miniSubscribeDTO, WxUserVo user) {
        String key = miniSubscribeDTO.getOrderId() + ":" + user.getNumberId();
        RedisUtil.setEx(BcRedisKeyEnum.APPLETS_USER_SUBSCRIBE_STATUS.keyBuilder(key), JsonUtils.toJson(miniSubscribeDTO.getTemplateInfo()), 60 * 60 * 24 * 15);
    }

    /**
     * 功能描述:
     * 更新用户unionid
     *
     * @param wxUser
     * @param unionid
     * @param requestId
     * @return:void
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-04-19 16:34
     */
    private void updateUnionId(WxUser wxUser, String unionid, String requestId) {
        try {
            if (wxUser != null && StringUtils.isNotEmpty(unionid) && !unionid.equals(wxUser.getUnionid())) {
                wxUser.setUpdateTime(LocalDateTime.now());
                wxUser.setUnionid(unionid);
                this.updateById(wxUser);
                log.info("用户更新成功-requestId==>{}", requestId);
            }
        } catch (Exception e) {
            log.warn("更新用户unionID失败-requestId==>{}", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unbindMobile() {
        WxUserVo wxUserVo = sessionUtil.getUser();
        WxUser wxUser = wxUserMapper.selectOne(new LambdaQueryWrapper<WxUser>().eq(WxUser::getId, wxUserVo.getId()));
        wxUser.setMobile(StringUtils.EMPTY).setUpdateTime(LocalDateTime.now());
        Integer result = wxUserMapper.updateById(wxUser);
        wxUserVo.setMobile(StringUtils.EMPTY);
        //将注册时间更新到当前渠道的用户缓存中
        MemberUser memberUser = memberUserMapper.selectOne(new QueryWrapper<MemberUser>().eq("member_id", wxUserVo.getNumberId()));
        if(memberUser!=null){
            wxUserVo.setCreateTime(memberUser.getCreateTime());
        }
        RedisUtil.setEx(BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(wxUserVo.getOpenid()), JSONObject.toJSONString(wxUserVo),expiration);
        //更新h5的token
        if (wxUserVo.getId() != null) {
            WxUserVo h5User = wxUserVo;
            h5User.setToken(JWTUtils.generateH5Token(wxUserVo.getId().toString(), expiration));
            RedisUtil.setEx(BcRedisKeyEnum.OAUTH_H5_TOKEN.keyBuilder(wxUserVo.getId().toString()), JSON.toJSONString(h5User), expiration);
        }
        return null != result && result >= 1;
    }

    @Override
    public List<Long> getIdsByIdAndNumberId(Integer numberId) {
        List<Long> ids = wxUserMapper.getIdsByIdAndNumberId(numberId);
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("numberId{},没有查到对应的ids", numberId);
            throw new ServiceException(ServiceErrCodeEnum.IS_NO_ACCESS_RESET_LOGIN);
        }
        return ids;
    }

    @Override
    public Integer maxNumberId() {
        return wxUserMapper.maxNumberId();
    }

    @Override
    public Integer maxJYUserId() {
        return wxUserMapper.maxJYUserId();
    }

    @Override
    public Boolean isLogin(WxUserVo user) {
        WxUser wxUser = beanMapper.map(user, WxUser.class);
        // 埋点：用户登录
        sendScoreDLMq(wxUser);
        //登录成功，更新活跃时间
        sendThisUserToUpdateTimeUser(wxUser);
        return true;
    }

    @Override
    public Boolean applyMember() throws InterruptedException {
        WxUserVo user = sessionUtil.getUser();
        //加锁
        RLock lock = redisson.getLock(ChannelMemberEnum.MemberUserRedis.APPLY_MEMBER + user.getNumberId());
        boolean isLock = lock.tryLock(0, 5, TimeUnit.SECONDS);
        try {
            if (isLock) {//加锁成功
                MemberRecordDTO dto = new MemberRecordDTO();
                dto.setMemberId(user.getNumberId());
                String requestId = UUID.randomUUID().toString().replaceAll("-", "");
                log.info("会员0元申请入参 request：{}，入参:{}", requestId, JSON.toJSONString(dto));
                //根据memberID查询会员,理论上只要是微信来源，都会有这个MemberUser
                LambdaQueryWrapper<MemberUser> memberUserQuery = new LambdaQueryWrapper<>();
                memberUserQuery.eq(MemberUser::getMemberId, dto.getMemberId());
                MemberUser memberUser = memberUserService.getOne(memberUserQuery);
                if (Objects.nonNull(memberUser)) {
                    //查询0元申请会员，同一memberId只能申请一次
                    LambdaQueryWrapper<MemberRechargeRecord> recordQueryWrapper = new LambdaQueryWrapper<>();
                    recordQueryWrapper.eq(MemberRechargeRecord::getMemberId, dto.getMemberId());
                    recordQueryWrapper.eq(MemberRechargeRecord::getApplyStatus, MemberRechargeRecordEnum.SUCCESS.getCoed());
                    recordQueryWrapper.eq(MemberRechargeRecord::getActivityType, MemberRechargeRecordEnum.ZERO_YUAN_APPLY.getCoed());
                    List<MemberRechargeRecord> list = memberRechargeRecordMapper.selectList(recordQueryWrapper);
                    if (list != null && list.size() > 0) {
                        throw new BusinessException(ResultCodeEnum.ZERO_YUAN_MEMBER_EXIST);
                    }
                    //会员申请
                    MemberRechargeRecord memberRechargeRecord = new MemberRechargeRecord();
                    memberRechargeRecord.setId(GenerationIdUtil.getId());
                    memberRechargeRecord.setMemberId(dto.getMemberId());
                    memberRechargeRecord.setMobile(memberUser.getMobile());
                    memberRechargeRecord.setApplyType(MemberRechargeRecordEnum.RECHARGE.getCoed());
                    memberRechargeRecord.setApplyTime(LocalDateTime.now());
                    memberRechargeRecord.setApplyStatus(MemberRechargeRecordEnum.SUCCESS.getCoed());
                    memberRechargeRecord.setPaidFee(BigDecimal.ZERO);
                    memberRechargeRecord.setMemberTime(180);
                    LocalDateTime startTime = LocalDateTime.now();
                    /**
                     * 已有会员时间，且非过期处理
                     */
                    if (memberUser.getMemberExpiresTime() != null && LocalDateTime.now().isBefore(memberUser.getMemberExpiresTime())) {
                        //开始时间为，到期时间的后一日
                        startTime = LocalDateTimeUtil.offset(memberUser.getMemberExpiresTime(), 1, ChronoUnit.DAYS);
                    }
                    memberRechargeRecord.setStartTime(startTime);
                    //时间偏移
                    LocalDateTime offset = LocalDateTimeUtil.offset(LocalDateTime.now(), memberRechargeRecord.getMemberTime(), ChronoUnit.DAYS);
                    memberRechargeRecord.setEndTime(offset);
                    memberRechargeRecord.setCreateTime(LocalDateTime.now());
                    memberRechargeRecord.setUpdateTime(LocalDateTime.now());
                    memberRechargeRecord.setActivityType(MemberRechargeRecordEnum.ZERO_YUAN_APPLY.getCoed());
                    int insert = memberRechargeRecordMapper.insert(memberRechargeRecord);
                    if (insert > 0) {
                        //如果新增成功，则更新用户会员表的过期时间
                        memberUser.setMemberExpiresTime(memberRechargeRecord.getEndTime());
                        memberUser.setUpdateTime(LocalDateTime.now());
                        memberUserService.updateById(memberUser);
                        //赠送优惠券
                        sendCoupon(user, requestId);

                    }
                }
            } else {
                log.warn("加锁失败：{}", user.getNumberId());
                return false;
            }
        } catch (Exception e) {
            log.warn("领取会员异常{}", e.getMessage());
            throw e;
        } finally {
            if (isLock) {
                lock.unlock();
            }
        }

        return true;
    }

    @Override
    public WxUserVo getUser(String code) {
        WxMaJscode2SessionResult wxUserInfo = iAppLoginService.getWxUserInfo(new WxUserDTO().setCode(code), UUID.randomUUID().toString());
        log.info("[微信授权解析]当前用户返回信息:{}", JSON.toJSONString(wxUserInfo));

        LambdaQueryWrapper<WxUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WxUser::getOpenid, wxUserInfo.getOpenid()).eq(WxUser::getIsDelete, 0).eq(WxUser::getType, ChannelSourceEnum.WX.getKey());
        WxUser user = getOne(wrapper);
        log.info("查询返回用户:{}", JSON.toJSONString(user));
        if (user == null) return null;

        Object obj = RedisUtil.get(BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(user.getId().toString()));
        if (obj == null) return null;

        return JSONObject.parseObject(obj.toString(), WxUserVo.class);
    }

    /**
     * 修改集运的用户id
     * @param user
     * @return
     */
    @Override
    public Integer updateJyUserId(WxUserVo user) {
        if(null!=user.getJyUserId() && null!=user.getNumberId()){
            //获取所有的主键id
            List<Long> ids = wxUserMapper.getIdListByIdNumberId(user.getNumberId());
            if(CollectionUtils.isNotEmpty(ids)){
                Collections.sort(ids);
                wxUserMapper.updateJyUserId(user.getJyUserId(),ids);
            }
        }
        return 0;
    }



    /**
     * 匹配符合规则的优惠券
     *
     * @param user
     * @param requestId
     */
    public void sendCoupon(WxUserVo user, String requestId) {
        new Thread(() -> {
            try {
                log.info("开始执行发送优惠券异步代码...");
                //查询活动配置。
                log.info("请求id==>{}查询活动配置,入参==>{}", requestId);
                Result<List<MemberActivityQueryDto>> listResult = promotionFeighClient.activityList();
                log.info("请求id==>{}查询活动配置,出参==>{}", requestId, JSON.toJSONString(listResult));
                List<MemberActivityQueryDto> result = listResult.result();
                if (result.isEmpty()) {
                    log.info("请求id==>{}当前没有查询到活动配置", requestId);
                }
                List<MemberActivityQueryDto> isRight = Lists.newArrayList();
                for (MemberActivityQueryDto memberActivityQueryDto : result) {
                    if (BaseDictionaryEnums.ACTIVITY_TYPE_3.getCode().equals(memberActivityQueryDto.getType())) {
                        log.info("请求id==>{}【开通星球】,符合活动配置", requestId);
                        isRight.add(memberActivityQueryDto);
                    }
                }
                //达到天数
                if (!isRight.isEmpty()) {
                    for (MemberActivityQueryDto queryDto : isRight) {
                        if (BaseDictionaryEnums.GIFT_TYPE_3.getCode().equals(queryDto.getGiftType())) {//签到券
                            Integer integer = handleCoupon(user, queryDto.getGiftRelation(), requestId);
//                    map.put("coupon", integer.intValue());//优惠券
                        }
                    }
                } else {
                    log.info("请求id==>{}当前没有查询到活动配置", requestId);
                }
            } catch (Exception e) {
                log.error("发送优惠券异步代码异常:{}", e.getMessage());
                e.printStackTrace();
            }
        }).start();
    }

    /**
     * 给用户发送优惠券
     *
     * @param user
     * @param giftRelation
     */
    private Integer handleCoupon(WxUserVo user, String giftRelation, String requestId) {
        PromotionUserGetAppletsDto appletsDto = new PromotionUserGetAppletsDto();
        appletsDto.setUserAlias(user.getNickName());
        appletsDto.setUserId(user.getId().longValue());
        appletsDto.setUserUuid(user.getOpenid());
        appletsDto.setUserPhone(user.getMobile());
        appletsDto.setNumberId(user.getNumberId().longValue());
        appletsDto.setProId(Long.parseLong(giftRelation));
        log.info("请求id==>{},领取优惠券,入参==>{}", requestId, JSON.toJSONString(appletsDto));
        Result<Long> activityCoupon = promotionFeighClient.getActivityCoupon(appletsDto);
        log.info("请求id==>{},领取优惠券,出参==>{}", requestId, JSON.toJSONString(activityCoupon));
        if (activityCoupon.isSucc()) {
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public void addActionRecord(ActionRecordDTO dto) {
        //保存用户行为记录
        UserActionRecord record = beanMapper.map(dto, UserActionRecord.class);
        record.setId(GenerationIdUtil.getId());
        //用户可能登录
        try {
            WxUserVo user = sessionUtil.getUser();
            record.setUserId(Long.valueOf(user.getId()));
            record.setMemberId(Long.valueOf(user.getNumberId()));
            record.setMobile(user.getMobile());
            record.setUserName(user.getNickName());
        } catch (Exception e) {
            e.printStackTrace();
            log.info("获取用户信息失败");
        }
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        userActionRecordMapper.insert(record);
    }

    @Override
    public Boolean extension(WxUserVo user) throws InterruptedException {
        log.info("用户会员身份时长延长：{}", JSON.toJSONString(user));
        RLock lock = redisson.getLock(ChannelMemberEnum.MemberUserRedis.APPLY_MEMBER_EXTENSION + user.getNumberId());
        boolean b = false;
        try {
            //避免重复延长
            b = lock.tryLock(0, 5, TimeUnit.SECONDS);
            if (b) {
                //重新查库，判断过期时间
                long start = System.currentTimeMillis();
                MemberUser memberUser = iMemberUserService.getOne(new QueryWrapper<MemberUser>().eq("member_id", user.getNumberId()));
                long end = System.currentTimeMillis();
                log.info("用户会员身份时长延长，出参：{} 耗时：{}", JsonUtils.toJson(memberUser), (end - start));
                if (memberUser == null) {
                    throw new BusinessException(ResultCodeEnum.USER_NOT_EXIST);
                }
                if (memberUser.getMemberExpiresTime() == null) {
                    throw new BusinessException(ResultCodeEnum.IS_NOT_MEMBER);
                }
                if (LocalDateTime.now().isBefore(memberUser.getMemberExpiresTime())) {
                    throw new BusinessException(ResultCodeEnum.MEMBER_IS_NOT_EXPIRED);
                }
                //从当天时间算
                LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).plusDays(180);

                memberUser.setMemberExpiresTime(localDateTime.withHour(23).withMinute(59).withSecond(59));
                log.info("用户会员身份时长延长，入库前数据：{}", JSON.toJSONString(memberUser));
                iMemberUserService.updateById(memberUser);
                return true;
            } else {
                log.info("用户会员身份时长延长枷锁失败");
                throw new BusinessException(ResultCodeEnum.OPERATE_REPEATE);
            }
        } catch (Exception e) {
            log.info("用户会员身份时长延长失败");
            throw e;
        } finally {
            if (b) {
                lock.unlock();
            }
        }
    }

}
