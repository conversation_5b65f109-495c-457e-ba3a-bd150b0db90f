package com.yl.applets.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.entity.LuckDrawPool;
import com.yl.applets.mapper.applets.LuckDrawPoolMapper;
import com.yl.applets.service.ILuckDrawPoolService;
import com.yl.common.base.config.OrikaBeanMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： zhanzhihong
 * @Date： 2022/08/08
 */
@Service
public class LuckDrawPoolServiceImpl extends ServiceImpl<LuckDrawPoolMapper, LuckDrawPool> implements ILuckDrawPoolService {
    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Autowired
    private LuckDrawPoolMapper luckDrawPoolMapper;



}
