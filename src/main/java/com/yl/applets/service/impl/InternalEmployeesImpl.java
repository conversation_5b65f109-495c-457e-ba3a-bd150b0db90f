package com.yl.applets.service.impl;

import com.yl.applets.feign.CustomerPlatformNetworkFeignClient;
import com.yl.applets.service.InternalEmployees;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/26 10:08
 */
@Service
@Slf4j
public class InternalEmployeesImpl implements InternalEmployees {
    /**
     * 小程序内部员工寄件白名单
     */
    @Value("#{'${applets.internal.employees.phone:11}'.split(',')}")
    private List<String> appletsPhoneList;

    @Autowired
    private CustomerPlatformNetworkFeignClient customerPlatformNetworkFeignClient;

    @Override
    public Boolean isInternalEmployees(String mobile) {
        //检验白名单员工
        if(appletsPhoneList.contains(mobile)){
            return true;
        }
        Result<Boolean> staffExistByMobile = customerPlatformNetworkFeignClient.getStaffExistByMobile(mobile);
        if(staffExistByMobile != null && staffExistByMobile.isSucc() && !Objects.isNull(staffExistByMobile.getData()) && staffExistByMobile.getData()){
            return true;
        }
        return false;
    }
}
