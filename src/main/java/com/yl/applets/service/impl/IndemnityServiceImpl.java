package com.yl.applets.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.yl.applets.dto.BatchWaybillsDTO;
import com.yl.applets.dto.indemnity.IndemnityVO;
import com.yl.applets.dto.indemnity.RequestDTO;
import com.yl.applets.entity.OmsWaybill;
import com.yl.applets.feign.BgApiFeignClient;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.IndemnityFeignClient;
import com.yl.applets.mapper.oms.OmsWaybillMapper;
import com.yl.applets.service.IndemnityService;
import com.yl.applets.utils.ResultUtil;
import com.yl.applets.vo.OmsWaybillDetailVO;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： zhanzhihong
 * @Date： 2023/01/09
 */
@Slf4j
@Service
public class IndemnityServiceImpl implements IndemnityService {
    @Autowired
    private IndemnityFeignClient indemnityFeignClient;
    @Resource
    private OmsWaybillMapper omsWaybillMapper;
    @Autowired
    private BgApiFeignClient bgApiFeignClient;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Value("${bigData.query.userId}")
    private String userId;

    @Value("${bigData.query.secretKey}")
    private String secretKey;

    @Value("${waybill.grey.phone:all}")
    private String greyPhone;

    @Override
    public Result<List<IndemnityVO>> wxQueryByUserId(WxUserVo user, Integer status) {
        List<IndemnityVO> result = new ArrayList<>();
        //可申请走大数据的接口,否则走服务质量的接口
        if (status == 1) {
            result = bgApiGetData(user);
        } else {
            log.info("理赔查询服务质量信息入参：用户id{},状态{}",JSONObject.toJSONString(user.getNumberId()),status);
            Result<List<IndemnityVO>> listResult = indemnityFeignClient.wxQueryByUserId(user.getNumberId(), status);
            log.info("理赔查询服务质量信息出参：{}",JSONObject.toJSONString(listResult));
            ResultUtil.checkFeignResult(listResult);
            result = listResult.result();
        }
        log.info("查询返回数据:{}", JSONObject.toJSONString(result));
        //查询运单表，补全参数
        if (result != null && !result.isEmpty()) {
            Map<String, OmsWaybillDetailVO> omsWaybillDetailMap = null;
            List<String> waybillNoList = result.stream().map(IndemnityVO::getWaybillNo).collect(Collectors.toList());
            log.info("理赔查询运单信息入参：{}",JSONObject.toJSONString(waybillNoList));
            List<OmsWaybillDetailVO> omsWaybillDetailVOS = new ArrayList<>(waybillNoList.size());
            if (StrUtil.equals(greyPhone, "all") || greyPhone.contains(user.getMobile())){
                //走网点接口
                this.handleChannelWaybillQuery(waybillNoList, omsWaybillDetailVOS);
            }else {
                log.info("工单查运单表老逻辑");
                omsWaybillDetailVOS = omsWaybillMapper.selectWaybillNoList(waybillNoList);
            }
            log.info("理赔查询运单信息返回：{}",JSONObject.toJSONString(omsWaybillDetailVOS));
            if (omsWaybillDetailVOS != null && omsWaybillDetailVOS.size() > 0) {
                omsWaybillDetailMap = omsWaybillDetailVOS.stream().collect(Collectors.toMap(OmsWaybillDetailVO::getWaybillNo, Function.identity()));
            }
            for (IndemnityVO indemnityVO : result) {
                OmsWaybillDetailVO omsWaybillDetailVO =new OmsWaybillDetailVO();
                if(omsWaybillDetailMap!=null){
                    omsWaybillDetailVO =  omsWaybillDetailMap.get(indemnityVO.getWaybillNo()) !=null?omsWaybillDetailMap.get(indemnityVO.getWaybillNo()):new OmsWaybillDetailVO();
                }
                if (Objects.nonNull(omsWaybillDetailVO)) {
                    indemnityVO.setSenderName(omsWaybillDetailVO.getSenderName());
                    indemnityVO.setSenderProvinceName(omsWaybillDetailVO.getSenderProvinceName());
                    indemnityVO.setReceiverName(omsWaybillDetailVO.getReceiverName());
                    indemnityVO.setReceiverProvinceName(omsWaybillDetailVO.getReceiverProvinceName());
                }

            }
        }
        return Result.success(result);
    }

    private void handleChannelWaybillQuery(List<String> waybillNoList, List<OmsWaybillDetailVO> omsWaybillDetailVOS) {
        BatchWaybillsDTO dto = new BatchWaybillsDTO();
        dto.setWaybillNos(waybillNoList);
        Result<List<OmsWaybill>> batchDetailResult = channelApiFeignClient.batchDetail(dto);
        ResultUtil.checkFeignResult(batchDetailResult);
        if (CollectionUtil.isEmpty(batchDetailResult.getData())) return;

        for (OmsWaybill item : batchDetailResult.getData()) {
            OmsWaybillDetailVO omsWaybillDetailVO = new OmsWaybillDetailVO();
            omsWaybillDetailVO.setWaybillNo(item.getWaybillNo());
            omsWaybillDetailVO.setSenderName(item.getSenderName());
            omsWaybillDetailVO.setSenderProvinceName(item.getSenderProvinceName());
            omsWaybillDetailVO.setReceiverName(item.getReceiverName());
            omsWaybillDetailVO.setReceiverProvinceName(item.getReceiverProvinceName());
            omsWaybillDetailVOS.add(omsWaybillDetailVO);
        }
    }

    /**
     * 大数据查询可申请理赔工单
     *  --微信小程序客户自助理赔
     * create table jms_dm.dm_wechat_custormer_auto_claim_detail_hi(
     *   create_date            date         comment '签收日期 YYYY-MM-DD'
     *  ,member_id              varchar(100) comment '用户ID'
     *  ,sender_mobile_phone    varchar(100) comment '寄件人手机号'
     *  ,receiver_mobile_phone  varchar(100) comment '收件人手机号'
     *  ,waybill_id             varchar(100) comment '运单号'
     *  ,id                     varchar(100) comment '订单编号'
     *  ,create_time            datetime     comment '创建时间'
     *  ,sign_time              datetime     comment '签收时间'
     * )
     * ENGINE = OLAP
     * DUPLICATE KEY( create_date,member_id)
     * COMMENT "微信小程序客户自助理赔表"
     * PARTITION BY RANGE(create_date)
     * (START ("2022-12-01") END ("2023-01-10") EVERY (INTERVAL 1 day)
     * )
     * DISTRIBUTED BY HASH(member_id) BUCKETS 4
     * PROPERTIES (
     * "replication_num" = "3",
     * "dynamic_partition.enable" = "true",
     * "dynamic_partition.time_unit" = "DAY",
     * "dynamic_partition.time_zone" = "Asia/Shanghai",
     * "dynamic_partition.start" = "-366",
     * "dynamic_partition.end" = "3",
     * "dynamic_partition.prefix" = "p",
     * "dynamic_partition.buckets" = "4",
     * "in_memory" = "false",
     * "storage_format" = "V2"
     * );
     * @param user  用户
     * @return
     */
    @Override
    public List<IndemnityVO> bgApiGetData(WxUserVo user) {
        RequestDTO requestDTO = new RequestDTO();
        String reqId = UUID.randomUUID().toString();
        requestDTO.setReqId(reqId);
        requestDTO.setUserId(userId);
        requestDTO.setTimestamp(System.currentTimeMillis());

        StringBuffer mulParamStr = new StringBuffer();
        mulParamStr.append("userId=").append(userId);
        mulParamStr.append("&secretKey=").append(secretKey);
        mulParamStr.append("&reqId=").append(reqId);
        mulParamStr.append("&timestamp=").append(requestDTO.getTimestamp());
        String md5Str = DigestUtils.md5DigestAsHex(mulParamStr.toString().getBytes());
        requestDTO.setSign(md5Str);
        String encryptMobile = "";
        if(!StringUtils.isEmpty(user.getMobile())){
            encryptMobile = omsWaybillMapper.selectF_encrypt(user.getMobile());
            log.info("查询加密手机号==>{}",encryptMobile);
        }
        String sql="";
        if(StringUtils.isEmpty(encryptMobile)){
            sql = "SELECT waybill_id AS waybillNo,member_id AS memberId,sender_mobile_phone AS senderMobilePhone,receiver_mobile_phone AS receiverMobilePhone,sign_time AS signTime" +
                    " FROM" +
                    " jms_dm.dm_wechat_custormer_auto_claim_detail_hi" +
                    " WHERE" +
                    " member_id =" + user.getNumberId()+
                    " or sender_mobile_phone = '"+user.getMobile() +"'"+
                    " ORDER BY create_time DESC" +
                    " limit 0,1000";
        }else {
            sql = "SELECT waybill_id AS waybillNo,member_id AS memberId,sender_mobile_phone AS senderMobilePhone,receiver_mobile_phone AS receiverMobilePhone,sign_time AS signTime" +
                    " FROM" +
                    " jms_dm.dm_wechat_custormer_auto_claim_detail_hi" +
                    " WHERE" +
                    " member_id =" + user.getNumberId()+
                    " or (sender_mobile_phone = '"+user.getMobile() +"' or receiver_mobile_phone ='"+encryptMobile+"' )"+
                    " ORDER BY create_time DESC" +
                    " limit 0,1000";
        }
        sql = String.format(sql, user.getNumberId());
        Map<String, Object> p = new HashMap<>();
        p.put("sql", sql);
        requestDTO.setP(p);
        requestDTO.setSql(sql);
        log.info("大数据查理赔,入参==>{}",JSON.toJSONString(requestDTO));
        JSONObject object = bgApiFeignClient.queryDataBySql(requestDTO);
        log.info("大数据返回理赔==>{}",JSON.toJSONString(object));
        String result = object.toJSONString();
        Result<List<IndemnityVO>> resultList = JSON.parseObject(result, new TypeReference<Result<List<IndemnityVO>>>() {});
        log.info("大数据返回==>{}",JSON.toJSONString(resultList));
        if (resultList != null && resultList.getCode() != 1) {
            log.info("大数据返回异常");
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
        List<IndemnityVO> resultVoList = resultList.result();
        if(CollectionUtil.isEmpty(resultVoList)){
            log.info("大数据返回为空");
            return new ArrayList<>();
        }
        resultVoList.forEach(r->{
            //判断是寄件人还是收件人
            if (StrUtil.equals(r.getMemberId(),String.valueOf(user.getNumberId())) || StrUtil.equals(r.getSenderMobilePhone(),user.getMobile())){
                r.setCustomerType(1);
            }else{
                r.setCustomerType(2);
                //收件人用当前用的手机号
                r.setReceiverMobilePhone(user.getMobile());
            }
        });

        return resultVoList;
    }
}
