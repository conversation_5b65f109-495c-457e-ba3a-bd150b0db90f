package com.yl.applets.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.entity.MemberLevel;
import com.yl.applets.mapper.applets.MemberLevelMapper;
import com.yl.applets.service.IMemberLevelService;
import com.yl.applets.vo.MemberLevelVO;
import com.yl.common.base.config.OrikaBeanMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * @Description: 用户管理-会员等级 实现
 * @CreateDate: Created in {2021/7/8 17:48}
 * @Author: DongFeixiang
 */
@Service
@Slf4j
public class MemberLevelServiceImpl extends ServiceImpl<MemberLevelMapper, MemberLevel> implements IMemberLevelService {


    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Override
    public List<MemberLevelVO> listLevel() {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("从数据库查询会员信息,请求编号:{}", requestId);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("批量查询会员信息");
        List<MemberLevel> list = list();
        stopWatch.stop();
        for (MemberLevel memberLevel : list) {
            //如果更新时间为空，则设置创建时间为操作时间
            LocalDateTime updateTime = memberLevel.getUpdateTime();
            if (Objects.isNull(updateTime)) {
                memberLevel.setUpdateTime(memberLevel.getCreatTime());
            }
        }
        return orikaBeanMapper.mapAsList(list, MemberLevelVO.class);
    }
}
