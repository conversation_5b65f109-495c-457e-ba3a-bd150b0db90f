package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yl.applets.entity.MemberRechargeRecord;
import com.yl.applets.entity.MemberUser;
import com.yl.applets.enums.ChannelMemberEnum;
import com.yl.applets.enums.MemberRechargeRecordEnum;
import com.yl.applets.mapper.applets.MemberRechargeRecordMapper;
import com.yl.applets.mapper.applets.MemberUserMapper;
import com.yl.applets.service.IMemberLevelService;
import com.yl.applets.service.IMemberUserService;
import com.yl.applets.vo.MemberLevelVO;
import com.yl.applets.vo.MemberUserDetailVo;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 会员用户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-7-7 17:31:07
 */
@Service
@Slf4j
public class MemberUserServiceImpl extends ServiceImpl<MemberUserMapper, MemberUser> implements IMemberUserService {

    @Autowired
    private MemberUserMapper memberUserMapper;

    @Autowired
    private IMemberLevelService memberLevelService;

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Autowired
    private MemberRechargeRecordMapper memberRechargeRecordMapper;

    static final String LEVEL_SIGN_KEY = "shk&jag186@alL71xv";

    @Override
    public Result<MemberUserDetailVo> queryMemberUser(WxUserVo user) {
        if (user.getNumberId() == null) {
            throw new ServiceException(ResultCodeEnum.USER_NOT_EXIST);
        }
        QueryWrapper<MemberUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("member_id", user.getNumberId()).eq("is_delete", 0);
        MemberUser memberUser = memberUserMapper.selectOne(queryWrapper);
        if (memberUser == null) {
            throw new ServiceException(ResultCodeEnum.USER_NOT_EXIST);
        }
        //先去redis拿，拿不到去二级缓存拿，redis和二级都为空，去查库
        Object value = RedisUtil.hGet(ChannelMemberEnum.MemberUserRedis.CHANEL_USER_GROW_MAP, user.getNumberId() + "");
        Object value2 = RedisUtil.hGet(ChannelMemberEnum.MemberUserRedis.CHANEL_USER_GROW_MAP_2, user.getNumberId() + "");
        Long o = value == null ? 0L : Long.parseLong(value + "");
        Long o2 = value2 == null ? 0L : Long.parseLong(value2 + "");
        Long growValue1 = memberUser.getGrowValue() == null ? 0 : memberUser.getGrowValue();
        Long max = Collections.max(Lists.newArrayList(o, o2, growValue1));
        memberUser.setGrowValue(max);
        //复制数据
        MemberUserDetailVo result = orikaBeanMapper.map(memberUser, MemberUserDetailVo.class);
        //获取会员等级list
        Object o1 = RedisUtil.get(ChannelMemberEnum.MemberUserRedis.CHANNEL_MEMBER_LEVEL_CONFIG);
        List<MemberLevelVO> levelVOList = Lists.newArrayList();
        if (o1 != null) {
            levelVOList = (List<MemberLevelVO>) o1;
            log.info("从缓存中获取会员等级：{}", JSON.toJSONString(levelVOList));
        } else {
            levelVOList = memberLevelService.listLevel();
            RedisUtil.set(ChannelMemberEnum.MemberUserRedis.CHANNEL_MEMBER_LEVEL_CONFIG, levelVOList);
            log.info("会员等级查询结果：{}放入redis中", JSON.toJSONString(levelVOList));
        }
        //按照大小排序
        Collections.sort(levelVOList, Comparator.comparing(MemberLevelVO::getMemberLevel));
        //根据当前成长值，匹配会员等级信息，并返回
        long growValue = memberUser.getGrowValue().longValue();
        for (MemberLevelVO vo : levelVOList) {
            long start = vo.getGrowScoreStart().longValue();
            long end = vo.getGrowScoreEnd().longValue();
            //特殊情况
            //当用户成长值不为0且不在任何等级区间内，以比此成长值低的最近一个区间为准
            if (growValue != 0) {
                //截止值的不大于当前值
                if (end <= growValue) {
                    result.setLevelName(vo.getLevelName());
                    result.setMemberLevel(vo.getMemberLevel());
                    result.setGrowScoreStart(start);
                    result.setGrowScoreEnd(end);
                }
            }
            //区间满足
            if (start <= growValue && end >= growValue) {
                result.setLevelName(vo.getLevelName());
                result.setMemberLevel(vo.getMemberLevel());
                result.setGrowScoreStart(start);
                result.setGrowScoreEnd(end);
                break;
            }
            // 当用户成长值为0 ，则按等级一计算，不管是一级配置的分值范围是多少
            if (growValue == 0) {
                if (1 == vo.getMemberLevel().intValue()) {
                    result.setLevelName(vo.getLevelName());
                    result.setMemberLevel(1);
                    result.setGrowScoreStart(start);
                    result.setGrowScoreEnd(end);
                    break;
                }
            }
        }
        //设置下一级的开始值
        for (int i = 0; i < levelVOList.size(); i++) {
            if (levelVOList.get(i).getMemberLevel().equals(result.getMemberLevel())) {
                if (i + 1 >= levelVOList.size()) {
                    result.setNextStartGrow(null);
                } else {
                    MemberLevelVO vo = levelVOList.get(i + 1);
                    result.setNextStartGrow(vo.getGrowScoreStart());
                    result.setNextLevelName(vo.getLevelName());
                }
                break;
            }
        }
        //判断是否是开通会员
        if (memberUser.getMemberExpiresTime() != null) {
            result.setIsMember(1);
            //判断会员是否过期
            if (LocalDateTime.now().isBefore(memberUser.getMemberExpiresTime())){
                result.setIsExpire(0);
            }
        }

        //首次申请会员的日期
        LambdaQueryWrapper<MemberRechargeRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberRechargeRecord::getMemberId,memberUser.getMemberId());
        wrapper.eq(MemberRechargeRecord::getApplyStatus, MemberRechargeRecordEnum.SUCCESS.getCoed());
        wrapper.eq(MemberRechargeRecord::getActivityType, MemberRechargeRecordEnum.ZERO_YUAN_APPLY.getCoed());
        MemberRechargeRecord memberRechargeRecord = memberRechargeRecordMapper.selectOne(wrapper);
        if (Objects.nonNull(memberRechargeRecord)){
            result.setApplyTime(memberRechargeRecord.getApplyTime());
        }
        //加验签，防止篡改
        long time = System.currentTimeMillis();
        result.setTimes(time+"");
        result.setSign(DigestUtils.md5Hex(DigestUtils.md5Hex(result.getMemberLevel()+""+time + LEVEL_SIGN_KEY)));
        return Result.success(result);
    }

}
