package com.yl.applets.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.entity.LuckDraw;
import com.yl.applets.mapper.applets.LuckDrawMapper;
import com.yl.applets.service.ILuckDrawService;
import com.yl.common.base.config.OrikaBeanMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： <PERSON><PERSON><PERSON><PERSON>
 * @Date： 2022/08/08
 */
@Service
@Slf4j
public class LuckDrawServiceImpl extends ServiceImpl<LuckDrawMapper, LuckDraw> implements ILuckDrawService {

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;



}
