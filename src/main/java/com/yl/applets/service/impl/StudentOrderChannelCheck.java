package com.yl.applets.service.impl;

import com.yl.applets.constant.BaseConstant;
import com.yl.applets.service.OrderChannelCheck;
import com.yl.applets.service.StudentSendingService;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/21 20:29
 */
@Component("studentOrderChannelCheck")
@Slf4j
public class StudentOrderChannelCheck implements OrderChannelCheck {

    @Autowired
    private StudentSendingService studentSendingService;
    @Override
    public void check(Integer numberId) {
        Integer authenticationStatus = studentSendingService.getAuthenticationStatus(numberId);
        if(!Objects.equals(authenticationStatus, BaseConstant.STUDENT_SENDING_ONE)){
            throw new BusinessException(ResultCodeEnum.STUDENT_ORDER_NOT_SENDING_ERROR);
        }
    }
}
