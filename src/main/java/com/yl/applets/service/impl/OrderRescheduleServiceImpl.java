package com.yl.applets.service.impl;

import com.yl.applets.dto.CustomerConfigDTO;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.service.OrderRescheduleService;
import com.yl.applets.vo.OmsOrderRescheduleVO;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/4/22 17:40
 */
@Slf4j
@Service
public class OrderRescheduleServiceImpl implements OrderRescheduleService {

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Override
    public OmsOrderRescheduleVO getCustomerConfigByOrderId(CustomerConfigDTO dto) {
        Result<OmsOrderRescheduleVO> result = channelApiFeignClient.getCustomerConfigByOrderId(dto);
        return result.result();
    }
}
