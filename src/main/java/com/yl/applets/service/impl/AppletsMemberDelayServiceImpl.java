package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.yl.applets.mapper.applets.AppletsMemberDelayMapper;
import com.yl.applets.service.AppletsMemberDelayService;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.vo.WxUserVo;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-02-09 16:24
 * @Version 1.0
 */
@Service
@Slf4j
public class AppletsMemberDelayServiceImpl implements AppletsMemberDelayService {

    @Value("${yl.member.extension.startTime:2022-08-01}")
    private String startTime;

    @Value("${yl.member.extension.endTime:2022-09-30}")
    private String endTime;

    @Value("${yl.member.extension.count:2}")
    private String count;

    @Value("${yl.member.extension.enable:true}")
    private Boolean flag;


    @Autowired
    private AppletsMemberDelayMapper appletsMemberDelayMapper;

    public Boolean qryMemberIsExtension(WxUserVo user) {

        if (flag) {
            //判断是否提示过当前用户
            String keyBuilder = BcRedisKeyEnum.APPLETS_MEMBER_EXPIRE_TOKEN.keyBuilder(user.getNumberId().toString());
            Boolean b = RedisUtil.hasKey(keyBuilder);
            log.info("用户：{} 查询redis缓存数据：{}", JSON.toJSONString(user), b);
            if (!b) {
                long start = System.currentTimeMillis();
                Integer integer = appletsMemberDelayMapper.qryMemberCount(user.getNumberId().toString(), startTime, endTime, count);
                log.info("查询续期会员数据：{}，耗时：{}", JSON.toJSONString(integer), (System.currentTimeMillis() - start));
                if (Objects.nonNull(integer) && integer > 0) {
                    RedisUtil.setEx(keyBuilder, user.getNumberId().toString(), 15638400);
                    return true;
                }
            }
        }
        return false;
    }
}
