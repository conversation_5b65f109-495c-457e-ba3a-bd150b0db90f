package com.yl.applets.service.impl;

import com.yl.applets.feign.MaterialSpecificationFeignClient;
import com.yl.applets.service.IAppMaterialSpecificationService;
import com.yl.applets.vo.MaterialSpecificationDetailVO;
import com.yl.applets.vo.lmdm.AppMaterialSpecificationVO;
import com.yl.common.base.config.OrikaBeanMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019-08-23
 */
@Slf4j
@Service
public class AppMaterialSpecificationServiceImpl implements IAppMaterialSpecificationService {

    @Autowired
    private MaterialSpecificationFeignClient materialSpecificationFeignClient;
    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Override
    public List<AppMaterialSpecificationVO> getAppList() {
        List<MaterialSpecificationDetailVO> list = materialSpecificationFeignClient.list(null).result();
        return orikaBeanMapper.mapAsList(list, AppMaterialSpecificationVO.class);
    }
}
