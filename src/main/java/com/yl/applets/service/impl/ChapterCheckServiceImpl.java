package com.yl.applets.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tencentcloudapi.captcha.v20190722.CaptchaClient;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaMiniResultRequest;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaMiniResultResponse;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.yl.applets.service.ChapterCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.TimeZone;
import java.util.TreeMap;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-10-26 14:41
 */
@Slf4j
@Service
public class ChapterCheckServiceImpl implements ChapterCheckService {
    // 腾讯云认证信息
    @Value("${qq.captcha.host:captcha.tencentcloudapi.com}")
    private String host;

    // 腾讯云认证信息
    @Value("${qq.captcha.secretId:AKIDUpysXxjKhpjPr57whFZeXCcpoZ5LM9hz}")
    private String SECRET_ID;

    // 腾讯云认证信息
    @Value("${qq.captcha.secretKey:5vV3QejdIm7JMgqncSFsHbFnASdgZP7d}")
    private String SECRET_KEY;

    // 腾讯云认证信息
    @Value("${qq.captcha.secretAes:ZTUgnJukVHNtQLC8}")
    private String secretAes;

    // 腾讯云认证信息
    @Value("${qq.captcha.CaptchaAppId:2033831116}")
    private Integer CaptchaAppId;

    // 腾讯云认证信息
    @Value("${qq.captcha.AppSecretKey:0JBcJ89619m2YGLifUxGSLg**}")
    private String AppSecretKey;


    // 腾讯云认证信息-小程序
    @Value("${qq.captcha.applets.AppSecretKey:0JBcJ89619m2YGLifUxGSLg**}")
    private String AppSecretKeyApplets;

    // 腾讯云认证信息-小程序
    @Value("${qq.captcha.applets.CaptchaAppId:2033831116}")
    private Integer CaptchaAppIdApplets;

    @Value("${chapter.isOpen:false}")
    private Boolean isOpen;

    private final static String CT_JSON = "application/json; charset=utf-8";
    private final static String service = "captcha";
    private final static String region = "ap-shanghai";
    private final static String action = "DescribeCaptchaResult";
    private final static String version = "2019-07-22";
    private final static String algorithm = "TC3-HMAC-SHA256";
    private final static String httpRequestMethod = "POST";
    private final static String canonicalUri = "/";
    private final static String canonicalQueryString = "";
    private final static String signedHeaders = "content-type;host";

    private final static Charset UTF8 = StandardCharsets.UTF_8;

    public  byte[] hmac256(byte[] key, String msg) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key, mac.getAlgorithm());
        mac.init(secretKeySpec);
        return mac.doFinal(msg.getBytes(UTF8));
    }

    public  String sha256Hex(String s) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] d = md.digest(s.getBytes(UTF8));
        return DatatypeConverter.printHexBinary(d).toLowerCase();
    }

    @Override
    public  boolean captchaCheckWeb(String ticket,String randstr,String userIp) throws Exception{
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 注意时区，否则容易出错
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        String date = sdf.format(new Date(Long.valueOf(timestamp + "000")));

        // ************* 步骤 1：拼接规范请求串 *************
        String canonicalHeaders = "content-type:application/json; charset=utf-8\n" + "host:" + host + "\n";

        Map<String,Object> paramMap=new TreeMap<>();
        paramMap.put("CaptchaType",9);
        paramMap.put("Ticket",ticket);
        paramMap.put("UserIp", userIp);
        paramMap.put("Randstr",randstr);
        paramMap.put("CaptchaAppId",CaptchaAppId);
        paramMap.put("AppSecretKey",AppSecretKey);
        String payload = JSONObject.toJSONString(paramMap);
        String hashedRequestPayload = sha256Hex(payload);
        String canonicalRequest = httpRequestMethod + "\n" + canonicalUri + "\n" + canonicalQueryString + "\n"
                + canonicalHeaders + "\n" + signedHeaders + "\n" + hashedRequestPayload;

        // ************* 步骤 2：拼接待签名字符串 *************
        String credentialScope = date + "/" + service + "/" + "tc3_request";
        String hashedCanonicalRequest = sha256Hex(canonicalRequest);
        String stringToSign = algorithm + "\n" + timestamp + "\n" + credentialScope + "\n" + hashedCanonicalRequest;

        // ************* 步骤 3：计算签名 *************
        byte[] secretDate = hmac256(("TC3" + SECRET_KEY).getBytes(UTF8), date);
        byte[] secretService = hmac256(secretDate, service);
        byte[] secretSigning = hmac256(secretService, "tc3_request");
        String signature = DatatypeConverter.printHexBinary(hmac256(secretSigning, stringToSign)).toLowerCase();

        // ************* 步骤 4：拼接 Authorization *************
        String authorization = algorithm + " " + "Credential=" + SECRET_ID + "/" + credentialScope + ", "
                + "SignedHeaders=" + signedHeaders + ", " + "Signature=" + signature;

        HttpResponse response =  HttpRequest.post(" https://"+host).
                header("Authorization",authorization).
                header("Content-Type",CT_JSON).
                header("Host",host).
                header("X-TC-Action",action).
                header("X-TC-Timestamp",timestamp).
                header("X-TC-Version",version).
                header("X-TC-Region",region).body(payload).execute();
        JSONObject jsonObject= JSON.parseObject(response.body());
//        if(!"success".equals(jsonObject.getString("retmsg"))){
//            log.error("验证码校验出现异常：{}",JSONObject.toJSONString(response.body()));
//            return false;
//        }
        log.info("jsonObject:{}",jsonObject);
        Integer captchaCode = jsonObject.getJSONObject("Response").getInteger("CaptchaCode");
        if(captchaCode == 1){
            return true;
        }else{
            log.info("验证码校验失败：{}",jsonObject.getJSONObject("Response").getString("CaptchaMsg"));
            return false;
        }
    }

    @Override
    public boolean captchaCheckApplets(String ticket, String userIp) throws Exception {
        if(!isOpen){
            log.info("不校验滑块验证码");
            return true;
        }
        try{
            // 实例化一个认证对象，入参需要传入腾讯云账户secretId，secretKey,此处还需注意密钥对的保密
            // 密钥可前往https://console.cloud.tencent.com/cam/capi网站进行获取
            Credential cred = new Credential(SECRET_ID, SECRET_KEY);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("captcha.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            CaptchaClient client = new CaptchaClient(cred, "", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            DescribeCaptchaMiniResultRequest req = new DescribeCaptchaMiniResultRequest();
            req.setCaptchaType(9L);
            req.setTicket(ticket);
            req.setUserIp(userIp);
            req.setCaptchaAppId(CaptchaAppIdApplets.longValue());
            req.setAppSecretKey(AppSecretKeyApplets);
            // 返回的resp是一个DescribeCaptchaMiniResultResponse的实例，与请求对象对应
            DescribeCaptchaMiniResultResponse resp = client.DescribeCaptchaMiniResult(req);
            Long captchaCode = resp.getCaptchaCode();
            if(captchaCode == 1){
                return true;
            }else{
                log.info("验证码校验失败：{}",resp.getCaptchaMsg());
                return false;
            }
        } catch (TencentCloudSDKException e) {
            log.info("验证码校验失败TencentCloudSDKException：{}",e);
        }
        return false;
    }


//    public static void main(String[] args) {
//        try{
//            // 实例化一个认证对象，入参需要传入腾讯云账户secretId，secretKey,此处还需注意密钥对的保密
//            // 密钥可前往https://console.cloud.tencent.com/cam/capi网站进行获取
//            Credential cred = new Credential("AKIDjNlsiIabMq3yh9zd2C6lANL4XmlUxA8q", "ThgPu99tMYTLyLSJuFLBm7C1assc6JFv");
//            // 实例化一个http选项，可选的，没有特殊需求可以跳过
//            HttpProfile httpProfile = new HttpProfile();
//            httpProfile.setEndpoint("captcha.tencentcloudapi.com");
//            // 实例化一个client选项，可选的，没有特殊需求可以跳过
//            ClientProfile clientProfile = new ClientProfile();
//            clientProfile.setHttpProfile(httpProfile);
//            // 实例化要请求产品的client对象,clientProfile是可选的
//            CaptchaClient client = new CaptchaClient(cred, "", clientProfile);
//            // 实例化一个请求对象,每个接口都会对应一个request对象
//            DescribeCaptchaMiniResultRequest req = new DescribeCaptchaMiniResultRequest();
//            req.setCaptchaType(9L);
//            req.setTicket("AIvgocZ7u/Jdi3bykL6Qo1Enb/h02yzpjkag5YSx526zixqt1eFlJJqZIw7+0gdsttyRldnHzljzI4RSFsyYTg==");
//            req.setUserIp("127.0.0.1");
//            req.setCaptchaAppId(191545301L);
//            req.setAppSecretKey("WeasN7HMo0ogVqhmTYMmi3VVL");
//            // 返回的resp是一个DescribeCaptchaMiniResultResponse的实例，与请求对象对应
//            DescribeCaptchaMiniResultResponse resp = client.DescribeCaptchaMiniResult(req);
//            Long captchaCode = resp.getCaptchaCode();
//            if(captchaCode == 1){
//                log.info("........成功");
//            }else{
//                log.info("验证码校验失败：{}",resp.getCaptchaMsg());
//            }
//        } catch (TencentCloudSDKException e) {
//            log.info("验证码校验失败TencentCloudSDKException：{}",e);
//        }
//    }

}
