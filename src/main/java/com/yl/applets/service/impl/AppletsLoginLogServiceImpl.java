package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.dto.AppletsLoginLogDTO;
import com.yl.applets.entity.UserInfoPoliceEntity;
import com.yl.applets.enums.ChannelSourceEnum;
import com.yl.applets.enums.EquipmentEnum;
import com.yl.applets.mapper.applets.AppletsLoginLogMapper;
import com.yl.applets.service.AppletsLoginLogService;
import com.yl.applets.service.AppletsRegisterLogService;
import com.yl.applets.service.UserInfoPoliceService;
import com.yl.applets.utils.IpUtils;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.AppletsLoginLogEntity;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.util.GenerationIdUtil;
import com.yl.common.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description 保存登录日志
 * <AUTHOR>
 * @Date 2022/10/24 16:26
 * @Version 1.0
 */
@Slf4j
@Service
public class AppletsLoginLogServiceImpl extends ServiceImpl<AppletsLoginLogMapper, AppletsLoginLogEntity> implements AppletsLoginLogService {

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private AppletsRegisterLogService appletsRegisterLogService;

    @Value("#{'${municipalities.directly:}'.split(',')}")
    private List<String> municipalitiesDirectlyList;

    @Value("#{'${county.directly:}'.split(',')}")
    private List<String> countyDirectlyList;

    @Autowired
    private UserInfoPoliceService userInfoPoliceService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public Boolean saveLoginLog(AppletsLoginLogDTO dto, WxUserVo user) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        if (dto == null) {
            log.info("requestId请求==>{}中dto传参数据为空，无法保存数据,用户信息：{}", requestId, JsonUtils.toJson(user));
            return false;
        }
        log.info("requestId==>{}小程序日志登录保存，当前getUser缓存用户数据：{}", requestId, JSON.toJSONString(user));
        //获取当前reques
        AppletsLoginLogEntity loginUser = new AppletsLoginLogEntity();
        loginUser.setId(GenerationIdUtil.getId());
        loginUser.setMemberId(user.getNumberId().longValue());
        //设置手机号
        loginUser.setMobile(user.getMobile());
        loginUser.setNickName(user.getNickName());
        loginUser.setGender(user.getGender());
        loginUser.setLoginTime(LocalDateTime.now());
        //设置IP地址
        loginUser.setIp(IpUtils.getIpAddr(request));
        loginUser.setProvince(dto.getProvince());
        loginUser.setZone(dto.getZone());
        //处理下市的数据，当城市是省直辖县时返回为空，以及城市为北京、上海、天津、重庆四个直辖市时，该字段返回为空
        if (StringUtils.isEmpty(dto.getCity())) {
            //首先判断是不是直辖市，那就是省字段就是直辖市
            if (municipalitiesDirectlyList.contains(dto.getProvince())) {
                //市的字段就是省的字段
                dto.setCity(dto.getProvince());
            }
            //判断当前区是不是直辖区
            if (countyDirectlyList.contains(dto.getZone())) {
                //市字段和区字段相等
                dto.setCity(dto.getZone());
            }
        }
        loginUser.setCity(dto.getCity());

        //设备信息
        loginUser.setEquipmentBrand(dto.getEquipmentBrand());
        loginUser.setEquipmentType(dto.getEquipmentType());
        //渠道来源
        loginUser.setResourceType(ChannelSourceEnum.WX.getKey());
        loginUser.setCreateTime(LocalDateTime.now());
        loginUser.setEquipmentModel(dto.getEquipmentBrand() +"-"+ dto.getEquipmentModel());
        loginUser.setEquipmentCode(EquipmentEnum.getCodeByType(dto.getEquipmentType()));
        //参考:https://ones.jtexpress.com.cn/wiki/#/team/5BXYuw3B/space/A3mD3eun/page/WxdciPny  登陆来源
        loginUser.setResourcePolice("20");
        save(loginUser);
        log.info("保存登陆日志成功，用户信息：{}", JSON.toJSONString(loginUser));

        try {
            //处理YL_APPLETS_USER_INFO_POLICE
            LambdaQueryWrapper<UserInfoPoliceEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserInfoPoliceEntity::getUserAccount, user.getMobile());
            UserInfoPoliceEntity oldInfoPoliceEntity = userInfoPoliceService.getOne(wrapper);
            log.info("查询公安推送历史数据:{}", JSON.toJSONString(oldInfoPoliceEntity));
            if (Objects.nonNull(oldInfoPoliceEntity) && oldInfoPoliceEntity.getRegCity() == null && dto.getCity() != null){
                oldInfoPoliceEntity.setRegCity(dto.getCity());
                userInfoPoliceService.updateById(oldInfoPoliceEntity);
            }

        }catch (Exception e){
            log.error("登录日志增加省市区数据失败:{}", e);
        }

        //开启新事务处理注册省市区
        appletsRegisterLogService.saveInfo(dto, user);

        return true;
    }
}
