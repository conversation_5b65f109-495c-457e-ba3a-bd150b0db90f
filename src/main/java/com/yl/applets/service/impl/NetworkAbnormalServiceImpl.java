package com.yl.applets.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.alibaba.fastjson.JSONObject;
import com.yl.applets.dto.DispatchCodeRequestDTO;
import com.yl.applets.dto.OmsOrderApiDTO;
import com.yl.applets.dto.OrderDTO;
import com.yl.applets.feign.DispatchCodeFeignClient;
import com.yl.applets.service.INetworkAbnormalService;
import com.yl.applets.vo.DispatchCodeResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-08-09 15:57
 */
@Slf4j
@Service
public class NetworkAbnormalServiceImpl implements INetworkAbnormalService {

    @Autowired
    private DispatchCodeFeignClient dispatchCodeFeignClient;

    @Override
    public DispatchCodeRequestDTO orderDTOToDispatchCodeRequestDTO(OrderDTO dto) {
        DispatchCodeRequestDTO queryDTO=new DispatchCodeRequestDTO();
        queryDTO.setProvinceId(dto.getSenderProvinceId()+0L);
        queryDTO.setProvince(dto.getSenderProvinceName());
        queryDTO.setCityId(dto.getSenderCityId()+0L);
        queryDTO.setCity(dto.getSenderCityName());
        queryDTO.setAreaId(dto.getSenderAreaId()+0L);
        queryDTO.setArea(dto.getSenderAreaName());
        queryDTO.setDetails(dto.getSenderDetailedAddress());
        queryDTO.setSenderProviceId(dto.getSenderProvinceId()+0L);
        queryDTO.setSenderCityId(dto.getSenderCityId()+0L);
        queryDTO.setSenderAreaId(dto.getSenderAreaId()+0L);
        queryDTO.setSenderProvice(dto.getSenderProvinceName());
        queryDTO.setSenderCity(dto.getSenderCityName());
        queryDTO.setSenderArea(dto.getSenderAreaName());
        return queryDTO;
    }

    @Override
    public DispatchCodeRequestDTO omsOrderApiDTOOToDispatchCodeRequestDTO(OmsOrderApiDTO dto) {
        DispatchCodeRequestDTO queryDTO=new DispatchCodeRequestDTO();
        queryDTO.setProvinceId(dto.getSenderProvinceId()+0L);
        queryDTO.setProvince(dto.getSenderProvinceName());
        queryDTO.setCityId(dto.getSenderCityId()+0L);
        queryDTO.setCity(dto.getSenderCityName());
        queryDTO.setAreaId(dto.getSenderAreaId()+0L);
        queryDTO.setArea(dto.getSenderAreaName());
        queryDTO.setDetails(dto.getSenderDetailedAddress());
        queryDTO.setSenderProviceId(dto.getSenderProvinceId()+0L);
        queryDTO.setSenderCityId(dto.getSenderCityId()+0L);
        queryDTO.setSenderAreaId(dto.getSenderAreaId()+0L);
        queryDTO.setSenderProvice(dto.getSenderProvinceName());
        queryDTO.setSenderCity(dto.getSenderCityName());
        queryDTO.setSenderArea(dto.getSenderAreaName());
        return queryDTO;
    }
    @Override
    public DispatchCodeResponseVO getNetworkByFetch(DispatchCodeRequestDTO queryDTO) {
        TimeInterval timer = DateUtil.timer();
        DispatchCodeResponseVO dispatchCodeResponseVO = dispatchCodeFeignClient.fetchCodesNew(queryDTO).getData();
        log.info("新增订单 获取三段码 耗时：{},返回{}", timer.interval(), JSONObject.toJSONString(dispatchCodeResponseVO));
        if(dispatchCodeResponseVO == null || dispatchCodeResponseVO.getDeliverNetworkCode() == null ){
            return null;
        }
        return dispatchCodeResponseVO;
    }

}
