package com.yl.applets.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.yl.applets.dto.ActivityConfigDTO;
import com.yl.applets.dto.ActivityConfigQueryDTO;
import com.yl.applets.dto.ForwardRequest;
import com.yl.applets.dto.lmdm.SysAreaNormalDTO;
import com.yl.applets.dto.lmdm.SysAreaPcaNamesDTO;
import com.yl.applets.entity.ActivityConfig;
import com.yl.applets.entity.ActivityConfigArea;
import com.yl.applets.enums.ActivityConfigStatusEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.OssApi;
import com.yl.applets.mapper.applets.ActivityConfigMapper;
import com.yl.applets.mapper.applets.ActivityVisitRecordMapper;
import com.yl.applets.service.IActivityAreaService;
import com.yl.applets.service.IActivityPromotionService;
import com.yl.applets.service.IGdAddress;
import com.yl.applets.utils.ResultUtil;
import com.yl.applets.vo.ActivityConfigVO;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.enums.SysAreaTypeEnum;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.CollectionUtils;
import com.yl.common.base.util.GenerationIdUtil;
import com.yl.common.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动管理
 * @author: xiongweibin
 * @create: 2020-08-24 11:46
 */
@Service
@Slf4j
public class ActivityPromotionServiceImpl implements IActivityPromotionService {

    @Autowired
    private ActivityConfigMapper activityConfigMapper;

    @Autowired
    private ActivityVisitRecordMapper activityVisitRecordMapper;

    @Autowired
    private IActivityAreaService activityAreaService;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;


    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Autowired
    private IGdAddress gdAddress;

    @Autowired
    private OssApi ossApi;

    @Value("${activity.startTime:2021-02-03 23:59:58}")
    private String activityStartTime;

    @Value("${activity.endTime:2021-02-19 23:59:58}")
    private String activityEndTime;


    @Override
    public Page<ActivityConfigVO> getPages(ActivityConfigQueryDTO queryDTO) {
        Page<ActivityConfigVO> page = new Page<ActivityConfigVO>();
        List<ActivityConfigVO> dtoList = Lists.newArrayList();
        Long total = activityConfigMapper.countActivityConfigPromotion(queryDTO);

        if (total > 0) {
            dtoList = activityConfigMapper.selectActivityConfigPagePromotion(queryDTO);
            List<Long> ids = dtoList.stream().map(ActivityConfigVO::getId).collect(Collectors.toList());
            QueryWrapper<ActivityConfigArea> queryWrapper=new QueryWrapper<>();
            queryWrapper.eq("is_delete",1).in("config_id",ids);
            List<ActivityConfigArea> list = activityAreaService.list(queryWrapper);
            //按照configid分组
            Map<Long, List<ActivityConfigArea>> configId = list.stream().collect(Collectors.groupingBy(ActivityConfigArea::getConfigId));
            dtoList.forEach(o->{
                //处理适用区域字段
                if(configId.get(o.getId())!=null){
                    List<ActivityConfigArea> configAreas = configId.get(o.getId());
                    List<ActivityConfigVO.Area> areas = orikaBeanMapper.mapAsList(configAreas, ActivityConfigVO.Area.class);
                    areas.forEach(oo->{
                        if(oo.getType()==1){
                            oo.setId(oo.getProvinceId());
                            oo.setName(oo.getProvinceName());
                        }
                        if(oo.getType()==2){
                            oo.setId(oo.getCityId());
                            oo.setName(oo.getCityName());
                        }
                    });
                    o.setUseArea(areas);
                }
            });
        }
        page.setSize(queryDTO.getSize());
        page.setCurrent(queryDTO.getCurrent());
        page.setRecords(dtoList);
        page.setTotal(total);
        return page;
    }

    @Override
    public List<ActivityConfigVO> list(ActivityConfigQueryDTO queryDTO) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        //查询发不中的活动列表
        List<ActivityConfigVO> list = activityConfigMapper.selectActivityConfigListPromotion(queryDTO);
        List<ActivityConfigVO> result = Lists.newArrayList();
        log.info("请求id==>{}小程序banner列表,地址入参==>address{}",requestId, queryDTO.getAddress());
        boolean flag=false;
        if(!StringUtils.isEmpty(queryDTO.getAddress())){//如果有地址，过滤出地址相关的
            //过滤掉不满足当前地址的数据
            List<Long> ids = list.stream().map(ActivityConfigVO::getId).collect(Collectors.toList());
            QueryWrapper<ActivityConfigArea> queryWrapper=new QueryWrapper<>();
            queryWrapper.eq("is_delete",1).in("config_id",ids);
            List<ActivityConfigArea> areas = activityAreaService.list(queryWrapper);
            //按照configid分组
            Map<Long, List<ActivityConfigArea>> groupArea = areas.stream().collect(Collectors.groupingBy(ActivityConfigArea::getConfigId));
            String address = queryDTO.getAddress();
            String[] split = address.split(",");
            if(split.length==2){//传了省市
                String provinceId = split[0];
                String cityId = split[1];
                //校验省市名是否和基础匹配
                String [] newAddress= null;
                try{
                    newAddress =  mappingBase(provinceId,cityId,requestId);
                }catch (Exception e){
                    log.info("请求id==>{}地址映射异常,取通用区域",requestId);
                }
                if(newAddress==null){
                    log.info("请求id==>{}地址映射失败,取通用区域",requestId);
                    flag=true;
                }else if(newAddress.length==1){//去基础资料匹配省，市，市匹配不上，拿省和配置表比是否有市对应的省能看,
                    list.forEach(o->{
                        if(o.getIsArea()==null || o.getIsArea()==0 || isUseAreaByProvince(provinceId,groupArea.get(o.getId()),requestId)){
                            result.add(o);
                        }
                    });
                }else if(newAddress.length==2){//基础资料匹配省，市，都匹配上，拿市区配置表匹配
                    list.forEach(o->{
                        if(o.getIsArea()==null || o.getIsArea()==0 || isUseArea(provinceId,cityId,groupArea.get(o.getId()),requestId)){
                            result.add(o);
                        }
                    });
                }
            }else if(split.length==1){//只传了省,可能是直辖市
                //处理特殊的省级
                ArrayList<String> areaCitys = Lists.newArrayList("北京","上海","天津","重庆");
                String provinceName = split[0];
                List<String> collect = areaCitys.stream().filter(o -> provinceName.contains(o)).collect(Collectors.toList());
                if(collect!=null&&!collect.isEmpty()){//如果是直辖市
                    String provinceId = collect.get(0);
                    String cityId = collect.get(0)+"市";
                    list.forEach(o->{
                        if(o.getIsArea()==null || o.getIsArea()==0 || isUseArea(provinceId,cityId,groupArea.get(o.getId()),requestId)){
                            result.add(o);
                        }
                    });
                }else {//只穿了一个省,但是非直辖市的,按省(或)省下第一个市处理
                    String provinceId = split[0];
                    list.forEach(o->{
                        if(o.getIsArea()==null || o.getIsArea()==0 || isUseAreaByProvince(provinceId,groupArea.get(o.getId()),requestId)){
                            result.add(o);
                        }
                    });
                }
            } else {
                flag=true;
            }
        }else {
            flag=true;
        }
        if(flag){
            //过滤掉有适用地址的数据
            list.forEach(o->{
                if(o.getIsArea()==null||o.getIsArea()==0){
                    result.add(o);
                }
            });
        }
        result.forEach(vo -> {
            supplementOssPath(vo);
        });
        log.info("请求id==>{}小程序查询活动列表返回结果==>{}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 和省匹配
     * @param province
     * @param activityConfigAreas
     * @return
     */
    private boolean isUseAreaByProvince(String province, List<ActivityConfigArea> activityConfigAreas,String requestId) {
        if(activityConfigAreas==null||activityConfigAreas.isEmpty()){
            return true;
        }else {
            for (ActivityConfigArea activityConfigArea : activityConfigAreas) {
                if(activityConfigArea.getType()==1&& org.apache.commons.lang3.StringUtils.equals(province,activityConfigArea.getProvinceName())){//省
                    log.info("请求id==>{}维护的省和当前定位省一致==>{},{}",requestId,activityConfigArea.getProvinceName(),province);
                    return true;
                }
            }
            long count = activityConfigAreas.stream().filter(o -> o.getType() == 2 &&  org.apache.commons.lang3.StringUtils.equals(province,o.getProvinceName())).count();
            if(count>0){//存在城市
                log.info("请求id==>{}当前定位只有省,使用省下城市的配置==>{}",requestId,province);
                return  true;
            }
            return false;
        }
    }

    private String[] mappingBase(String provinceId, String cityId,String requestId) {
        List<SysAreaPcaNamesDTO> list=Lists.newArrayList();
        SysAreaPcaNamesDTO province = new SysAreaPcaNamesDTO();
        province.setName(provinceId);
        province.setType(SysAreaTypeEnum.PROVINCE.getId());
        list.add(province);
        SysAreaPcaNamesDTO city = new SysAreaPcaNamesDTO();
        city.setName(cityId);
        city.setType(SysAreaTypeEnum.CITY.getId());
        list.add(city);
        log.info("请求id==>{},查询基础省市信息入参==>{}",requestId,JSON.toJSONString(list));
        ForwardRequest request=new ForwardRequest();
        request.setRequestId(requestId);
        request.setRequestUri("/lmdmapi/sysArea/getByNames");
        request.setBody(JSON.toJSONString(list));
        log.info("请求id==>{}接口入参==>{}",requestId,JSON.toJSONString(request));
        Result<?> forward = channelApiFeignClient.forward(request);
        log.info("请求id==>{}接口返回==>{}",requestId,JSON.toJSONString(forward));
        Object resultO = forward.result();
        //转换出参
        List<SysAreaNormalDTO> result = JSON.parseArray(JSON.toJSONString(resultO), SysAreaNormalDTO.class);
        log.info("请求id==>{},查询基础省市信息出参==>{}",requestId,JSON.toJSONString(result));
        if (result!=null&&result.size()==2){
            return new String[]{provinceId,cityId};
        }else {
            for (SysAreaNormalDTO sysAreaNormalDTO : result) {
                if(sysAreaNormalDTO.getType()==SysAreaTypeEnum.PROVINCE.getId()){
                    return new String[]{sysAreaNormalDTO.getNativeName()};
                }
            }
        }
        return null;
    }

    private boolean isUseArea(String province,String city,List<ActivityConfigArea> activityConfigAreas,String requestId) {
        if(activityConfigAreas==null||activityConfigAreas.isEmpty()){
            return true;
        }else {
            for (ActivityConfigArea activityConfigArea : activityConfigAreas) {
                if(activityConfigArea.getType()==2&&
                        org.apache.commons.lang3.StringUtils.equals(province,activityConfigArea.getProvinceName())//省
                        &&org.apache.commons.lang3.StringUtils.equals(city,activityConfigArea.getCityName())){//市
                    log.info("请求id==>{}维护的市和当前定位市一致==>{},{}",requestId,activityConfigArea.getCityName(),city);
                    return true;
                }
            }
            return false;
        }
    }

    @Override
    @Transactional
    public Result<ActivityConfig> add(ActivityConfigDTO dto) {
        QueryWrapper<ActivityConfig> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("type",dto.getType()).eq("is_delete",1).eq("status",2);
        if(!StringUtils.isEmpty(dto.getSort())){
            queryWrapper.eq("sort",dto.getSort());
        }
        List<ActivityConfig> activityConfigs = activityConfigMapper.selectList(queryWrapper);
        if(!CollectionUtils.isEmpty(activityConfigs)){
            log.info("活动配置新增,已存在");
            return Result.error(ResultCodeEnum.ACTIVITY_IS_EXIST);
        }
        List<ActivityConfigDTO.Area> useArea = dto.getUseArea();
        ActivityConfig activityConfig = orikaBeanMapper.map(dto, ActivityConfig.class);
        if (dto.getOnTime().isBefore(dto.getReleaseTime())) {
            activityConfig.setOnTime(dto.getReleaseTime());
        }

        if (dto.getOffTime().isBefore(dto.getReleaseTime())) {
            return Result.error(ResultCodeEnum.ACTIVITY_CONFIG_OFF_TIME_AFTER_RELEASE_TIME_ERROR);
        }

        if (dto.getOnTime().isAfter(dto.getOffTime())) {
            return Result.error(ResultCodeEnum.ACTIVITY_CONFIG_ON_TIME_AFTER_OFF_TIME_ERROR);
        }
        Date dt = new Date();
        StringBuffer code = new StringBuffer();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
        String data = sdf.format(dt);
        int x;//定义两变量
        x = new Random().nextInt(9999 - 1000 + 1) + 1000;
        code.append("HD").append(data).append(x);
        activityConfig.setId(GenerationIdUtil.getId());
        activityConfig.setCode(code.toString());
        activityConfig.setVersionNo(1);
        activityConfig.setStatus(ActivityConfigStatusEnum.STAY_RELEASE.getCode());
        if(activityConfig.getType() == 1){
            activityConfig.setSort("");
        }
        List<ActivityConfigArea> configAreas = Lists.newArrayList();
        if (useArea != null && !useArea.isEmpty()) {
            activityConfig.setIsArea(1);//有区域
            LocalDateTime now = LocalDateTime.now();
            for (ActivityConfigDTO.Area area : useArea) {
                ActivityConfigArea activityConfigArea = orikaBeanMapper.map(area, ActivityConfigArea.class);
                activityConfigArea.setConfigId(activityConfig.getId());
                activityConfigArea.setId(GenerationIdUtil.getId());
                activityConfigArea.setCreateTime(now);
                activityConfigArea.setIsDelete(1);
                configAreas.add(activityConfigArea);
            }
        }else {
            activityConfig.setIsArea(0);//无区域
        }
        int insert = activityConfigMapper.insert(activityConfig);
        if(insert>0) {
            activityAreaService.saveBatch(configAreas);
            log.info("新增banner配置,区域表成功");
        }
        return Result.success(activityConfig);
    }

    @Override
    @Transactional
    public Result delete(ActivityConfigDTO dto) {
        ActivityConfig activityConfig = activityConfigMapper.selectById(dto.getId());
        if(activityConfig==null){
            return Result.error(ResultCodeEnum.DATA_NOT_FOUND);
        }
        Integer status = activityConfig.getStatus();
        if (!ActivityConfigStatusEnum.STAY_RELEASE.getCode().equals(status)) {
            return Result.error(ResultCodeEnum.ACTIVITY_CONFIG_DELETE_ERROR);
        }
        activityConfig.setUpdateByCode(dto.getUpdateByCode());
        activityConfig.setUpdateByName(dto.getUpdateByName());
        activityConfig.setUpdateTime(LocalDateTime.now());
        activityConfig.setIsDelete(2);
        activityConfig.setVersionNo(activityConfig.getVersionNo() + 1);
        int i = activityConfigMapper.updateById(activityConfig);
        if(i>0){
            QueryWrapper<ActivityConfigArea> queryWrapper=new QueryWrapper<>();
            queryWrapper.select("id").eq("config_id",activityConfig.getId());
            List<ActivityConfigArea> list = activityAreaService.list(queryWrapper);
            List<Long> ids = list.stream().map(ActivityConfigArea::getId).collect(Collectors.toList());
            if(ids!=null&&!ids.isEmpty()){
                activityAreaService.isDeleteAreas(ids);
            }
        }
        return Result.success(activityConfig);
    }

    @Override
    public Result offline(ActivityConfigDTO dto) {
        ActivityConfig activityConfig = activityConfigMapper.selectById(dto.getId());
        if(activityConfig==null){
            return Result.error(ResultCodeEnum.DATA_NOT_FOUND);
        }
        Integer status = activityConfig.getStatus();
        if (!ActivityConfigStatusEnum.ALREADY_RELEASE.getCode().equals(status)) {
            return Result.error(ResultCodeEnum.ACTIVITY_CONFIG_OFF_LINE_ERROR);
        }
        activityConfig.setUpdateByCode(dto.getUpdateByCode());
        activityConfig.setUpdateByName(dto.getUpdateByName());
        activityConfig.setUpdateTime(dto.getUpdateTime());
        activityConfig.setOffTime(dto.getUpdateTime());
        activityConfig.setStatus(ActivityConfigStatusEnum.STAY_OFFLINE.getCode());
        activityConfig.setVersionNo(activityConfig.getVersionNo() + 1);
        activityConfigMapper.updateById(activityConfig);
        return Result.success(activityConfig);
    }

    @Override
    @Transactional
    public Result<ActivityConfig> update(ActivityConfigDTO dto) {
        QueryWrapper<ActivityConfig> queryWrapperUpdate=new QueryWrapper<>();
        queryWrapperUpdate.eq("type",dto.getType()).eq("is_delete",1).eq("status",2).ne("id",dto.getId());
        if(!StringUtils.isEmpty(dto.getSort())){
            queryWrapperUpdate.eq("sort",dto.getSort());
        }
        List<ActivityConfig> activityConfigs = activityConfigMapper.selectList(queryWrapperUpdate);
        if(!CollectionUtils.isEmpty(activityConfigs)){
            log.info("活动配置新增,已存在");
            return Result.error(ResultCodeEnum.ACTIVITY_IS_EXIST);
        }
        ActivityConfig activityConfig = activityConfigMapper.selectById(dto.getId());
        if (ObjectUtil.isNull(activityConfig)) {
            return Result.error(ResultCodeEnum.DATA_NOT_FOUND);
        }

        if (dto.getOnTime().isBefore(dto.getReleaseTime())) {
            activityConfig.setOnTime(dto.getReleaseTime());
        } else {
            activityConfig.setOnTime(dto.getOnTime());
        }

        if (dto.getOffTime().isBefore(dto.getReleaseTime())) {
            return Result.error(ResultCodeEnum.ACTIVITY_CONFIG_OFF_TIME_AFTER_RELEASE_TIME_ERROR);
        }

        if (dto.getOnTime().isAfter(dto.getOffTime())) {
            return Result.error(ResultCodeEnum.ACTIVITY_CONFIG_ON_TIME_AFTER_OFF_TIME_ERROR);
        }

        activityConfig.setOffTime(dto.getOffTime());
        activityConfig.setReleaseTime(dto.getReleaseTime());
        activityConfig.setTitle(dto.getTitle());
        activityConfig.setActivityLine(dto.getActivityLine());
        activityConfig.setImagePath(dto.getImagePath());
        activityConfig.setType(dto.getType());
        activityConfig.setUpdateByName(dto.getUpdateByName());
        activityConfig.setUpdateByCode(dto.getUpdateByCode());
        activityConfig.setUpdateTime(dto.getUpdateTime());
        activityConfig.setVersionNo(activityConfig.getVersionNo() + 1);
        activityConfig.setNewsText(dto.getNewsText());
        activityConfig.setButtonText(dto.getButtonText());
        activityConfig.setButtonWay(dto.getButtonWay());
        List<ActivityConfigDTO.Area> useArea = dto.getUseArea();
        if(useArea!=null&&!useArea.isEmpty()){
            activityConfig.setIsArea(1);
        }else {
            activityConfig.setIsArea(0);
        }
        if(activityConfig.getType() == 1){
            activityConfig.setSort("");
        }else{
            activityConfig.setSort(dto.getSort());
        }
        int s=activityConfigMapper.updateById(activityConfig);
        if(s>0){
            QueryWrapper<ActivityConfigArea> queryWrapper=new QueryWrapper<>();
            queryWrapper.select("id").eq("config_id",activityConfig.getId());
            List<ActivityConfigArea> list = activityAreaService.list(queryWrapper);
            List<Long> oldIds = list.stream().map(ActivityConfigArea::getId).collect(Collectors.toList());
            if(activityConfig.getIsArea()==1){//有地址
                if(oldIds!=null&&!oldIds.isEmpty()) activityAreaService.isDeleteAreas(oldIds);
                //新增
                List<ActivityConfigArea> configAreas = Lists.newArrayList();
                LocalDateTime now = LocalDateTime.now();
                for (ActivityConfigDTO.Area area : useArea) {
                    ActivityConfigArea activityConfigArea = orikaBeanMapper.map(area, ActivityConfigArea.class);
                    activityConfigArea.setConfigId(activityConfig.getId());
                    activityConfigArea.setId(GenerationIdUtil.getId());
                    activityConfigArea.setCreateTime(now);
                    activityConfigArea.setIsDelete(1);
                    configAreas.add(activityConfigArea);
                }
                activityAreaService.saveBatch(configAreas);
            }else if(activityConfig.getIsArea()==0&&oldIds!=null&&!oldIds.isEmpty()){//无地址
                activityAreaService.isDeleteAreas(oldIds);
            }
        }
        return Result.success(activityConfig);
    }



    @Override
    public Result<ActivityConfigVO> getDetailById(Long id) {
        ActivityConfigVO vo = activityConfigMapper.getDetailById(id);
        //处理适用区域字段
        if(vo.getIsArea()!=null&&vo.getIsArea()==1){
            QueryWrapper<ActivityConfigArea> queryWrapper=new QueryWrapper<>();
            queryWrapper.eq("config_id",vo.getId());
            List<ActivityConfigArea> list = activityAreaService.list(queryWrapper);
            List<ActivityConfigVO.Area> areas = orikaBeanMapper.mapAsList(list, ActivityConfigVO.Area.class);
            areas.forEach(oo->{
                if(oo.getType()==1){
                    oo.setId(oo.getProvinceId());
                    oo.setName(oo.getProvinceName());
                }
                if(oo.getType()==2){
                    oo.setId(oo.getCityId());
                    oo.setName(oo.getCityName());
                }
            });
            vo.setUseArea(areas);
        }
        supplementOssPath(vo);
        return Result.success(vo);
    }

    private void supplementOssPath(ActivityConfigVO activityConfigVO) {
        if (Objects.nonNull(activityConfigVO) && Objects.nonNull(activityConfigVO.getImagePath())) {
            Result<Map<String, String>> ossResult = ossApi.getDownloadSignedUrlDetail(Arrays.asList(activityConfigVO.getImagePath()));
            ResultUtil.checkFeignResult(ossResult);
            Map<String, String> ossPathMap = ossResult.getData();
            if (CollectionUtils.isNotEmpty(ossPathMap.values())) {
                activityConfigVO.setImageUrl(ossPathMap.get(activityConfigVO.getImagePath()));
            }
        }
    }

}
