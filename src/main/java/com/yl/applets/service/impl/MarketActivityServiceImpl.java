package com.yl.applets.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.config.MarketActivitySourceProperties;
import com.yl.applets.constant.CommonConstants;
import com.yl.applets.entity.MarketActivity;
import com.yl.applets.entity.MarketActivityBrowseLog;
import com.yl.applets.enums.MarketActivitySourceEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.mapper.market.MarketActivityBrowseLogMapper;
import com.yl.applets.mapper.market.MarketActivityMapper;
import com.yl.applets.service.MarketActivityService;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.util.GenerationIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/9 11:24
 * @description
 */
@Service
@Slf4j
public class MarketActivityServiceImpl extends ServiceImpl<MarketActivityMapper, MarketActivity> implements MarketActivityService {

    @Resource
    private MarketActivitySourceProperties marketActivitySourceProperties;
    @Resource
    private MarketActivityBrowseLogMapper marketActivityBrowseLogMapper;
    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Override
    public Boolean refreshPageViews(String marketActivityCode, String source) {
        // 检查活动
        MarketActivity one = this.checkMarketActivity(marketActivityCode);
        if (one == null) return false;
        // 检查来源
        this.checkSource(marketActivityCode,source);
        // 保存日志
        return this.saveLog(source, one);
    }

    private boolean saveLog(String source, MarketActivity one) {
        MarketActivityBrowseLog browseLog = new MarketActivityBrowseLog();
        browseLog.setId(GenerationIdUtil.getId());
        browseLog.setMarketActivityId(one.getId());
        browseLog.setMarketActivityCode(one.getCode());
        browseLog.setSource(source);
        browseLog.setCreateTime(LocalDateTime.now());
        return marketActivityBrowseLogMapper.insert(browseLog) > 0;
    }

    private MarketActivity checkMarketActivity(String marketActivityCode) {
        LambdaQueryWrapper<MarketActivity> wrapper = Wrappers.<MarketActivity>query().lambda().eq(MarketActivity::getCode, marketActivityCode);
        MarketActivity one = this.getOne(wrapper);
        if (Objects.isNull(one)) return null;
        return one;
    }

    private void checkSource(String marketActivityCode, String curSource) {
//        if (!marketActivitySourceProperties.contains(curSource)) {
//            throw new BusinessException(ResultCodeEnum.SOURCE_NON_EXISTENT);
//        }
//        if (!CommonConstants.SOURCE_LIST.contains(curSource)) {
//            throw new BusinessException(ResultCodeEnum.SOURCE_CAN_NOT_REPORT);
//        }

        String channel = channelApiFeignClient.getChannelByCode(marketActivityCode).result();
        if (StringUtils.isNotBlank(channel)) {
            List<String> channelList = Arrays.asList(channel.split(","));
            if (!channelList.contains(curSource)) {
                log.info("checkMarketActivitySource 渠道来源不匹配");
                throw new BusinessException(ResultCodeEnum.SOURCE_CAN_NOT_REPORT);
            }
        }
    }
}
