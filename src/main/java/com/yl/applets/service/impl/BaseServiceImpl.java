package com.yl.applets.service.impl;

import com.yl.applets.service.IBaseService;
import com.yl.applets.utils.SessionUtil;
import com.yl.applets.vo.WxUserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-08-08 14:39 <br>
 * @Author: zhipeng.liu
 */
@Service
public class BaseServiceImpl implements IBaseService {

    @Autowired
    private SessionUtil sessionUtil;

    @Override
    public WxUserVo getUser() {
        return sessionUtil.getUser();
    }
}
