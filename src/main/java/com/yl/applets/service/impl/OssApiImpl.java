package com.yl.applets.service.impl;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jt.file.FileLifeEnum;
import com.jt.file.YlFileUtil;
import com.yl.applets.dto.OssUrlSignDTO;
import com.yl.applets.feign.OssApi;
import com.yl.applets.vo.OssUrlSignVO;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.*;

@Service
@Slf4j
public class OssApiImpl implements OssApi {

    @Override
    public Result<List<String>> getDownloadSignedUrl(List<String> paths) {
        List<String> pathResultList = Lists.newArrayList();
        paths.forEach(path -> {
            String downloadCloudUrl = getDownloadCloudUrl(path);
            pathResultList.add(downloadCloudUrl);
        });
        return Result.success(pathResultList);
    }

    @Override
    public Result<Map<String, String>> getDownloadSignedUrlDetail(List<String> paths) {
        Map<String, String> pathResultMap = Maps.newHashMap();
        paths.forEach(p -> {
            String downloadCloudUrl = getDownloadCloudUrl(p);
            pathResultMap.put(p, downloadCloudUrl);
        });
        return Result.success(pathResultMap);
    }

    @Override
    public Result<List<OssUrlSignVO>> getUploadSignedUrl(List<OssUrlSignDTO> list) {
        return null;
    }

    @Override
    public Result<String> upload(String projectName, String moduleName, MultipartFile file) {
        String fileName = UUID.randomUUID().toString().replaceAll("-", "") + file.getOriginalFilename();

        String uri;
        try (InputStream inputStream = file.getInputStream()){
            uri = YlFileUtil.upload(inputStream, file.getSize(), projectName, moduleName,
                   fileName, FileLifeEnum.FOREVER, null, UUID.randomUUID().toString(), "");
        } catch (IOException e) {
            log.error("上传文件失败：{}", e.getMessage());
            throw new ServiceException("上传文件失败!");
        }

        if (StrUtil.isBlank(uri)) throw new ServiceException("上传文件失败!");

        String ossUrl = Optional.of(uri)
                .map(u -> YlFileUtil.getDownloadCloudUrl(u, null, null, Duration.ofDays(7), "", ""))
                .orElse("");
        if (StrUtil.isBlank(ossUrl)) throw new ServiceException("file url is empty!!!");
        return Result.success(ossUrl);
    }

    @Override
    public Result<String> uploadFile(String projectName, String moduleName, MultipartFile file) {
        String fileName = UUID.randomUUID().toString().replaceAll("-", "") + file.getOriginalFilename();

        String uri;
        try (InputStream inputStream = file.getInputStream()) {
            uri = YlFileUtil.upload(inputStream, file.getSize(), projectName, moduleName,
                    fileName, FileLifeEnum.FOREVER, null, UUID.randomUUID().toString(), "");
        } catch (IOException e) {
            log.error("上传文件失败：{}", e.getMessage());
            throw new ServiceException("上传文件失败!");
        }

        if (StrUtil.isBlank(uri)) throw new ServiceException("上传文件失败!");
        return Result.success(uri);
    }

    /**
     * 获取文件下载路径
     * 调用OSS-API获取文件的下载路径
     * @param path 文件路径
     * @return 文件下载路径
     */
    private String getDownloadCloudUrl(String path) {
        if (StringUtils.isBlank(path)) {
            return StringUtils.EMPTY;
        }
        try {
            log.info("调用OSS-API获取文件下载路径：{}", path);
            String downloadCloudUrl = YlFileUtil.getDownloadCloudUrl(path, null, null, Duration.ofDays(1), null, null);
            log.info("调用OSS-API获取文件下载路径：{}", downloadCloudUrl);
            return downloadCloudUrl;
        } catch (Exception e) {
            log.error("调用OSS-API获取文件下载路径", e);
            throw new ServiceException(100001, "API获取文件下载路径失败");
        }
    }

}

