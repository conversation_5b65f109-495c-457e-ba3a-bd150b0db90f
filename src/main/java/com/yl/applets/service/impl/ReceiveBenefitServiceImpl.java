package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.config.SourceProperties;
import com.yl.applets.dto.DispatchCodeRequestDTO;
import com.yl.applets.dto.OrderDTO;
import com.yl.applets.dto.ReceiveBenefitDTO;
import com.yl.applets.dto.ReceiveBenefitQueryDTO;
import com.yl.applets.entity.ReceiveBenefit;
import com.yl.applets.enums.ActivityTypeEnum;
import com.yl.applets.mapper.applets.ReceiveBenefitMapper;
import com.yl.applets.service.IReceiveBenefitService;
import com.yl.applets.vo.DispatchCodeResponseVO;
import com.yl.applets.vo.ReceiveBenefitVo;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.util.GenerationIdUtil;
import com.yl.common.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.SQLIntegrityConstraintViolationException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： zhanzhihong
 * @Date： 2022/07/12
 */
@Slf4j
@Service
public class ReceiveBenefitServiceImpl extends ServiceImpl<ReceiveBenefitMapper, ReceiveBenefit> implements IReceiveBenefitService {
    @Autowired
    private OrikaBeanMapper orikaBeanMapper;
    @Autowired
    private SourceProperties sourceProperties;
    @Autowired
    private NetworkAbnormalServiceImpl networkAbnormalService;

    /**
     * 市场部 活动开关
     */
    @Value("${MarketDepartment.activityStatus:false}")
    private Boolean activityStatus;

    /**
     * 市场部 圣诞报名活动开关
     */
    @Value("${MarketDepartment.christmasRegistrationActivityStatus:false}")
    private Boolean christmasRegistrationActivityStatus;

    /**
     * 添加福利申领记录
     *
     * @param receiveBenefitDTO
     * @param requestId
     * @return
     */
    @Override
    public Boolean addReceiveBenefit(ReceiveBenefitDTO receiveBenefitDTO, String requestId) {
        if (!activityStatus) {
            throw new BusinessException(ResultCodeEnum.ACTIVITY_ENDED);
        }
        List<SourceProperties.source> sourceList = sourceProperties.getList();
        SourceProperties.source source = sourceList.stream().filter(x -> x.getCode().equals(receiveBenefitDTO.getSource())).findFirst().orElse(null);
        if (source == null) {
            throw new BusinessException(ResultCodeEnum.SOURCE_NON_EXISTENT);
        }
        //优惠券下单,补全三段码-网点信息
        OrderDTO dto = new OrderDTO();
        dto.setSenderProvinceId(receiveBenefitDTO.getProvinceId());
        dto.setSenderProvinceName(receiveBenefitDTO.getProvinceName());
        dto.setSenderCityId(receiveBenefitDTO.getCityId());
        dto.setSenderCityName(receiveBenefitDTO.getCityName());
        dto.setSenderAreaId(receiveBenefitDTO.getAreaId());
        dto.setSenderAreaName(receiveBenefitDTO.getAreaName());
        dto.setSenderDetailedAddress(receiveBenefitDTO.getAddress());
        DispatchCodeRequestDTO queryDto = networkAbnormalService.orderDTOToDispatchCodeRequestDTO(dto);
        log.info("请求三段码，requestId{},获取网点信息入参：{}", requestId, JSONObject.toJSONString(queryDto));
        DispatchCodeResponseVO dispatchCodeResponseVO = networkAbnormalService.getNetworkByFetch(queryDto);
        log.info("请求三段码，requestId{},获取网点信息出参：{}", requestId, JSONObject.toJSONString(dispatchCodeResponseVO));

        ReceiveBenefit receiveBenefit = orikaBeanMapper.map(receiveBenefitDTO, ReceiveBenefit.class);
        receiveBenefit.setId(GenerationIdUtil.getId());
        receiveBenefit.setCreateTime(LocalDateTime.now());
        receiveBenefit.setUpdateTime(LocalDateTime.now());
        //set网点信息
        if (Objects.nonNull(dispatchCodeResponseVO)) {
            receiveBenefit.setNetworkCode(dispatchCodeResponseVO.getDeliverNetworkCode());
            receiveBenefit.setNetworkName(dispatchCodeResponseVO.getDeliverNetworkName());
        }
        boolean save = false;
        try {
            //手机号唯一索引
            save = this.save(receiveBenefit);
        } catch (Exception e) {
            log.info("添加福利申领记录失败：requestId{},{}", requestId, e.getMessage());
            throw new BusinessException(ResultCodeEnum.MOBILE_RECEIVE_EXISTENT);
        }
        return save;
    }


    @Override
    public Boolean christmasRegistrationActivity(ReceiveBenefitDTO receiveBenefitDTO) {
        if (!christmasRegistrationActivityStatus) {
            throw new BusinessException(ResultCodeEnum.ACTIVITY_ENDED);
        }

        ReceiveBenefit receiveBenefit = orikaBeanMapper.map(receiveBenefitDTO, ReceiveBenefit.class);
        receiveBenefit.setId(GenerationIdUtil.getId());
        receiveBenefit.setCreateTime(LocalDateTime.now());
        receiveBenefit.setUpdateTime(LocalDateTime.now());
        receiveBenefit.setActivityType(ActivityTypeEnum.CHRISTMAS_REGISTRATION.getCode());
        boolean save = false;
        try {
            //手机号唯一索引
            save = this.save(receiveBenefit);
        } catch (Exception e) {
            Throwable cause = e.getCause();
            if(cause instanceof java.sql.SQLIntegrityConstraintViolationException){
                log.warn("重复报名，唯一索引异常：", e);
                throw new BusinessException(ResultCodeEnum.MOBILE_RECEIVE_EXISTENT);
            }
            log.warn("报名异常：", e);
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
        return save;
    }
}
