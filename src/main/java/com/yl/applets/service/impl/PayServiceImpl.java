package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yl.applets.config.WxMaProperties;
import com.yl.applets.dto.IpayLooseOrderWXDTO;
import com.yl.applets.dto.WxPayDto;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.OldNetworkFeighClient;
import com.yl.applets.feign.PayFeignClient;
import com.yl.applets.service.IOrderService;
import com.yl.applets.service.IPayService;
import com.yl.applets.vo.*;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2023
 *
 * <AUTHOR>
 * @Description:微信支付
 * @date 2023-07-18
 */


@Service
@Slf4j
public class PayServiceImpl extends BaseServiceImpl implements IPayService {


    //微信支付商户号
    @Value("${wx.pay.commercial:1613021961}")
    private String wxPayCommercialNum;

    //商户类型
    @Value("${wx.pay.type:微信支付服务商}")
    private String wxPayType;

    @Autowired
    private WxMaProperties wxMaProperties;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private OldNetworkFeighClient appletsCustomerFeignClient;


    @Autowired
    private PayFeignClient payFeignClient;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClientl;

    @Autowired
    private IPayService payService;

    @Override
    public Object getPrePayId(WxPayDto dto) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("微信预支付接口，请求id：{}，入参：{}", requestId, JSON.toJSONString(dto));
        //查询订单详情
        log.info("查询订单状态：{}，请求id：{}", JSON.toJSONString(dto.getOrderNo()), requestId);
        OmsOrderApiVO detail = orderService.detail(Long.parseLong(dto.getOrderNo()));
        log.info("请求详情：{}，请求id：{}", JSON.toJSONString(detail), requestId);
        if (detail == null) {
            return Result.error(ResultCodeEnum.ORDER_IS_PAYED);
        }
        WxUserVo user = getUser();
        //越权校验
        if (!StringUtils.equals(user.getNumberId() + "", detail.getMemberId() + "") &&
                !user.getMobile().equals(detail.getSenderMobilePhone()) && !user.getNumberId().equals(detail.getMemberId())) {
            log.warn("请求id==>{}当前登录人不是下单人,没有权限取消订单详情", requestId);
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_ERROR);
        }
        if (detail.getOrderPayStatus() != null && detail.getOrderPayStatus() == 2) {// 未支付
            //IpayWxPlaceOrderDTO paydto=new IpayWxPlaceOrderDTO();
            IpayLooseOrderWXDTO paydto = new IpayLooseOrderWXDTO();
            //根据寄件网点查所属加盟商
            log.info("请求网点资料接口，根据网点查所属加盟商的接口，入参：{}", dto.getNetworkCode());
            Result<SpmiFranchiseeNetworkVO> proxyAndFranchiseeByCode = appletsCustomerFeignClient.getFranchiseeByCode(dto.getNetworkCode());
            log.info("请求网点资料接口，根据网点查所属加盟商的接口，出参：{}", JSON.toJSONString(proxyAndFranchiseeByCode));
            if (proxyAndFranchiseeByCode != null && proxyAndFranchiseeByCode.result() != null) {
                if (proxyAndFranchiseeByCode.isSucc()) {
                    SpmiFranchiseeNetworkVO resultvo = proxyAndFranchiseeByCode.result();
                    log.info("网点资料请求成功返回数据：{}", resultvo);
                    paydto.setFranchiseeCode(resultvo.getFranchiseeCode());
                    paydto.setFranchiseeId(resultvo.getFranchiseeId());
                    paydto.setFranchiseeName(resultvo.getFranchiseeName());
                    paydto.setFinancialCenterId(resultvo.getFinancialCenterId());
                    paydto.setFinancialCenterName(resultvo.getFinancialCenterDesc());
                    paydto.setFinancialCenterCode(resultvo.getFinancialCenterCode());
                    paydto.setFinancialCenterCode(resultvo.getFinancialCenterCode());
                    paydto.setNetworkId(resultvo.getId());
                    paydto.setNetworkCode(resultvo.getCode());
                    paydto.setNetworkName(resultvo.getName());
                    log.info("补全加盟商,代理区：{}", JSON.toJSONString(paydto));
                } else {
                    String msg = proxyAndFranchiseeByCode.getMsg();
                    log.info("网点资料请求失败返回数据：{}", msg);
                    return Result.error(proxyAndFranchiseeByCode.getCode(), proxyAndFranchiseeByCode.getMsg());
                }
            }

            log.info("请求id{},查询订单/运单详情,参数{}", requestId, dto.getWaybillNo());
            AppOmsWaybillDetailVO appOmsWaybillDetailVO = channelApiFeignClientl.getDetailByNo(dto.getWaybillNo()).result();
            log.info("请求id{},查询订单/运单详情出参：{}", requestId, JSON.toJSONString(appOmsWaybillDetailVO));

            if (appOmsWaybillDetailVO != null && appOmsWaybillDetailVO.getCollectTime() != null) {
                paydto.setCollectTime(appOmsWaybillDetailVO.getCollectTime());
            }
            String appId = wxMaProperties.getConfigs().get(0).getAppid();
            StopWatch stopWatch = new StopWatch();
            paydto.setApp_id(appId);
            paydto.setPayerClientIp(wxPayCommercialNum);
            paydto.setWaybillNo(dto.getWaybillNo());
            paydto.setOrderNo(dto.getOrderNo());
//            paydto.setNetworkId(Integer.parseInt(dto.getNetworkId()));
//            paydto.setNetworkCode(dto.getNetworkCode());
//            paydto.setNetworkName(dto.getNetworkName());
            paydto.setAmount(dto.getOrderAmount());
            paydto.setSpOpenid(user.getOpenid());
            paydto.setDescription(dto.getDescription());
//            paydto.setCoupon_Amount(dto.getCouponAmount());//优惠金额
//            paydto.setCoupon_code(dto.getCouponCode());//优惠券code
//            paydto.setCoupon_id(dto.getCouponId());//优惠券id
            log.info("调用财务预支付接口，请求id：{}，入参：{}", requestId, JSON.toJSONString(paydto));
            stopWatch.start("请求prepay_id接口");
            Result<String> stringResult = payFeignClient.placeOrder(paydto);
            stopWatch.stop();
            log.info("调用财务预支付接口，请求id：{}，出参：{}", requestId, JSON.toJSONString(stringResult));
            if (stringResult.isSucc()) {
                String result = stringResult.result();
                log.info("财务预支付请求成功返回数据：{}", result);
                JSONObject jsonObject = JSONObject.parseObject(result);
                String prepayId = jsonObject.get("prepay_id") != null ? jsonObject.get("prepay_id").toString() : "";
                //拼接
                String prepay_id="prepay_id="+prepayId;
                IpayTokenVO ipayTokenVO = this.getToken(prepay_id,appId);
                ipayTokenVO.setPrepayId(prepayId);
                log.info("获取调起密码框的入参：{}", ipayTokenVO);
                return Result.success(ipayTokenVO);
            } else {
                String msg = stringResult.getMsg();
                log.error("财务预支付请求失败返回数据：{}", msg);
                return Result.error(stringResult.getCode(), stringResult.getMsg());
            }
        } else {
            return Result.error(ResultCodeEnum.ORDER_IS_PAYED);
        }
    }

    @Override
    public Object getUmsMiniPrePayId(WxPayDto dto) {

        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("云闪付预支付接口，请求id：{}，入参：{}", requestId, JSON.toJSONString(dto));
        log.info("云闪付查询订单状态：{}，请求id：{}", JSON.toJSONString(dto.getOrderNo()), requestId);
        OmsOrderApiVO detail = orderService.detail(Long.parseLong(dto.getOrderNo()));
        log.info("云闪付请求详情：{}，请求id：{}", JSON.toJSONString(detail), requestId);
        if (detail == null) {
            return Result.error(ResultCodeEnum.ORDER_IS_PAYED);
        }
        WxUserVo user = getUser();
        //越权校验
        if (!StringUtils.equals(user.getNumberId() + "", detail.getMemberId() + "") &&
                !user.getMobile().equals(detail.getSenderMobilePhone()) && !user.getNumberId().equals(detail.getMemberId())) {
            log.warn("云闪付请求id==>{}当前登录人不是下单人,没有权限取消订单详情", requestId);
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_ERROR);
        }
        if (detail.getOrderPayStatus() != null && detail.getOrderPayStatus() == 2) {// 未支付
            IpayLooseOrderWXDTO paydto = new IpayLooseOrderWXDTO();
            //根据寄件网点查所属加盟商
            log.info("云闪付请求网点资料接口，根据网点查所属加盟商的接口，入参：{}", dto.getNetworkCode());
            Result<SpmiFranchiseeNetworkVO> proxyAndFranchiseeByCode = appletsCustomerFeignClient.getFranchiseeByCode(dto.getNetworkCode());
            log.info("云闪付请求网点资料接口，根据网点查所属加盟商的接口，出参：{}", JSON.toJSONString(proxyAndFranchiseeByCode));
            if (proxyAndFranchiseeByCode != null && proxyAndFranchiseeByCode.result() != null) {
                if (proxyAndFranchiseeByCode.isSucc()) {
                    SpmiFranchiseeNetworkVO resultvo = proxyAndFranchiseeByCode.result();
                    log.info("云闪付网点资料请求成功返回数据：{}", resultvo);
                    paydto.setFranchiseeCode(resultvo.getFranchiseeCode());
                    paydto.setFranchiseeId(resultvo.getFranchiseeId());
                    paydto.setFranchiseeName(resultvo.getFranchiseeName());
                    paydto.setFinancialCenterId(resultvo.getFinancialCenterId());
                    paydto.setFinancialCenterName(resultvo.getFinancialCenterDesc());
                    paydto.setFinancialCenterCode(resultvo.getFinancialCenterCode());
                    paydto.setFinancialCenterCode(resultvo.getFinancialCenterCode());
                    paydto.setNetworkId(resultvo.getId());
                    paydto.setNetworkCode(resultvo.getCode());
                    paydto.setNetworkName(resultvo.getName());
                    log.info("云闪付补全加盟商,代理区：{}", JSON.toJSONString(paydto));
                } else {
                    String msg = proxyAndFranchiseeByCode.getMsg();
                    log.info("云闪付网点资料请求失败返回数据：{}", msg);
                    return Result.error(proxyAndFranchiseeByCode.getCode(), proxyAndFranchiseeByCode.getMsg());
                }
            }

            log.info("云闪付请求id{},查询订单/运单详情,参数{}", requestId, dto.getWaybillNo());
            AppOmsWaybillDetailVO appOmsWaybillDetailVO = channelApiFeignClientl.getDetailByNo(dto.getWaybillNo()).result();
            log.info("云闪付请求id{},查询订单/运单详情出参：{}", requestId, JSON.toJSONString(appOmsWaybillDetailVO));

            if (appOmsWaybillDetailVO != null && appOmsWaybillDetailVO.getCollectTime() != null) {
                paydto.setCollectTime(appOmsWaybillDetailVO.getCollectTime());
            }
            String appId = wxMaProperties.getConfigs().get(0).getAppid();
            StopWatch stopWatch = new StopWatch();
            paydto.setApp_id(appId);
            paydto.setPayerClientIp(wxPayCommercialNum);
            paydto.setWaybillNo(dto.getWaybillNo());
            paydto.setOrderNo(dto.getOrderNo());
            paydto.setAmount(dto.getOrderAmount());
            paydto.setSpOpenid(user.getOpenid());
            paydto.setDescription(dto.getDescription());
            log.info("云闪付调用财务预支付接口，请求id：{}，入参：{}", requestId, JSON.toJSONString(paydto));
            stopWatch.start("云闪付请求prepay_id接口");
            Result<String> stringResult = payFeignClient.umsMiniPrePayId(paydto);
            stopWatch.stop();
            log.info("云闪付调用财务预支付接口，请求id：{}，出参：{}", requestId, JSON.toJSONString(stringResult));
            if (stringResult.isSucc()) {
                String result = stringResult.result();
                log.info("云闪付财务预支付请求成功返回数据：{}", result);
                JSONObject jsonObject = JSONObject.parseObject(result);
                return Result.success(jsonObject);
            } else {
                String msg = stringResult.getMsg();
                log.error("云闪付财务预支付请求失败返回数据：{}", msg);
                return Result.error(stringResult.getCode(), stringResult.getMsg());
            }
        } else {
            return Result.error(ResultCodeEnum.ORDER_IS_PAYED);
        }
    }


    public IpayTokenVO getToken(String body,String appid) {
        Result<IpayLooseTokenVO> ipayLooseTokenVOResult = payFeignClient.getToken(body,appid);
        log.info("调用财务预支付Token接口，出参：{}", JSON.toJSONString(ipayLooseTokenVOResult));
        IpayLooseTokenVO ipayLooseTokenVO = ipayLooseTokenVOResult.result();
        IpayTokenVO vo = new IpayTokenVO();
        if (ipayLooseTokenVO != null) {
            vo.setNonceStr(ipayLooseTokenVO.getNonce_str());
            vo.setTimeStamp(String.valueOf(ipayLooseTokenVO.getTimestamp()));
            vo.setPaySign(ipayLooseTokenVO.getSignature());
            return vo;
        }
        return vo;
    }
}
