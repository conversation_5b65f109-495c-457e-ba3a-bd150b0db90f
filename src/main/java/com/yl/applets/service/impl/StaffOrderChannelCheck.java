package com.yl.applets.service.impl;

import com.yl.applets.constant.BaseConstant;
import com.yl.applets.dto.OrderChannelCheckDTO;
import com.yl.applets.feign.CustomerPlatformNetworkFeignClient;
import com.yl.applets.service.InternalEmployees;
import com.yl.applets.service.OrderChannelCheck;
import com.yl.applets.service.StudentSendingService;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/21 20:29
 */
@Component("staffOrderChannelCheck")
@Slf4j
public class StaffOrderChannelCheck implements OrderChannelCheck {


    @Autowired
    private   InternalEmployees internalEmployees;


    @Override
    public void check(OrderChannelCheckDTO dto) {
        Boolean isInternalEmployees = internalEmployees.isInternalEmployees(dto.getMobile());
        if(!isInternalEmployees){
            throw new BusinessException(ResultCodeEnum.ORDER_CHANNEL_NOT_STAFF_ERROR);
        }
    }
}
