package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.yl.applets.constant.AppCacheConstant;
import com.yl.applets.dto.AppAreaDTO;
import com.yl.applets.dto.ForwardRequest;
import com.yl.applets.dto.SysAreaOnlineConfigQueryDTO;
import com.yl.applets.entity.lmdm.SysArea;
import com.yl.applets.enums.SysAreaOnlineConfigTypeEnum;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.OldLmdmFeignClient;
import com.yl.applets.feign.OldNetworkFeighClient;
import com.yl.applets.service.IAppAreaService;
import com.yl.applets.vo.AreaSearchVO;
import com.yl.applets.vo.lmdm.AppAreaVO;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019-08-23
 */
@Service
@Slf4j
public class AppAreaServiceImpl implements IAppAreaService {

    @Autowired
    private OldLmdmFeignClient sysAreaOnlineConfigFeignClient;

    @Autowired
    private OldNetworkFeighClient ediAreaOnlineConfigDispatchFeignClient;

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Value("${app.version:1.0}")
    private String version;

    private Long timeOut = 60L;


    @Override
    public List<AppAreaVO> getAppList() {
        SysAreaOnlineConfigQueryDTO sysAreaOnlineConfigQueryDTO = new SysAreaOnlineConfigQueryDTO();
        sysAreaOnlineConfigQueryDTO.setPlateformType(SysAreaOnlineConfigTypeEnum.TYPE_SMALL_ROUTINE.getType());
        List<SysArea> list = null;
        Object listObject = RedisUtil.get(AppCacheConstant.SYS_AREA_LIST);
        if (listObject != null) {
            list = JSON.parseArray(listObject.toString(), SysArea.class);
            return orikaBeanMapper.mapAsList(list, AppAreaVO.class);
        }
//        list = sysAreaOnlineConfigFeignClient.findOnLineAreas(sysAreaOnlineConfigQueryDTO).result();
        String requestIds = UUID.randomUUID().toString().replaceAll("-", "");
        ForwardRequest requests=new ForwardRequest();
        requests.setRequestId(requestIds);
        requests.setRequestUri("/lmdmapi/sysArea/online/config/findOnLineAreas");
        requests.setBody(JSON.toJSONString(sysAreaOnlineConfigQueryDTO));
        //调接口
        log.info("请求id==>{}接口入参==>{}",requestIds,JSON.toJSONString(requests));
        Result<?> forwards = channelApiFeignClient.forward(requests);
        log.info("请求id==>{}接口返回==>{}",requestIds,JSON.toJSONString(forwards));
        Object results = forwards.result();
        list = JSON.parseArray(JSON.toJSONString(results), SysArea.class);
        List<AppAreaVO> listVo = orikaBeanMapper.mapAsList(list, AppAreaVO.class);
        com.yl.redis.util.RedisUtil.setEx(AppCacheConstant.SYS_AREA_LIST, JSON.toJSONString(listVo), timeOut);
        return listVo;
    }

    @Override
    public List<AppAreaVO> getAppList(Integer parentId, Integer type) {
        List<AppAreaVO> list = getAppList();
        return list.stream().filter(a -> type.equals(a.getType()) && parentId.equals(a.getParentId())).collect(Collectors.toList());
    }

    @Override
    public List<AreaSearchVO> getAppList(String keyWord) {
        List<AreaSearchVO> listVo = new ArrayList<>();
        List<AppAreaVO> list = getAppList();

        List<AppAreaVO> collectProvince = list.stream().filter(a -> a.getType().equals(2)).collect(Collectors.toList());

        List<AppAreaVO> collectCity = list.stream().filter(a -> a.getType().equals(3)).collect(Collectors.toList());

        List<AppAreaVO> collectArea = list.stream().filter(a -> a.getType().equals(4)).collect(Collectors.toList());

        for (int a = 0; a < collectProvince.size(); a++) {
            AppAreaVO a1 = collectProvince.get(a);
            for (int b = 0; b < collectCity.size(); b++) {
                AppAreaVO b1 = collectCity.get(b);
                for (int c = 0; c < collectArea.size(); c++) {
                    AppAreaVO c1 = collectArea.get(c);
                    if(c1.getParentId().equals(b1.getId())&& b1.getParentId().equals(a1.getId())){
                        AreaSearchVO searchVO = new AreaSearchVO();
                        searchVO.setAddrees(a1.getNativeName()+"-"+b1.getNativeName()+"-"+c1.getNativeName());
                        searchVO.setProvinceName(a1.getNativeName());
                        searchVO.setProvinceId(a1.getId());
                        searchVO.setCityId(b1.getId());
                        searchVO.setCityName(b1.getNativeName());
                        searchVO.setAreaId(c1.getId());
                        searchVO.setAreaName(c1.getNativeName());
                        listVo.add(searchVO);
                    }
                }
            }
        }
        return listVo.stream().filter(a -> a.getAddrees().contains(keyWord)).collect(Collectors.toList());
}

    @Override
    public List<AppAreaVO> getAppListV2(AppAreaDTO appAreaDTO) {
        StopWatch stopWatch = new StopWatch();
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        log.info("requestId==>{},获取行政区域==>{}", requestId, JSON.toJSONString(appAreaDTO));
        Integer addressType = appAreaDTO.getAddressType();
        if(addressType==null){
            throw new BusinessException(ServiceErrCodeEnum.SEARCHTYPE_ISNULL);
        }
        String redisKey = "";
        switch (addressType){
            case 1:
                //寄件
                redisKey = AppCacheConstant.SYS_SENDER_AREA_LIST;
                break;
            case 2:
                //收件
                redisKey = AppCacheConstant.SYS_RECEIVER_AREA_LIST;
                break;
            case 3:
                //公用：老的基础数据接口
                redisKey = AppCacheConstant.SYS_AREA_LIST;
                break;
            default:
                throw new BusinessException(ServiceErrCodeEnum.SEARCHTYPE_ERROR);
        }
        List<SysArea> list = null;
        Object listObject = RedisUtil.get(redisKey);
        if (listObject != null) {
            list = JSON.parseArray(listObject.toString(), SysArea.class);
            return orikaBeanMapper.mapAsList(list, AppAreaVO.class);
        }

        SysAreaOnlineConfigQueryDTO sysAreaOnlineConfigQueryDTO = new SysAreaOnlineConfigQueryDTO();
        sysAreaOnlineConfigQueryDTO.setPlateformType(SysAreaOnlineConfigTypeEnum.TYPE_SMALL_ROUTINE.getType());
        switch (addressType){
            case 1:
                //寄件
                stopWatch.start("调用基础数据派件区域范围");
                list = ediAreaOnlineConfigDispatchFeignClient.findSendOnLineAreas(sysAreaOnlineConfigQueryDTO).result();
                stopWatch.stop();
                break;
            case 2:
                //收件
                stopWatch.start("调用基础数据收件区域范围");
                list = ediAreaOnlineConfigDispatchFeignClient.findOnLineAreas(sysAreaOnlineConfigQueryDTO).result();
                stopWatch.stop();
                break;
            case 3:
                //公用：老的基础数据接口
                stopWatch.start("调用基础数据老的（公用）区域范围");
//                list = sysAreaOnlineConfigFeignClient.findOnLineAreas(sysAreaOnlineConfigQueryDTO).result();
                String requestIds = UUID.randomUUID().toString().replaceAll("-", "");
                ForwardRequest requests=new ForwardRequest();
                requests.setRequestId(requestIds);
                requests.setRequestUri("/lmdmapi/sysArea/online/config/findOnLineAreas");
                requests.setBody(JSON.toJSONString(sysAreaOnlineConfigQueryDTO));
                //调接口
                log.info("请求id==>{}接口入参==>{}",requestIds,JSON.toJSONString(requests));
                Result<?> forwards = channelApiFeignClient.forward(requests);
                log.info("请求id==>{}接口返回==>{}",requestIds,JSON.toJSONString(forwards));
                Object results = forwards.result();
                list = JSON.parseArray(JSON.toJSONString(results), SysArea.class);
                stopWatch.stop();
                break;
            default:
                throw new BusinessException(ServiceErrCodeEnum.SEARCHTYPE_ERROR);
        }
        List<AppAreaVO> listVo = orikaBeanMapper.mapAsList(list, AppAreaVO.class);

        // 设置永久缓存，针对于case3 进行永久key，使用job 定时刷新5min，缓存交由channel中台处理
        // 定时刷 otherFeignClient.findOnLineAreas(areaOnlineConfigQueryDTO)
        if (Objects.equals(addressType, 3)) {
            com.yl.redis.util.RedisUtil.set(redisKey, JSON.toJSONString(listVo));
        } else {
            com.yl.redis.util.RedisUtil.setEx(redisKey, JSON.toJSONString(listVo), timeOut);
        }
       log.info("requestId==>{},耗时==>{}，获取行政区域结果==>",requestId, stopWatch.prettyPrint());
        return listVo;
    }

    @Override
    public List<AppAreaVO> listChildV2(AppAreaDTO appAreaDTO) {
        List<AppAreaVO> list = getAppListV2(appAreaDTO);
        return list.stream().filter(a -> appAreaDTO.getType().equals(a.getType()) && appAreaDTO.getParentId().equals(a.getParentId())).collect(Collectors.toList());
    }

    @Override
    public List<AreaSearchVO> listQueryV2(AppAreaDTO appAreaDTO) {
        List<AreaSearchVO> listVo = new ArrayList<>();
        List<AppAreaVO> list = getAppListV2(appAreaDTO);

        List<AppAreaVO> collectProvince = list.stream().filter(a -> a.getType().equals(2)).collect(Collectors.toList());

        List<AppAreaVO> collectCity = list.stream().filter(a -> a.getType().equals(3)).collect(Collectors.toList());

        List<AppAreaVO> collectArea = list.stream().filter(a -> a.getType().equals(4)).collect(Collectors.toList());

        for (int a = 0; a < collectProvince.size(); a++) {
            AppAreaVO a1 = collectProvince.get(a);
            for (int b = 0; b < collectCity.size(); b++) {
                AppAreaVO b1 = collectCity.get(b);
                for (int c = 0; c < collectArea.size(); c++) {
                    AppAreaVO c1 = collectArea.get(c);
                    if(c1.getParentId().equals(b1.getId())&& b1.getParentId().equals(a1.getId())){
                        AreaSearchVO searchVO = new AreaSearchVO();
                        searchVO.setAddrees(a1.getNativeName()+"-"+b1.getNativeName()+"-"+c1.getNativeName());
                        searchVO.setProvinceName(a1.getNativeName());
                        searchVO.setProvinceId(a1.getId());
                        searchVO.setCityId(b1.getId());
                        searchVO.setCityName(b1.getNativeName());
                        searchVO.setAreaId(c1.getId());
                        searchVO.setAreaName(c1.getNativeName());
                        listVo.add(searchVO);
                    }
                }
            }
        }
        return listVo.stream().filter(a -> a.getAddrees().contains(appAreaDTO.getKeyWord())).collect(Collectors.toList());
    }


}
