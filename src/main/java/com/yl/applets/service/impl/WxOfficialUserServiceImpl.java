/**
 * Copyright (C), 2015-2021, 云路供应链科技有限公司
 * FileName: WxOfficialUserServiceImpl
 * Author:   luhong
 * Date:     2021-03-31 18:17
 * Description: 公众号用户
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.entity.WxOfficialUser;
import com.yl.applets.mapper.applets.WxOfficialUserMapper;
import com.yl.applets.service.WxOfficialUserService;
import org.springframework.stereotype.Service;

/**
 * 〈一句话功能简述〉<br> 
 * 〈公众号用户〉
 *
 * <AUTHOR>
 * @create 2021-03-31
 * @since 1.0.0
 */
@Service
public class WxOfficialUserServiceImpl extends ServiceImpl<WxOfficialUserMapper, WxOfficialUser> implements WxOfficialUserService {

}