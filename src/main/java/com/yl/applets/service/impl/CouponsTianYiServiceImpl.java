package com.yl.applets.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alipay.api.internal.util.AlipaySignature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yl.applets.dto.CouponOneKeyDto;
import com.yl.applets.dto.PromotionUserGetAppletsDto;
import com.yl.applets.dto.coupons.TianYiTradeDTO;
import com.yl.applets.dto.coupons.TianyiDTO;
import com.yl.applets.dto.coupons.TianyiPublicDTO;
import com.yl.applets.entity.MemberUser;
import com.yl.applets.entity.WxUser;
import com.yl.applets.enums.BaseDictionaryEnums;
import com.yl.applets.enums.ChannelSourceEnum;
import com.yl.applets.feign.PromotionFeighClient;
import com.yl.applets.mapper.applets.MemberUserMapper;
import com.yl.applets.service.IWxUserService;
import com.yl.applets.service.coupons.CouponsTianYiService;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.exception.OutServiceException;
import com.yl.common.base.model.ResultVO;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-05-24 16:37
 * @Version 1.0
 */
@Component
@Slf4j
public class CouponsTianYiServiceImpl implements CouponsTianYiService {

    @Value("${tianyi.secret.key:iumjhjJHYHk5e@Jtexpress}")
    private String secretKey;

    @Value("${tianyi.coupons.Ids:121}")
    private String couponIds;

    @Value("${tianyi.coupons.publicKey:MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDbGjx/5cUXIDcmUfZQmOyr4ogaemlx4L+Dl1s+jtVPa5sTBq7C/41fBQiRJrzabrVXnPJ+UMhpP3YzKE+vC8eamTBdsicmlxdd2ldKqdsRmYpT2EEXZ3IlxBGbE9zGNy8Q+ZP5sqNKzWW5aq10nlo4O+HLfcMgSiRLoYIX4b+uvwIDAQAB}")
    private String publicKey;

    @Autowired
    private MemberUserMapper memberUserMapper;

    @Resource
    protected RedissonClient redissonClient;

    @Autowired
    private IWxUserService iWxUserService;

    @Autowired
    private PromotionFeighClient promotionFeighClient;


    @Override
    public Boolean checkPhoneAvailability(TianyiDTO dto, HttpServletRequest request) {

        //根据消息头拿到时间戳和签名
        String timeStamp = request.getHeader("timeStamp");
        String sign = request.getHeader("sign");
        log.info("【对接翼支付优惠券】时间戳：{} sign：{}", timeStamp, sign);
        if (StrUtil.hasBlank(timeStamp, sign)) {
            log.info("【对接翼支付优惠券】参数缺失");
            return false;
        }
        //验签
        String signNew = DigestUtils.md5Hex(DigestUtils.md5Hex(JSON.toJSONString(dto) + timeStamp + secretKey));
        log.info("【对接翼支付优惠券】后端加密参数之后，sign：{}", signNew);

        if (!StringUtils.equals(signNew, sign)) {
            throw new BusinessException(0, ResultCodeEnum.IS_NO_ACCESS.getMsg());
        }

        QueryWrapper<MemberUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("MOBILE", dto.getPhone()).eq("is_delete", 0);
        MemberUser memberUser = memberUserMapper.selectOne(queryWrapper);
        return memberUser != null;
    }


    private void checkRsa(TianYiTradeDTO tradeDTO) {
        Map<String, String> params = new TreeMap<>();
        try {
            Field[] fields = tradeDTO.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                Object fieldValue = field.get(tradeDTO);
                if (fieldValue instanceof String && StrUtil.isNotBlank((String) fieldValue)) {
                    params.put(fieldName, fieldValue.toString());
                }
            }
            boolean rsaCheck = AlipaySignature.rsaCheckV1(params, publicKey, tradeDTO.getCharset(), tradeDTO.getSignType());
            if (!rsaCheck) {
                throw new OutServiceException(41, "签名校验失败");
            }
        } catch (Exception e) {
            log.error("翼支付对接优惠券，验证签名失败:{}", e.getMessage());
            throw new OutServiceException(41, "签名校验失败");

        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultVO bindUserAndTrade(TianYiTradeDTO tradeDto) {

        log.info("天翼支付绑定优惠券，入参：{}", JSON.toJSONString(tradeDto));

        String requestId = UUID.randomUUID().toString().replaceAll("-", "");

        if (tradeDto == null || StrUtil.hasBlank(tradeDto.getSign(), tradeDto.getCpUserAccount(), tradeDto.getOrderNo(), tradeDto.getGoodCode())) {
            throw new OutServiceException(40, "必要参数缺失");
        }
        if (StrUtil.isNotBlank(tradeDto.getGoodNumber()) && Integer.parseInt(tradeDto.getGoodNumber()) > 1) {
            throw new OutServiceException(43, "商品数量大于1，一笔订单只能绑定一笔");
        }

        ResultVO vo = new ResultVO();

        //验签
        this.checkRsa(tradeDto);

        //手机号 交易号  优惠券id
        String phone = tradeDto.getCpUserAccount();
        String tradeNo = tradeDto.getOrderNo();
        String couponId = tradeDto.getGoodCode();

        if (!couponIds.contains(couponId)) {
            throw new OutServiceException(2, "商品不存在");
        }

        RLock lock;
        boolean b = false;

        //加锁
        lock = redissonClient.getLock(BcRedisKeyEnum.OUTSIDE_TY_PHONE.keyBuilder(phone));
        try {
            b = lock.tryLock(0, 5, TimeUnit.SECONDS);
            if (b) {
                //处理天翼下包用户新增
                WxUser wxUser = iWxUserService.handleCMCCBindPhone(new WxUserVo().setOpenid(phone), phone, requestId, ChannelSourceEnum.TIANYI.getKey());
                log.info("【处理天翼下包】用户信息注册，返回user：{}", JSON.toJSONString(wxUser));


                //优惠券中台领取优惠券DTO
                List<PromotionUserGetAppletsDto> list = new ArrayList<>(1);

                PromotionUserGetAppletsDto appletsDto = new PromotionUserGetAppletsDto();
                appletsDto.setUserAlias("极兔用户");
                appletsDto.setUserId(wxUser.getId().longValue());
                appletsDto.setUserUuid(wxUser.getOpenid());
                appletsDto.setUserPhone(wxUser.getMobile());
                appletsDto.setNumberId(wxUser.getNumberId().longValue());
                appletsDto.setProId(Long.parseLong(couponId));
                appletsDto.setExternalTrade(tradeNo);
                list.add(appletsDto);


                log.info("请求id==>{},【处理天翼下包】用户领取优惠券,入参==>{}", requestId, JSON.toJSONString(list));
                Result<List<Long>> result = promotionFeighClient.getCouponByOutActiveValue(list);
                log.info("请求id==>{},【处理天翼下包】用户领取优惠券,出参==>{}", requestId, JSON.toJSONString(result));
                if (!result.getCode().equals(ResultCodeEnum.SUCCESS.getCode())) {
                    throw new OutServiceException(result.getCode(), result.getMsg());
                }

                //拼装返回数据
                vo.setCode(1);
                vo.setMsg("请求成功");
                ResultVO.TianYiTradeVO detailVO = new ResultVO.TianYiTradeVO();
                detailVO.setCpTradeNo(result.getData().get(0).toString());
                detailVO.setOrderNo(tradeNo);
                vo.setResults(detailVO);
                return vo;

            } else {
                throw new OutServiceException(5, "交易进行中");
            }

        } catch (InterruptedException e) {
            log.error("【处理天翼下包】请求id==>{}获取锁失败：{}", requestId, e);
            throw new OutServiceException(0, "交易失败");

        } finally {
            if (b) {
                lock.unlock();
            }
        }
    }


    @Override
    public ResultVO bindUserAndTradeQuery(TianYiTradeDTO dto) {

        log.info("【处理天翼下包】请求入参：{}", JSON.toJSONString(dto));

        if (dto == null || StrUtil.hasBlank(dto.getSign(), dto.getOrderNo())) {
            throw new OutServiceException(40, "必要参数缺失");
        }

        //验签
        this.checkRsa(dto);


        Result<Long> result = promotionFeighClient.bindUserAndTradeQuery(dto.getOrderNo());
        log.info("【处理天翼下包】请求反参：{}", JSON.toJSONString(result));

        if (result.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
            throw new OutServiceException(0, "查询失败");
        }

        if (Objects.isNull(result.getData())) {
            throw new OutServiceException(50, "交易查询失败");
        }

        ResultVO vo = new ResultVO().setCode(1).setMsg("请求成功");
        ResultVO.TianYiTradeVO detailVO = new ResultVO.TianYiTradeVO();
        detailVO.setOrderNo(dto.getOrderNo());
        detailVO.setCpTradeNo(result.getData().toString());
        vo.setResults(detailVO);

        return vo;
    }
}
