package com.yl.applets.service.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.dto.AppletsLoginLogDTO;
import com.yl.applets.entity.AppletsLoginRegisterEntity;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.mapper.applets.AppletsLoginRegisterMapper;
import com.yl.applets.service.AppletsRegisterLogService;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.util.GenerationIdUtil;
import com.yl.common.base.util.StringUtils;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2022-10-31 16:03
 * @Version 1.0
 */
@Slf4j
@Service
public class AppletsRegisterLogServiceImpl extends ServiceImpl<AppletsLoginRegisterMapper, AppletsLoginRegisterEntity> implements AppletsRegisterLogService {

    @Value("#{'${municipalities.directly:}'.split(',')}")
    private List<String> municipalitiesDirectlyList;

    @Value("#{'${county.directly:}'.split(',')}")
    private List<String> countyDirectlyList;

    @Value("${jwt.expiration:2592000}")
    private Long expiration;

    private static final DateTimeFormatter DTF = DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00");

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public boolean saveInfo(AppletsLoginLogDTO dto, WxUserVo user) {
        String requestId = UUID.randomUUID().toString().replace("-", "");

        AppletsLoginRegisterEntity entity = new AppletsLoginRegisterEntity();
        entity.setId(GenerationIdUtil.getId());
        entity.setMemberId(user.getNumberId().longValue());
        //处理省市区数据
        entity.setProvince(dto.getProvince());
        entity.setZone(dto.getZone());
        //处理下市的数据，当城市是省直辖县时返回为空，以及城市为北京、上海、天津、重庆四个直辖市时，该字段返回为空
        if (StringUtils.isEmpty(dto.getCity())) {
            //首先判断是不是直辖市，那就是省字段就是直辖市
            if (municipalitiesDirectlyList.contains(dto.getProvince())) {
                //市的字段就是省的字段
                dto.setCity(dto.getProvince());
            }
            //判断当前区是不是直辖区
            if (countyDirectlyList.contains(dto.getZone())) {
                //市字段和区字段相等
                dto.setCity(dto.getZone());
            }
        }
        entity.setCity(dto.getCity());
        entity.setCreateTime(LocalDateTime.now());

        //下面判断是否需要保存
        Object obj = RedisUtil.get(BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(user.getId().toString()));
        //拦截器已拦截，不用判断null。转换数据
        WxUserVo loginVO = JSONObject.parseObject(obj.toString(), WxUserVo.class);
        log.info("requestId==>{},从缓存中拿到的token：{}", requestId, JsonUtils.toJson(loginVO));
        if (loginVO.getRecordNum() == null || loginVO.getRecordNum() == 0) {
            if (loginVO.getProvince() == null && loginVO.getCity() == null) {
                //年月需要特殊处理一下  取注册时间的所属年月
                entity.setYear((long) loginVO.getCreateTime().getYear());
                entity.setMonth((long) loginVO.getCreateTime().getMonth().getValue());
                //保存创建时间的所属月份
                entity.setRegisterTime(loginVO.getCreateTime());
                //当缓存中记录数为null或者0  说明之前用户没有存过
                try {
                    save(entity);
                } catch (Exception e) {
                    log.warn("requestId==>{},保存注册省市区信息出错，dto数据为:{},重复数据为：{}", requestId, JsonUtils.toJson(dto), JsonUtils.toJson(entity));
                    return false;
                }
                loginVO.setRecordNum(1);
                //当这边过期时间到了，重新登陆还是会进入判断。表中做了memberId唯一索引，重复插入会报错
                RedisUtil.setEx(BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(user.getId().toString()), JSON.toJSONString(loginVO), expiration);
                log.info("requestId==>{},注册省市区重新更新token,loginVO：{}", requestId, JsonUtils.toJson(loginVO));
                log.info("requestId==>{},保存注册省市区日志成功，入参信息：{}", requestId, JSON.toJSONString(entity));
                return true;
            }
        }
        return false;
    }
}
