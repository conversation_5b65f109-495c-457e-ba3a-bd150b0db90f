package com.yl.applets.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.entity.ActivityConfigArea;
import com.yl.applets.mapper.applets.ActivityConfigAreaMapper;
import com.yl.applets.service.IActivityAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ActivityAreaServiceImpl extends ServiceImpl<ActivityConfigAreaMapper, ActivityConfigArea> implements IActivityAreaService {

    @Autowired
    private ActivityConfigAreaMapper activityConfigAreaMapper;


    public int isDeleteAreas(List<Long> ids){
        return activityConfigAreaMapper.deleteBatchIds(ids);
    }

}
