package com.yl.applets.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.dto.ActivityPrizeRecordDto;
import com.yl.applets.entity.ActivityUserRecord;
import com.yl.applets.mapper.applets.ActivityUserRecordMapper;
import com.yl.applets.service.IActivityUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动管理
 * @author: xiongweibin
 * @create: 2020-08-24 11:46
 */
@Service
@Slf4j
public class ActivityUserServiceImpl extends ServiceImpl<ActivityUserRecordMapper, ActivityUserRecord> implements IActivityUserService {


    @Autowired
    private ActivityUserRecordMapper activityUserRecordMapper;

    @Override
    public ActivityPrizeRecordDto getPrizeInfo(Integer numberId,Integer type) {
        return activityUserRecordMapper.getPrizeInfo(numberId,type);
    }
}
