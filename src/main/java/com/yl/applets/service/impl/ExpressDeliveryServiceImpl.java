package com.yl.applets.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.ThirdExpressApiDTO;
import com.yl.applets.enums.OrderChannelTypeEnum;
import com.yl.applets.enums.OrderSourceEnum;
import com.yl.applets.feign.CcmOrderMarkApiFeignClient;
import com.yl.applets.feign.OmsCcmVipOrderFeign;
import com.yl.applets.feign.OmsMiniGateWayFeign;
import com.yl.applets.service.IExpressDeliveryService;
import com.yl.applets.vo.OrderMarkCodeVO;
import com.yl.applets.vo.ThirdExpressListVO;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-23
 */
@Service
@Slf4j
public class ExpressDeliveryServiceImpl extends BaseServiceImpl implements IExpressDeliveryService {

    public static final String YLS = "已揽收";
    public static final String YLJ = "已揽件";
    @Autowired
    private OmsMiniGateWayFeign omsMiniGateWayFeign;

    @Autowired
    private OmsCcmVipOrderFeign omsCcmVipOrderFeign;

    @Value("${logistic.grey.phone:all}")
    private String logisticStatusGrey;

    @Autowired
    private CcmOrderMarkApiFeignClient ccmOrderMarkApiFeignClient;

    @Override
    public Page<ThirdExpressListVO> getPages(ThirdExpressApiDTO dto) {
        WxUserVo user = getUser();
        LocalDateTime createTime = user.getCreateTime();
        LocalDateTime now = LocalDateTime.now();
        if(createTime!=null){
            if(createTime.plusDays(90).isBefore(now)){
                log.info("查订单列表,注册时间在90天以上");
            }else {
                dto.setInputTimeStart(createTime);
                dto.setInputTimeEnd(now);
            }
        }
        log.info("查单List,入参==>{} 查询列表状态灰度配置:{}",JSON.toJSONString(dto), logisticStatusGrey);
        Page<ThirdExpressListVO> page = (StrUtil.equals(logisticStatusGrey, "all") || logisticStatusGrey.contains(user.getMobile())) ?
                omsCcmVipOrderFeign.getMyExpressPage(dto).result() : omsMiniGateWayFeign.getMyExpressPage(dto).result();
        log.info("查单List,出参==>{}",JSON.toJSONString(page));
        List<ThirdExpressListVO> records = page.getRecords();
        Map<Long,List<String> > markCodeMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(records)){
            List<Long> orderIds = records.stream().map(ThirdExpressListVO::getId).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(orderIds)){
                List<OrderMarkCodeVO> orderMarkList = ccmOrderMarkApiFeignClient.queryMarkCodes(orderIds).result();
                if(CollectionUtils.isNotEmpty(orderMarkList)){
                    Map<Long, List<String>> map = orderMarkList.stream().collect(Collectors.toMap(OrderMarkCodeVO::getOrderId,OrderMarkCodeVO::getMarkCodes, (k1, k2) -> k1));
                    markCodeMap.putAll(map);
                }
            }
        }
        records.forEach(r->{
            if(YLS.equals(r.getStatusName()) ){
                r.setStatusName(YLJ);
            }
            if (Objects.equals(2, r.getOrderPayStatus()) && (Objects.equals(OrderSourceEnum.WX_MINIPROGRAM.getCode(), r.getOrderSourceCode())
                    ||Objects.equals(OrderSourceEnum.ZSJ.getCode(), r.getOrderSourceCode())  ) &&
                    Objects.equals(user.getNumberId(), r.getMemberId())){
                r.setIsShowPay(Boolean.TRUE);
            }
            if(!Objects.isNull(markCodeMap.get(r.getId()) ) ){
                r.setOrderChannelType(OrderChannelTypeEnum.getMappingCode(markCodeMap.get(r.getId())));
            }
        });
        page.setRecords(records);
        log.info("获取订单列表,入参：{},结果：{}", JSONObject.toJSONString(dto), JSONObject.toJSONString(page));
        return page;
    }

    @Override
    public Map<String, Long> count(ThirdExpressApiDTO dto) {
        WxUserVo user = getUser();
        log.info("count远程入参:{} 查询列表状态灰度配置:{}", JSON.toJSONString(dto), logisticStatusGrey);
        return (StrUtil.equals(logisticStatusGrey, "all") || logisticStatusGrey.contains(user.getMobile())) ?
                omsCcmVipOrderFeign.getMyExpressCnt(dto).result() : omsMiniGateWayFeign.getMyExpressCnt(dto).result();
    }

}
