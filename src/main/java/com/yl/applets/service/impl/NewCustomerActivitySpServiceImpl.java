package com.yl.applets.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.yl.applets.config.MarketActivitySourceProperties;
import com.yl.applets.constant.CommonConstants;
import com.yl.applets.dto.DispatchCodeFetchNetworkIdQuery;
import com.yl.applets.dto.DispatchCodeRequestDTO;
import com.yl.applets.dto.market.NewCustomerActivitySpSignUpDTO;
import com.yl.applets.entity.MarketActivity;
import com.yl.applets.entity.NewCustomerActivitySp;
import com.yl.applets.enums.MarketActivitStatusEnum;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.*;
import com.yl.applets.helper.TencentDescribeCaptchaHelper;
import com.yl.applets.mapper.market.MarketActivityMapper;
import com.yl.applets.mapper.market.NewCustomerActivitySpMapper;
import com.yl.applets.service.NewCustomerActivitySpService;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.utils.SmsAccessUtils;
import com.yl.applets.vo.*;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.CollectionUtils;
import com.yl.common.base.util.GenerationIdUtil;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/9 15:02
 * @description
 */
@Slf4j
@Service
public class NewCustomerActivitySpServiceImpl extends ServiceImpl<NewCustomerActivitySpMapper, NewCustomerActivitySp> implements NewCustomerActivitySpService {
    @Resource
    private MarketActivityMapper marketActivityMapper;
    @Resource
    private NewCustomerActivitySpMapper newCustomerActivitySpMapper;
    @Resource
    private DispatchCodeFeignClient dispatchCodeFeignClient;
    @Resource
    private CustomerPlatformNetworkFeignClient customerPlatformNetworkFeignClient;
    @Resource
    private TencentDescribeCaptchaHelper tencentDescribeCaptchaHelper;
    @Resource
    private NewDispatchCodeFeignClient newDispatchCodeFeignClient;
    @Resource
    private MarketActivitySourceProperties marketActivitySourceProperties;
    @Resource
    private RedisTemplate<String,Object> redisTemplate;

    @Resource
    private SysCustomerFeignClient sysCustomerFeignClient;

    @Autowired
    private  ChannelApiFeignClient channelApiFeignClient;

    @Autowired
    private SmsAccessUtils smsAccessUtils;

    @Value("${activity.expiration:1}")
    private Long expiration;

    @Value("${activity.maxCount:5}")
    private Integer maxCount;

    @Override
    public Boolean signUp(NewCustomerActivitySpSignUpDTO dto) {
        log.info("拉新活动报名==>检查当前活动是否存在/开始dto===>{}",JSON.toJSONString(dto));
        // 检验滑块
        // this.checkTencentCaptcha(dto);
        // 检验图片验证码
        this.checkCaptcha(dto);
        // 检查渠道来源
        this.checkMarketActivitySource(dto.getMarketActivityCode(),dto.getSource());
        // 检查当前活动是否存在/开始
        MarketActivity marketActivity = this.checkMarketActivityStart(dto);
        // 填充报名信息
        log.info("拉新活动报名==>填充报名信息开始");
        NewCustomerActivitySp newCustomerActivitySp = this.populateNewCustomerActivitySp(dto, marketActivity);
        log.info("拉新活动报名==>填充报名信息结束，填充信息为: {}", JSON.toJSONString(newCustomerActivitySp));
        // 保存报名信息
        this.save(newCustomerActivitySp);
        log.info("拉新活动报名==>报名结束");
        return true;
    }

    private void checkCaptcha(NewCustomerActivitySpSignUpDTO dto) {
        String captchaKey = BcRedisKeyEnum.APPLETS_CAPTCHA.keyBuilder(dto.getCtoken());
        String captcha = (String) redisTemplate.opsForValue().get(captchaKey);
        //校验验证码
        if (Objects.isNull(captcha)) {
            throw new BusinessException(ServiceErrCodeEnum.CAPTCHA_ERROR);
        }
        if (!dto.getCaptcha().equalsIgnoreCase(captcha)) {
            throw new BusinessException(ServiceErrCodeEnum.CAPTCHA_ERROR);
        }
        RedisUtil.delete(captchaKey);
    }

    @Deprecated
    private void checkTencentCaptcha(NewCustomerActivitySpSignUpDTO dto) {
        tencentDescribeCaptchaHelper.sendCaptchaCode(dto.getRandstr(), dto.getTicket());
    }


    /**
     * 检查渠道来源
     */
    private void checkMarketActivitySource(String marketActivityCode, String curSource) {
//        if (!marketActivitySourceProperties.contains(curSource)) {
//            throw new BusinessException(ResultCodeEnum.SOURCE_NON_EXISTENT);
//        }
//        if (!CommonConstants.SOURCE_LIST.contains(curSource)) {
//            throw new BusinessException(ResultCodeEnum.SOURCE_CAN_NOT_REPORT);
//        }
        String channel = channelApiFeignClient.getChannelByCode(marketActivityCode).result();
        if (StringUtils.isNotBlank(channel)) {
            List<String> channelList = Arrays.asList(channel.split(","));
            if (!channelList.contains(curSource)) {
                log.info("checkMarketActivitySource 渠道来源不匹配");
                throw new BusinessException(ResultCodeEnum.SOURCE_CAN_NOT_REPORT);
            }
        }
    }


    /**
     * 填充报名信息
     */
    private NewCustomerActivitySp populateNewCustomerActivitySp(NewCustomerActivitySpSignUpDTO dto, MarketActivity marketActivity) {

        NewCustomerActivitySp newCustomerActivitySp = new NewCustomerActivitySp();
        // 通过省市区、详细地址获取网点ID、网点name
//         DispatchCodeResponseVO result = this.getDispatchCodeResponseVO(dto);
        //更换接口
        GetDispatchNetworkIdMapVO dispatchNetworkIdMapVO = this.getDispatchNetworkIdMapVO(dto);
//        Map<String, Object> map = dispatchNetworkIdMapVO.getMap();

        // 通过省市区、详细地址获取网点ID、网点name
        DispatchCodeResponseVO dispatchCodeResponseVO = this.getNetworkIdMapVO(dto);
            if (dispatchCodeResponseVO!=null && dispatchCodeResponseVO.getDeliverNetworkId()!=null) {
                Integer networkId = dispatchCodeResponseVO.getDeliverNetworkId();
                log.info("通过网点id查询代理区，网点ID：{}", networkId);
                // 通过网点id查询代理区、加盟商信息，出参: 代理区、加盟商（ID、code、名称）
                GetProxyAndfranchiseeVO vo = this.getProxyAndfranchiseeById(networkId);
                if (Objects.nonNull(vo)) {
                    newCustomerActivitySp.setProxyName(vo.getProxyName());
                    newCustomerActivitySp.setProxyCode(vo.getProxyCode());
                    newCustomerActivitySp.setFranchiseeName(vo.getFranchiseeName());
                    newCustomerActivitySp.setFranchiseeCode(vo.getFranchiseeCode());
                    newCustomerActivitySp.setNetworkName(vo.getName());
                    newCustomerActivitySp.setNetworkCode(vo.getCode());
                }
            }
        // 填充活动信息
        newCustomerActivitySp.setMarketActivityId(marketActivity.getId());
        newCustomerActivitySp.setMarketActivityCode(marketActivity.getCode());
        newCustomerActivitySp.setMarketActivityName(marketActivity.getName());
        // 填写用户信息
        newCustomerActivitySp.setId(GenerationIdUtil.getId());
        newCustomerActivitySp.setSource(dto.getSource());
        newCustomerActivitySp.setName(dto.getName());
        newCustomerActivitySp.setPhone(dto.getPhone());
        newCustomerActivitySp.setInviter(dto.getInviter());
        newCustomerActivitySp.setIsInviter(dto.getIsInviter());
        // 用户填写的省市区+详细地址
        final String fullAddress = dto.getProvinceName() + dto.getCityName() + dto.getAreaName() + dto.getAddress();
        newCustomerActivitySp.setFullAddress(fullAddress);
        newCustomerActivitySp.setCreateBy(-1L);
        newCustomerActivitySp.setCreateByName("system");
        newCustomerActivitySp.setCreateTime(LocalDateTime.now());
        newCustomerActivitySp.setIsCooperated(0);
        return newCustomerActivitySp;
    }


    private GetProxyAndfranchiseeVO getProxyAndfranchiseeById(Integer networkId) {
        List<Long> networkIds = Collections.singletonList(networkId.longValue());
        log.info("通过网点id查询==>入参: {}", JSON.toJSONString(networkIds));
        Result<List<GetProxyAndfranchiseeVO>> result = customerPlatformNetworkFeignClient.getProxyAndfranchiseeById(networkIds);
        log.info("通过网点id查询==>出参: {}", JSON.toJSONString(result));
        if (ResultCodeEnum.SUCCESS.getCode() != result.getCode()) {
            return null;
        }
        List<GetProxyAndfranchiseeVO> vos = result.getData();
        if (CollUtil.isEmpty(vos)) return null;
        return vos.get(0);
    }

    /**
     * 由于精度问题，弃用
     */
    @Deprecated
    private DispatchCodeResponseVO getDispatchCodeResponseVO(NewCustomerActivitySpSignUpDTO dto) {
        DispatchCodeRequestDTO dispatchCodeRequestDTO = new DispatchCodeRequestDTO();
        dispatchCodeRequestDTO.setProvinceId(Long.valueOf(dto.getProvinceId()));
        dispatchCodeRequestDTO.setProvince(dto.getProvinceName());
        dispatchCodeRequestDTO.setCityId(Long.valueOf(dto.getCityId()));
        dispatchCodeRequestDTO.setCity(dto.getCityName());
        dispatchCodeRequestDTO.setAreaId(Long.valueOf(dto.getAreaId()));
        dispatchCodeRequestDTO.setArea(dto.getAreaName());
        dispatchCodeRequestDTO.setDetails(dto.getAddress());
        log.info("通过省市区、详细地址获取网点信息=>入参: {}", JSON.toJSONString(dto));
        Result<DispatchCodeResponseVO> dispatchCodeResponseVOResult = dispatchCodeFeignClient.fetchCodesNew(dispatchCodeRequestDTO);
        log.info("通过省市区、详细地址获取网点信息=>出参: {}", JSON.toJSONString(dispatchCodeResponseVOResult));
        return dispatchCodeResponseVOResult.result();
    }

    //更换通过省市区获取网点三段码接口（旧）
    private GetDispatchNetworkIdMapVO getDispatchNetworkIdMapVO(NewCustomerActivitySpSignUpDTO dto) {
        DispatchCodeFetchNetworkIdQuery query = new DispatchCodeFetchNetworkIdQuery();
        String uq = GenerationIdUtil.getId().toString();
        query.setUniqueSequence(uq);
        query.setProvinceId(dto.getProvinceId());
        query.setProvince(dto.getProvinceName());
        query.setCityId(dto.getCityId());
        query.setCity(dto.getCityName());
        query.setAreaId(dto.getAreaId());
        query.setArea(dto.getAreaName());
        query.setDetails(query.fullAddress());
        List<DispatchCodeFetchNetworkIdQuery> queryList = Collections.singletonList(query);
        log.info("通过省市区、详细地址获取网点信息=>入参: {}", JSON.toJSONString(queryList));
        Result<Map<String, Object>> mapResult = newDispatchCodeFeignClient.fetchNetworkIds(queryList);
        log.info("通过省市区、详细地址获取网点信息=>出参: {}", JSON.toJSONString(mapResult));
        Map<String, Object> map = mapResult.result();
        GetDispatchNetworkIdMapVO vo = new GetDispatchNetworkIdMapVO();
        vo.setMap(map);
        vo.setUq(uq);
        return vo;
    }

    //更换通过省市区获取网点三段码接口（新）
    private DispatchCodeResponseVO getNetworkIdMapVO(NewCustomerActivitySpSignUpDTO dto) {
        DispatchCodeFetchNetworkIdQuery query = new DispatchCodeFetchNetworkIdQuery();
        String uq = GenerationIdUtil.getId().toString();
        query.setUniqueSequence(uq);
        query.setProvinceId(dto.getProvinceId());
        query.setProvince(dto.getProvinceName());
        query.setCityId(dto.getCityId());
        query.setCity(dto.getCityName());
        query.setAreaId(dto.getAreaId());
        query.setArea(dto.getAreaName());
        query.setDetails(dto.getAddress());
        query.setBizSource("市场拉新");
        List<DispatchCodeFetchNetworkIdQuery> queryList = Collections.singletonList(query);
        log.info("通过省市区、详细地址获取网点信息=>入参: {}", JSON.toJSONString(queryList));
        Result<Map<String, DispatchCodeResponseVO>> mapResult = dispatchCodeFeignClient.fetchNetworkIds(queryList);
        log.info("通过省市区、详细地址获取网点信息=>出参: {}", JSON.toJSONString(mapResult));
        Map<String, DispatchCodeResponseVO> map = mapResult.result();
        DispatchCodeResponseVO dispatchCodeResponseVO = new DispatchCodeResponseVO();
        if(map!=null && map.get(uq)!=null){
            dispatchCodeResponseVO=map.get(uq);
        }
        return dispatchCodeResponseVO;
    }

    /**
     * 检查当前活动是否存在
     */
    private MarketActivity checkMarketActivityStart(NewCustomerActivitySpSignUpDTO dto) {
        // md5
        String sign = DigestUtils.md5Hex(DigestUtils.md5Hex(dto.getTimes() + CommonConstants.H5_MARKET_ACTIVITY_MD5_KEY));

        log.info("后端MD5加密之后: {}", sign);

        if (!Objects.equals(sign, dto.getSign())) {
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }

        // 根据活动编码查询当前活动是否存在
        LambdaQueryWrapper<MarketActivity> wrapper = Wrappers.<MarketActivity>query()
                .lambda()
                .select(MarketActivity::getId, MarketActivity::getCode, MarketActivity::getName, MarketActivity::getStartTime, MarketActivity::getEndTime, MarketActivity::getStatus)
                .eq(MarketActivity::getCode, dto.getMarketActivityCode());
        MarketActivity marketActivity = marketActivityMapper.selectOne(wrapper);

        log.info("MarketActivityCode===> {},邀请人===> {},活动marketActivity===> {},",dto.getMarketActivityCode(), dto.getInviter(),JSON.toJSONString(marketActivity));
        if (Objects.isNull(marketActivity)) {
            throw new BusinessException(ResultCodeEnum.ACTIVITY_NOT_EXIST_ERROR);
        }


        if (Objects.equals(0, marketActivity.getStatus()) || (Objects.nonNull(marketActivity.getEndTime()) && marketActivity.getEndTime().isBefore(LocalDateTime.now()))) {
            throw new BusinessException(ResultCodeEnum.ACTIVITY_NOT_PERMIT_SIGN_UP);
        }

        if (Objects.equals(0, marketActivity.getStatus()) || (Objects.nonNull(marketActivity.getStartTime()) && marketActivity.getStartTime().isAfter(LocalDateTime.now()))) {
            throw new BusinessException(ResultCodeEnum.ACTIVITY_NOT_PERMIT_SIGN_UP);
        }

        // 根据手机号和活动编码查询当前用户报名是否重复报名
        LambdaQueryWrapper<NewCustomerActivitySp> spWrapper = Wrappers.<NewCustomerActivitySp>query()
                .lambda()
                .eq(NewCustomerActivitySp::getPhone, dto.getPhone())
                .eq(NewCustomerActivitySp::getMarketActivityCode, dto.getMarketActivityCode());
        Integer count = newCustomerActivitySpMapper.selectCount(spWrapper);
        if (count > 0) {
            throw new BusinessException(ResultCodeEnum.NOT_PERMIT_DOUBLE_SIGN_UP);
        }

        //多个活动并行时，根据手机号做判断，一个手机号只能参加一次活动
        List<String>  marketActivityIds= newCustomerActivitySpMapper.getInfoByPhone(dto.getPhone());
        if(CollectionUtils.isNotEmpty(marketActivityIds)){
            List<String> marketActivityIdList = marketActivityIds.stream().filter(item -> !Objects.equals(item, marketActivity.getId() + "")).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(marketActivityIdList)){
                List<MarketActivity> marketActivityList = marketActivityMapper.getInfoById(marketActivityIdList);
                if(CollectionUtils.isNotEmpty(marketActivityList)){
                    marketActivityList.forEach(item->{
                        if(!Objects.isNull(item.getStartTime()) && !Objects.isNull(item.getEndTime()) && !Objects.isNull(item.getStatus())){
                            LocalDateTime now = LocalDateTime.now();
                            if( item.getStartTime().isBefore(now) && item.getEndTime().isAfter(now) && Objects.equals(item.getStatus(), MarketActivitStatusEnum.YES.getCode())){
                                throw new BusinessException(ResultCodeEnum.NOT_PERMIT_DOUBLE_SIGN_UP);
                            }
                        }
                    });

                }
            }

        }



        //如果邀请人不为空，判断邀请人客户编码是否存在
        if(dto.getInviter()!=null && dto.getInviter()!=""){
            StopWatch stopwatch = new StopWatch();
            // 调用客户资料接口获取=>客户创建时间、客户编码、商家ID
            stopwatch.start("请求客户资料接口querySameMallIdByCodes");
            // 根据客户编码调用客户资料接口获取=>客户创建时间、客户编码、商家ID
            List<SysCustomerGroupByMallVO> sysCustomerSameMallGroupVO  = this.getCustomerSameMallGroupsResult(dto.getInviter());
            stopwatch.stop();

            //如果邀请人客户编码不存在则「否，客户编码不存在」2
            if(CollectionUtils.isEmpty(sysCustomerSameMallGroupVO)){
                dto.setIsInviter(2);
            }else {
                if(marketActivity!=null && marketActivity.getStartTime()!=null){
                    boolean isInviter= isInviter(sysCustomerSameMallGroupVO,marketActivity.getStartTime(),dto.getInviter());
                    //若注册时间在活动开始时间之后，则「邀请人是否有效」一栏为：「否，邀请人为新客户」
                    //若注册时间在活动开始时间之前，则「邀请人是否有效」一栏为：「是」
                    if (isInviter) {
                        dto.setIsInviter(1);
                    }else {
                        dto.setIsInviter(0);
                    }
                }
            }
        }

        //限流，增加三段码接口调用次数限制，调用5次后，无法调用，前端提示：当前报名人数较多，请稍后再试哦～
        String key = BcRedisKeyEnum.WAYBILL_QUERY_COUNT_PER_MINUTER.keyBuilder("ACTIVITY");
        //每秒允许调5次
        smsAccessUtils.currentLimiting(key, maxCount, "拉新活动老带新活动报名", expiration);

        return marketActivity;
    }



    public boolean isInviter(List<SysCustomerGroupByMallVO> sysCustomerSameMallGroupVO,LocalDateTime startTime,String inviter){
        for (SysCustomerGroupByMallVO sysCustomerGroupByMallVO: sysCustomerSameMallGroupVO) {
            //若邀请人注册时间在活动开始时间之前，则「邀请人是否有效」一栏为：「是」
            if(sysCustomerGroupByMallVO!=null && sysCustomerGroupByMallVO.getCreateTime()!=null
                    && sysCustomerGroupByMallVO.getCode()!=null
                    && sysCustomerGroupByMallVO.getCode().equals(inviter)
                    && sysCustomerGroupByMallVO.getCreateTime().isBefore(startTime)){
                return true;
            }
        }
        return false;
    }

    /**
     * 调用客户资料接口，获取
     * 1、客户创建时间
     * 2、客户编码
     * 3、商家ID
     * 4、商家id相同客户：客户创建时间、 客户编码、商家ID
     */
    private List<SysCustomerGroupByMallVO> getCustomerSameMallGroupsResult(String customerCode) {
        List<String> customerCodes = Collections.singletonList(customerCode);
        log.info("请求客户资料接口querySameMallIdByCodes===>入参: {}", JSON.toJSONString(customerCodes));
        Result<List<SysCustomerGroupByMallVO>> customerSameMallGroupsResult = sysCustomerFeignClient.querySameMallIdByCodes(customerCodes);
        log.info("请求客户资料接口querySameMallIdByCodes===>出参: {}", JSON.toJSONString(customerSameMallGroupsResult));
        List<SysCustomerGroupByMallVO> vos = customerSameMallGroupsResult.result();
        return vos;
    }


}
