package com.yl.applets.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.entity.MemberScoreRecord;
import com.yl.applets.mapper.applets.MemberScoreRecordMapper;
import com.yl.applets.service.IMemberScoreRecordService;
import com.yl.applets.vo.MemberScoreRecordVo;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-07-15
 */
@Service
@Slf4j
public class MemberScoreRecordImpl extends ServiceImpl<MemberScoreRecordMapper, MemberScoreRecord> implements IMemberScoreRecordService {


    @Autowired
    private MemberScoreRecordMapper memberScoreRecordMapper;

    @Autowired
    private OrikaBeanMapper beanMapper;


    @Override
    public Result<IPage<MemberScoreRecordVo>> queryScoreRecord(WxUserVo user, Page page) {
        IPage<MemberScoreRecordVo> result= new Page<>();
        QueryWrapper<MemberScoreRecord> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0).eq("member_id", user.getNumberId()).isNotNull("grow_record");
        queryWrapper.orderByDesc("create_time");
        IPage<MemberScoreRecord> page1 = memberScoreRecordMapper.selectPage(page, queryWrapper);
        List<MemberScoreRecordVo> memberScoreRecordVos = beanMapper.mapAsList(page1.getRecords(), MemberScoreRecordVo.class);
        result.setRecords(memberScoreRecordVos);
        result.setCurrent(page1.getCurrent());
        result.setTotal(page1.getTotal());
        result.setSize(page1.getSize());
        return Result.success(result);
    }
}
