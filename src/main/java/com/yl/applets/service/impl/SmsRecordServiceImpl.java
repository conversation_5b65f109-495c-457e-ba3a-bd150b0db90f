package com.yl.applets.service.impl;

import com.yl.applets.entity.SmsRecord;
import com.yl.applets.mapper.applets.SmsRecordMapper;
import com.yl.applets.service.ISmsRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 短信记录 服务实现类
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-29
 */
@Service
public class SmsRecordServiceImpl extends ServiceImpl<SmsRecordMapper, SmsRecord> implements ISmsRecordService {

}
