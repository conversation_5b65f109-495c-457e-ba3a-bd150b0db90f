package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.constant.RedisKeyConstants;
import com.yl.applets.dto.UserCardDTO;
import com.yl.applets.entity.UserCard;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.OldNetworkFeighClient;
import com.yl.applets.mapper.applets.UserCardMapper;
import com.yl.applets.service.IUserCardService;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.utils.InvokeCardUtil;
import com.yl.applets.vo.UserCardVo;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.util.YlPreconditions;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * <p>
 * 用户实名认证表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-22
 */
@Service
@Slf4j
public class UserCardServiceImpl extends ServiceImpl<UserCardMapper, UserCard> implements IUserCardService {


    @Autowired
    private OldNetworkFeighClient appletsCustomerFeignClient;


    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;


    @Resource
    private InvokeCardUtil invokeCardUtil;

    @Value("${applets.ocr.limitNum:3}")
    public Integer limitNum;


    @Override
    public UserCardVo saveUserCard(UserCardDTO dto,String openId) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("实名制新增,请求id:{},入参==>{}",requestId,JSON.toJSONString(dto));
        //调用第三方接口，校验用户身份证信息
        log.info("实名制调用channel入参==>{},请求id==>{}",JSON.toJSONString(dto),requestId);
        UserCardVo userCardVo = channelApiFeignClient.userCardSave(dto).result();
        log.info("实名制调用channel出参==>{},请求id==>{}",JSON.toJSONString(userCardVo),requestId);

        // @update: 通过微信小程序的openid做判断，每个openid只能调用身份验证接口3次
        final String limitKey = RedisKeyConstants.ID_CARD_INVOKE_LIMIT_KEY + openId + ":";
        invokeCardUtil.increment(limitKey,limitNum );

        return  userCardVo;
    }


    public static boolean isIDNumber(String IDNumber) {
        if (IDNumber == null || "".equals(IDNumber)) {
            return false;
        }
        // 定义判别用户身份证号的正则表达式（15位或者18位，最后一位可以为字母）
        String regularExpression =
                "(^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|"
                        + "(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}$)";
        boolean matches = IDNumber.matches(regularExpression);
        // 判断第18位校验值
        if (matches) {
            if (IDNumber.length() == 18) {
                try {
                    char[] charArray = IDNumber.toCharArray();
                    // 前十七位加权因子
                    int[] idCardWi = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
                    // 这是除以11后，可能产生的11位余数对应的验证码
                    String[] idCardY = {"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"};
                    int sum = 0;
                    for (int i = 0; i < idCardWi.length; i++) {
                        int current = Integer.parseInt(String.valueOf(charArray[i]));
                        int count = current * idCardWi[i];
                        sum += count;
                    }
                    char idCardLast = charArray[17];
                    int idCardMod = sum % 11;
                    if (idCardY[idCardMod].toUpperCase()
                            .equals(String.valueOf(idCardLast).toUpperCase())) {
                        return true;
                    } else {
                       log.info("身份证最后一位:" + String.valueOf(idCardLast).toUpperCase()
                                + "错误,正确的应该是:" + idCardY[idCardMod].toUpperCase());
                        return false;
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    return false;
                }
            } else {
                return false;
            }
        }
        return matches;
    }



}
