package com.yl.applets.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.service.IGdAddress;
import com.yl.applets.vo.AddressPCDVO;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-12-20
 */

@Service
@Slf4j
@SuppressWarnings({"unchecked","rawtypes"})
public class GdAddressImpl implements IGdAddress {

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Override
    public AddressPCDVO intelligentAddressRecognitionV2(String address) {
        if (StrUtil.isBlank(address)) {
            throw new BusinessException(ServiceErrCodeEnum.ADDR_STR_ERROR);
        }
        log.info("调用channel-地址解析=>入参: {}", address);
        Result<AddressPCDVO> result = channelApiFeignClient.intelligentAddressRecognitionV2(address);
        log.info("调用channel-地址解析=>出参: {}", JSON.toJSONString(result));
        return result.result();
    }


}
