package com.yl.applets.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.alibaba.fastjson.JSONObject;
import com.yl.applets.config.WxMaConfiguration;
import com.yl.applets.config.WxMaProperties;
import com.yl.applets.constant.BaseConstant;
import com.yl.applets.dto.StudentSendingDTO;
import com.yl.applets.dto.appletsgeneralbusiness.AppletsGeneralBusinessDTO;
import com.yl.applets.dto.appletsgeneralbusiness.AppletsGeneralStudentDTO;
import com.yl.applets.enums.AppletsGeneralBusinessEnum;
import com.yl.applets.enums.AppletsUserSendingEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.service.StudentSendingService;
import com.yl.applets.utils.DateUtil;
import com.yl.applets.utils.StudentSendingRedisUtils;
import com.yl.applets.vo.studentsending.WXStudentSendingResultVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/4/18 10:47
 */
@Service
@Slf4j
public class StudentSendingServiceImpl implements StudentSendingService {

    @Value("${student.sending.url:https://api.weixin.qq.com/intp/quickcheckstudentidentity}")
    private String wxUrl;
    @Autowired
    private StudentSendingRedisUtils studentSendingRedisUtils;

    @Autowired
    private WxMaProperties wxMaProperties;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Value("${applets.student.sending.day.expire:30}")
    private Integer dayExpire = 30;

    @Override
    public Integer getAuthenticationStatus(Integer numberId) {
        return studentSendingRedisUtils.getFromRedisById(numberId);
    }



    @Override
    public Result<WXStudentSendingResultVO> authentication(StudentSendingDTO dto) {

        String jsonString = JSONObject.toJSONString(dto);
        try {
            log.info("StudentSendingServiceImpl saveAuthentication dto:{}", jsonString);

            Integer authenticationStatus = getAuthenticationStatus(dto.getNumberId());
            if(!Objects.isNull(authenticationStatus)){
                WXStudentSendingResultVO result=new WXStudentSendingResultVO();
                result.setIs_student(true);
                result.setBind_status(3L);
                result.setErrcode(0);
                return Result.success(result);
            }
            String appId = wxMaProperties.getConfigs().get(0).getAppid();
            final WxMaService wxService = WxMaConfiguration.getMaService(appId);
            String responseContent = null;
            try {
                responseContent = wxService.post(wxUrl, jsonString);
            } catch (WxErrorException e) {
                responseContent = e.getError().getJson();
                log.error("StudentSendingServiceImpl saveAuthentication  wxError:{}", e);
            }
            log.info("StudentSendingServiceImpl saveAuthentication  result:{}", responseContent);
            if(StringUtils.isBlank(responseContent)){
                return Result.error(ResultCodeEnum.STUDENT_SENDING_AUTHENTICATION_ERROR);
            }
            WXStudentSendingResultVO wxStudentSendingResultVO = JSONObject.parseObject(responseContent, WXStudentSendingResultVO.class);
            log.info("StudentSendingServiceImpl saveAuthentication  WXStudentSendingResultVO:{}", JSONObject.toJSONString(wxStudentSendingResultVO));
            if (Objects.isNull(wxStudentSendingResultVO)) {
                return Result.error(ResultCodeEnum.STUDENT_SENDING_AUTHENTICATION_ERROR);
            }
            Boolean isStudent = wxStudentSendingResultVO.getIs_student();
            if (Boolean.TRUE.equals(isStudent)) {
               studentSendingRedisUtils.saveFromRedisById(dto.getNumberId());
                try {
                    AppletsGeneralBusinessDTO send=new AppletsGeneralBusinessDTO();
                    AppletsGeneralStudentDTO generalBusinessDTO=new AppletsGeneralStudentDTO();
                    generalBusinessDTO.setSource(AppletsGeneralBusinessEnum.APPLETS_GENERAL_STUDENT.getCode());//微信来源
                    generalBusinessDTO.setSendingTimeStr(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
                    generalBusinessDTO.setNumberId(dto.getNumberId());
                    generalBusinessDTO.setExpireTimeStr(DateUtil.format(DateUtil.addDays(new Date(),30),"yyyy-MM-dd HH:mm:ss"));
                    generalBusinessDTO.setType(AppletsUserSendingEnum.STUDENT.getCode());
                    send.setBeanName(AppletsGeneralBusinessEnum.APPLETS_GENERAL_STUDENT.getName());
                    send.setData(generalBusinessDTO);
                    log.info("StudentSendingServiceImpl appletsGeneralBusinessSave 入参:send{}", JSONObject.toJSONString(send));
                    channelApiFeignClient.appletsGeneralBusinessSave(send);
                } catch (Exception e) {
                    log.error("StudentSendingServiceImpl appletsGeneralBusinessSave error:{}", e);
                }

            }
            return Result.success(wxStudentSendingResultVO);
        } catch (Exception e) {
            log.error("StudentSendingServiceImpl saveAuthentication error:{}", e);
        }
        return Result.error(ResultCodeEnum.STUDENT_SENDING_AUTHENTICATION_ERROR);
    }
}
