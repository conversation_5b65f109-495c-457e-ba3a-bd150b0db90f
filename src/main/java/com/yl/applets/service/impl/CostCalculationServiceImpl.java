package com.yl.applets.service.impl;

import com.yl.applets.dto.SpmApiInsuranceTrialDTO;
import com.yl.applets.dto.SpmApiTrialDTO;
import com.yl.applets.feign.SpmoBusinessInsuranceFeeFeignClient;
import com.yl.applets.feign.SpmoStandardShippingQuoteFeignClient;
import com.yl.applets.service.ICostCalculationService;
import com.yl.applets.vo.SpmCommonCostVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description: 费用计算业务
 * @Project:
 * @CreateDate: Created in 2019-08-31 13:57 <br>
 * @Author: zhipeng.liu
 */
@Service
public class CostCalculationServiceImpl extends BaseServiceImpl implements ICostCalculationService {

    @Autowired
    private SpmoBusinessInsuranceFeeFeignClient spmoBusinessInsuranceFeeFeignClient;

    @Autowired
    private SpmoStandardShippingQuoteFeignClient spmoStandardShippingQuoteFeignClient;

    @Override
    public BigDecimal computationCost(SpmApiInsuranceTrialDTO spmApiInsuranceTrialDTO) {
        return spmoBusinessInsuranceFeeFeignClient.computationCost(spmApiInsuranceTrialDTO).result();
    }

    @Override
    public SpmCommonCostVO comCost(SpmApiTrialDTO spmApiTrialDTO) {
        return spmoStandardShippingQuoteFeignClient.comCostAndWeight(spmApiTrialDTO).result();
    }
}
