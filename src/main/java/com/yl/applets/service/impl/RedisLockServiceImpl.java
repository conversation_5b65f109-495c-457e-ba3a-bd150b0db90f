package com.yl.applets.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.yl.applets.holder.RedisTemplateHolder;
import com.yl.applets.holder.Statement;
import com.yl.applets.service.IRedisLockService;
import com.yl.applets.utils.OmsExceptionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Protocol;
import redis.clients.util.SafeEncoder;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: xiongweibin
 * @create: 2020-06-11 15:29
 */
@Service
@Slf4j
public class RedisLockServiceImpl implements IRedisLockService {
    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public boolean setAndExpireIfAbsent(String key, long expire) {
        Boolean result = (Boolean) RedisTemplateHolder.execute(new Statement() {
                @Override
                public Object prepare(RedisTemplate redisTemplate) {
                    return redisTemplate.execute(new RedisCallback<Boolean>() {
                        @Override
                        public Boolean doInRedis(RedisConnection connection) throws DataAccessException {
                            long nowTime = System.currentTimeMillis();
                            Object obj = connection.execute("set", key.getBytes(), String.valueOf(nowTime).getBytes(), SafeEncoder.encode("NX"), SafeEncoder.encode("EX"), Protocol.toByteArray(expire));
                            return ObjectUtil.isNotEmpty(obj) ? true : false;
                        }
                    });
                }
            }, redisTemplate);
        return result;
    }

    @Override
    public boolean delete(String key) {
        Boolean result = true;
        try {
            result = (Boolean) RedisTemplateHolder.execute(new Statement() {
                @Override
                public Object prepare(RedisTemplate redisTemplate) {
                    return redisTemplate.execute(new RedisCallback<Boolean>() {
                        @Override
                        public Boolean doInRedis(RedisConnection connection) throws DataAccessException {
                            Object obj = connection.execute("DEL", key.getBytes());
                            if (ObjectUtil.isEmpty(obj)) {
                                return false;
                            }
                            return Integer.valueOf(obj.toString())>0 ? true : false;
                        }
                    });
                }
            }, redisTemplate);
        } catch (Exception e) {
            log.error("RedisLockServiceImpl delete 异常：{}" + OmsExceptionUtils.getStackTrace(e));
        }
        return result;
    }

    @Override
    public boolean existsKey(String key) {
        boolean obj = false;
        try {
            obj = (Boolean) RedisTemplateHolder.execute(new Statement() {
                @Override
                public Object prepare(RedisTemplate redisTemplate) {
                    return redisTemplate.execute(new RedisCallback() {
                        @Override
                        public Object doInRedis(RedisConnection connection) throws DataAccessException {
                            Object obj = connection.execute("EXISTS", key.getBytes());
                            log.info("RedisLockServiceImpl_existsKey:{}",obj);
                            return obj;
                        }
                    });
                }
            }, redisTemplate);
        } catch (Exception e) {
            log.error("RedisLockServiceImpl existsKey 异常：{}" + OmsExceptionUtils.getStackTrace(e));
        }
        return obj;
    }
}
