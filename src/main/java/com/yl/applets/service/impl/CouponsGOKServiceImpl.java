package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.yl.applets.config.WxCouponConfig;
import com.yl.applets.dto.CouponOneKeyDto;
import com.yl.applets.dto.PromotionUserGetAppletsDto;
import com.yl.applets.enums.BaseDictionaryEnums;
import com.yl.applets.feign.PromotionFeighClient;
import com.yl.applets.service.CouponsGOKService;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-04-24 11:43
 * @Version 1.0
 */
@Slf4j
@Service
public class CouponsGOKServiceImpl implements CouponsGOKService {


    @Value("${gok.secret.couponId:123456}")
    private String couponId;

    @Autowired
    private PromotionFeighClient promotionFeighClient;

    @Autowired
    private WxCouponConfig wxCouponConfig;


    public Boolean bindGokCoupons(WxUserVo user) {
        log.info("活动优惠券配置:{}", JSON.toJSONString(wxCouponConfig.getConfigs()));
        return this.handleCoupon(user, wxCouponConfig.getConfigs().get(0).getCouponId().get(0), UUID.randomUUID().toString().replaceAll("-", ""), wxCouponConfig.getConfigs().get(0).getType());
    }


    /**
     * 给用户发送优惠券
     *
     * @param user
     * @param couponId
     */
    private Boolean handleCoupon(WxUserVo user, String couponId, String requestId, Integer type) {
        log.info("王者用户领取优惠券，用户信息：{},", JSON.toJSONString(user));
        CouponOneKeyDto dto = new CouponOneKeyDto();
        //活动类型
        dto.setType(type);
        dto.setUserAlias(user.getNickName());
        dto.setUserId(user.getId().longValue());
        dto.setUserUuid(user.getOpenid());
        dto.setNumberId(user.getNumberId().longValue());
        dto.setUserPhone(user.getMobile());
        //优惠券数据
        CouponOneKeyDto.OneKeyList oneKeyList = new CouponOneKeyDto.OneKeyList();
        oneKeyList.setProId(Long.parseLong(couponId));
        //设置优惠券
        dto.setOneKeyListList(Collections.singletonList(oneKeyList));

        log.info("请求id==>{},王者用户领取优惠券,入参==>{}", requestId, JSON.toJSONString(dto));
        Result<Boolean> result = promotionFeighClient.getCouponByOutActive(dto);
        log.info("请求id==>{},王者用户领取优惠券,出参==>{}", requestId, JSON.toJSONString(result));
        if (!result.getCode().equals(ResultCodeEnum.SUCCESS.getCode())) {
            throw new BusinessException(result.getCode(), result.getMsg());
        }
        return result.getData();
    }

}
