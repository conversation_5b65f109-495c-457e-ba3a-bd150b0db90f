package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yl.applets.builder.EsSearchRequestBuilder;
import com.yl.applets.constant.AppCacheConstant;
import com.yl.applets.dto.EsSearchDTO;
import com.yl.applets.dto.ForwardRequest;
import com.yl.applets.dto.NearbyOutletsDTO;
import com.yl.applets.entity.lmdm.SysNetwork;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.EsGeoFeignClient;
import com.yl.applets.feign.OldLmdmFeignClient;
import com.yl.applets.feign.OldNetworkFeighClient;
import com.yl.applets.service.IAppNetworkService;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.EsSearchResult;
import com.yl.applets.vo.lmdm.AppNetworkVO;
import com.yl.applets.vo.lmdm.SysNetworkSafeVO;
import com.yl.applets.vo.lmdm.SysNetworkVO;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019-08-23
 */
@Service
@Slf4j
public class AppNetworkServiceImpl implements IAppNetworkService {

    @Autowired
    private OldLmdmFeignClient sysNetworkFeignClient;

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Autowired
    private EsGeoFeignClient esGeoFeignClient;

    @Autowired
    private OldNetworkFeighClient oldNetworkFeighClient;

    @Value("${nearby.network.es:true}")
    private Boolean esFlag;

    @Value("${nearby.network.es.quality:500}")
    private Integer esQuality;

    private static final String START_BUSINESS_TIME = "start_business_time";

    private static final String END_BUSINESS_TIME = "end_business_time";

    @Override
    public List<AppNetworkVO> getAppList() {
        //查询缓存，如果没有，则查库
        Object redisObj = com.yl.redis.util.RedisUtil.get(AppCacheConstant.SYSTEM_NAME + AppCacheConstant.SYS_NETWORK_LIST);
        if (Objects.nonNull(redisObj)) {
            List<SysNetwork> list = (List<SysNetwork>) redisObj;
            return orikaBeanMapper.mapAsList(list, AppNetworkVO.class);
        }
//        List<SysNetwork> list = sysNetworkFeignClient.networkRedisAll().result();
        String requestIds = UUID.randomUUID().toString().replaceAll("-", "");
        ForwardRequest requests=new ForwardRequest();
        requests.setRequestId(requestIds);
        requests.setRequestUri("/lmdmapi/network/redisAll");
        requests.setBody("");
        //调接口
        log.info("请求id==>{}接口入参==>{}",requestIds,JSON.toJSONString(requests));
        Result<?> forwards = channelApiFeignClient.forward(requests);
        log.info("请求id==>{}接口返回==>{}",requestIds,JSON.toJSONString(forwards));
        Object results = forwards.result();
        //2.转换出参LIST
        List<SysNetwork> list = JSON.parseArray(JSON.toJSONString(results), SysNetwork.class);
        //放进缓存
        com.yl.redis.util.RedisUtil.set(AppCacheConstant.SYSTEM_NAME + AppCacheConstant.SYS_NETWORK_LIST, list);
        return orikaBeanMapper.mapAsList(list, AppNetworkVO.class);
    }


    @Override
    public SysNetworkSafeVO getDetail(Integer networkId) {
//        sysNetworkFeignClient.getDetailById(networkId).result();
        String requestIds = UUID.randomUUID().toString().replaceAll("-", "");
        ForwardRequest requests=new ForwardRequest();
        requests.setRequestId(requestIds);
        requests.setRequestUri("/lmdmapi/network/detail");
        requests.setBody(networkId+"");
        //调接口
        log.info("请求id==>{}接口入参==>{}",requestIds,JSON.toJSONString(requests));
        Result<?> forwards = channelApiFeignClient.forward(requests);
        log.info("请求id==>{}接口返回==>{}",requestIds,JSON.toJSONString(forwards));
        Object results = forwards.result();
        SysNetworkSafeVO networkVO = JSON.parseObject(JSON.toJSONString(results), SysNetworkSafeVO.class);
        return networkVO;
    }

    @Override
    public EsSearchResult getNearbyOutlets(NearbyOutletsDTO nearbyOutletsDTO) {
        EsSearchRequestBuilder esSearchRequestBuilder = EsSearchRequestBuilder.requestBuilder("sys_network");
        //城市
        if(StringUtils.isNotEmpty(nearbyOutletsDTO.getCity())){
            esSearchRequestBuilder.equal("city_desc",nearbyOutletsDTO.getCity());
        }
        //城市id
        if(StringUtils.isNotEmpty(nearbyOutletsDTO.getCityId())){
            esSearchRequestBuilder.equal("city_id",nearbyOutletsDTO.getCityId());
        }
        //区域id
        if(StringUtils.isNotEmpty(nearbyOutletsDTO.getAreaId())){
            esSearchRequestBuilder.equal("area_id",nearbyOutletsDTO.getAreaId());
        }
        //查询关键字
        if(StringUtils.isNotEmpty(nearbyOutletsDTO.getKeyWord())){
            esSearchRequestBuilder.wildcard("name","*"+nearbyOutletsDTO.getKeyWord()+"*");
        }

        EsSearchDTO esSearchDTO = esSearchRequestBuilder.equal("is_visibility", 1)
                .in("type_id", 336, 337)
                /* 距离排序 仅供有经纬度的表。如果使用距离排序，则不能再添加其他排序 */
                .distanceSort("coordinate", nearbyOutletsDTO.getLatitude().concat(",").concat(nearbyOutletsDTO.getLongitude()), null, null)
                /* 指定返回哪些字段 */
                .includeFields("id", "name", "address", "longitude", "latitude", START_BUSINESS_TIME, END_BUSINESS_TIME,"customer_service_telephone")
                .limit(1,esQuality)
                .build();
                log.info("esSearchDTO:{} esFlag:{}", JsonUtils.toJson(esSearchDTO), esFlag);
        EsSearchResult esSearchResult = esFlag ? oldNetworkFeighClient.distanceSort(esSearchDTO).getData() : esGeoFeignClient.distanceSort(esSearchDTO).getData();
        //代码优化 2020年8月20日16:42:09  去除一层if  需要测试
        if(esSearchResult!=null){
            List<JSONObject> jsonObjects = esSearchResult.getData();
            jsonObjects = jsonObjects.stream().filter(jsonObject -> {
                String name = jsonObject.getString("name");
                return !(StringUtils.isNotEmpty(name) && name.contains("测试"));
            }).collect(Collectors.toList());
            jsonObjects.forEach(jsonObject -> {
                String startBusinessTime = jsonObject.getString(START_BUSINESS_TIME);
                if(StringUtils.isNotEmpty(startBusinessTime)){
                    jsonObject.put(START_BUSINESS_TIME,startBusinessTime.substring(startBusinessTime.indexOf('T')+1,startBusinessTime.lastIndexOf(':')));
                }
                String endBusinessTime = jsonObject.getString(END_BUSINESS_TIME);
                if(StringUtils.isNotEmpty(endBusinessTime)){
                    jsonObject.put(START_BUSINESS_TIME,startBusinessTime.substring(endBusinessTime.indexOf('T')+1,endBusinessTime.lastIndexOf(':')));
                }
            });
            esSearchResult.setData(jsonObjects);
        }
        return esSearchResult;
    }
}
