package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.yl.applets.config.WxCouponConfig;
import com.yl.applets.dto.CouponOneKeyDto;
import com.yl.applets.feign.PromotionFeighClient;
import com.yl.applets.service.coupons.AppletsCouponsCommonService;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-10-30 15:58
 * @Version 1.0
 */
@Slf4j
@Service
public class AppletsCouponsCommonServiceImpl extends BaseServiceImpl implements AppletsCouponsCommonService {

    @Autowired
    private PromotionFeighClient promotionFeighClient;

    @Autowired
    private WxCouponConfig wxCouponConfig;

    @Resource
    protected RedissonClient redissonClient;


    @Override
    public boolean getCoupons(Integer type) {

        WxCouponConfig.Config config = wxCouponConfig.getConfigs().stream().filter(item -> Objects.equals(item.getType(), type)).findFirst()
                .orElse(null);
        log.info("过滤活动配置:{}", JSON.toJSONString(config));

        if (config == null) throw new BusinessException(ResultCodeEnum.COUPON_CONFIG_NOT_EXIST);

        WxUserVo user = getUser();
        log.info("活动通用领取优惠券，用户信息：{},", JSON.toJSONString(user));

        RLock rLock = null;
        Result<Boolean> result;

        try {
            String key = user.getMobile() + ":" + config.getType();
            //同一用户可以在不同活动领取
            rLock = redissonClient.getLock(BcRedisKeyEnum.APPLETS_COUPONS_COMMON_GET.keyBuilder(key));
            rLock.lock(1, TimeUnit.MINUTES);

            CouponOneKeyDto dto = new CouponOneKeyDto();
            //活动类型
            dto.setType(type);
            dto.setUserAlias(user.getNickName());
            dto.setUserId(user.getId().longValue());
            dto.setUserUuid(user.getOpenid());
            dto.setNumberId(user.getNumberId().longValue());
            dto.setUserPhone(user.getMobile());

            //优惠券数据
            List<CouponOneKeyDto.OneKeyList> oneKeyList = new ArrayList<>();
            for (String s : config.getCouponId()) {
                CouponOneKeyDto.OneKeyList oneKey = new CouponOneKeyDto.OneKeyList();
                oneKey.setProId(Long.parseLong(s));
                //优惠券数据
                oneKeyList.add(oneKey);
            }

            //设置优惠券
            dto.setOneKeyListList(oneKeyList);
            log.info("活动通用领取优惠券,入参==>{}", JSON.toJSONString(dto));
            result = promotionFeighClient.getCouponByOutActive(dto);
            log.info("活动通用领取优惠券,出参==>{}", JSON.toJSONString(result));
            if (!result.getCode().equals(ResultCodeEnum.SUCCESS.getCode())) {
                throw new BusinessException(result.getCode(), result.getMsg());
            }
        } finally {

            if (rLock != null && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
        return result.getData();
    }
}
