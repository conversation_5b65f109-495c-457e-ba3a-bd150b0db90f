package com.yl.applets.service.impl;

import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.service.SmsCheckService;
import com.yl.common.base.exception.ServiceException;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.concurrent.TimeUnit;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-10-11 17:04
 */
@Slf4j
@Service
public class SmsCheckServiceImpl implements SmsCheckService {
    /**
     * 同一个手机号码，同一个功能，一条最多发送多少调短信
     */
    @Value("${sms.phone.verify.max:10}")
    private Integer smsPhoneVerifyMax;

    @Override
    public void smsSendCheck(String path,String ip,String type,String phone,Integer maxCount,Integer seconds){
        //统计该IP请求次数间隔时间
        String timeKey ="APPLETS:VERIFYCODE:REQUEST:TIME:"+path+ ":"+type+":" + ip  ;
        //统计今天该IP请求了多少次
        String countKey ="APPLETS:VERIFYCODE:REQUEST:COUNT:"+path + ":" +type+":" + ip ;
        //统计今天该手机发送了多少次短信
        String countPhoneKey ="APPLETS:VERIFYCODE:REQUEST:COUNT:"+path + ":" +type+":" + phone;
        Object timeObj= RedisUtil.get(timeKey);
        Object countTObj=RedisUtil.get(countKey);
        Object countPhoneObj=RedisUtil.get(countPhoneKey);
        Integer count =0;
        Integer countPhone =0;

        if(timeObj != null){
            throw new ServiceException(ServiceErrCodeEnum.OP_REPETITION_ERR);
        }
        if(countTObj != null){
            count = Integer.valueOf(countTObj.toString());
        }
        if(countPhoneObj != null){
            countPhone = Integer.valueOf(countPhoneObj.toString());
        }
        if (count < maxCount) {
            count = count+1;
            RedisUtil.setEx(timeKey, "1",seconds);
            RedisUtil.setEx(countKey, count+"",getSecondNumber());
            if (countPhone < smsPhoneVerifyMax && !"".equals(phone)) {
                countPhone = countPhone+1;
                RedisUtil.setEx(countPhoneKey, countPhone+"",getSecondNumber());
            }else{
                if( !"".equals(phone)) {
                    log.info("同一手机号频繁请求 手机号：{}", phone);
                    throw new ServiceException(ServiceErrCodeEnum.OP_REPETITION_TIME_ERR);
                }
            }
        }else{
            log.info("同一IP地址频繁请求 ip:{}",ip);
            throw new ServiceException(ServiceErrCodeEnum.OP_REPETITION_TIME_ERR);
        }
    }

    //获取当前时间，到凌晨 相差的秒数
    private int getSecondNumber() {
        LocalTime midnight = LocalTime.MIDNIGHT;
        LocalDate today = LocalDate.now();
        LocalDateTime todayMidnight = LocalDateTime.of(today, midnight);
        LocalDateTime tomorrowMidnight = todayMidnight.plusDays(1);
        long seconds = TimeUnit.NANOSECONDS.toSeconds(Duration.between(LocalDateTime.now(), tomorrowMidnight).toNanos());
        return (int) seconds;
    }
}
