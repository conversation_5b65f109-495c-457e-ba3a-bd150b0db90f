package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.yl.applets.dto.*;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.service.IPandaPostOrderService;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.*;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-11-08
 */

@Service
@Slf4j
public class PandaPostOrderServiceImpl implements IPandaPostOrderService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${panda.url:http://api.java.105.100baimi.cn:10099/jtSend/openPlatform}")
    private String pandaUrl;

    @Value("${panda.accountName:JTTEST}")
    private String accountName;

    @Value("${panda.accountPassword:123456}")
    private String accountPassword;


    @Override
    public Result<Page<PandaOrderDetailVO>> queryPage(ThirdExpressApiDTO dto, String requestId) {
        PandaResult<PandaQueryVO> pandaResult = null;
        try {
            ResponseEntity<String> exchange = null;
            JSONObject param = new JSONObject();
            param.put("pageIndex", dto.getCurrent());
            param.put("pageSize", dto.getSize());
            param.put("jtUserId", String.valueOf(dto.getCustomerId()));
            String bizData = param.toJSONString();
            //接口名称
            String apiName = "JT_SEND_PAGE_LIST";
            //请求熊猫驿站接口
            log.info("请求熊猫驿站,请求编号:{},获取极兔小程序用户下单在熊猫快收的订单列表 请求入参:{}", requestId, JSON.toJSONString(dto));
            exchange = requestPanda(bizData, apiName, exchange, requestId);
            pandaResult = JSON.parseObject(exchange.getBody(), new TypeReference<PandaResult<PandaQueryVO>>() {
            });
            List<QueryPandaOrderVO> QueryPandaOrderVOs = pandaResult.getD().getRecords();
            log.info("请求熊猫驿站,请求编号:{},获取极兔小程序用户下单在熊猫快收的订单列表 结果:{}", requestId, JSON.toJSONString(pandaResult));
            Page<PandaOrderDetailVO> voPage = new Page<>(dto.getCurrent(), dto.getSize());
            voPage.setTotal(pandaResult.getD().getTotal());
            List<PandaOrderDetailVO> pandaOrderDetailVOs = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(QueryPandaOrderVOs)) {
                QueryPandaOrderVOs.forEach(d -> {
                    if (d != null) {
                        log.info("请求编号:{},组装返回参数入参：{}", requestId, JSON.toJSONString(d));
                        PandaOrderDetailVO pandaOrderDetailVO = pandaResult(d);
                        log.info("请求编号:{},组装返回参数结果：{}", requestId, JSON.toJSONString(pandaOrderDetailVO));
                        if (pandaOrderDetailVO != null) {
                            pandaOrderDetailVOs.add(pandaOrderDetailVO);
                        }
                    }
                });
            }
            voPage.setRecords(pandaOrderDetailVOs);
            if (!pandaResult.isSuccess()) {
                log.error("请求熊猫驿站，请求编号:{},获取极兔小程序用户下单在熊猫快收的订单列表异常 请求:{} 错误:{}", requestId, JSON.toJSONString(dto), JSON.toJSONString(pandaResult.getM()));
                throw new BusinessException(pandaResult.getM());
            }
            return Result.success(voPage);
        } catch (BusinessException e) {
            log.error("请求熊猫驿站，请求编号:{},获取极兔小程序用户下单在熊猫快收的订单列表失败 请求:{} 错误:{}", requestId, JSON.toJSONString(dto), JSON.toJSONString(pandaResult.getM()));
            throw e;
        } catch (Exception e) {
            log.error("请求熊猫驿站，请求编号:{},订单列表异常 请求:{} 错误:{}", requestId, JSON.toJSONString(dto), ExceptionUtils.getStackTrace(e));
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
    }

    @Override
    public Result<Boolean> cancelOrder(String orderId, Integer numberId, String requestId) {
        PandaResult<List<String>> pandaResult = null;
        try {
            ResponseEntity<String> exchange = null;
            JSONObject param = new JSONObject();
            param.put("sendId", orderId);
            param.put("jtUserId", numberId);
            String bizData = param.toJSONString();
            //接口名称
            String apiName = "JT_SEND_CANCEL";
            //请求熊猫驿站接口
            log.info("请求熊猫驿站,请求编号:{},取消订单接口 请求入参:{}", requestId, bizData);
            exchange = requestPanda(bizData, apiName, exchange, requestId);
            pandaResult = JSON.parseObject(exchange.getBody(), new TypeReference<PandaResult<List<String>>>() {
            });
            log.info("请求熊猫驿站,请求编号:{},取消订单接口 结果:{}", requestId, JSON.toJSONString(pandaResult));
            if (!pandaResult.isSuccess()) {
                log.error("请求熊猫驿站，请求编号:{},取消订单接口异常 请求:{} 错误:{}", requestId, JSON.toJSONString(param), JSON.toJSONString(pandaResult.getM()));
                throw new BusinessException(pandaResult.getM());
            }
            return Result.success(true);
        } catch (BusinessException e) {
            log.error("请求熊猫驿站，请求编号:{},取消订单接口异常 请求失败orderId:{} 错误:{}", requestId, orderId, JSON.toJSONString(pandaResult.getM()));
            throw e;
        } catch (Exception e) {
            log.error("请求熊猫驿站，请求编号:{},取消订单接口异常 请求orderId:{} 错误:{}", requestId, orderId, ExceptionUtils.getStackTrace(e));
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
    }

    @Override
    public Result<Boolean> deleteCancelOrder(String orderId, Integer numberId, String requestId) {
        PandaResult<List<String>> pandaResult = null;
        try {
            ResponseEntity<String> exchange = null;
            JSONObject param = new JSONObject();
            param.put("sendId", orderId);
            param.put("jtUserId", numberId);
            String bizData = param.toJSONString();
            //接口名称
            String apiName = "JT_SEND_REMOVE";
            //请求熊猫驿站接口
            log.info("请求熊猫驿站,请求编号:{},删除已取消订单接口 请求入参:{}", requestId, bizData);
            exchange = requestPanda(bizData, apiName, exchange, requestId);
            pandaResult = JSON.parseObject(exchange.getBody(), new TypeReference<PandaResult<List<String>>>() {
            });
            log.info("请求熊猫驿站,请求编号:{},删除已取消订单接口 结果:{}", requestId, JSON.toJSONString(pandaResult));
            if (!pandaResult.isSuccess()) {
                log.error("请求熊猫驿站，请求编号:{},删除已取消订单接口异常 请求:{} 错误:{}", requestId, JSON.toJSONString(param), JSON.toJSONString(pandaResult.getM()));
                throw new BusinessException(pandaResult.getM());
            }
            return Result.success(true);
        } catch (BusinessException e) {
            log.error("请求熊猫驿站，请求编号:{},删除已取消订单接口异常 请求失败orderId:{} 错误:{}", requestId, orderId, JSON.toJSONString(pandaResult.getM()));
            throw e;
        } catch (Exception e) {
            log.error("请求熊猫驿站，请求编号:{},删除已取消订单接口异常 请求orderId:{} 错误:{}", requestId, orderId, ExceptionUtils.getStackTrace(e));
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
    }


    @Override
    public Result<PandaOrderDetailVO> addOrder(PandaOrderDTO dto, String requestId) {
        PandaResult<QueryPandaOrderVO> pandaResult = null;
        //收寄件地址不能相同
        if (dto.getSenderProvinceName().replaceAll(" ", "").equals(dto.getReceiverProvinceName().replaceAll(" ", ""))
                && dto.getSenderCityName().replaceAll(" ", "").equals(dto.getReceiverCityName().replaceAll(" ", ""))
                && dto.getSenderAreaName().replaceAll(" ", "").equals(dto.getReceiverAreaName().replaceAll(" ", ""))
                && dto.getSenderDetailedAddress().replaceAll(" ", "").equals(dto.getReceiverDetailedAddress().replaceAll(" ", ""))) {
            throw new BusinessException(ServiceErrCodeEnum.DETAILEDADDRESS_EQUALS);
        }
        //总运费 = 标准运费+保价费
        // dto.setTotalFreight(dto.getStandardValue().add(dto.getInsuredValue()));
        dto.setTotalFreight(dto.getStandardValue());
        //如果是签回单 赋值回单金额
//            if (dto.getSignReceipt() != null && dto.getSignReceipt() == 1) {
//                dto.setReceiptFreight(AppCacheConstant.RECEIPT_FREIGHT);
//                dto.setTotalFreight(dto.getTotalFreight().add(AppCacheConstant.RECEIPT_FREIGHT));
//            }
        try {
            ResponseEntity<String> exchange = null;
            JSONObject param = new JSONObject();
            param.put("stationId", dto.getStationId());
            param.put("jtUserId", dto.getJtUserId());
            param.put("sendName", dto.getSenderName());
            param.put("idCard", dto.getCardNo());
            param.put("realName", dto.getRealName());
            param.put("sendPhone", dto.getSenderMobilePhone());
            param.put("sendProvinceName", dto.getSenderProvinceName());
            param.put("sendCityName", dto.getSenderCityName());
            param.put("sendAreaName", dto.getSenderAreaName());
            param.put("sendDetailPlace", dto.getSenderDetailedAddress());
            param.put("receiveName", dto.getReceiverName());
            param.put("receivePhone", dto.getReceiverMobilePhone());
            param.put("receiveProvinceName", dto.getReceiverProvinceName());
            param.put("receiveCityName", dto.getReceiverCityName());
            param.put("receiveAreaName", dto.getReceiverAreaName());
            param.put("receiveDetailPlace", dto.getReceiverDetailedAddress());
            param.put("weight", dto.getPackageTotalWeight());
            param.put("money", dto.getTotalFreight());
            param.put("note", dto.getRemarks());
            if (StringUtils.isNotEmpty(dto.getGoodsTypeName())) {
                //物品类型
                Integer orderType = goodsType(dto.getGoodsTypeName());
                param.put("orderType", orderType);
            }
            String bizData = param.toJSONString();
            //接口名称
            String apiName = "JT_SEND_ADD";
            //请求熊猫驿站接口
            log.info("请求熊猫驿站,请求编号:{},寄件下单接口 请求入参:{}", requestId, JSON.toJSONString(dto));
            exchange = requestPanda(bizData, apiName, exchange, requestId);
            pandaResult = JSON.parseObject(exchange.getBody(), new TypeReference<PandaResult<QueryPandaOrderVO>>() {
            });
            log.info("请求熊猫驿站,请求编号:{},寄件下单接口 结果:{}", requestId, JSON.toJSONString(pandaResult));
            QueryPandaOrderVO pandaOrderDetailResultVO = pandaResult.getD();
            //熊猫vo转成极兔vo
            PandaOrderDetailVO pandaOrderDetailVO = pandaResult(pandaOrderDetailResultVO);
            log.info("请求熊猫驿站,请求编号:{},寄件下单接口 结果:{}", requestId, JSON.toJSONString(pandaResult));
            if (!pandaResult.isSuccess()) {
                log.error("请求熊猫驿站，请求编号:{},寄件下单接口异常 请求:{} 错误:{}", requestId, JSON.toJSONString(dto), JSON.toJSONString(pandaResult.getM()));
                throw new BusinessException(pandaResult.getM());
            }
            return Result.success(pandaOrderDetailVO);
        } catch (BusinessException e) {
            log.error("请求熊猫驿站，请求编号:{},熊猫驿站寄件下单接口异常 请求:{} 错误:{}", requestId, JSON.toJSONString(dto), JSON.toJSONString(pandaResult.getM()));
            throw e;
        } catch (Exception e) {
            log.error("请求熊猫驿站，请求编号:{},寄件下单接口异常 请求:{} 错误:{}", requestId, JSON.toJSONString(dto), ExceptionUtils.getStackTrace(e));
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
    }

    @Override
    public Result<Boolean> updateOrder(PandaOrderDTO dto, String requestId) {
        PandaResult<String> pandaResult = null;
        //收寄件地址不能相同
        if (dto.getSenderProvinceName().replaceAll(" ", "").equals(dto.getReceiverProvinceName().replaceAll(" ", ""))
                && dto.getSenderCityName().replaceAll(" ", "").equals(dto.getReceiverCityName().replaceAll(" ", ""))
                && dto.getSenderAreaName().replaceAll(" ", "").equals(dto.getReceiverAreaName().replaceAll(" ", ""))
                && dto.getSenderDetailedAddress().replaceAll(" ", "").equals(dto.getReceiverDetailedAddress().replaceAll(" ", ""))) {
            throw new BusinessException(ServiceErrCodeEnum.DETAILEDADDRESS_EQUALS);
        }
        //总运费 = 标准运费+保价费
        // dto.setTotalFreight(dto.getStandardValue().add(dto.getInsuredValue()));
        dto.setTotalFreight(dto.getStandardValue());
        //如果是签回单 赋值回单金额
//            if (dto.getSignReceipt() != null && dto.getSignReceipt() == 1) {
//                dto.setReceiptFreight(AppCacheConstant.RECEIPT_FREIGHT);
//                dto.setTotalFreight(dto.getTotalFreight().add(AppCacheConstant.RECEIPT_FREIGHT));
//            }
        try {
            ResponseEntity<String> exchange = null;
            JSONObject param = new JSONObject();
            param.put("sendId", dto.getSendId());
            param.put("jtUserId", dto.getJtUserId());
            param.put("sendName", dto.getSenderName());
            param.put("sendPhone", dto.getSenderMobilePhone());
            param.put("sendProvinceName", dto.getSenderProvinceName());
            param.put("sendCityName", dto.getSenderCityName());
            param.put("sendAreaName", dto.getSenderAreaName());
            param.put("sendDetailPlace", dto.getSenderDetailedAddress());
            param.put("receiveName", dto.getReceiverName());
            param.put("receivePhone", dto.getReceiverMobilePhone());
            param.put("receiveProvinceName", dto.getReceiverProvinceName());
            param.put("receiveCityName", dto.getReceiverCityName());
            param.put("receiveAreaName", dto.getReceiverAreaName());
            param.put("receiveDetailPlace", dto.getReceiverDetailedAddress());
            param.put("weight", dto.getPackageTotalWeight());
            param.put("money", dto.getTotalFreight());
            param.put("note", dto.getRemarks());
            if (StringUtils.isNotEmpty(dto.getGoodsTypeName())) {
                //物品类型
                Integer orderType = goodsType(dto.getGoodsTypeName());
                param.put("orderType", orderType);
            }
            String bizData = param.toJSONString();
            //接口名称
            String apiName = "JT_SEND_MODIFY";
            //请求熊猫驿站接口
            log.info("请求熊猫驿站,请求编号:{},寄件修改订单接口 请求入参:{}", requestId, JSON.toJSONString(dto));
            exchange = requestPanda(bizData, apiName, exchange, requestId);
            pandaResult = JSON.parseObject(exchange.getBody(), new TypeReference<PandaResult<String>>() {
            });
            log.info("请求熊猫驿站,请求编号:{},寄件修改订单接口 结果:{}", requestId, JSON.toJSONString(pandaResult));
            if (!pandaResult.isSuccess()) {
                log.error("请求熊猫驿站，请求编号:{},寄件修改订单接口异常 请求:{} 错误:{}", requestId, JSON.toJSONString(dto), pandaResult.getM());
                throw new BusinessException(pandaResult.getM());
            }
            return Result.success(true);
        } catch (BusinessException e) {
            log.error("请求熊猫驿站，请求编号:{},寄件修改订单接口异常 请求失败:{} 错误:{}", requestId, JSON.toJSONString(dto), JSON.toJSONString(pandaResult.getM()));
            throw e;
        } catch (Exception e) {
            log.error("请求熊猫驿站，请求编号:{},寄件修改订单接口异常 请求:{} 错误:{}", requestId, JSON.toJSONString(dto), ExceptionUtils.getStackTrace(e));
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
    }

    @Override
    public Result<PandaOrderDetailVO> detail(PandadetailDTO dto, String requestId) {
        PandaResult<QueryPandaOrderVO> pandaResult = null;
        try {
            ResponseEntity<String> exchange = null;
            JSONObject param = new JSONObject();
            param.put("jtUserId", dto.getJtUserId());
            param.put("sendId", dto.getSendId());
            param.put("orderNum", dto.getOrderNum());
            param.put("expressNum", dto.getExpressNum());
            String bizData = param.toJSONString();
            //接口名称
            String apiName = "JT_SEND_DETAIL";
            //请求熊猫驿站接口
            log.info("请求熊猫驿站,请求编号:{},订单详情接口 请求入参:{}", requestId, JSON.toJSONString(dto));
            exchange = requestPanda(bizData, apiName, exchange, requestId);
            pandaResult = JSON.parseObject(exchange.getBody(), new TypeReference<PandaResult<QueryPandaOrderVO>>() {
            });
            QueryPandaOrderVO pandaOrderDetailResultVO = pandaResult.getD();
            //熊猫vo转成极兔vo
            PandaOrderDetailVO pandaOrderDetailVO = pandaResult(pandaOrderDetailResultVO);
            log.info("请求熊猫驿站,请求编号:{},订单详情接口 结果:{}", requestId, JSON.toJSONString(pandaResult));
            if (!pandaResult.isSuccess()) {
                log.error("请求熊猫驿站，请求编号:{},订单详情接口异常 请求:{} 错误:{}", requestId, JSON.toJSONString(dto), JSON.toJSONString(pandaResult.getM()));
                throw new BusinessException(pandaResult.getM());
            }
            return Result.success(pandaOrderDetailVO);
        } catch (BusinessException e) {
            log.error("请求熊猫驿站，请求编号:{},订单详情接口异常 请求失败:{} 错误:{}", requestId, JSON.toJSONString(dto), JSON.toJSONString(pandaResult.getM()));
            throw e;
        } catch (Exception e) {
            log.error("请求熊猫驿站，请求编号:{},订单详情接口异常 请求:{} 错误:{}", requestId, JSON.toJSONString(dto), ExceptionUtils.getStackTrace(e));
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
    }

    @Override
    public Result<Page<Object>> queryNearStation(PandaNearStationDto dto) {
        return null;
    }

    @Override
    public Result<Object> queryFreight(SpmApiTrialDTO spmApiTrialDTO) {
        return null;
    }

    private HttpHeaders getHttpHeaders() {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        return httpHeaders;
    }

    @Override
    public Result<List<PandaNearbyVO>> nearby(PandaNearbyDTO pandaNearbyDTO, String requestId) {
        PandaResult<List<PandaNearbyVO>> pandaResult = null;
        try {
            ResponseEntity<String> exchange = null;
            JSONObject param = new JSONObject();
            param.put("lng", pandaNearbyDTO.getLng());
            param.put("lat", pandaNearbyDTO.getLat());
            String bizData = param.toJSONString();
            //接口名称
            String apiName = "JT_SEND_NEARBY";
            //请求熊猫驿站接口
            log.info("请求熊猫驿站,请求编号:{},获取附近5公里熊猫门店 请求入参:{}", requestId, JSON.toJSONString(pandaNearbyDTO));
            exchange = requestPanda(bizData, apiName, exchange, requestId);
            pandaResult = JSON.parseObject(exchange.getBody(), new TypeReference<PandaResult<List<PandaNearbyVO>>>() {
            });
            log.info("请求熊猫驿站,请求编号:{},获取附近5公里熊猫门店 结果:{}", requestId, JSON.toJSONString(pandaResult));
            if (!pandaResult.isSuccess()) {
                log.error("请求熊猫驿站，请求编号:{},获取附近5公里熊猫门店异常 请求:{} 错误:{}", requestId, JSON.toJSONString(pandaNearbyDTO), JSON.toJSONString(pandaResult.getM()));
                throw new BusinessException(pandaResult.getM());
            }
            return Result.success(pandaResult.getD());
        } catch (BusinessException e) {
            log.error("请求熊猫驿站，请求编号:{},订单详情接口异常 请求失败入参:{} 错误:{}", requestId, JSON.toJSONString(pandaNearbyDTO), JSON.toJSONString(pandaResult.getM()));
            throw e;
        } catch (Exception e) {
            log.error("请求熊猫驿站，请求编号:{},获取附近5公里熊猫门店异常 请求:{} 错误:{}", requestId, JSON.toJSONString(pandaNearbyDTO), ExceptionUtils.getStackTrace(e));
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
    }

    public PandaOrderDetailVO pandaResult(QueryPandaOrderVO d) {
        PandaOrderDetailVO pandaOrderDetailVO = new PandaOrderDetailVO();
        if (d != null) {
            pandaOrderDetailVO.setId(Objects.isNull(d.getSendId()) ? null : d.getSendId());
            pandaOrderDetailVO.setOrderNum(Objects.isNull(d.getOrderNum()) ? null : d.getOrderNum());
            pandaOrderDetailVO.setWaybillNo(Objects.isNull(d.getExpressNum()) ? null : d.getExpressNum());
            pandaOrderDetailVO.setSenderName(Objects.isNull(d.getSendName()) ? null : d.getSendName());
            pandaOrderDetailVO.setSenderMobilePhone(Objects.isNull(d.getSendPhone()) ? null : d.getSendPhone());
            pandaOrderDetailVO.setSenderProvinceName(Objects.isNull(d.getSendProvinceName()) ? null : d.getSendProvinceName());
            pandaOrderDetailVO.setSenderCityName(Objects.isNull(d.getSendCityName()) ? null : d.getSendCityName());
            pandaOrderDetailVO.setSenderAreaName(Objects.isNull(d.getSendAreaName()) ? null : d.getSendAreaName());
            pandaOrderDetailVO.setSenderDetailedAddress(Objects.isNull(d.getSendDetailPlace()) ? null : d.getSendDetailPlace());
            pandaOrderDetailVO.setReceiverName(Objects.isNull(d.getReceiveName()) ? null : d.getReceiveName());
            pandaOrderDetailVO.setReceiverMobilePhone(Objects.isNull(d.getReceivePhone()) ? null : d.getReceivePhone());
            pandaOrderDetailVO.setReceiverProvinceName(Objects.isNull(d.getReceiveProvinceName()) ? null : d.getReceiveProvinceName());
            pandaOrderDetailVO.setReceiverCityName(Objects.isNull(d.getReceiveCityName()) ? null : d.getReceiveCityName());
            pandaOrderDetailVO.setReceiverAreaName(Objects.isNull(d.getReceiveAreaName()) ? null : d.getReceiveAreaName());
            pandaOrderDetailVO.setReceiverDetailedAddress(Objects.isNull(d.getReceiveDetailPlace()) ? null : d.getReceiveDetailPlace());
            pandaOrderDetailVO.setStationProvince(Objects.isNull(d.getStationProvince()) ? null : d.getStationProvince());
            pandaOrderDetailVO.setStationPlace(Objects.isNull(d.getStationPlace()) ? null : d.getStationPlace());
            pandaOrderDetailVO.setStationArea(Objects.isNull(d.getStationArea()) ? null : d.getStationArea());
            pandaOrderDetailVO.setStationDetailPlace(Objects.isNull(d.getStationDetailPlace()) ? null : d.getStationDetailPlace());
            pandaOrderDetailVO.setStationMobile(Objects.isNull(d.getStationMobile()) ? null : d.getStationMobile());
            pandaOrderDetailVO.setStationName(Objects.isNull(d.getStationName()) ? null : d.getStationName());
            if (d.getTime() != null) {
                pandaOrderDetailVO.setCustomerOrderTime(toDate(d.getTime()));
            }
            if (d.getOrderState() != null) {
                pandaOrderDetailVO.setOrderStatusName(toOrderStatus(d.getOrderState()));
                pandaOrderDetailVO.setOrderStatusCode(d.getOrderState());
            }
            if (d.getPaymentState() != null) {
                pandaOrderDetailVO.setOrderPayStatusName(toOrderPayStatus(d.getPaymentState()));
                pandaOrderDetailVO.setOrderPayStatus(d.getPaymentState());
            }
            pandaOrderDetailVO.setPackageTotalWeight(Objects.isNull(d.getWeight()) ? null : d.getWeight());
            pandaOrderDetailVO.setTotalFreight(Objects.isNull(d.getMoney()) ? null : d.getMoney());
            pandaOrderDetailVO.setRemarks(Objects.isNull(d.getNote()) ? null : d.getNote());
            if (d.getOrderType() != null) {
                String goodsTypeName = goodsName(d.getOrderType());
                pandaOrderDetailVO.setGoodsTypeName(goodsTypeName);
                //衣物箱包,配饰,短靴在极兔都是服饰
                pandaOrderDetailVO.setGoodsTypeCode(String.valueOf(d.getOrderType() == 5 || d.getOrderType() == 7 || d.getOrderType() == 8 ? 5 : 4));
            }
            if (d.getCancelTime() != null) {
                pandaOrderDetailVO.setCancelTime(toDate(d.getCancelTime()));
                pandaOrderDetailVO.setStatusTimeName("取消时间 :");
            } else {
                pandaOrderDetailVO.setStatusTimeName("下单时间 :");
            }
        }
        log.info("请求熊猫驿站,返回前端vo 结果:{}", JSON.toJSONString(pandaOrderDetailVO));
        return pandaOrderDetailVO;
    }

    //物品类型转换
    public Integer goodsType(String goodsTypeName) {
        Integer orderType = null;
        switch (goodsTypeName) {
            case "文件":
                orderType = 1;
                break;
            case "生活用品":
                orderType = 2;
                break;
            case "数码产品":
                orderType = 3;
                break;
            case "其他":
                orderType = 4;
                break;
            case "服饰":
                orderType = 5;
                break;
            case "食品":
                orderType = 6;
                break;
            default:
                orderType = 4;
        }
        return orderType;
    }

    //物品类型转换
    public String goodsName(Integer orderType) {
        String goodsTypeName = null;
        switch (orderType) {
            case 1:
                goodsTypeName = "文件";
                break;
            case 2:
                goodsTypeName = "生活用品";
                break;
            case 3:
                goodsTypeName = "数码产品";
                break;
            case 4:
                goodsTypeName = "其他";
                break;
            case 5:
                goodsTypeName = "服饰";
                break;
            case 6:
                goodsTypeName = "食品";
                break;
            case 7:
                goodsTypeName = "服饰";
                break;
            case 8:
                goodsTypeName = "服饰";
                break;
            default:
                goodsTypeName = "其他";
        }
        return goodsTypeName;
    }

    //时间戳单位秒，转换成时间
    public String toDate(String time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String sd = sdf.format(new Date(Long.parseLong(time + "000")));      // 时间戳转换成时间
        return sd;
    }

    //订单支付状态转换
    public String toOrderPayStatus(Integer paymentState) {
        String orderPayStatusName;
        switch (paymentState) {
            case 0:
                orderPayStatusName = "待确认支付";
                break;
            case 1:
                orderPayStatusName = "待付款";
                break;
            case 2:
                orderPayStatusName = "已付款";
                break;
            default:
                orderPayStatusName = "未知";
        }
        return orderPayStatusName;
    }

    //订单状态转换
    public String toOrderStatus(Integer OrderStatus) {
        String orderStatusName;
        switch (OrderStatus) {
            case 2:
                orderStatusName = "已打印";
                break;
            case 3:
                orderStatusName = "已取消";
                break;
            case 5:
                orderStatusName = "待取号";
                break;
            case 6:
                orderStatusName = "已取号";
                break;
            default:
                orderStatusName = "未知";
        }
        return orderStatusName;
    }

    public ResponseEntity<String> requestPanda(String bizData, String apiName, ResponseEntity<String> exchange, String requestId) {
        StopWatch stopWatch = new StopWatch();
        MultiValueMap<String, Object> parameters = new LinkedMultiValueMap<>();
        parameters.add("bizData", bizData);
        long timestamp = System.currentTimeMillis();
        parameters.add("timestamp", String.valueOf(timestamp));
        parameters.add("accountName", accountName);
        parameters.add("apiName", apiName);
        parameters.add("sign", sign(bizData, apiName, timestamp));
        stopWatch.start("开始请求熊猫驿站");
        HttpEntity httpEntity = new HttpEntity(parameters, getHttpHeaders());
        log.info("请求熊猫驿站 请求编号:{},请求入参:{}", requestId, JsonUtils.toJson(parameters));
        exchange = restTemplate.exchange(pandaUrl, HttpMethod.POST, httpEntity, String.class);
        stopWatch.stop();
        log.info("请求熊猫驿站 请求编号:{},响应:{} 耗时:{}", requestId, JsonUtils.toJson(exchange.getBody()), stopWatch.getLastTaskTimeMillis());
        return exchange;
    }

    //签名
    public String sign(String bizData, String apiName, long timestamp) {
        log.info("签名参数为:{}， 请求接口名称:{}，时间戳为:{}", JsonUtils.toJson(bizData), JsonUtils.toJson(apiName), timestamp);
        //接口名称
        //String apiName = "JT_SEND_PAGE_LIST";
        //开放平台账号
        //String accountName = "JTTEST";
        // 取得账户密码
        //String password = accountPassword;
        //md5加密密文
        String md5DigestAsHex = DigestUtils.md5DigestAsHex("XmyzdhbwqijheiO".getBytes(StandardCharsets.UTF_8));
        //待签名数据：业务参数+开放平台账号+接口名称+时间戳+密码
        String todoSign = bizData + accountName + apiName + timestamp + accountPassword;
        //签名规则base64(MD5(待签名数据+md5加密密文))
        String serverSign = Base64.getEncoder().encodeToString(DigestUtils.md5DigestAsHex((todoSign + md5DigestAsHex).getBytes()).getBytes());
        log.info("最终签名为:{}", serverSign);
        return serverSign;
    }

}
