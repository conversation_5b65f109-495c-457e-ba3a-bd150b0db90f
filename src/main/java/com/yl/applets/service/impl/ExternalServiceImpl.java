package com.yl.applets.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.alibaba.fastjson.JSON;
import com.yl.applets.config.WxMaConfiguration;
import com.yl.applets.config.WxMaProperties;
import com.yl.applets.service.IExternalService;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.CheckWxPhoneVO;
import com.yl.common.base.config.OrikaBeanMapper;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * @ClassName ExternalServiceImpl
 * @Description 对外接口服务类
 * <AUTHOR>
 * @Date 2021-08-12
 * @Version 1.0
 **/
@Slf4j
@Service
public class ExternalServiceImpl  implements IExternalService {

    @Autowired
    private WxMaProperties wxMaProperties;
    @Autowired
    private OrikaBeanMapper orikaBeanMapper;
    private static final String CHECK_PHONE_IS_BIND_URL="https://api.weixin.qq.com/cgi-bin/express/delivery/userquery";

    @Override
    public CheckWxPhoneVO checkPhoneIsBind(String phone) {
        String requestId= UUID.randomUUID().toString().replace("-","");
        StopWatch stopWatch=new StopWatch();
        stopWatch.start("通过手机号调用微信校验是否绑定");
        log.info("通过手机号调用微信校验是否绑定信息请求id:{},入参==>{}", requestId, phone);
        String appId = wxMaProperties.getConfigs().get(0).getAppid();
        final WxMaService wxService = WxMaConfiguration.getMaService(appId);
        Map<String,String> params=new HashMap<>();
        params.put("phone",phone);
        String responseContent=null;
        try {
            log.info("requestId:{},通过手机号调用微信校验是否绑定,请求参数为：{}",requestId,JsonUtils.toJson(params));
             responseContent = wxService.post(CHECK_PHONE_IS_BIND_URL,JsonUtils.toJson(params));
            stopWatch.stop();
            log.info("requestId:{},耗时==>{},通过手机号调用微信校验是否绑定结果：{}",requestId, stopWatch.getTotalTimeSeconds(),responseContent);
            if(!StringUtils.isEmpty(responseContent)){
                CheckWxPhoneVO checkWxPhoneVO= JSON.parseObject(responseContent,CheckWxPhoneVO.class);
                return checkWxPhoneVO;
            }
        } catch (WxErrorException e) {
            log.warn("requestId:{},通过手机号调用微信校验是否绑定异常:{}",requestId,e);
        } catch (Exception w){
            log.warn("requestId:{},通过手机号调用微信校验是否绑定Exception异常:{}",requestId,w);
        }
        return null;
    }
}
