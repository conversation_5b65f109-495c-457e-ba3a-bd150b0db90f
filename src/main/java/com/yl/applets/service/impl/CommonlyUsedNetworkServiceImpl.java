package com.yl.applets.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yl.applets.entity.CommonlyUsedNetwork;
import com.yl.applets.mapper.applets.CommonlyUsedNetworkMapper;
import com.yl.applets.service.ICommonlyUsedNetworkService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 常用网点表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2020-03-14
 */
@Service
public class CommonlyUsedNetworkServiceImpl extends ServiceImpl<CommonlyUsedNetworkMapper, CommonlyUsedNetwork> implements ICommonlyUsedNetworkService {

    @Override
    public IPage<CommonlyUsedNetwork> getPage(String longitude, String latitude,  List<Long> ids) {
        LambdaQueryWrapper lambdaQueryWrapper =  new LambdaQueryWrapper<CommonlyUsedNetwork>().in(CommonlyUsedNetwork::getUserId,ids).orderByDesc(CommonlyUsedNetwork::getUpdateTime);
        IPage<CommonlyUsedNetwork> page = this.page(new Page<CommonlyUsedNetwork>(1, 3), lambdaQueryWrapper);
        //去重
        Set<Long> code= Sets.newHashSet();
        List<CommonlyUsedNetwork> result= Lists.newArrayList();
        if(page.getRecords()!=null && !page.getRecords().isEmpty()){
            for (CommonlyUsedNetwork record : page.getRecords()) {
                if(!code.contains(record.getNetworkId())){
                    result.add(record);
                }
                code.add(record.getNetworkId());
            }
        }
        page.setRecords(result);
        return page;
    }
}
