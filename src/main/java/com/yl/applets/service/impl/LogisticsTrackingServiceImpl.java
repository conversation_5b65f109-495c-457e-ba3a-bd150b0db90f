package com.yl.applets.service.impl;

import com.yl.applets.dto.PodTrackingQueryDto;
import com.yl.applets.enums.TrackingTypeEnum;
import com.yl.applets.feign.OpsPodTrackingClient;
import com.yl.applets.service.ILogisticsTrackingService;
import com.yl.applets.vo.PodTrackingListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-08-06
 */
@Service
@Slf4j
public class LogisticsTrackingServiceImpl extends BaseServiceImpl implements ILogisticsTrackingService {

    @Autowired
    private OpsPodTrackingClient opsPodTrackingClient;


    @Override
    public Object logisticsTracking(String waybillNo) {
        List<String> list = new ArrayList<>();
        list.add(waybillNo);

        PodTrackingQueryDto podTrackingQueryDto = new PodTrackingQueryDto();
        podTrackingQueryDto.setKeywordList(list);
        podTrackingQueryDto.setTypeEnum(TrackingTypeEnum.CUSTOMER);
        List<PodTrackingListVO> listVOS = opsPodTrackingClient.innerPodTrackingList(podTrackingQueryDto).result();
        PodTrackingListVO prodVo = null;
        if (!listVOS.isEmpty()) {
            prodVo = listVOS.get(0);
        }
        if(prodVo ==null){
            prodVo = new PodTrackingListVO();
        }
        if(prodVo.getDetails()==null){
            prodVo.setDetails(new ArrayList<>());
        }

        return prodVo;
    }

}
