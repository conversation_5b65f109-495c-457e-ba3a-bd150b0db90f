package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yl.applets.entity.MemberScoreConfig;
import com.yl.applets.enums.ChannelSourceEnum;
import com.yl.applets.mapper.applets.MemberScoreConfigMapper;
import com.yl.applets.service.IMemberScoreConfigService;
import com.yl.applets.vo.MemberScoreConfigVo;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * @Description: 用户管理-会员成长分值 实现类
 * @CreateDate: Created in {2021/7/16 15:03}
 * @Author: DongFeixiang
 */
@Service
@Slf4j
public class MemberScoreConfigServiceImpl extends ServiceImpl<MemberScoreConfigMapper, MemberScoreConfig>
        implements IMemberScoreConfigService {

    @Autowired
    private OrikaBeanMapper beanMapper;

    @Autowired
    private MemberScoreConfigMapper configMapper;


    @Override
    public Result<List<MemberScoreConfigVo>> qureyGrowConfigByChannel(WxUserVo userVo) {
        if(userVo==null||userVo.getNumberId()==null){
            throw new ServiceException(ResultCodeEnum.USER_NOT_EXIST);
        }
        MemberScoreConfigVo vo=new MemberScoreConfigVo();
        vo.setType(ChannelSourceEnum.WX.getKey());
        vo.setMemberId(userVo.getNumberId());
        log.info("查询用户积分配置，入参：{}", JSON.toJSONString(vo));
        List<MemberScoreConfigVo> memberConfigAndStatus = configMapper.getMemberConfigAndStatus(vo);
        log.info("查询用户积分配置，出参：{}", JSON.toJSONString(memberConfigAndStatus));
        //微信小程序页面，不展示登录和注册
        List<MemberScoreConfigVo> collect = memberConfigAndStatus.stream().filter(o -> !o.getMemberAction().equals("register") && !o.getMemberAction().equals("login")).collect(Collectors.toList());

        log.info("查询用户积分配置，过滤之后：{}", JSON.toJSONString(collect));
        return Result.success(collect);
    }

}
