package com.yl.applets.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yl.applets.dto.CustomerPrintDataDTO;
import com.yl.applets.dto.ForwardRequest;
import com.yl.applets.entity.CustomerPrintData;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.*;
import com.yl.applets.mapper.applets.CustomerPrintDataMapper;
import com.yl.applets.service.ICustomerPrintDataService;
import com.yl.applets.service.IOrderService;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.utils.DateUtil;
import com.yl.applets.utils.VIPMD5Utils;
import com.yl.applets.vo.CloudPrintStatusVo;
import com.yl.applets.vo.OmsOrderApiVO;
import com.yl.applets.vo.SysCustomeAppletVO;
import com.yl.applets.vo.VIPLoginVO;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.GenerationIdUtil;
import com.yl.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-23
 */
@Service
@Slf4j
public class CustomerPrintDataServiceImpl extends ServiceImpl<CustomerPrintDataMapper, CustomerPrintData> implements ICustomerPrintDataService {

    @Autowired
    private OldLmdmFeignClient sysCustomerExtFeignClient;

    @Autowired
    private VIPSystemFeigntClient vipSystemFeigntClient;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Autowired
    private OrderFeigntClient orderFeigntClient;

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private OmsPrintClient omsPrintClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomerPrintData bind(CustomerPrintDataDTO customerPrintDataDTO) {
        //查询客户信息
//        SysCustomeAppletVO sysCustomeAppletVO = sysCustomerExtFeignClient.getSysCustomerAppletInfo(customerPrintDataDTO.getPrinterEquipmentId()).result();
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        ForwardRequest forwardRequest=new ForwardRequest();
        forwardRequest.setRequestId(requestId);
        forwardRequest.setBody(customerPrintDataDTO.getPrinterEquipmentId()+"");
        forwardRequest.setRequestUri("/lmdmapi/sysCustomerPrinterEquipment/getSysCustomerAppletInfo");
        log.info("请求id==>{}channel,入参==>{}",requestId, JSON.toJSONString(forwardRequest));
        Result<?> forward = channelApiFeignClient.forward(forwardRequest);
        log.info("请求id==>{}channel,出参==>{}",requestId, JSON.toJSONString(forward));
        Object result = forward.result();
        SysCustomeAppletVO sysCustomeAppletVO = JSON.parseObject(JSON.toJSONString(result), SysCustomeAppletVO.class);
        if(sysCustomeAppletVO == null){
            log.info("没有查询到基础数据客户信息,customerId:{}",customerPrintDataDTO.getCustomerId());
            throw new BusinessException(ServiceErrCodeEnum.BIND_PRINT_ERROR);
        }

        VIPLoginVO loginData = vipSystemFeigntClient.getAccountPwd(sysCustomeAppletVO.getCode()).result();
        log.info("查询到VIP客户信息==>{}", JSON.toJSONString(loginData));
        if(loginData == null){
            log.info("没有查询到VIP客户信息,customerId:{}",customerPrintDataDTO.getCustomerId());
            throw new BusinessException(ServiceErrCodeEnum.BIND_PRINT_ERROR);
        }

        //验证密码
        String signRsult = VIPMD5Utils.MD5(customerPrintDataDTO.getPwd());
        if(!signRsult.equals(loginData.getPwd())){
            Long errorNum = RedisUtil.incrBy(BcRedisKeyEnum.APPLETS_PRINTER_ERROR.keyBuilder(customerPrintDataDTO.getCustomerId() + ""), 1L);
            RedisUtil.expire(BcRedisKeyEnum.APPLETS_PRINTER_ERROR.keyBuilder(customerPrintDataDTO.getCustomerId()+""),1800);
            if(errorNum.compareTo(10L) > 0){
                throw new BusinessException(ServiceErrCodeEnum.BIND_PWD_TIME_ERROR);
            }
            throw new BusinessException(ServiceErrCodeEnum.BIND_PWD_ERROR);
        }else{
            RedisUtil.delete(BcRedisKeyEnum.APPLETS_PRINTER_ERROR.keyBuilder(customerPrintDataDTO.getCustomerId() + ""));
        }
        CustomerPrintData customerPrintData = getOne(new LambdaQueryWrapper<CustomerPrintData>().eq(CustomerPrintData::getUserId, customerPrintDataDTO.getUserId()).eq(CustomerPrintData::getPrinterEquipmentId,customerPrintDataDTO.getPrinterEquipmentId()));
        if(customerPrintData != null){
            throw new BusinessException(ServiceErrCodeEnum.PRINT_IS_BIND);
        }

        CustomerPrintData customerPrintData1 = orikaBeanMapper.map(sysCustomeAppletVO,CustomerPrintData.class);
        customerPrintData1.setId(GenerationIdUtil.getId());
        customerPrintData1.setCustomerId(sysCustomeAppletVO.getId());
        customerPrintData1.setUpdateTime(LocalDateTime.now());
        customerPrintData1.setUserId(customerPrintDataDTO.getUserId());
        if(!save(customerPrintData1)){
            throw new BusinessException(ServiceErrCodeEnum.BIND_PRINT_ERROR);
        }
        return customerPrintData1;
    }

    @Override
    public Result print(Long orderId, Integer userId) {

        OmsOrderApiVO omsOrderApiVO = orderService.detail(orderId);
        if(omsOrderApiVO == null){
            throw new BusinessException(ServiceErrCodeEnum.YD_NO_EXEITS);
        }
        //非待上门不能打印
        if(!omsOrderApiVO.getExpressStatusCode().equals(1)){
            throw new BusinessException(ServiceErrCodeEnum.PRINT_STATUS_ERROR);
        }

        LocalDateTime customerOrderTime = omsOrderApiVO.getInputTime();

        LocalDateTime end = LocalDateTime.now();

        Duration duration = Duration.between(customerOrderTime,end);
        //超过48小时不能打印
        long hours = duration.toHours();//相差的小时数
        if(hours > 48){
            throw new BusinessException(ServiceErrCodeEnum.PRINT_DATE_LIMIT_ERROR);
        }

      return  orderFeigntClient.cloudPrint(orderId);

    }

    @Override
    public  List<CloudPrintStatusVo> printStatus(List<Long> orderIds){
        return omsPrintClient.queryCloudPrintStatus(orderIds).result();
    }
}
