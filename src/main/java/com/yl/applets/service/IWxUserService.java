package com.yl.applets.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.applets.dto.ActionRecordDTO;
import com.yl.applets.dto.MiniSubscribeDTO;
import com.yl.applets.dto.WxUserDTO;
import com.yl.applets.entity.WxUser;
import com.yl.applets.vo.MyStaffVO;
import com.yl.applets.vo.SubscribeVO;
import com.yl.applets.vo.WxUserVo;

import java.util.List;

/**
 * <p>
 * 小程序用户 服务类
 * </p>
 *
 * <AUTHOR> @since 2019-09-21
 */
public interface IWxUserService extends IService<WxUser> {

    /**
     * 绑定手机号
     *
     * @param mobile
     * @return
     */
    WxUserVo bindMobile(String mobile,String verificationCode,Integer type,String openid,String unionid);

    /**
     * 解绑手机号
     *
     * @return
     */
    Boolean unbindMobile();

    WxUserVo handleBindPhone(WxUserVo user, String phoneNumber, String requestId);

    WxUser handleCMCCBindPhone(WxUserVo user, String phoneNumber, String requestId, Integer key);

    void updateFirstTYD();

    SubscribeVO isSubscribeV2(WxUserDTO wxUserDTO);

    void miniSubscribeReport(MiniSubscribeDTO miniSubscribeDTO,WxUserVo user);

    List<Long> getIdsByIdAndNumberId(Integer id);

    Integer maxNumberId();


    Integer maxJYUserId();


    Boolean isLogin(WxUserVo user);

    Boolean applyMember() throws InterruptedException;

    void addActionRecord(ActionRecordDTO dto);

    Boolean extension(WxUserVo user) throws InterruptedException;

    Integer isFirstTYD();

    WxUserVo getUser(String code);


    Integer updateJyUserId(WxUserVo user);

}
