package com.yl.applets.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.feign.dto.SpmApiCustomerShippingQuoteTryCalcDTO;
import com.yl.applets.feign.dto.citicmonthlysettlement.*;
import com.yl.applets.vo.CiticSpmCommonCostVO;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.model.vo.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/12/12 10:39
 */
public interface CiticMonthlySettlementService {

    /**
     * 绑定月结账号信息
     * @param dto
     * @return
     */
    Result<Integer> bindInfo(CiticMonthlySettlementDTO dto);


    /**
     * 解除绑定
     * @param dto
     * @return
     */
    Result<Integer> unBindInfo(CiticMonthlySettlementUpdateDTO dto);

    /**
     * 修改默认账号
     * @param dto
     * @return
     */
    Result<Integer> updateDefaultAccount(@Validated @RequestBody CiticMonthlySettlementUpdateDTO dto);

    /**
     * 查询当前用户下的月付账号列表
     * @param dto
     * @return
     */
    Result<Page<CiticMonthlySettlementResultDTO>> getMonthlySettlementList(CiticMonthlySettlementQueryDTO dto);

    /**
     * 判断兔优达
     * @param account
     * @return
     */
    Result<Boolean> judgeRabbitDelivery(String account);


    /**
     * 判断账号是否可用
     * @param account
     * @return
     */
    Result<Boolean> judgeAccount(String account);



    /**
     * 查询共享客户信息
     * @param account
     * @return
     */
    Result<MonthlySettlementShareCustomerDTO> findShareCustomer(String account);


    /**
     * 查询默认客户信息
     * @param memberId
     * @return
     */
    Result<CiticMonthlySettlementResultDTO> findDefaultAccount(String memberId);



    /**
     * 根据customerCode 查询月结账号信息
     * @param customerCode
     * @return
     */
    Result<CiticMonthlySettlementResultDTO> findAccountByCusCode(String memberId,String customerCode);


    /**
     * 客户运费报价-算费
     *
     * @param spmApiCustomerShippingQuoteTryCalcDTO
     * @return
     */
    CiticSpmCommonCostVO spmCustomerShippingQuoteCost(SpmApiCustomerShippingQuoteTryCalcDTO spmApiCustomerShippingQuoteTryCalcDTO);





}
