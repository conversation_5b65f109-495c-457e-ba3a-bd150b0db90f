package com.yl.applets.service;

import com.yl.applets.dto.CustomerConfigDTO;
import com.yl.applets.vo.OmsOrderRescheduleVO;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/4/22 17:37
 */

public interface OrderRescheduleService {
    /**
     * 根据订单号查询改约信息
     * @return
     */
    OmsOrderRescheduleVO  getCustomerConfigByOrderId(CustomerConfigDTO dto);
}
