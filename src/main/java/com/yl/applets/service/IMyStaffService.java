package com.yl.applets.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.MyStaffCheckAddressDTO;
import com.yl.applets.dto.MyStaffPageDTO;
import com.yl.applets.dto.MyStaffUpdateDefaultDTO;
import com.yl.applets.entity.MyStaff;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.applets.vo.FrequentAddressVO;
import com.yl.applets.vo.MyStaffVO;
import com.yl.applets.vo.RandomStaffVo;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.model.vo.Result;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * 专属会员 服务类
 * </p>
 *
 * <AUTHOR> @since 2020-01-07
 */
public interface IMyStaffService extends IService<MyStaff> {

    Boolean bindStaff(String staffCode, WxUserVo user,Integer isExclusive);


    RandomStaffVo getStaffAndBinding(String randomCode,WxUserVo userVo);

    Boolean queryStaff(String staffCode, WxUserVo user);


    Result<Page<MyStaffVO>> getMyStaffList(MyStaffPageDTO page);


    /**
     * 修改默认是否专属快递员
     * @return
     */
    Result<String> updateDefaultStaff(MyStaffUpdateDefaultDTO dto);


    /**
     * 检查寄件信息是否符合标准
     * @param dto
     * @return
     */
    Result<Boolean> checkSendAddress(MyStaffCheckAddressDTO dto);


    /**
     * 获取客户的常用地址
     * @param userId
     * @return
     */
    Result<List<FrequentAddressVO>> getFrequentAddressByUserId(@RequestParam("userId") Integer userId);

}
