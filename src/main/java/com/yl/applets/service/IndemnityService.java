package com.yl.applets.service;

import com.yl.applets.dto.indemnity.IndemnityVO;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.model.vo.Result;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： <PERSON><PERSON><PERSON><PERSON>
 * @Date： 2023/01/09
 */
public interface IndemnityService {
    /**
     * 理赔列表查询
     * @param user
     * @param status
     * @return
     */
    Result<List<IndemnityVO>> wxQueryByUserId(WxUserVo user, Integer status);

    List<IndemnityVO> bgApiGetData(WxUserVo user);

}
