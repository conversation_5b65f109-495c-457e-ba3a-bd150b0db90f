package com.yl.applets.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.applets.entity.MemberUser;
import com.yl.applets.vo.MemberUserDetailVo;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.model.vo.Result;

/**
 * <p>
 *  会员用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-7-7 17:28:33
 */
public interface IMemberUserService extends IService<MemberUser> {


    Result<MemberUserDetailVo> queryMemberUser(WxUserVo user);
}
