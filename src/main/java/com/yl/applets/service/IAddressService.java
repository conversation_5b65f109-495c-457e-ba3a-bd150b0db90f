package com.yl.applets.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.applets.dto.AddressDTO;
import com.yl.applets.entity.Address;
import com.yl.applets.vo.AddressVo;

import java.util.List;

/**
 * 地址管理
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-20
 */
public interface IAddressService extends IService<Address> {

    /**
     * 地址列表
     *
     * @param page
     * @param keyword
     * @param userId
     * @return
     */
    Page<AddressVo> getPages(Page<Object> page, String keyword, Integer userId,Integer type,List<Long> signs);

    /**
     * 新增
     *
     * @param dto
     * @return
     */
    AddressVo save(AddressDTO dto);

    /**
     * 修改
     *
     * @param dto
     * @return
     */
    Boolean update(AddressDTO dto);

    /**
     * 获取默认寄件地址
     *
     * @return
     */
    AddressVo getDefault(List<Long> ids, List<Long> addressIds);

    /**
     * 获取默认寄件地址
     *
     * @return
     */
    AddressVo getDefaultByMobile(String mobile);

}
