package com.yl.applets.service;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: 腾讯图形验证码校验
 * @date 2021-10-26 14:42
 */
public interface ChapterCheckService {

    /**
     * 验证码校验
     * @param ticket
     * @param randstr
     * @param userIp
     * @return
     */
    boolean captchaCheckWeb(String ticket,String randstr,String userIp)  throws Exception;


    boolean captchaCheckApplets(String ticket,String userIp)  throws Exception;
}
