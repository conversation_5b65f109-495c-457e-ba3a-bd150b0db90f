package com.yl.applets.service;

import com.yl.applets.dto.BatchOrderDTO;
import com.yl.applets.dto.OmsOrderBatchApiDto;
import com.yl.applets.dto.OrderCancelDTO;
import com.yl.applets.dto.OrderDTO;
import com.yl.applets.entity.UserCard;
import com.yl.applets.vo.OmsOrderApiVO;
import com.yl.applets.vo.OmsOrderBatchApiVo;
import com.yl.applets.vo.OmsOrderUpdateApiVO;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.model.vo.Result;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-08-31 12:01 <br>
 * @Author: zhipeng.liu
 */

public interface IOrderService {

    /**
     * 订单明细
     *
     * @param orderId
     * @return
     */
    OmsOrderApiVO detail(Long orderId);

    /**
     * 新增
     *
     * @param dto
     * @return
     */
    OmsOrderApiVO save(OrderDTO dto, WxUserVo wxUserVo );

    /**
     * 修改
     *
     * @param dto
     * @return
     */
    OmsOrderUpdateApiVO update(OrderDTO dto, WxUserVo wxUserVo);

    /**
     * 取消
     *
     * @param orderIds
     * @return
     */
    Boolean cancel(List<Long> orderIds,String requestId);

    /**
     * 订单取消原因
     *
     * @param orderCancelDTO
     * @return
     */
    Boolean cancelReason(OrderCancelDTO orderCancelDTO,String requestId);



    /**
     * 删除
     *
     * @param orderId
     * @return
     */
    Boolean delete(Long orderId);

    /**
     * 云打印-批量订单
     *
     * @param dto
     * @return
     */
    OmsOrderBatchApiVo cloudPrintOrder(BatchOrderDTO dto);


    /**
     * 批量新增
     *
     * @param dto
     * @return
     */
    OmsOrderBatchApiVo batchOrder(OmsOrderBatchApiDto dto);

}
