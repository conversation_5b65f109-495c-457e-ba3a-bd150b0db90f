package com.yl.applets.service.coupons;

import com.yl.applets.dto.coupons.TianYiTradeDTO;
import com.yl.applets.dto.coupons.TianyiDTO;
import com.yl.applets.dto.coupons.TianyiPublicDTO;
import com.yl.common.base.model.ResultVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-05-24 16:14
 * @Version 1.0
 */
public interface CouponsTianYiService {

    Boolean checkPhoneAvailability(TianyiDTO dto, HttpServletRequest request);

    ResultVO bindUserAndTrade(TianYiTradeDTO dto);

    ResultVO bindUserAndTradeQuery(TianYiTradeDTO dto);
}
