package com.yl.applets.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.ThirdExpressApiDTO;
import com.yl.applets.vo.ThirdExpressListVO;

import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-23
 */
public interface IExpressDeliveryService {

    /**
     * 快件列表
     *
     * @param dto
     * @return
     */
    Page<ThirdExpressListVO> getPages(ThirdExpressApiDTO dto);

    /**
     * 快件数量
     *
     * @param dto
     * @return
     */
    Map<String, Long> count(ThirdExpressApiDTO dto);

}
