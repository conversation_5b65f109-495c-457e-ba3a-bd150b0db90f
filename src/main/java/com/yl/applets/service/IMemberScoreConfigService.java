package com.yl.applets.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.applets.entity.MemberScoreConfig;
import com.yl.applets.vo.MemberScoreConfigVo;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.model.vo.Result;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * @Description:
 * @CreateDate: Created in {2021/7/16 15:02}
 * @Author: DongFeixiang
 */
public interface IMemberScoreConfigService extends IService<MemberScoreConfig> {


    Result<List<MemberScoreConfigVo>> qureyGrowConfigByChannel(WxUserVo userVo);

}
