package com.yl.applets.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yl.applets.dto.ActivityUserRecordDto;
import com.yl.applets.dto.PrizeDto;
import com.yl.applets.entity.ActivityPrizeRecord;
import com.yl.applets.entity.LuckDraw;
import com.yl.applets.entity.LuckDrawItem;
import com.yl.applets.entity.LuckDrawPool;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.model.vo.Result;

import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动管理
 * @author: xiongweibin
 * @create: 2020-08-24 11:46
 */
public interface IActivityPrizeService extends IService<ActivityPrizeRecord> {


    Result<ActivityUserRecordDto> updatePoolAndSaveRecord(LuckD<PERSON> luckDraw,LuckDrawPool luckDrawPool, LuckDrawPool prize, PrizeDto paramDto, WxUserVo user, LocalDateTime now, String requestId);


    ActivityUserRecordDto noPrize(<PERSON><PERSON><PERSON> luckDraw,PrizeDto paramDto, WxUserVo user, LocalDateTime now, String requestId);

    ActivityUserRecordDto yesPrize(LuckDraw luckDraw,LuckDrawItem luckDrawItem, PrizeDto paramDto, WxUserVo user, LocalDateTime now, String requestId);

}
