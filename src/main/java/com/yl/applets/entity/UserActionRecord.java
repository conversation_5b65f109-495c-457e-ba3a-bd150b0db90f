package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：    用户行为记录
 * @Author： zhanzhihong
 * @Date： 2022/07/25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_USER_ACTION_RECORD")
public class UserActionRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.INPUT)
    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户昵称
     */
    private String userName;

    @ApiModelProperty(value = "事件code")
    private String elementCode;

    @ApiModelProperty(value = "事件内容")
    private String elementContent;

    @ApiModelProperty(value = "事件名称")
    private String elementEventName;

    @ApiModelProperty(value = "元素id")
    private String elementId;

    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @ApiModelProperty(value = "机型")
    private String phoneModel;

    @ApiModelProperty(value = "上报地址")
    private String reportAddress;

    @ApiModelProperty(value = "上报经纬度")
    private String reportLocation;

    @ApiModelProperty(value = "页面路径")
    private String pagePath;

    @ApiModelProperty(value = "ip地址")
    private String ipAddress;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
