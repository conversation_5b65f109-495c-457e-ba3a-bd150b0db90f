package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2023/3/9 11:30
 * @description 市场活动表
 */

@ApiModel(description = "市场活动浏览日志")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "YL_MARKET_ACTIVITY_BROWSE_LOG")
public class MarketActivityBrowseLog implements Serializable {

    @TableId(value = "ID")
    private Long id;

    @TableField(value = "MARKET_ACTIVITY_ID")
    private Long marketActivityId;


    @TableField(value = "MARKET_ACTIVITY_CODE")
    private String marketActivityCode;


    @TableField(value = "SOURCE")
    private String source;

    @TableField(value = "CREATE_TIME")
    private LocalDateTime createTime;

}