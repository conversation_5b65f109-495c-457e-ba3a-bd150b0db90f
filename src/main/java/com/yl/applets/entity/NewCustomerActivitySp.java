package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2023/3/9 11:48
 * @description 大客户拉新活动表
 */

@ApiModel(description = "大客户拉新活动表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "YL_NEW_CUSTOMER_ACTIVITY_SP")
public class NewCustomerActivitySp implements Serializable {
    /**
     * ID
     */
    @TableField(value = "ID")
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 渠道来源
     */
    @TableField(value = "SOURCE")
    @ApiModelProperty(value = "渠道来源")
    private String source;

    /**
     * 姓名
     */
    @TableField(value = "NAME")
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 手机号
     */
    @TableField(value = "PHONE")
    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     * 地址
     */
    @TableField(value = "FULL_ADDRESS")
    @ApiModelProperty(value = "地址")
    private String fullAddress;

    /**
     * 代理区名称
     */
    @TableField(value = "PROXY_NAME")
    @ApiModelProperty(value = "代理区名称")
    private String proxyName;

    /**
     * 代理区编码
     */
    @TableField(value = "PROXY_CODE")
    @ApiModelProperty(value = "代理区编码")
    private String proxyCode;

    /**
     * 加盟商名称
     */
    @TableField(value = "FRANCHISEE_NAME")
    @ApiModelProperty(value = "加盟商名称")
    private String franchiseeName;

    /**
     * 加盟商编码
     */
    @TableField(value = "FRANCHISEE_CODE")
    @ApiModelProperty(value = "加盟商编码")
    private String franchiseeCode;

    /**
     * 网点名称
     */
    @TableField(value = "NETWORK_NAME")
    @ApiModelProperty(value = "网点名称")
    private String networkName;

    /**
     * 网点编码
     */
    @TableField(value = "NETWORK_CODE")
    @ApiModelProperty(value = "网点编码")
    private String networkCode;

    /**
     * 是否合作: 0否，1是
     */
    @TableField(value = "IS_COOPERATED")
    @ApiModelProperty(value = "是否合作: 0否，1是")
    private Integer isCooperated;

    /**
     * 客户编码
     */
    @TableField(value = "CUSTOMER_CODE")
    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    /**
     * 商家ID
     */
    @TableField(value = "MALL_ID")
    @ApiModelProperty(value = "商家ID")
    private String mallId;

    /**
     * 是否满足条件：是/否/客户编码错误
     */
    @TableField(value = "STATUS")
    @ApiModelProperty(value = "是否满足条件：是/否/客户编码错误")
    private Short status;

    /**
     * 活动ID
     */
    @TableField(value = "MARKET_ACTIVITY_ID")
    @ApiModelProperty(value = "活动ID")
    private Long marketActivityId;

    /**
     * 活动编码：从J00000开始
     */
    @TableField(value = "MARKET_ACTIVITY_CODE")
    @ApiModelProperty(value = "活动编码：从J00000开始")
    private String marketActivityCode;

    /**
     * 活动名称
     */
    @TableField(value = "MARKET_ACTIVITY_NAME")
    @ApiModelProperty(value = "活动名称")
    private String marketActivityName;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATE_BY")
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @TableField(value = "CREATE_BY_NAME")
    @ApiModelProperty(value = "创建人姓名")
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(value = "UPDATE_BY")
    @ApiModelProperty(value = "更新人ID")
    private Long updateBy;

    /**
     * 更新人姓名
     */
    @TableField(value = "UPDATE_BY_NAME")
    @ApiModelProperty(value = "更新人姓名")
    private String updateByName;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 邀请人
     */
    @TableField(value = "INVITER")
    @ApiModelProperty(value = "邀请人")
    private String inviter;

    /**
     * 邀请人是否有效：1是/0否
     */
    @TableField(value = "IS_INVITER")
    @ApiModelProperty(value = "邀请人是否有效：1是/0否")
    private Integer isInviter;
}