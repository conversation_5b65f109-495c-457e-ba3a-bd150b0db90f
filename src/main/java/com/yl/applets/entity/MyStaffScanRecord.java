package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 专属会员
 * </p>
 *
 * <AUTHOR> @since 2020-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_MY_STAFF_SCAN_RECORD")
public class MyStaffScanRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户id
     */
    @TableField("USER_ID")
    private Long userId;

    /**
     * 姓名
     */
    @TableField("USER_NAME")
    private String userName;

    /**
     * 手机号
     */
    @TableField("MOBILE")
    private String mobile;

    /**
     * 业务员号
     */
    @TableField("STAFF_NO")
    private String staffNo;

    /**
     * 随机码
     */
    @TableField("RANDOM_CODE")
    private String randomCode;


    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;


    /**
     * 1.微信
     * 2.支付宝
     */
    @TableField("USER_CHANNEL")
    private Integer userChannel;
}
