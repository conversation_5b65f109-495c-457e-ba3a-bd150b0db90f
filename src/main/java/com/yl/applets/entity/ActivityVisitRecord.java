package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动访问/曝光记录
 * @author: xiongweibin
 * @create: 2020-08-24 13:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_APPLETS_ACTIVITY_RECORD")
public class ActivityVisitRecord implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 类型 1:曝光 2:访问
     */
    private Integer type;
    /**
     * 手机
     */
    private String phone;
    /**
     * 访问时间
     */
    private LocalDateTime activityTime;

    /**
     * openId
     */
    private String openId;
}
