package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-10-11 17:05
 * @Version 1.0
 */

@Data
@TableName("YL_APPLETS_USER_INFO_POLICE")
public class UserInfoPoliceEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.INPUT)
    private Long id;

    /**
     * 帐号
     */
    @TableField("USER_ACCOUNT")
    private String userAccount;

    /**
     * 用户内部id
     */
    @TableField("USER_INTENRALID")
    private String userIntenralid;

    /**
     * 用户类型
     */
    @TableField("USER_TYPE")
    private String userType;

    /**
     * 关联的支付宝账号
     */
    @TableField("JOIN_ALIPAY")
    private String joinAlipay;

    /**
     * 关联的微信账号
     */
    @TableField("JOIN_WEIXIN")
    private String joinWeixin;

    /**
     * 昵称
     */
    @TableField("NICKNAME")
    private String nickname;

    /**
     * 手机号
     */
    @TableField("MOBILEPHONE")
    private String mobilePhone;

    /**
     * 用户头像附件
     */
    @TableField("USER_HEADATTACHMENT")
    private String userHeadAttachment;

    /**
     * 证件类型
     */
    @TableField("IDENTIFICATION_TYPE")
    private String identificationType;

    /**
     * 证件号码
     */
    @TableField("IDENTIFICATION_ID")
    private String identificationId;

    /**
     * 性别
     */
    @TableField("SEX")
    private Integer sex;

    /**
     * 注册ip地址
     */
    @TableField("REG_IP")
    private String regIp;

    /**
     * 注册端口号
     */
    @TableField("REG_PORT")
    private String regPort;

    /**
     * 注册时间
     */
    @TableField("REG_TIME")
    private LocalDateTime regTime;

    /**
     * 注册城市
     */
    @TableField("REG_CITY")
    private String regCity;

    /**
     * 动作类型
     */
    @TableField("ACTION_TYPE")
    private String actionType;

    /**
     * 动作时间
     */
    @TableField("ACTION_TIME")
    private LocalDateTime actionTime;

    /**
     * 合作账号类型
     */
    @TableField("CONSOCIATION_TYPE")
    private String consociationType;

    /**
     * 实名认证方式
     */
    @TableField("IDEN_TYPE")
    private String idenType;

}
