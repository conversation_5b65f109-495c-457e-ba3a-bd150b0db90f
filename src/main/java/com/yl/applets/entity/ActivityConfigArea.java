package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动管理配置
 * @author: xiongweibin
 * @create: 2020-08-24 11:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_APPLETS_ACT_CONFIG_AREA")
public class ActivityConfigArea implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    /**
     * 配置id
     */
    private Long configId;
    /**
     * 省
     */
    private String provinceId;
    private String provinceName;
    /**
     * 市
     */
    private String cityId;
    private String cityName;
    /**
     * type
     */
    private Integer type;
    /**
     *
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
