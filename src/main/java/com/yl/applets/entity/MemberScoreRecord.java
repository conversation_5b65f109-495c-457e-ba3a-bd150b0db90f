package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 小程序用户
 * </p>
 *
 * <AUTHOR> @since 2019-09-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_MEMBER_SCORE_RECORD")
public class MemberScoreRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 下单使用的id
     */
    private Integer memberId;

    //用户行为
    private String memberAction;

    /**
     * 用户端-渠道
     */
    private Integer type;

    //成长值
    private Integer growValue;

    //成长记录
    private Integer growRecord;

    //积分值
    private Integer scoreValue;

    //积分记录
    private Integer scoreRecord;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     *  0未删除，1已删除
     */
    private Integer isDelete;

    //创建人
    private Long createBy;

    //更新人
    private Long updateBy;


}
