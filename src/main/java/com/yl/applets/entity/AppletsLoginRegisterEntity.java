package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description 用户信息-累计注册省市区信息表
 * <AUTHOR>
 * @Date 2022-10-28 18:05
 * @Version 1.0
 */

@Data
@TableName("APPLETS_LOGN_REGISTER_INFO")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AppletsLoginRegisterEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ID")
    private Long id;

    @TableField("MEMBER_ID")
    private Long memberId;


    @TableField("PROVINCE")
    private String province;

    @TableField("CITY")
    private String city;


    @TableField("ZONE")
    private String zone;


    @TableField("YEAR")
    private Long year;

    @TableField("MONTH")
    private Long month;


    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @TableField("REGISTER_TIME")
    private LocalDateTime registerTime;
}
