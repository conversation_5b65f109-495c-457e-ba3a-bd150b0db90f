package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021<br>
 *
 * @description: 二要素的接口调用记录表
 * @author: caiyao
 * @create: 2022-01-07 10:11
 */
@Data
@TableName("YL_APPLETS_USER_CARD_RECORD")
public class UserCardRecord implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 入参
     */
    private String inParams;

    /**
     * 出参
     */
    private String outParams;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
