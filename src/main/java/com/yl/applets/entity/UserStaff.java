/**
 * Copyright (C), 2015-2021, 云路供应链科技有限公司
 * FileName: UserStaff
 * Author:   luhong
 * Date:     2021-02-01 16:39
 * Description: 用户业务员
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 〈一句话功能简述〉<br> 
 * 〈用户业务员〉
 * 用户业务员绑定关系表
 * <AUTHOR>
 * @create 2021-02-01
 * @since 1.0.0
 */
@Data
@TableName("YL_APPLETS_USER_STAFF")
public class UserStaff implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 完整地址
     */
    private String fullAddress;

    /**
     * 业务员code
     */
    private String staffCode;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}