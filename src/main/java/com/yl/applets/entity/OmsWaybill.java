package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 运单表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_OMS_OMS_WAYBILL")
public class OmsWaybill implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 运单号
     */

    private String waybillNo;

    /**
     * 运单状态
     */

    private Integer waybillStatusCode;

    /**
     * 物品类型id
     */

    private Integer goodsTypeId;

    /**
     * 物品类型code
     */

    private String goodsTypeCode;

    /**
     * 物品类型名称
     */

    private String goodsTypeName;

    /**
     * 物品名称Z
     */

    private String goodsName;

    /**
     * 件数,＞1表示子母件，如果业务上不支持子母件，则前端限制不显示，并默认为1
     */

    private Integer packageNumber;

    /**
     * 包裹总长,单位厘米
     */

    private BigDecimal packageLength;

    /**
     * 包裹总宽,单位厘米
     */

    private BigDecimal packageWide;

    /**
     * 包裹总高,单位厘米
     */

    private BigDecimal packageHigh;

    /**
     * OmsWaybillDataDTO
     * 包裹体积重,单位立方厘米
     */

    private BigDecimal packageVolume;

    /**
     * 包裹总重量,单位千克
     */

    private BigDecimal packageTotalWeight;

    /**
     * 包裹总体积,单位立方厘米
     */

    private BigDecimal packageTotalVolume;

    /**
     * 包裹收件重量,单位千克
     */
    private BigDecimal packageCollectWeight;

    /**
     * 包裹入仓重量,单位千克
     */
    private BigDecimal packageReceiptWeight;

    /**
     * 包裹集散到件重量,单位千克
     */
    private BigDecimal packageArrivalWeight;

    /**
     * 包裹中心到件重量,单位千克
     */
    private BigDecimal packageCenterArrivalWeight;

    /**
     * 包裹内部计费重量,单位千克
     */
    private BigDecimal packageInsideChargeWeight;

    /**
     * 包裹计费重量,单位千克
     */
    private BigDecimal packageChargeWeight;

    /**
     * 转运中心扫描标识:1已转运,0未转运,默认0
     */
    private Integer isDistributeScan;

    /**
     * 集散到件标识:1是,0否,默认0
     */
    private Integer isArrivalScan;

    /**
     * 包材规格code
     */

    private String boxStandardCode;

    /**
     * 包材规格名称
     */

    private String boxStandardName;

    /**
     * 包材数量
     */

    private Integer boxNumber;

    /**
     * 箱子价格
     */

    private BigDecimal boxPrice;

    /**
     * 揽件业务员code
     */

    private String collectStaffCode;

    /**
     * 揽件业务员名称
     */

    private String collectStaffName;

    /**
     * 揽件时间
     */

    private LocalDateTime collectTime;

    /**
     * 派件业务员code
     */

    private String dispatchStaffCode;

    /**
     * 派件业务员名称
     */

    private String dispatchStaffName;

    /**
     * 派件时间
     */

    private LocalDateTime dispatchTime;

    /**
     * 派件网点code
     */

    private String dispatchNetworkCode;

    /**
     * 派件网点名称
     */

    private String dispatchNetworkName;

    /**
     * 异常原因code
     */
    private String exceptionCode;

    /**
     * 异常原因
     */
    private String exceptionReason;

    /**
     * 签收标识,1是，0否
     */

    private Integer isSign;

    /**
     * 签收网点code
     */

    private String signNetworkCode;

    /**
     * 签收网点名称
     */

    private String signNetworkName;

    /**
     * 签收时间
     */

    private LocalDateTime signTime;

    /**
     * 是否实名制,1是，0否
     */
    private Integer isRealName;

    /**
     * 实名姓名
     */
    private String realName;

    /**
     * 证件类型
     */
    private Integer idNoType;

    /**
     * 证件号码
     */
    private String idNo;

    /**
     * 问题件标识,1是，0否
     */

    private Integer isAbnormal;

    /**
     * 性别 1男 2女
     */
    private Integer sex;

//    /**
//     * 问题件登记网点code
//     */
//
//    private String abnormalRegNetworkCode;
//
//    /**
//     * 问题件登记网点名称
//     */
//
//    private String abnormalRegNetworkName;

    /**
     * 问题件登记时间
     */

    private LocalDateTime abnormalRegTime;

//    /**
//     * 问题件登记人code
//     */
//
//    private String abnormalRegStaffCode;
//
//    /**
//     * 问题件登记人名称
//     */
//
//    private String abnormalRegStaffName;


    /*

    private Integer shiftId;


    private String shiftName;
    */

    /**
     * 内部订单编号
     */

    private Long orderId;

    /**
     * 客户订单编号
     */

    private String customerOrderId;

    /**
     * 运单来源code
     */

    private String waybillSourceCode;

    /**
     * 运单来源名称
     */

    private String waybillSourceName;

    /**
     * 寄件方式code
     */

    private String sendCode;

    /**
     * 寄件方式名称
     */

    private String sendName;

    /**
     * 派件方式code
     */

    private String dispatchCode;

    /**
     * 派件方式名称
     */

    private String dispatchName;

    /**
     * 是否作废件,1是，0否
     */

    private Integer isVoid;

    /**
     * 是否退件转寄，0否，1退件，2转寄
     */

    private Integer isRefund;

    /**
     * 是否签回单,1是，0否
     */

    private Integer isNeedReceipt;

    /**
     * 关联单号
     */

    private String receiptNo;

    /**
     * 结算审核标识,1是，0否
     */

    private Integer isSettlement;

    private LocalDateTime settlementTime;

    /*

    private Integer isSub;
    */

    private Integer customerId;

    /**
     * 客户编号code
     */

    private String customerCode;

    /**
     * 客户编号名称
     */

    private String customerName;

//    /**
//     * 客户网点id
//     */
//    private Integer customerNetworkId;

    /**
     * 客户所属：1-总部共享,2-代理区共享,3-自有客户
     */
    private Integer customerType;

    /**
     * 客户网点code
     */
    private String customerNetworkCode;

    /**
     * 客户编号网点名称
     */
    private String customerNetworkName;

    /**
     * 寄件人姓名
     */

    private String senderName;

    /**
     * 寄件人手机号
     */

    private String senderMobilePhone;

    /**
     * 寄件人座机
     */

    private String senderTelphone;

    /**
     * 寄件国家Id
     */

    private Integer senderCountryId;

    /**
     * 寄件国家名称
     */

    private String senderCountryName;

    /**
     * 寄件省份id
     */

    private Integer senderProvinceId;

    /**
     * 寄件省份名称
     */

    private String senderProvinceName;

    /**
     * 寄件城市id
     */

    private Integer senderCityId;

    /**
     * 寄件城市名称
     */

    private String senderCityName;

    /**
     * 寄件区域Id
     */

    private Integer senderAreaId;

    /**
     * 寄件区域名称
     */

    private String senderAreaName;

    /**
     * 寄件乡镇
     */

    private String senderTownship;

    /**
     * 寄件街道
     */

    private String senderStreet;

    /**
     * 寄件详细地址
     */

    private String senderDetailedAddress;

    /**
     * 寄件邮编
     */

    private String senderPostalCode;

    /**
     * 寄件邮箱
     */

    private String senderEmail;

//    /**
//     * 签收短信通知,1是，0否
//     */
//
//    private Integer signSmsNotify;
//
//    /**
//     * 寄件短信通知,1是，0否
//     */
//
//    private Integer senderSmsNotify;

    /**
     * 始发地id
     */

    private Integer originId;

    /**
     * 始发地code
     */

    private String originCode;

    /**
     * 始发地名称
     */

    private String originName;

    /**
     * 寄件网点code
     */

    private String pickNetworkCode;

    /**
     * 寄件网点名称
     */

    private String pickNetworkName;

    /**
     * 运费
     */

    private BigDecimal freight;

    /**
     * 包材费
     */

    private BigDecimal packageCost;

//    /**
//     * 税后总运费
//     */
//
//    private BigDecimal afterTaxFreight;

    /**
     * 总运费
     */

    private BigDecimal totalFreight;

    /**
     * 预收面单费单价
     */
    private BigDecimal tax;

    /**
     * 手工费
     */

    private BigDecimal handicraftFee;

    /**
     * 其他费
     */

    private BigDecimal otherFee;

    /**
     * 回单费
     */
    private BigDecimal receiptFee;

    /**
     * 到付款
     */

    private BigDecimal receivePayFee;

    /**
     * 寄件财务中心code
     */

    private String pickFinanceCode;

    /**
     * 寄件财务中心名称
     */

    private String pickFinanceName;

    /**
     * 派件财务中心code
     */

    private String dispatchFinanceCode;

    /**
     * 派件财务中心名称
     */

    private String dispatchFinanceName;

    /**
     * 转运中心财务中心id
     */
    private Integer distributeFinanceId;

    /**
     * 转运中心财务中心code
     */
    private String distributeFinanceCode;

    /**
     * 转运中心财务中心名称
     */
    private String distributeFinanceName;

    /**
     * 产品类型id
     */

    private Integer expressTypeId;

    /**
     * 产品类型code
     */

    private String expressTypeCode;

    /**
     * 产品类型名称
     */

    private String expressTypeName;

    /**
     * 是否需要保价,1是，0否
     */

    private Integer insured;

    /**
     * 保价金额
     */

    private BigDecimal insuredAmount;

    /**
     * 保价费
     */

    private BigDecimal insuredFee;

    /**
     * 是否需要代收货款,1是，0否
     */

    private Integer codNeed;

    /**
     * 代收货款金额
     */

    private BigDecimal codMoney;

    /**
     * 代收货款金额
     */

    private BigDecimal codFee;

    /**
     * 结算方式id
     */

    private Integer settlementId;

    /**
     * 结算方式编码
     */

    private String settlementCode;

    /**
     * 结算方式名称
     */

    private String settlementName;

    /**
     * 收件人姓名
     */

    private String receiverName;

    /**
     * 收件人公司
     */

    private String receiverCompany;

    /**
     * 收件人手机号
     */

    private String receiverMobilePhone;

    /**
     * 收件人座机
     */

    private String receiverTelphone;

    /**
     * 收件国家id
     */

    private Integer receiverCountryId;

    /**
     * 收件国家名称
     */

    private String receiverCountryName;

    /**
     * 收件省份id
     */

    private Integer receiverProvinceId;

    /**
     * 收件省份名称
     */

    private String receiverProvinceName;

    /**
     * 收件城市id
     */

    private Integer receiverCityId;

    /**
     * 收件城市名称
     */

    private String receiverCityName;

    /**
     * 收件区域id
     */

    private Integer receiverAreaId;

    /**
     * 收件区域名称
     */

    private String receiverAreaName;

    /**
     * 收件乡镇
     */

    private String receiverTownship;

    /**
     * 收件街道
     */

    private String receiverStreet;

    /**
     * 收件详细地址
     */

    private String receiverDetailedAddress;

    /**
     * 收件邮编
     */

    private String receiverPostalCode;

    /**
     * 收件邮箱
     */

    private String receiverEmail;

    /**
     * 目的地id
     */

    private Integer destinationId;

    /**
     * 目的地code
     */

    private String destinationCode;

    /**
     * 目的地名称
     */

    private String destinationName;

    /**
     * 收件分拣码
     */

    private String receiverSortingCode;

    /**
     * 录入时间
     */

    private LocalDateTime inputTime;

    /**
     * 寄件时间
     */

    private LocalDateTime deliveryTime;

    /**
     * 录入人id
     */

    private Integer inputStaffBy;

    /**
     * 录入人code
     */

    private String inputStaffCode;

    /**
     * 录入人名称
     */

    private String inputStaffName;

    /**
     * 录入网点code
     */

    private String inputNetworkCode;

    /**
     * 录入网点名称
     */

    private String inputNetworkName;

    /**
     * 优惠券编号
     */

    private String couponCode;

    /**
     * 优惠金额
     */

    private BigDecimal couponAmount;

    /**
     * 备注
     */

    private String remarks;

    /**
     * 打印次数
     */

    private Integer printsNumber;

//    /**
//     * 发票编号
//     */
//
//    private String invoiceNo;

//    /**
//     * 是否纸质运单,1是，0否
//     */
//
//    private Integer isPaper;

    /**
     * 账单编号
     */

    private String billNo;

    /**
     * 是否现金收款,1是，0否
     */

    private Integer isCash;

    /**
     * COD收款标识,1是，0否
     */

    private Integer isCodReceive;
    /**
     * 发件标识,做了寄件网点发件扫描后标记是（控制是否允许作废),1是，0否
     */
    /*
    @Deprecated

    private Integer isSend;
    */

    /**
     * 货币币别编码
     */

    private String currencyCode;

    /**
     * 货币币别名称
     */

    private String currencyName;

//    /**
//     * 支付方式id
//     */
//
//    private Integer paidModeId;

    /**
     * 支付方式编码
     */

    private String paidModeCode;

    /**
     * 支付方式名称
     */

    private String paidModeName;

    /**
     * 最后更新时间
     */

    private LocalDateTime lastUpdateTime;

//    /**
//     * 最后修改网点code
//     */
//
//    private String lastUpdateNetworkCode;
//
//    /**
//     * 最后修改网点名称
//     */
//
//    private String lastUpdateNetworkName;
//
//    /**
//     * 最后修改人员code
//     */
//
//    private String lastUpdateStaffCode;
//
//    /**
//     * 最后修改人员名称
//     */
//
//    private String lastUpdateStaffName;

    /*
     *//**
     * 同步ES更新时间
     *//*

    private LocalDateTime lastUpdateTimeSync;*/

    /**
     * 是否删除,1未删除，2已删除
     */

    private Integer isDelete;

    private Integer dispatchNetworkId;

    private Integer signNetworkId;

//    private Integer abnormalRegNetworkId;

    private Integer pickNetworkId;

    private Integer pickFinanceId;

//    private Integer dispatchFinanceId;

//    private Integer inputNetworkId;

//    private Integer lastUpdateNetworkId;

//    private String goodsUrl;

    private BigDecimal settlementWeight;

//    private Integer signFinanceId;

    private String signFinanceCode;

    private String signFinanceName;

    //订单来源编码(现阶段主要用于小程序业务判断等等)
    private String orderSourceCode;

    private LocalDateTime refundTime;

    /**
     * 三段码
     **/

    private String terminalDispatchCode;

//    /**
//     * 三段码准确度
//     **/
//
//    private Boolean accurate;

//    /**
//     * 智能设备数据更新时间
//     */
//    private LocalDateTime smartDeviceDataUpdateTime;

    /**
     * 寄/收件人信息数据是否明文
     */
    private Boolean isPlaintext;

//    /**
//     * 用于日志记录
//     */
//    private String traceId;

//    /**
//     * 检查方式
//     */
//    private String inspectionMethod;

    /**
     * 是否残缺 1残缺、0完整
     */
    private Integer missFlag;

    /**
     * 客户商务件类型，1商务件 2非商务件
     */
    private Integer isBusiness;

    /**
     * 运单重量
     * http://jmt.yl-scm.com/task-view-985.html
     */
    private BigDecimal waybillWeight;

    /**
     * 是否已收款 1已收款，2未收款，10收款中
     */
    private Integer isReceive;

    /**
     * 费用异常类型：1:计费重量为0、2:未获取到始发地、3:未获取到目的地、4:未获取到报价、5:产品类型为空、6:物品类型为空、7:服务方式为空、8:正常计费
     */
    private Integer calculateFeeCode;

    /**
     * 费用异常信息
     */
    private String calculateFeeDesc;

    /**
     * 是否打印客户存根 0否，1是
     */
    private Integer printerCounterfoil;

    /**
     * 报价类型 报价标示 0:跨省件,1同城件,2同省件
     */
    private Integer quotetypeCode;



}
