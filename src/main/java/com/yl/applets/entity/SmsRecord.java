package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 短信记录
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SmsRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 所属国家
     */
    private Integer countryId;

    /**
     * 是否启用:1启用,2不启用
     */
    private Integer isEnable;

    /**
     * 是否删除:1未删除,2已删除
     */
    private Integer isDelete;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 最后更新人ID
     */
    private Long updateBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 最后修改人名称
     */
    private String updateByName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    private String version;

    /**
     * 排序
     */
    private Integer sort;


}
