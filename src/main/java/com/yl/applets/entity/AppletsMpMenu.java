package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * @Description:
 * @CreateDate: Created in {2021年9月3日11:45:46}
 * @Author: hhf
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_APPLETS_WX_MP_MENU")
public class AppletsMpMenu implements Serializable {

    private static final long serialVersionUID = 1564321L;
    /**
     * 等级序号
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;


    /**
     * 创建人
     */
    private Integer createBy;

    /**
     * 更新人
     */
    private Integer updateBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 0未删除，1已删除
     */
    private Integer isDelete;

    /**
     * 菜单json
     */
    private String menuJson;

    /**
     * 发送消息：内容
     */
    private String textContent;

    /**
     * 素材内容
     */
    private String mediaContent;
}
