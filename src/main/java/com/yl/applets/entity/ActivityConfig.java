package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动管理配置
 * @author: xiongweibin
 * @create: 2020-08-24 11:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_APPLETS_ACTIVITY_CONFIG")
public class ActivityConfig implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    /**
     * 编号
     */
    private String code;
    /**
     * 标题
     */
    private String title;
    /**
     * 类型 1:小程序开屏 2:Banner
     */
    private Integer type;
    /**
     * 顺序位 1:一位 2:二位 3:三位
     */
    private String sort;
    /**
     * 图片地址
     */
    private String imagePath;

    /**
     * 活动链接
     */
    private String activityLine;

    /**
     * 小程序appId
     */
    private String wxAppId;
    /**
     * 状态 1:待发布 2:发布中 3:已下线
     */
    private Integer status;
    /**
     * 上线时间
     */
    private LocalDateTime onTime;
    /**
     * 下线时间
     */
    private LocalDateTime offTime;
    /**
     * 发布时间
     */
    private LocalDateTime releaseTime;
    /**
     * 版本号
     */
    private Integer versionNo;
    /**
     * 逻辑删除
     */
    private Integer isDelete;
    /**
     * 创建人code
     */
    private String createByCode;
    /**
     * 创建人
     */
    private String createByName;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新人code
     */
    private String updateByCode;
    /**
     * 更新人
     */
    private String updateByName;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 访问数量
     */
    private Long visitQuantity;
    /**
     * 曝光数量
     */
    private Long exposureQuantity;
    /**
     * news文字内容
     */
    private String newsText;
    /**
     * news按钮内容
     */
    private String buttonText;
    /**
     * 按钮跳转方式 1:链接 2:小程序页面
     */
    private Integer buttonWay;

    //是否有地址
    private Integer isArea;

}
