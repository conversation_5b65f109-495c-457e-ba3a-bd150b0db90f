package com.yl.applets.entity.lmdm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yl.applets.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 区域信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_area")
public class SysArea extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     *父级ID-所属区域
     */
    private Integer parentId;

    /**
     *区域类型
     */
    private Integer type;

    /**
     *所属国家
     */
    private Integer countryId;

    /**
     *区域编码
     */
    private String code;

    /**
     *国际编码
     */
    private String internationalCode;

    /**
     *区号
     */
    private String areaNo;

    /**
     *三字码
     */
    private String threeCode;

    /**
     *中文名称
     */
    private String cnName;

    /**
     *英文名称
     */
    private String enName;

    /**
     *母语名称
     */
    private String nativeName;

    /**
     *邮政编码
     */
    private String zipcode;

    /**
     *简称
     */
    private String abbreviation;

    /**
     *经度
     */
    private String longitude;

    /**
     *纬度
     */
    private String latitude;

    /**
     *是否启用
     */
    private Integer isEnable;

    /**
     *是否删除
     */
    private Integer isDelete;

    /**
     *版本号
     */
    private String version;

    /**
     *排序
     */
    private Integer sort;

    /**
     *大头笔名称
     */
    private String dtbName;

    /**
     *高德编码
     */
    private String adCode;
}

