/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: SysNetwork
 * Author:   luhong
 * Date:     2020-10-13 16:07
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.entity.lmdm;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.yl.applets.dto.lmdm.SysDistributionDTO;
import com.yl.applets.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-13
 * @since 1.0.0
 */
@TableName("sys_network")
@Data
public class SysNetwork extends BaseEntity {
    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Integer id;
    @ApiModelProperty(
            name = "name",
            value = "名称"
    )
    private String name;
    @ApiModelProperty(
            name = "simpleName",
            value = "简称"
    )
    private String simpleName;
    @ApiModelProperty(
            name = "code",
            value = "编号"
    )
    private String code;
    @ApiModelProperty(
            name = "signatureCode",
            value = "特征码"
    )
    private String signatureCode;
    @ApiModelProperty(
            name = "cnName",
            value = "中文名称"
    )
    private String cnName;
    @ApiModelProperty(
            name = "enName",
            value = "英文名称"
    )
    private String enName;
    @ApiModelProperty(
            name = "countryId",
            value = "所属国家id"
    )
    private Integer countryId;
    @ApiModelProperty(
            name = "countryCode",
            value = "所属国家编码"
    )
    private String countryCode;
    @ApiModelProperty(
            name = "countryDesc",
            value = "所属国家描述"
    )
    private String countryDesc;
    @ApiModelProperty(
            name = "countryThreeCode",
            value = "所属国家三字码"
    )
    private String countryThreeCode;
    @ApiModelProperty(
            name = "regionalId",
            value = "大区ID"
    )
    private Integer regionalId;
    @ApiModelProperty(
            name = "regionalDesc",
            value = "大区名称"
    )
    private String regionalDesc;
    @ApiModelProperty(
            name = "areaInfoId",
            value = "片区ID"
    )
    private Integer areaInfoId;
    @ApiModelProperty(
            name = "areaInfoDesc",
            value = "片区描述"
    )
    private String areaInfoDesc;
    @ApiModelProperty(
            name = "providerId",
            value = "省份ID"
    )
    private Integer providerId;
    @ApiModelProperty(
            name = "providerDesc",
            value = "省份描述"
    )
    private String providerDesc;
    @ApiModelProperty(
            name = "cityId",
            value = "城市ID"
    )
    private Integer cityId;
    @ApiModelProperty(
            name = "cityDesc",
            value = "城市描述"
    )
    private String cityDesc;
    @ApiModelProperty(
            name = "areaId",
            value = "区/县ID"
    )
    private Integer areaId;
    @ApiModelProperty(
            name = "areaDesc",
            value = "区/县描述"
    )
    private String areaDesc;
    @ApiModelProperty(
            name = "principal",
            value = "负责人"
    )
    private String principal;
    @ApiModelProperty(
            name = "mobile",
            value = "手机号码"
    )
    private String mobile;
    @ApiModelProperty(
            name = "telephone",
            value = "座机号"
    )
    private String telephone;
    @ApiModelProperty(
            name = "parentNetworkId",
            value = "所属网点ID"
    )
    private Integer parentNetworkId;
    @ApiModelProperty(
            name = "parentNetworkCode",
            value = "所属网点编码"
    )
    private String parentNetworkCode;
    @ApiModelProperty(
            name = "parentNetworkName",
            value = "所属网点名称"
    )
    private String parentNetworkName;
    @ApiModelProperty(
            name = "financialCenterId",
            value = "财务中心ID"
    )
    private Integer financialCenterId;
    @ApiModelProperty(
            name = "financialCenterCode",
            value = "财务中心Code"
    )
    private String financialCenterCode;
    @ApiModelProperty(
            name = "financialCenterDesc",
            value = "财务中心名称"
    )
    private String financialCenterDesc;
    @ApiModelProperty(
            name = "functionTypeId",
            value = "功能类型ID"
    )
    private Integer functionTypeId;
    @ApiModelProperty(
            name = "functionTypeDesc",
            value = "功能类型描述"
    )
    private String functionTypeDesc;
    @ApiModelProperty(
            name = "typeId",
            value = "网点类型ID"
    )
    private Integer typeId;
    @ApiModelProperty(
            name = "typeDesc",
            value = "网点类型描述"
    )
    private String typeDesc;
    @ApiModelProperty(
            name = "institutionalLevelId",
            value = "机构级别ID"
    )
    private Integer institutionalLevelId;
    @ApiModelProperty(
            name = "institutionalLevelDesc",
            value = "机构级别描述"
    )
    private String institutionalLevelDesc;
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @ApiModelProperty(
            name = "maxCollectionMoney",
            value = "代收货款上限"
    )
    private Long maxCollectionMoney;
    @ApiModelProperty(
            name = "settlementDestinationId",
            value = "结算目的地ID"
    )
    private Integer settlementDestinationId;
    @ApiModelProperty(
            name = "settlementDestinationDesc",
            value = "结算目的地描述"
    )
    private String settlementDestinationDesc;
    @ApiModelProperty(
            name = "isCollectionIdentifier",
            value = "代收货款标识"
    )
    private Integer isCollectionIdentifier;
    @ApiModelProperty(
            name = "isDistributionCenter",
            value = "是否分拨中心标识"
    )
    private Integer isDistributionCenter;
    @ApiModelProperty(
            name = "businessModelId",
            value = "经营方式ID"
    )
    private Integer businessModelId;
    @ApiModelProperty(
            name = "businessModelDesc",
            value = "经营方式描述"
    )
    private String businessModelDesc;
    @ApiModelProperty(
            name = "localCurrencyId",
            value = "本位币ID"
    )
    private Integer localCurrencyId;
    @ApiModelProperty(
            name = "localCurrencyCode",
            value = "本位币编码"
    )
    private String localCurrencyCode;
    @ApiModelProperty(
            name = "localCurrencyDesc",
            value = "本位币描述"
    )
    private String localCurrencyDesc;
    @ApiModelProperty(
            name = "isFinancialCenter",
            value = "财务中心标识"
    )
    private Integer isFinancialCenter;
    @ApiModelProperty(
            name = "isWaybillIdentification",
            value = "运单发放标识"
    )
    private Integer isWaybillIdentification;
    @ApiModelProperty(
            name = "isToPayment",
            value = "到付款标识"
    )
    private Integer isToPayment;
    @ApiModelProperty(
            name = "isVisibility",
            value = "是否对外可见:1是,2否"
    )
    private Integer isVisibility;
    @ApiModelProperty(
            name = "isEnable",
            value = "是否启用"
    )
    private Integer isEnable;
    @ApiModelProperty(
            name = "isLeaf",
            value = "是否叶子节点"
    )
    private Integer isLeaf;
    @ApiModelProperty(
            name = "isDelete",
            value = "是否删除"
    )
    private Integer isDelete;
    @ApiModelProperty(
            value = "详细地址",
            name = "address"
    )
    private String address;
    @ApiModelProperty(
            name = "businessPrincipal",
            value = "业务负责人"
    )
    private String businessPrincipal;
    @ApiModelProperty(
            name = "businessTelephone",
            value = "业务电话"
    )
    private String businessTelephone;
    @ApiModelProperty(
            name = "customerServicePrincipal",
            value = "客服负责人"
    )
    private String customerServicePrincipal;
    @ApiModelProperty(
            name = "customerServiceTelephone",
            value = "客服电话"
    )
    private String customerServiceTelephone;
    @ApiModelProperty(
            name = "longitude",
            value = "经度"
    )
    private String longitude;
    @ApiModelProperty(
            name = "latitude",
            value = "纬度"
    )
    private String latitude;
    @ApiModelProperty(
            name = "version",
            value = "版本号"
    )
    private String version;
    @ApiModelProperty(
            name = "sort",
            value = "排序"
    )
    private Integer sort;
    @ApiModelProperty(
            name = "remark",
            value = "备注"
    )
    private String remark;
    @ApiModelProperty(
            name = "path",
            value = "网点路径"
    )
    private String path;
    @ApiModelProperty(
            name = "isReceiving",
            value = "是否有收货电子围栏"
    )
    private Integer isReceiving;
    @ApiModelProperty(
            name = "isDispatch",
            value = "是否有派货电子围栏"
    )
    private Integer isDispatch;
    @ApiModelProperty(
            name = "zipCode",
            value = "邮编"
    )
    private String zipCode;
    @ApiModelProperty(
            name = "startBusinessTime",
            value = "开始营业时间"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @JSONField(
            format = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonDeserialize(
            using = LocalDateTimeDeserializer.class
    )
    @JsonSerialize(
            using = LocalDateTimeSerializer.class
    )
    private LocalDateTime startBusinessTime;
    @ApiModelProperty(
            name = "endBusinessTime",
            value = "结束营业时间"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @JSONField(
            format = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonDeserialize(
            using = LocalDateTimeDeserializer.class
    )
    @JsonSerialize(
            using = LocalDateTimeSerializer.class
    )
    private LocalDateTime endBusinessTime;
    @ApiModelProperty(
            name = "coordinate",
            value = "经纬度"
    )
    private String coordinate;
    @ApiModelProperty(
            name = "subordinateAgent",
            value = "所属代理"
    )
    private String subordinateAgent;
    @ApiModelProperty(
            name = "isFirstNetworkSettle",
            value = "是否一级网点标识"
    )
    private Integer isFirstNetworkSettle;
    @TableField(
            exist = false
    )
    @ApiModelProperty(
            name = "distributionList",
            value = "交货分拨"
    )
    private List<SysDistributionDTO> distributionList;
    @ApiModelProperty(
            name = "isFirstFranchisee",
            value = "一级加盟商"
    )
    private Integer isFirstFranchisee;
    @ApiModelProperty(
            name = "isSecondFranchisee",
            value = "二级加盟商"
    )
    private Integer isSecondFranchisee;
    @ApiModelProperty(
            name = "isEntrepot",
            value = "集散"
    )
    private Integer isEntrepot;
    @ApiModelProperty(
            name = "deliveryRange",
            value = "特殊区域"
    )
    @TableField(
            exist = false
    )
    private String deliveryRange;
    @ApiModelProperty(
            name = "receivingRange",
            value = "收件范围"
    )
    @TableField(
            exist = false
    )
    private String receivingRange;
    @ApiModelProperty(
            name = "nonDrRange",
            value = "不派件范围"
    )
    @TableField(
            exist = false
    )
    private String nonDrRange;
}