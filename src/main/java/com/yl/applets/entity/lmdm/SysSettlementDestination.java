/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: SysSettlementDestination
 * Author:   luhong
 * Date:     2020-10-13 15:51
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.entity.lmdm;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.Version;
import com.yl.applets.entity.BaseEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-13
 * @since 1.0.0
 */
@Data
public class SysSettlementDestination extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer id;
    private String name;
    private String code;
    private Integer countryId;
    private Integer providerId;
    private Integer cityId;
    private Integer regionalId;
    private Integer areaId;
    @TableField(
            value = "service_outlet_id",
            strategy = FieldStrategy.IGNORED
    )
    private Integer serviceOutletId;
    private Integer isPay;
    private Integer isCollectionId;
    @TableField(
            value = "distribution_id",
            strategy = FieldStrategy.IGNORED
    )
    private Integer distributionId;
    private Integer isReceipt;
    private Integer expressType;
    private Integer isEnable;
    private Integer isDelete;
    @Version
    private Integer version;
    private Integer sort;
}