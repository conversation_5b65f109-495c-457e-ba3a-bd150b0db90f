/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: SysAbnormalPiece
 * Author:   luhong
 * Date:     2020-10-13 15:09
 * Description: 基础数据api
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.entity.lmdm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yl.applets.entity.BaseEntity;
import lombok.Data;

/**
 * 〈一句话功能简述〉<br> 
 * 〈基础数据api〉
 *
 * <AUTHOR>
 * @create 2020-10-13
 * @since 1.0.0
 */
@TableName("sys_abnormal_piece")
@Data
public class SysAbnormalPiece extends BaseEntity {
    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Integer id;
    private String name;
    private String cnName;
    private String enName;
    private Integer countryId;
    private Integer operatorType;
    private String code;
    private String detailedDescription;
    private Integer isProducerOrder;
    private Integer isEnable;
    private Integer isDelete;
    private String version;
    private Integer sort;
}