package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 专属会员
 * </p>
 *
 * <AUTHOR> @since 2020-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_APPLETS_MY_STAFF")
public class MyStaff implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 网点ID
     */
    private Integer networkId;

    /**
     * 网点编码
     */
    private String networkCode;

    /**
     * 网点名称
     */
    private String networkName;

    /**
     * 业务员号
     */
    private String staffNo;

    /**
     * 开始营业时间
     */
    private String startBusinessTime;

    /**
     * 结束营业时间
     */
    private String endBusinessTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    /**
     * 是否专属
     * 1.是 2.否
     */
    private Integer isExclusive;

    /**
     * 1.微信
     * 2.支付宝
     */
    private Integer userChannel;

    /**
     * 默认专属快递员 0 否 1是
     */
    private Integer defaultNum;


}
