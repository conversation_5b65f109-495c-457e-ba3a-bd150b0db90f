package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：活动奖项表
 * @Author： zhanzhihong
 * @Date： 2022/08/08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_LUCK_DRAW_ITEM")
public class LuckDrawItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.INPUT)
    @JsonSerialize(using = ToStringSerializer.class)

    /**
     * 主键id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 活动编号
     */
    private String activityCode;

    /**
     * 奖项等级
     */
    private Integer itemLevel;

    /**
     * 奖项类型
     */
    private Integer itemType;

    /**
     * 奖品名称
     */
    private String name;

    /**
     * 奖品数量
     */
    private Integer amount;

    /**
     * 中奖比例
     */
    private Integer rate;

    /**
     * 奖品图片地址
     */
    private String imgUrl;

    /**
     * 中奖图片
     */
    private String winImgUrl;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 奖品剩余数
     */
    private Integer remainder;
}
