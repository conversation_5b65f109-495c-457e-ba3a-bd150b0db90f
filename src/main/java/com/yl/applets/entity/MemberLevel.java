package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * @Description:
 * @CreateDate: Created in {2021/7/8 16:50}
 * @Author: DongFeixiang
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_APPLETS_MEMBER_LEVEL")
public class MemberLevel implements Serializable {

    private static final long serialVersionUID = 1564321L;
    /**
     * 等级序号
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 用户等级
     */
    private Integer memberLevel;

    /**
     * 等级名称
     */

    private String levelName;

    /**
     * 成长值开始范围
     */
    private Long growScoreStart;

    /**
     * 成长值结束范围
     */
    private Long growScoreEnd;

    /**
     * 创建人
     */
    private Integer creatBy;

    /**
     * 创建时间
     */
    private LocalDateTime creatTime;

    /**
     * 更新人
     */
    private Integer updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 0未删除，1已删除
     */
    private Integer isDelete;
}
