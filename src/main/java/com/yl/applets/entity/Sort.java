/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: Sort
 * Author:   luhong
 * Date:     2020-11-30 16:53
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.entity;

import com.yl.applets.enums.SortEnum;
import lombok.Data;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-11-30
 * @since 1.0.0
 */
@Data
public class Sort {
    private String field;
    private SortEnum sortEnum;
    private Boolean isDistanceSort = false;
    private String unit = "m";
    private String coordinate;

    public Sort(String field) {
        this.field = field;
    }

    public Sort(String field, SortEnum sortEnum) {
        this.field = field;
        this.sortEnum = sortEnum;
    }

    public Sort(String field, SortEnum sortEnum, Boolean isDistanceSort, String unit, String coordinate) {
        this.field = field;
        this.sortEnum = sortEnum;
        this.isDistanceSort = isDistanceSort;
        this.unit = unit;
        this.coordinate = coordinate;
    }
}