package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * @Description: 用户管理-用户成长值/积分值配置表
 * @CreateDate: Created in {2021/7/16 14:49}
 * @Author: DongFeixiang
 */
@Data
@TableName("YL_MEMBER_SCORE_CONFIG")
public class MemberScoreConfig {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 用户行为
     */
    private String memberAction;

    /**
     * 用户端-渠道
     */
    private Integer type;
    /**
     * 成长分值/次
     */
    private Long growValue;
    /**
     * 成长限制/日
     */
    private Long growLimit;
    /**
     * 积分分值/次
     */
    private Long scoreValue;
    /**
     * 积分限制次数
     */
    private Long scoreLimit;
    /**
     * 创建时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /**
     * 创建人id
     */
    private Integer createBy;
    /**
     * 更新人id
     */
    private Integer updateBy;
    /**
     * 是否删除,0:未删除，1:已删除
     */
    private Integer isDelete;
}
