package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021<br>
 *
 * @description: 小程序自动回复规则
 * @author: caiyao
 * @create: 2021-05-20 10:11
 */
@Data
@TableName("YL_APPLETS_AUTO_REPLY")
public class AutoReply implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 回复类型 1:关注回复 2:收到消息回复 3:关键词回复
     */
    private Integer replyType;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 关键词
     */
    private String keyWord;
    ;

    /**
     * 回复方式 1:回复全部 2:随机回复一条
     */
    private Integer replyMode;

    /**
     * 回复内容
     */
    private String replyContent;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
