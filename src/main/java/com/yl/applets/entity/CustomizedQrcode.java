package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 定制二维码表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_CUSTOMIZED_QRCODE")
public class CustomizedQrcode implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableField("ID")
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;

    /**
     * 二维码唯一code
     */
    @TableField("QR_CODE")
    private String qrCode;

    /**
     * 标题
     */
    @TableField("TITLE")
    private String title;

    /**
     * 说明
     */
    @TableField("REMARKS")
    private String remarks;

    /**
     * 寄件省id
     */
    @TableField("SENDER_PROVINCE_ID")
    private String senderProvinceId;

    /**
     * 寄件省名字
     */
    @TableField("SENDER_PROVINCE_NAME")
    private String senderProvinceName;

    /**
     * 寄件市id
     */
    @TableField("SENDER_CITY_ID")
    private String senderCityId;

    /**
     * 寄件市名字
     */
    @TableField("SENDER_CITY_NAME")
    private String senderCityName;

    /**
     * 寄件区id
     */
    @TableField("SENDER_ZONE_ID")
    private String senderZoneId;

    /**
     * 寄件区名字
     */
    @TableField("SENDER_ZONE_NAME")
    private String senderZoneName;

    /**
     * 寄件详细地址
     */
    @TableField("SENDER_ADDRESS")
    private String senderAddress;

    /**
     * 结束时间
     */
    @TableField("END_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 寄件网点id
     */
    @TableField("SENDER_NETWORK_ID")
    private String senderNetworkId;

    /**
     * 寄件网点名字
     */
    @TableField("SENDER_NETWORK_NAME")
    private String senderNetworkName;

    /**
     * 收派员id
     */
    @TableField("SENDER_STAFF_CODE")
    private String senderStaffCode;

    /**
     * 收派员名字
     */
    @TableField("SENDER_STAFF_NAME")
    private String senderStaffName;

    @TableField("QR_URL")
    private String qrUrl;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableField("IS_DELETE")
    private Integer isDelete;


    /**
     * 扫码次数
     */
    @TableField("SCAN_NUM")
    private Integer scanNum;

    /**
     * 创建人id
     */
    @TableField("CREATE_BY")
    private Long createBy;

    /**
     * 创建人name
     */
    @TableField("CREATE_NAME")
    private String createName;

    /**
     * 修改人id
     */
    @TableField("UPDATE_BY")
    private Long updateBy;


    /**
     * 修改人name
     */
    @TableField("UPDATE_NAME")
    private String updateName;

}
