package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 开票抬头表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_APPLETS_INVOICE_TITLE")
@KeySequence(value="SEQ_APPLETS_INVOICE_TITLE",clazz=Integer.class)
public class InvoiceTitle implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    /**
     * user_id
     */
    private Integer userId;

    /**
     * 类型1企业2个人
     */
    private Integer type;

    /**
     * 税号
     */
    private String taxNumber;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 发票抬头
     */
    private String name;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


}
