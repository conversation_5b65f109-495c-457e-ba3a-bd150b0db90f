package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_APPLETS_CUSTOMER_PRINT_DATA")
public class CustomerPrintData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;

    /**
     *
     */
    private Integer customerId;

    /**
     * 客服code
     */
    private String code;

    /**
     * 客户名称
     */
    private String name;

    /**
     * 客户所属网点id
     */
    private String networkId;

    /**
     * 客户所属网点code
     */
    private String networkCode;

    /**
     * 客户所属网点名称
     */
    private String networkName;

    /**
     *  打印机设备编码
     */
    private String printerEquipmentNumber;

    /**
     * 打印机设备厂商
     */
    private String printerEquipmentManufacturer;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    private LocalDateTime updateTime;

    /**
     * 设备id
     */
    private Integer printerEquipmentId;

    /**
     * 设备名称
     */
    private String printerEquipmentName;


}
