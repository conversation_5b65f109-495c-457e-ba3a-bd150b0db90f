package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 小程序用户
 * </p>
 *
 * <AUTHOR> @since 2019-09-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_MEMBER_USER")
@JsonInclude(JsonInclude.Include.NON_NULL)//为null字段不展示该字段
public class MemberUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * unionid
     */
    private String unionid;


    private String city;


    private String province;

    /**
     * openid
     */
    private String openid;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 头像
     */
    private String avatarUrl;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     *  0未删除，1已删除
     */
    private Integer isDelete;

    /**
     * 渠道类型(详见：ChannelSourceEnum)
     */
    private Integer type;

    /**
     * 下单使用的id
     */
    private Integer memberId;

    //区
    private String zone;

    //设备信息
    private String equipmentInfo;

    //创建人
    private Long createBy;

    //更新人
    private Long updateBy;

    //成长值
    private Long growValue;

    //积分值
    private Long scoreValue;

    //会员等级
    private Integer memberLevel;

    //会员过期时间
    private LocalDateTime memberExpiresTime;

}
