package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 地址管理
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_APPLETS_ADDRESS")
public class Address implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 省份id
     */
    private Integer provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区域id
     */
    private Integer areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 地址类型 1寄件 2收件
     */
    private Integer type;

    /**
     * 详细地址
     */
    private String detailedAddress;

    /**
     * 是否默认地址:1是,2否
     */
    private Integer isDefault;

    /**
     * 所属国家
     */
    private Integer countryId;

    /**
     * 是否启用:1启用,2不启用
     */
    private Integer isEnable;

    /**
     * 是否删除:1未删除,2已删除
     */
    private Integer isDelete;

    /**
     * 创建人ID
     */
    private Integer createBy;

    /**
     * 最后更新人ID
     */
    private Integer updateBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 最后修改人名称
     */
    private String updateByName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    private String version;

    /**
     * 排序
     */
    private Integer sort;

    //地址标签
    @TableField(value = "address_sign", strategy= FieldStrategy.IGNORED)
    private Long addressSign;

}
