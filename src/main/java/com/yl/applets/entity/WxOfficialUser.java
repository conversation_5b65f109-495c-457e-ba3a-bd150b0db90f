/**
 * Copyright (C), 2015-2021, 云路供应链科技有限公司
 * FileName: WxOfficialUser
 * Author:   luhong
 * Date:     2021-03-31 18:02
 * Description: 微信公众号用户
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 〈一句话功能简述〉<br> 
 * 〈微信公众号用户〉
 *
 * <AUTHOR>
 * @create 2021-03-31
 * @since 1.0.0
 */
@Data
@TableName("YL_APPLETS_OFFICIAL_USER")
public class WxOfficialUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    private String openid;

    private String unionid;

    private String nickName;

    private Integer sex;

    private String  country;

    private String  province;

    private String  city;

    private String  headImgUrl;

    /**
     * 订阅时间戳
     */
    private Long  subscribeTime;

    /**
     * ADD_SCENE_SEARCH 公众号搜索，
     * ADD_SCENE_ACCOUNT_MIGRATION 公众号迁移，
     * ADD_SCENE_PROFILE_CARD 名片分享，
     * ADD_SCENE_QR_CODE 扫描二维码，
     * ADD_SCENE_PROFILE_LINK 图文页内名称点击，
     * ADD_SCENE_PROFILE_ITEM 图文页右上角菜单，
     * ADD_SCENE_PAID 支付后关注，
     * ADD_SCENE_WECHAT_ADVERTISEMENT 微信广告，
     * ADD_SCENE_OTHERS 其他
     */
    private String  subscribeScene;

    /**
     * 1订阅
     */
    private Integer subscribe;

    /**
     * 取消关注时间
     */
    private LocalDateTime unsubscribeTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


}