package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2023/3/9 11:30
 * @description 市场活动表
 */

@ApiModel(description = "市场活动表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "YL_MARKET_ACTIVITY")
public class MarketActivity implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID")
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 活动编码：从J00000开始
     */
    @TableField(value = "CODE")
    @ApiModelProperty(value = "活动编码：从J00000开始")
    private String code;

    /**
     * 活动名称
     */
    @TableField(value = "\"NAME\"")
    @ApiModelProperty(value = "活动名称")
    private String name;

    /**
     * 活动类型: 1:大客户拉新活动
     */
    @TableField(value = "TYPE")
    @ApiModelProperty(value = "活动类型: 1:大客户拉新活动")
    private Integer type;

    /**
     * 活动介绍
     */
    @TableField(value = "INTRODUCTION")
    @ApiModelProperty(value = "活动介绍")
    private String introduction;

    /**
     * 0: 禁用，1: 启用
     */
    @TableField(value = "STATUS")
    @ApiModelProperty(value = "0: 禁用，1: 启用")
    private Integer status;

    /**
     * 开始时间
     */
    @TableField(value = "START_TIME")
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField(value = "END_TIME")
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATE_BY")
    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @TableField(value = "CREATE_BY_NAME")
    @ApiModelProperty(value = "创建人姓名")
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(value = "UPDATE_BY")
    @ApiModelProperty(value = "更新人ID")
    private Long updateBy;

    /**
     * 更新人姓名
     */
    @TableField(value = "UPDATE_BY_NAME")
    @ApiModelProperty(value = "更新人姓名")
    private String updateByName;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}