package com.yl.applets.entity.promotion;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 优惠劵分发表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("YL_CUST_PROMOTION_DISTRIBUTE")
@ApiModel(value = "CustPromotionDistribute对象", description = "优惠劵分发表")
public class CustPromotionDistribute implements Serializable {


    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "优惠劵id")
    private Long proId;

    @ApiModelProperty(value = "优惠券CODE")
    private String proCode;

    @ApiModelProperty(value = "申请编码")
    private String applyCode;

    @ApiModelProperty(value = "优惠券标题")
    private String couponTitle;

    @ApiModelProperty(value = "代理区名字")
    private String agencyName;

    @ApiModelProperty(value = "代理区ID")
    private Long agencyId;

    @ApiModelProperty(value = "领取条件类型0.无门槛1.数字2.仅一张")
    private Integer getCondition;

    @ApiModelProperty(value = "领取条件参数")
    private Integer getValue;

    @ApiModelProperty(value = "使用条件0.无门槛1.数字2.仅一张")
    private Integer useCondition;

    @ApiModelProperty(value = "使用条件参数")
    private Integer useValue;

    @ApiModelProperty(value = "优惠类型1.直减2.折扣")
    private Integer couponType;

    @ApiModelProperty(value = "优惠类型参数或内容")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "优惠限制")
    private BigDecimal couponLimit;

    @ApiModelProperty(value = "优惠券总数")
    private Integer couponCount;

    @ApiModelProperty(value = "领取数量")
    private Integer beReceiveCount;

    @ApiModelProperty(value = "使用数量")
    private Integer beUsedCount;

    @ApiModelProperty(value = "核销数量")
    private Integer beFinishCount;

    @ApiModelProperty(value = "失效数量")
    private Integer beInvalidCount;

    @ApiModelProperty(value = "发放时间")
    private LocalDateTime grantTime;

    @ApiModelProperty(value = "发放截止时间")
    private LocalDateTime grantEndTime;

    @ApiModelProperty(value = "优惠券使用生效时间类型")
    private Integer couponTimeType;

    @ApiModelProperty(value = "优惠券生效开始时间")
    private LocalDateTime couponValidTime;

    @ApiModelProperty(value = "优惠券生效截止时间")
    private LocalDateTime couponInvalidTime;

    @ApiModelProperty(value = "优惠券领取之后有效天数")
    private Integer couponTimeValue;

    @ApiModelProperty(value = "用途")
    private String purpose;

    @ApiModelProperty(value = "支付方式1.微信支付")
    private Integer payMode;

    @ApiModelProperty(value = "创建人ID")
    private Long createUserid;

    @ApiModelProperty(value = "创建人名字")
    private String createUsername;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "网点id")
    private Long networkId;

    @ApiModelProperty(value = "网点名称")
    private String networkName;

    @ApiModelProperty(value = "网点code")
    private String networkCode;

    @ApiModelProperty(value = "代理区code")
    private String agencyCode;

    @ApiModelProperty(value = "是否停止发放优惠劵，触发了风控 1是2否")
    private Integer isStop;

    @ApiModelProperty(value = "优惠劵状态")
    private Integer status;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "发放主体")
    private Long grantSubject;

    private Long couponStartTimeValue;


}
