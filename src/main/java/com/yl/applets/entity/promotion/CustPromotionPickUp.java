package com.yl.applets.entity.promotion;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 优惠劵限制收寄地址范围表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("YL_CUST_PROMOTION_PICK_UP")
@ApiModel(value = "CustPromotionPickUp对象", description = "优惠劵限制收寄地址范围表")
public class CustPromotionPickUp implements Serializable {


    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "优惠劵id")
    private Long proId;

    @ApiModelProperty(value = "城市id")
    private Long cityId;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "网点id")
    private Long networkId;

    @ApiModelProperty(value = "网点名称")
    private String netwrokName;

    @ApiModelProperty(value = "网点code")
    private String networkCode;

    @ApiModelProperty(value = "类型 收1 寄2")
    private Integer puType;

    @ApiModelProperty(value = "代理区id")
    private Long agencyId;

    @ApiModelProperty(value = "代理区")
    private String agencyName;

    @ApiModelProperty(value = "区域id")
    private String districtId;

    @ApiModelProperty(value = "区域名称")
    private String districtName;


}
