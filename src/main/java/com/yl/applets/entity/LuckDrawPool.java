package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：活动奖项表
 * @Author： zhanzhihong
 * @Date： 2022/08/08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_LUCK_DRAW_POOL")
public class LuckDrawPool implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.INPUT)
    /**
     * 主键id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 奖品id
     */
    private Long itemId;

    /**
     * 会员id
     */
    private Integer numberId;

    /**
     * 奖品数量
     */
    private Integer prizeNum;

    /**
     * 散列中奖时间
     */
    private String encryReleaseTime;


    private String day;

    private LocalDateTime getTime;



}
