package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 小程序用户
 * </p>
 *
 * <AUTHOR> @since 2019-09-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_APPLETS_WX_USER")
@KeySequence(value = "seq_applets_user")
public class WxUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    /**
     * unionid
     */
    private String unionid;


    private String city;


    private String province;

    /**
     * openid
     */
    private String openid;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 头像
     */
    private String avatarUrl;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     *  0未删除，1已删除
     */
    private Integer isDelete;
    /**
     * 渠道类型（H5:1，微信：2，百度：3）
     */
    private Integer type;

    /**
     * 下单使用的id
     */
    private Integer numberId;

    //区
    private String zone;

    //设备信息
    private String equipmentInfo;

    /**
     * 是否第一次点击兔优达（1:是：2:否）
     */
    @TableField(value = "IS_FIRST_RABBIT_DELIVERY")
    private Integer isFirstRabbitDelivery;



    /**
     * 集运的用户id
     */
    private Integer jyUserId;

}
