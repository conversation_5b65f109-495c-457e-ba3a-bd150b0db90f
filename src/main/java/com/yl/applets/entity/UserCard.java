package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户实名认证表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_APPLETS_USER_CARD")
public class UserCard implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     *     BUSINESS_LICENSE(1, "统一信用代码"),
     *     ORGANIZATION_CODE(2, "组织机构代码"),
     *     TAX_REGISTRATION(3, "税务登记号"),
     *     IDENTITY_CARD(4, "居民身份证"),
     *     INTERIM_IDENTITY_CARD(5, "临时身份证"),
     *     RESIDENCE_BOOKLET(6, "户口簿"),
     *     ID_CARD_OF_PLA_SOLDIERS(7, "中国人民解放军军人身份证"),
     *     ID_CARD_OF_CHINESE_ARMED_POLICE(8, "中国人民武装警察身份证"),
     *     HOME_RETURN_PERMIT(9, "港澳居民来往内地通行证"),
     *     TAIWAN_RETURN_PERMIT(10, "台湾居民来往内地通行证"),
     *     PASSPORT_OF_FOREIGN_CITIZEN(11, "外国公民护照"),
     *     CHINESE_PASSPORT(12, "中国公民护照");
     */
    private Integer cardType;

    /**
     * 查验方式 1手动输入2扫描上传
     */
    private Integer checkType;

    /**
     * 1审核通过2未通过
     */
    private Integer status;

    /**
     * 证件号
     */
    private String cardNum;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
