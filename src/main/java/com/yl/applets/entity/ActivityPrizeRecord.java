package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动管理配置
 * @author: xiongweibin
 * @create: 2020-08-24 11:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_ACTIVITY_PRIZE_RECORD")
public class ActivityPrizeRecord implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    /**
     * 抽奖记录id
     */
    private Long recordId;
    /**
     * 收货人手机
     */
    private String userMobile;
    /**
     *  收货人名字
     */
    private String userName;

    /**
     *  收货省
     */
    private String provinceName;

    /**
     *  收货省
     */
    private String provinceId;

    /**
     *  收货市
     */
    private String cityName;

    /**
     *  收货市
     */
    private String cityId;

    /**
     *  收货区
     */
    private String zoneName;

    /**
     *  收货区
     */
    private String zoneId;


    /**
     * 详细地址
     */
    private String detailAddress;


    /**
     * 运单号
     */
    private String waybillNo;

    /**
     * 新增时间
     */
    private LocalDateTime getTime;

}
