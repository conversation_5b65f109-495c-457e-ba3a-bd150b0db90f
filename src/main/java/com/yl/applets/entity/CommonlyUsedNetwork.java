package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 常用网点表
 * </p>
 *
 * <AUTHOR> @since 2020-03-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_APPLETS_OFTEN_NETWORK")
public class CommonlyUsedNetwork implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 网点id
     */
    private Long networkId;

    /**
     * 网点名称
     */
    private String networkName;

    /**
     * 网点联系手机号
     */
    private String mobile;

    /**
     * 网点联系座机号
     */
    private String telephone;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 开始营业时间
     */
    private String startBusinessTime;

    /**
     * 结束营业时间
     */
    private String endBusinessTime;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后寄件时间
     */
    private LocalDateTime updateTime;


}
