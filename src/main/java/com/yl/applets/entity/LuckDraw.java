package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：抽奖活动
 * @Author： zhanzhihong
 * @Date： 2022/08/08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_LUCK_DRAW")
public class LuckDraw implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.INPUT)
    /**
     * id
     */
    private Long id;

    /**
     * activity_name
     */
    private String activityName;

    /**
     * activity_code
     */
    private String activityCode;

    /**
     * activity_status
     */
    private Integer activityStatus;

    /**
     * remark
     */
    private String remark;

    /**
     * start_time
     */
    private LocalDateTime startTime;

    /**
     * end_time
     */
    private LocalDateTime endTime;

    /**
     * create_by
     */
    private String createBy;

    /**
     * create_time
     */
    private LocalDateTime createTime;

    /**
     * update_by
     */
    private String updateBy;

    /**
     * update_time
     */
    private LocalDateTime updateTime;

    /**
     * 活动参与端 1 = 微信,2 = app/浏览器
     */
    private Integer clientType;

}
