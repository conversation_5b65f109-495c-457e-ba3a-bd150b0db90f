package com.yl.applets.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：会员充值流水表
 * @Author： zhanzhihong
 * @Date： 2022/07/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("YL_MEMBER_RECHARGE_RECORD")
public class MemberRechargeRecord {

    @TableId(type = IdType.INPUT)
    /**
     * 主键id
     */
    private Long id;

    /**
     * 会员id
     */
    @ApiModelProperty(value = "会员id")
    private Integer memberId;

    /**
     * 会员手机号
     */
    @ApiModelProperty(value = "会员手机号")
    private String mobile;

    /**
     * 申请方式 1 = 充值
     */
    @ApiModelProperty(value = "申请方式 1 = 充值")
    private Integer applyType;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;

    /**
     * 申请状态：0 = 正常 1 = 失败
     */
    @ApiModelProperty(value = "申请状态：0 = 正常 1 = 失败")
    private Integer applyStatus;

    /**
     * 实付金额
     */
    @ApiModelProperty(value = "实付金额")
    private BigDecimal paidFee;

    /**
     * 会员时长(天)
     */
    @ApiModelProperty(value = "会员时长（天）")
    private Integer memberTime;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 到期时间
     */
    @ApiModelProperty(value = "到期时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 活动类型：1 = 0元领取会员
     */
    @ApiModelProperty(value = "活动类型：1 = 0元领取会员")
    private Integer activityType;
}
