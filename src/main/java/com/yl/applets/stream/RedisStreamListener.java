package com.yl.applets.stream;

import cn.binarywang.wx.miniapp.api.WxMaMsgService;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeData;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.binarywang.wx.miniapp.bean.WxMaTemplateData;
import cn.binarywang.wx.miniapp.bean.WxMaUniformMessage;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.yl.applets.config.WxMaConfiguration;
import com.yl.applets.config.WxMaProperties;
import com.yl.applets.dto.CouponDeadLineDTO;
import com.yl.applets.dto.CouponDeadLineMsgDTO;
import com.yl.applets.entity.WxOfficialUser;
import com.yl.applets.entity.WxUser;
import com.yl.applets.enums.ChannelSourceEnum;
import com.yl.applets.service.IWxUserService;
import com.yl.applets.service.WxOfficialUserService;
import com.yl.chaos.log.traceid.LogTranceId;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@EnableBinding(BcInputInterface.class)
public class RedisStreamListener {

    @Autowired
    private WxMaProperties wxMaProperties;

    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private WxOfficialUserService wxOfficialUserService;

    /**
     * 快递签收模板id
     */
    @Value("${wx.templateId.couponDeadMp:To2kPdIHkti2w1sbWYRA0cE8oyf7Q82f6J_hCVUU2JA}")
    private String signTemplateId;

    /**
     * 快递签收模板id
     */
    @Value("${wx.templateId.couponDeadMp:To2kPdIHkti2w1sbWYRA0cE8oyf7Q82f6J_hCVUU2JA}")
    private String signMp;

    /**
     * 关联公众号appid
     */
    @Value("${wx.mpAppid:wxa6290015367cf156}")
    private String mpAppId;


    /**
     * wx监听到优惠券过期消息
     *
     * @param message
     */
    @StreamListener(BcInputInterface.APPLETS_COUPON_DEADLINE_INPUT)
    @LogTranceId
    public void wxCouponDeadLinePush(@Payload Message<List<LinkedHashMap>> message) {
        long start = System.currentTimeMillis();
        //本次请求id
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("【优惠券过期推送】requestId====》{},wx监听到优惠券过期消息，Message:{}", requestId, message.getPayload());
        List<CouponDeadLineMsgDTO> list = message.getPayload().stream().map(data -> {
            String jsonStr = JSONObject.toJSON(data).toString();
            return JSONObject.parseObject(jsonStr, CouponDeadLineMsgDTO.class);
        }).collect(Collectors.toList());

        //拿到当前数据用户
        String userId = list.get(0).getUserId();
        if (StringUtils.isEmpty(userId)) {
            log.info("【优惠券过期推送】requestId:{},没有用户数据，忽略", requestId);
            return;
        }
        List<CouponDeadLineDTO> pushList = Lists.newArrayList();

        //统一用户根据不同过期时间分组推送
        Map<String, List<CouponDeadLineMsgDTO>> stringListMap = list.stream().collect(Collectors.groupingBy(CouponDeadLineMsgDTO::getInvalidTime));
        stringListMap.forEach((k, v) -> {
            CouponDeadLineDTO msgDTO = new CouponDeadLineDTO();
            msgDTO.setNumber(v.size());
            msgDTO.setInvalidTime(k);
            msgDTO.setUserId(userId);
            pushList.add(msgDTO);
        });
        //根据配置拿到appid
        String appId = wxMaProperties.getConfigs().get(0).getAppid();
        final WxMaService wxService = WxMaConfiguration.getMaService(appId);
        //判断是否关注公众号
        WxUser wxUser = wxUserService.getOne(new LambdaQueryWrapper<WxUser>().eq(WxUser::getNumberId, userId).eq(WxUser::getIsDelete, 0).eq(WxUser::getType, ChannelSourceEnum.WX.getKey()));
        log.info("【优惠券过期推送】requestId:{},查询到用户：{}", requestId, JSON.toJSONString(wxUser));
        if (wxUser == null) {
            log.info("【优惠券过期推送】requestId:{},没有匹配到用户，忽略", requestId);
            return;
        }

        //当前用户循环推送
        for (CouponDeadLineDTO dto : pushList) {
            //推送服务通知
            handleInfoPush(requestId, wxService, wxUser, dto);
//            if(StringUtils.isEmpty(wxUser.getUnionid())||wxUser.getUnionid().equals(wxUser.getOpenid())){
//            }else {
//                WxOfficialUser wxOfficialUser = wxOfficialUserService.getOne(new LambdaQueryWrapper<WxOfficialUser>().eq(WxOfficialUser::getUnionid,wxUser.getUnionid()));
//                //推送服务通知
//                if(wxOfficialUser==null || wxOfficialUser.getSubscribe()!=1){
//                    handleInfoPush(requestId, wxService, wxUser, dto);
//                }else {
//                    //推送公众号
////                    handleOfficalInfo(requestId, appId, wxService, wxUser, dto);
//                }
//            }
        }
        log.info("requestId:{},用户：{}  处理优惠券到期提醒推送消息结束，耗时：{}", requestId, JSON.toJSONString(wxUser), System.currentTimeMillis() - start);

    }

    /**
     * 推送公众号
     *
     * @param requestId 请求id
     * @param appId     appid
     * @param wxService
     * @param wxUser    当前用户
     * @param dto       推送数据
     */
    private void handleOfficalInfo(String requestId, String appId, WxMaService wxService, WxUser wxUser, CouponDeadLineDTO dto) {
        try {
            log.info("requestId:{},推送公众号", requestId);
            WxMaUniformMessage wxMaUniformMessage = new WxMaUniformMessage();
            wxMaUniformMessage.setAppid(mpAppId);
            wxMaUniformMessage.setToUser(wxUser.getOpenid());
            wxMaUniformMessage.setMpTemplateMsg(true);
            wxMaUniformMessage.setTemplateId(signMp);

            List<WxMaTemplateData> data = new ArrayList<>();
            data.add(new WxMaTemplateData("thing2", "优惠卷即将过期"));
            data.add(new WxMaTemplateData("number5", dto.getNumber().toString()));
            data.add(new WxMaTemplateData("thing4", "兔兔优惠券保质期告急,记得使用"));
            data.add(new WxMaTemplateData("time3", dto.getInvalidTime()));
            wxMaUniformMessage.setData(data);
            WxMaUniformMessage.MiniProgram miniProgram = new WxMaUniformMessage.MiniProgram();
            miniProgram.setAppid(appId);
            miniProgram.setPagePath("packageA/coupon/index");
            wxMaUniformMessage.setMiniProgram(miniProgram);

            String responseContent = wxService.post(WxMaMsgService.UNIFORM_MSG_SEND_URL, wxMaUniformMessage.toJson());

            log.info("requestId:{},公众号处理优惠券到期提醒消息成功:{} 返回结果：{}", requestId, JSON.toJSONString(wxMaUniformMessage), responseContent);
        } catch (WxErrorException e) {
            log.warn("requestId:{},公众号处理优惠券到期提醒消息异常:{}", requestId, e);
        }
    }

    /**
     * 推送服务通知
     *
     * @param requestId traceid
     * @param wxService wxService
     * @param wxUser    用户
     * @param dto       推送数据
     */
    private void handleInfoPush(String requestId, WxMaService wxService, WxUser wxUser, CouponDeadLineDTO dto) {
        try {
            log.info("【优惠券过期推送】requestId:{},推送服务通知", requestId);
            WxMaSubscribeMessage wxMaSubscribeMessage = new WxMaSubscribeMessage();
            wxMaSubscribeMessage.setToUser(wxUser.getOpenid());
            wxMaSubscribeMessage.setPage("packageA/coupon/index");
            wxMaSubscribeMessage.setTemplateId(signTemplateId);

            List<WxMaSubscribeData> listData = new ArrayList<>();
            listData.add(new WxMaSubscribeData("thing2", "优惠卷即将过期"));
            listData.add(new WxMaSubscribeData("number5", dto.getNumber().toString()));
            listData.add(new WxMaSubscribeData("thing4", "兔兔优惠券保质期告急,记得使用"));
            listData.add(new WxMaSubscribeData("time3", dto.getInvalidTime()));
            wxMaSubscribeMessage.setData(listData);

            String responseContent = wxService.post(WxMaMsgService.SUBSCRIBE_MSG_SEND_URL, wxMaSubscribeMessage.toJson());
            log.info("【优惠券过期推送】requestId:{},小程序处理优惠券到期提醒消息成功:{} 返回结果：{}", requestId, JSON.toJSONString(wxMaSubscribeMessage), responseContent);
        } catch (WxErrorException e) {
            log.warn("【优惠券过期推送】requestId:{},小程序处理优惠券到期提醒消息异常:{}", requestId, e);
        }
    }

}
