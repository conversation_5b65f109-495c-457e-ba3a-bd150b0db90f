package com.yl.applets.stream;


import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

/**
 * 功能描述:
 * 小程序新增实名制推送给王一超实名制
 * @since: 1.0.0
 * @Author:luhong
 * @Date: 2021-01-06 14:21
 */
@Component
public interface UserCardPushOutputInterface {

    String USER_CARD_PUSH_OUTPUT = "user-card-push-output";

    @Output(USER_CARD_PUSH_OUTPUT)
    MessageChannel sendUserCardPushMessage();
}
