package com.yl.applets.stream;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.yl.applets.entity.ActivityConfig;
import com.yl.applets.enums.ActivityConfigStatusEnum;
import com.yl.applets.mapper.applets.ActivityConfigMapper;
import com.yl.applets.utils.OmsExceptionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动配置发布mq消息监听处理类
 * @author: xiongweibin
 * @create: 2020-08-25 10:56
 */
@EnableBinding(value = {ActivityConfigPushInputInterface.class})
@Component
@Slf4j
public class ActivityConfigPushReceiver {

    @Autowired
    private ActivityConfigMapper activityConfigMapper;

    /**
     * 活动配置发布消息监听
     * @param message
     */
    @StreamListener(value = ActivityConfigPushInputInterface.ACTIVITY_CONFIG_PUSH_INPUT)
    public void acceptActivityConfigPushMessage(@Payload Message<ActivityConfig> message) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        log.info("活动配置发布mq消息监听-requestid==>{},收到MQ消息原始报文==>{}", uuid, JSON.toJSONString(message));
        try {
            ActivityConfig activityConfigDTO = message.getPayload();
            if (ObjectUtil.isNull(activityConfigDTO) || ObjectUtil.isNull(activityConfigDTO.getId())) {
                log.info("活动配置发布mq消息监听-requestid==>{},参数异常！", uuid);
                return;
            }

            ActivityConfig activityConfig = activityConfigMapper.selectById(activityConfigDTO.getId());
            if (ObjectUtil.isNull(activityConfig) || !ActivityConfigStatusEnum.STAY_RELEASE.getCode().equals(activityConfig.getStatus())) {
                log.info("活动配置发布mq消息监听-requestid==>{},找不到需要发布的标题！", uuid);
                return;
            }

            log.info("活动配置发布mq消息监听-requestid==>{},查询返回值==>{}", uuid, JSON.toJSONString(activityConfig));
            if (!activityConfigDTO.getVersionNo().equals(activityConfig.getVersionNo())) {
                log.info("活动配置发布mq消息监听-requestid==>{},重新发布过忽略不处理！", uuid);
                return;
            }

            ActivityConfig activityConfigUpdate = activityConfigMapper.queryActivityConfigByCondition(activityConfigDTO);
            if (ObjectUtil.isNotNull(activityConfigUpdate)) {
                activityConfigUpdate.setStatus(ActivityConfigStatusEnum.STAY_OFFLINE.getCode());
                activityConfigUpdate.setUpdateTime(LocalDateTime.now());
                activityConfigUpdate.setOffTime(LocalDateTime.now());
                activityConfigMapper.updateById(activityConfigUpdate);
                log.info("活动配置发布mq消息监听-requestid==>{},自动下线==>{}", uuid, JSON.toJSONString(activityConfigUpdate));
            }
            activityConfig.setStatus(ActivityConfigStatusEnum.ALREADY_RELEASE.getCode());
            activityConfig.setUpdateTime(LocalDateTime.now());
            activityConfigMapper.updateById(activityConfig);
            log.info("活动配置发布mq消息监听-requestid==>{},发布成功==>{}", uuid, JSON.toJSONString(activityConfig));
        } catch (Exception e) {
            log.error("活动配置发布mq消息监听异常-requestid==>{},异常信息==>{}", uuid, OmsExceptionUtils.getStackTrace(e));
        }

    }

    /**
     * 活动配置下线队列监听
     *
     * @param message
     */
    @StreamListener(value = ActivityConfigPushInputInterface.ACTIVITY_CONFIG_OFF_LINE_INPUT)
    public void acceptActivityConfigOffLineMessage(@Payload Message<ActivityConfig> message) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        log.info("活动配置下线mq消息监听-requestid==>{},收到MQ消息原始报文==>{}", uuid, JSON.toJSONString(message));
        try {
            ActivityConfig activityConfigDTO = message.getPayload();
            if (ObjectUtil.isNull(activityConfigDTO) || ObjectUtil.isNull(activityConfigDTO.getId())) {
                log.info("活动配置下线mq消息监听-requestid==>{},参数异常！", uuid);
                return;
            }

            ActivityConfig activityConfig = activityConfigMapper.selectById(activityConfigDTO.getId());
            if (ObjectUtil.isNull(activityConfig) || !ActivityConfigStatusEnum.ALREADY_RELEASE.getCode().equals(activityConfig.getStatus())) {
                log.info("活动配置下线mq消息监听-requestid==>{},找不到需要发布的标题！", uuid);
                return;
            }

            log.info("活动配置下线mq消息监听-requestid==>{},查询返回值==>{}", uuid, JSON.toJSONString(activityConfig));
            if (!activityConfigDTO.getVersionNo().equals(activityConfig.getVersionNo())) {
                log.info("活动配置下线mq消息监听-requestid==>{},重新发布过忽略不处理！", uuid);
                return;
            }
            activityConfig.setStatus(ActivityConfigStatusEnum.STAY_OFFLINE.getCode());
            activityConfig.setUpdateTime(LocalDateTime.now());
            activityConfigMapper.updateById(activityConfig);
            log.info("活动配置下线mq消息监听-requestid==>{},下线成功==>{}", uuid, JSON.toJSONString(activityConfig));
        } catch (Exception e) {
            log.error("活动配置下线mq消息监听异常-requestid==>{},异常信息==>{}", uuid, OmsExceptionUtils.getStackTrace(e));
        }
    }
}
