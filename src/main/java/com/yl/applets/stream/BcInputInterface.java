package com.yl.applets.stream;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019_08_22
 */
@Component
interface BcInputInterface {
    /**
     * 小程序推送到期优惠券-推送mq
     */
    String APPLETS_COUPON_DEADLINE_INPUT = "coupon-deadline-input";

    /**
     * 优惠券到期提醒推送公众号
     *
     * @return
     */
    @Input(APPLETS_COUPON_DEADLINE_INPUT)
    MessageChannel couponDeadlinePushMsg();

}
