package com.yl.applets.stream;

import com.alibaba.fastjson.JSON;
import com.yl.applets.stream.dto.MemberUserMqDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * 功能描述:
 * 推送统一用户
 * @since: 1.0.0
 * @Author:hhf
 * @Date: 2021-7-9 11:57:31
 */
@Component
@Slf4j
@EnableBinding(value = {PushMemberUserOutputInterface.class})
public class MemberUserPushOutputProcessor {

    @Autowired
    private PushMemberUserOutputInterface pushMemberUserInterface;

    public void sendMemberUserPushMessage(MemberUserMqDto mqDto) {
        //本次请求id
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("渠道[wx]新增用户mq消息推送-requestid==>{},原始报文==>{}", uuid, JSON.toJSONString(mqDto));
        Message msg = null;
        try {
            msg = MessageBuilder.withPayload(mqDto).build();
            pushMemberUserInterface.sendMemberUserPushMessage().send(msg);
        } catch (Exception e) {
            log.error("requestid==>{},渠道新增用户mq推送失败：{}",uuid,e);
        }
        log.info("渠道[wx]新增用户mq消息推送成功-requestid==>{},推送内容：{}", uuid,msg);
    }
}
