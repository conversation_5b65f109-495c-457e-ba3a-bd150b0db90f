package com.yl.applets.stream;

import com.alibaba.fastjson.JSON;
import com.yl.applets.entity.ActivityConfig;
import com.yl.applets.entity.UserCard;
import com.yl.applets.utils.OmsExceptionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.UUID;

/**
 * 功能描述:
 * 小程序实名制推送
 * @since: 1.0.0
 * @Author:luhong
 * @Date: 2021-01-06 14:48
 */
@Component
@Slf4j
@EnableBinding(value = {UserCardPushOutputInterface.class})
public class UserCardPushOutputProcessor {

    @Autowired
    private UserCardPushOutputInterface userCardPushOutputInterface;

    public void sendUserCardAsynMessage(UserCard userCard) {
        //本次请求id
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("小程序新增实名制mq消息推送-requestid==>{},原始报文==>{}", uuid, JSON.toJSONString(userCard));
        Message msg = null;
        try {
            msg = MessageBuilder.withPayload(userCard).build();
            userCardPushOutputInterface.sendUserCardPushMessage().send(msg);
        } catch (Exception e) {
            log.error("requestid==>{},小程序实名制mq推送失败：{}",uuid,e);
        }
        log.info("小程序新增实名制mq消息推送成功-requestid==>{},推送内容：{}", uuid,msg);
    }
}
