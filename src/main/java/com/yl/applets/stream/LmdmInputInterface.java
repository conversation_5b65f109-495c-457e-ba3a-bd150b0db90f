package com.yl.applets.stream;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019_08_22
 */
@Component
interface LmdmInputInterface {

    /**
     * 基础数据数据变更
     */
    String APPLETS_LMDM_DATA_CHANGE_INPUT = "applets-lmdm-data-change-input";


    /**
     * 基础数据变更
     * @return
     */
    @Input(APPLETS_LMDM_DATA_CHANGE_INPUT)
    MessageChannel appletsLmdmDataChangeInput();
}
