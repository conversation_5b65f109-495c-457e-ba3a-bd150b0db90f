package com.yl.applets.stream;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yl.applets.dto.ForwardRequest;
import com.yl.applets.dto.LmdmRedisMessage;
import com.yl.applets.dto.SysPrinterEquipmentChange;
import com.yl.applets.entity.CustomerPrintData;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.OldLmdmFeignClient;
import com.yl.applets.service.ICustomerPrintDataService;
import com.yl.applets.vo.SysCustomeAppletVO;
import com.yl.chaos.log.traceid.LogTranceId;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@EnableBinding(LmdmInputInterface.class)
@LogTranceId
public class LmdmStreamListener {

    public static final String SYS_PRINTER_EQUIPMENT_MANUFACTURER = "sys_printer_equipment_manufacturer";


    @Autowired
    private ICustomerPrintDataService customerPrintDataService;

    @Autowired
    private OldLmdmFeignClient sysCustomerExtFeignClient;


    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;


    enum  OpType{
        //删除
        DELETE(2),
        //更新
        UPDATE(1);
        private Integer code;
        OpType(Integer code){
            this.code = code;
        }
    }


    /**
     * 基础数据数据变更
     * @param msg
     */
    @StreamListener(LmdmInputInterface.APPLETS_LMDM_DATA_CHANGE_INPUT)
    public void appletsLmdmDataChange(@Payload Message<LmdmRedisMessage> msg) {
        LmdmRedisMessage payload = msg.getPayload();

        if(SYS_PRINTER_EQUIPMENT_MANUFACTURER.equals(payload.getModule())){
            String json = payload.getJson();
            log.info("基础数据云打印设备信息有改动。。json:{}",json);
            SysPrinterEquipmentChange sysPrinterEquipmentChange = JSON.parseObject(json, SysPrinterEquipmentChange.class);
            //设备删除
            if(OpType.DELETE.code.equals(sysPrinterEquipmentChange.getOpType())){

                sysPrinterEquipmentChange.getEquipmentIds().forEach(id->{
                    log.info("云打印设备被删除，同步删除记录：{}",id);
                    LambdaQueryWrapper<CustomerPrintData> sql = new LambdaQueryWrapper<CustomerPrintData>().eq(CustomerPrintData::getPrinterEquipmentId, id);
                    customerPrintDataService.remove(sql);
                });

            }

            //设备更新
            if(OpType.UPDATE.code.equals(sysPrinterEquipmentChange.getOpType())){
                sysPrinterEquipmentChange.getEquipmentIds().forEach(id->{
                    log.info("云打印设备更新，同步更新记录：{}",id);
                    String requestId = UUID.randomUUID().toString().replaceAll("-", "");
//                    SysCustomeAppletVO sysCustomerAppletInfo = sysCustomerExtFeignClient.getSysCustomerAppletInfo(id).result();
                    ForwardRequest forwardRequest=new ForwardRequest();
                    forwardRequest.setRequestId(requestId);
                    forwardRequest.setBody(id+"");
                    forwardRequest.setRequestUri("/lmdmapi/sysCustomerPrinterEquipment/getSysCustomerAppletInfo");
                    log.info("请求id==>{}channel,入参==>{}",requestId, JSON.toJSONString(forwardRequest));
                    Result<?> forward = channelApiFeignClient.forward(forwardRequest);
                    log.info("请求id==>{}channel,出参==>{}",requestId, JSON.toJSONString(forward));
                    Object result = forward.result();
                    SysCustomeAppletVO sysCustomerAppletInfo = JSON.parseObject(JSON.toJSONString(result), SysCustomeAppletVO.class);
                    if(sysCustomerAppletInfo == null){
                        return;
                    }
                    List<CustomerPrintData> list = customerPrintDataService.list(new LambdaQueryWrapper<CustomerPrintData>().eq(CustomerPrintData::getPrinterEquipmentId, id));
                    list.forEach(data->{
                        data.setNetworkCode(sysCustomerAppletInfo.getNetworkCode());
                        data.setNetworkId(sysCustomerAppletInfo.getId().toString());
                        data.setNetworkName(sysCustomerAppletInfo.getNetworkName());
                        data.setUpdateTime(LocalDateTime.now());
                        data.setPrinterEquipmentName(sysCustomerAppletInfo.getPrinterEquipmentName());
                        data.setPrinterEquipmentManufacturer(sysCustomerAppletInfo.getPrinterEquipmentManufacturer().toString());
                        data.setPrinterEquipmentNumber(sysCustomerAppletInfo.getPrinterEquipmentNumber());
                        customerPrintDataService.updateById(data);
                    });
                });
            }
        }
    }
}
