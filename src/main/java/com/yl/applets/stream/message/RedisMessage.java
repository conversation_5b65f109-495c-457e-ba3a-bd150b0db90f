/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: RedisMessage
 * Author:   luhong
 * Date:     2020-10-14 14:36
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.stream.message;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-14
 * @since 1.0.0
 */
@Data
public class RedisMessage {
    private String messageId;
    private String streamName;
    private String cacheKey;
    private String updateUrl;
    private String system;
    private LocalDateTime createTime;
}