package com.yl.applets.stream;


import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动配置发布消息生产队列
 * @author: xiongweibin
 * @create: 2020-08-25 10:32
 */
@Component
public interface ActivityConfigPushOutputInterface {

    String ACTIVITY_CONFIG_PUSH_OUTPUT = "activity-config-push-output";
    String ACTIVITY_CONFIG_OFF_LINE_OUTPUT = "activity-config-off-line-output";

    @Output(ACTIVITY_CONFIG_PUSH_OUTPUT)
    MessageChannel sendActivityConfigPushMessage();

    @Output(ACTIVITY_CONFIG_OFF_LINE_OUTPUT)
    MessageChannel sendActivityConfigOffLineMessage();
}
