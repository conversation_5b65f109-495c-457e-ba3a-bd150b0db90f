package com.yl.applets.stream;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动配置发布消费队列
 * @author: xiongweibin
 * @create: 2020-08-25 10:51
 */
@Component
public interface ActivityConfigPushInputInterface {

    String ACTIVITY_CONFIG_PUSH_INPUT = "activity-config-push-input";

    String ACTIVITY_CONFIG_OFF_LINE_INPUT = "activity-config-off-line-input";
    /**
     * 活动配置发布消息监听队列
     * @return
     */
    @Input(ACTIVITY_CONFIG_PUSH_INPUT)
    SubscribableChannel acceptActivityConfigPushMessage();

    /**
     * 活动配置下线消息监听队列
     * @return
     */
    @Input(ACTIVITY_CONFIG_OFF_LINE_INPUT)
    SubscribableChannel acceptActivityConfigOffLineMessage();

}
