package com.yl.applets.stream;

import com.alibaba.fastjson.JSON;
import com.yl.applets.entity.ActivityConfig;
import com.yl.applets.utils.OmsExceptionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动配置发布mq消息推送
 * @author: xiongweibin
 * @create: 2020-08-25 10:37
 */
@Component
@Slf4j
@EnableBinding(value = {ActivityConfigPushOutputInterface.class})
public class ActivityConfigPushOutputProcessor {

    @Autowired
    private ActivityConfigPushOutputInterface activityConfigPushOutputInterface;

    public void sendActivityConfigAsynMessage(ActivityConfig activityConfig) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        log.info("活动配置发布/下线mq消息推送-requestid==>{},原始报文==>{}", uuid, JSON.toJSONString(activityConfig));
        try {
            long onDelayTime = 1000;
            Duration onDuration = Duration.between(activityConfig.getReleaseTime(), activityConfig.getOnTime());
            if(onDuration.toMillis() > 1000) {
                onDelayTime = onDuration.toMillis();
            }
            Message onMessage = MessageBuilder.withPayload(activityConfig).setHeader("x-delay", onDelayTime).build();
            activityConfigPushOutputInterface.sendActivityConfigPushMessage().send(onMessage);
            log.info("活动配置发布mq消息推送成功-requestid==>{},延迟发布时间==>{}ms", uuid, onDelayTime);

            Duration offLineDuration = Duration.between(activityConfig.getReleaseTime(), activityConfig.getOffTime());
            Message offLineMessage = MessageBuilder.withPayload(activityConfig).setHeader("x-delay", offLineDuration.toMillis()).build();
            activityConfigPushOutputInterface.sendActivityConfigOffLineMessage().send(offLineMessage);
            log.info("活动配置下线mq消息推送成功-requestid==>{},延迟发布时间==>{}ms", uuid, offLineDuration.toMillis());
        } catch (Exception e) {
            log.error("活动配置发布/下线mq消息推送异常-requestid==>{},异常信息==>{}", uuid, OmsExceptionUtils.getStackTrace(e));
        }
    }
}
