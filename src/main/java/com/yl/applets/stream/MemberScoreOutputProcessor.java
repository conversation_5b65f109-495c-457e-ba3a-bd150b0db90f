package com.yl.applets.stream;

import com.alibaba.fastjson.JSON;
import com.yl.applets.stream.dto.MemberScoreMqDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * 功能描述:
 * 会员 成长值
 * @since: 1.0.0
 * @Author:hhf
 * @Date: 2021-7-9 11:57:31
 */
@Component
@Slf4j
@EnableBinding(value = {MemberScoreOutputInterface.class})
public class MemberScoreOutputProcessor {

    @Autowired
    private MemberScoreOutputInterface memberScoreOutputInterface;

    public void sendScorePushMessage(MemberScoreMqDto mqDto) {
        //本次请求id
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("渠道[wx]用户埋点mq消息推送-requestid==>{},原始报文==>{}", uuid, JSON.toJSONString(mqDto));
        Message msg = null;
        try {
            msg = MessageBuilder.withPayload(mqDto).build();
            memberScoreOutputInterface.sendMemberScoreMessage().send(msg);
        } catch (Exception e) {
            log.error("requestid==>{},[wx]用户埋点mq推送失败：{}",uuid,e);
        }
        log.info("渠道[wx]用户埋点mq消息推送成功-requestid==>{},推送内容：{}", uuid,msg);
    }


    public void sendScoreActivityMessage(MemberScoreMqDto mqDto) {
        //本次请求id
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("渠道[wx]用户活动积分/成长值mq消息推送-requestid==>{},原始报文==>{}", uuid, JSON.toJSONString(mqDto));
        Message msg = null;
        try {
            msg = MessageBuilder.withPayload(mqDto).build();
            memberScoreOutputInterface.sendMemberActivityScoreMessage().send(msg);
        } catch (Exception e) {
            log.error("requestid==>{},[wx]用户活动积分/成长值mq推送失败：{}",uuid,e);
        }
        log.info("渠道[wx]用户活动积分/成长值mq消息推送成功-requestid==>{},推送内容：{}", uuid,msg);
    }
}
