package com.yl.applets.stream;


import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 会员成长值
 * @author: hhf
 * @create: 2021-7-29 15:11:36
 */
@Component
public interface MemberScoreOutputInterface {

    String MEMBER_SCORE_OUTPUT = "member-score-output";

    @Output(MEMBER_SCORE_OUTPUT)
    MessageChannel sendMemberScoreMessage();

    String MEMBER_ACTIVITY_SCORE_OUTPUT = "member-activity-score-output";

    @Output(MEMBER_ACTIVITY_SCORE_OUTPUT)
    MessageChannel sendMemberActivityScoreMessage();

}
