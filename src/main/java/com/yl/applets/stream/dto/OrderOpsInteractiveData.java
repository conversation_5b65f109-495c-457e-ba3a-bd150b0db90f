package com.yl.applets.stream.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单推送消息操作mq
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-08-10 20:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OrderOpsInteractiveData {

    /**
     * 唯一号
     */
    private String sn;

    private Long memberId;

    /**
     * 订单id
     */
    private Long orderNo;

    /**
     * 运单号
     */
    private String waybillNo;

    /**
     * 员工id
     */
    private Integer userId;

    /**
     * 员工编号
     */
    private String userCode;

    /**
     * 员工名称
     */
    private String userName;

    /**
     * 支付金额
     */
    private BigDecimal orderAmount;

    /**
     * 支付状态   0、"支付成功"   2、"未支付" 3、"已关闭"（支付取消）  6、"支付失败"
     */
    private Integer orderPayStatus;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 付款方式 1、微信支付 2、现金支付
     */
    private Integer paymentSource;


    /**
     * 是否启用人工算费 1，是 2.否
     */
    private Integer offCost;

    /**
     * 保价费
     */
    private BigDecimal insuredValue;

    /**
     * 总费用
     */
    private BigDecimal totalFreight;

    /**
     * 标准运费
     */
    private BigDecimal standardValue;

    /**
     * 回单费用
     */
    private BigDecimal receiptFreight;

    /**
     * 代收货款金额
     */
    private BigDecimal codMoney;

    /**
     * 包材费
     */
    private BigDecimal packageCost;

    /**
     * 寄件手工运费
     */
    private BigDecimal donationManualFreight;


    /**
     * 到付手工运费
     */
    private BigDecimal arrivalManualFreight;


}
