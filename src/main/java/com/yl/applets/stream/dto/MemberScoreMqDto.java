package com.yl.applets.stream.dto;

import com.yl.applets.dto.ShareOrderDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description: (埋点)用户成长值/积分mq
 * @date 2021-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MemberScoreMqDto {

    private Long id;

    private Integer memberId;

    private Integer type;

    private String memberAction;

    private ShareOrderDto dto;

    private Integer scoreValue;

    private Integer growValue;

}
