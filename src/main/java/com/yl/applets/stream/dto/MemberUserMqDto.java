package com.yl.applets.stream.dto;

import com.yl.applets.vo.AppletsLoginLogEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 小程序用户
 * </p>
 *
 * <AUTHOR> @since 2019-09-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MemberUserMqDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * unionid
     */
    private String unionid;


    private String city;


    private String province;

    /**
     * openid
     */
    private String openid;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 头像
     */
    private String avatarUrl;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     *  0未删除，1已删除
     */
    private Integer isDelete;

    /**
     * 渠道类型(详见：ChannelSourceEnum)
     */
    private Integer type;

    /**
     * 下单使用的id
     */
    private Integer numberId;

    //区
    private String zone;

    //设备信息
    private String equipmentInfo;

    //MQ类型：save保存、update更新
    private String mqType;


    //带入注册信息
    private MemberScoreMqDto scoreMqDto;

    private String ip;
    private String port;


}
