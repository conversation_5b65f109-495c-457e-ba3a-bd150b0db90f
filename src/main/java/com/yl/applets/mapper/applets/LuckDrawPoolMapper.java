package com.yl.applets.mapper.applets;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yl.applets.entity.LuckDrawPool;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： <PERSON><PERSON><PERSON><PERSON>
 * @Date： 2022/08/08
 */
public interface LuckDrawPoolMapper extends BaseMapper<LuckDrawPool> {

    public List<LuckDrawPool> getListByTime(@Param("activityId")String activityId,@Param("day") String day);

    public int deleteByTime(@Param("startTime")String startTime,@Param("endTime")String endTime);

    public int updateByPrize(@Param("dto")LuckDrawPool dto);

}
