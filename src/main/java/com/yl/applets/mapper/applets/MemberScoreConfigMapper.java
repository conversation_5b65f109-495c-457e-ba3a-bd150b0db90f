package com.yl.applets.mapper.applets;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yl.applets.entity.MemberScoreConfig;
import com.yl.applets.vo.MemberScoreConfigVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * @Description: 用户管理-用户成长值/积分值配置表
 * @CreateDate: Created in {2021/7/16 15:04}
 * @Author: DongFeixiang
 */
public interface MemberScoreConfigMapper extends BaseMapper<MemberScoreConfig> {


    List<MemberScoreConfigVo> getMemberConfigAndStatus(@Param("dto") MemberScoreConfigVo vo);
}
