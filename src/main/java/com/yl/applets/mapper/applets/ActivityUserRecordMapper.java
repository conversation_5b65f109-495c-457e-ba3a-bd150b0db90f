package com.yl.applets.mapper.applets;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yl.applets.dto.ActivityPrizeRecordDto;
import com.yl.applets.entity.ActivityUserRecord;
import org.apache.ibatis.annotations.Param;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动管理配置
 * @author: xiongweibin
 * @create: 2020-08-24 13:53
 */
public interface ActivityUserRecordMapper extends BaseMapper<ActivityUserRecord> {


    ActivityPrizeRecordDto getPrizeInfo(@Param("numberId") Integer numberId,@Param("type") Integer type);

}
