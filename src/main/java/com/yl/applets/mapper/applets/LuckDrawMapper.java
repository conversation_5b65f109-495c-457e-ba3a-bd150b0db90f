package com.yl.applets.mapper.applets;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yl.applets.dto.LuckDrawDto;
import com.yl.applets.entity.LuckDraw;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： z<PERSON><PERSON>
 * @Date： 2022/08/08
 */
public interface LuckDrawMapper extends BaseMapper<LuckDraw> {

    List<LuckDrawDto> selectCurrentActivity(@Param("time") String time,@Param("id")Long id);

}
