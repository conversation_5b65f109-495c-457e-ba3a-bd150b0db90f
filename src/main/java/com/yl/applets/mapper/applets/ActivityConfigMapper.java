package com.yl.applets.mapper.applets;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yl.applets.dto.ActivityConfigQueryDTO;
import com.yl.applets.dto.ActivityVisitRecordDTO;
import com.yl.applets.entity.ActivityConfig;
import com.yl.applets.vo.ActivityConfigVO;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动管理配置
 * @author: xiongweibin
 * @create: 2020-08-24 13:53
 */
public interface ActivityConfigMapper extends BaseMapper<ActivityConfig> {

    /**
     * 活动配置分页统计
     * @param queryDTO
     * @return
     */
    Long countActivityConfig(ActivityConfigQueryDTO queryDTO);

    Long countActivityConfigPromotion(ActivityConfigQueryDTO queryDTO);

    /**
     * 活动配置分页列表查询
     * @param queryDTO
     * @return
     */
    List<ActivityConfigVO> selectActivityConfigPage(ActivityConfigQueryDTO queryDTO);

    List<ActivityConfigVO> selectActivityConfigPagePromotion(ActivityConfigQueryDTO queryDTO);

    /**
     * 查询发不中的活动列表
     * @param queryDTO
     * @return
     */
    List<ActivityConfigVO> selectActivityConfigList(ActivityConfigQueryDTO queryDTO);

    List<ActivityConfigVO> selectActivityConfigListPromotion(ActivityConfigQueryDTO queryDTO);

    /**
     * 根据id查询详情
     * @param id
     * @return
     */
    ActivityConfigVO getDetailById(Long id);

    /**
     * 根据条件查询
     * @param activityConfigDTO
     * @return
     */
    ActivityConfig queryActivityConfigByCondition(ActivityConfig activityConfigDTO);


    /**
     * 更新活动曝光/访问次数
     * @param dto
     */
    void updateActivityConfigByVisitRecord(ActivityVisitRecordDTO dto);
}
