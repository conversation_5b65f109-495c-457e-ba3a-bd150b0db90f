package com.yl.applets.mapper.applets;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.entity.Address;
import com.yl.applets.vo.AddressVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 地址管理
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-20
 */
public interface AddressMapper extends BaseMapper<Address> {

    /**
     * 地址列表
     *
     * @param page
     * @param keyword
     * @param userId
     * @return
     */
    Page<AddressVo> getPages(Page<Object> page, @Param("keyword") String keyword, @Param("userId") Integer userId, @Param("type") Integer type,
                             @Param("signs")  List<Long> sign);

    /**
     * 将地址更新为非默认
     *
     * @param ids
     */
    void updateToNonDefault(@Param("ids") List<Long> ids);

    void disabledAddress(@Param("ids") List<Long> ids);

    List<Address> getDefaultByMobile(@Param("mobile") String mobile);

}
