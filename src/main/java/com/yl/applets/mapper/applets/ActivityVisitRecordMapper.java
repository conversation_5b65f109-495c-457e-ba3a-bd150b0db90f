package com.yl.applets.mapper.applets;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yl.applets.entity.ActivityVisitRecord;
import com.yl.applets.vo.ActivityVisitRecordStatisticsVO;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动访问/曝光记录
 * @author: xiongweibin
 * @create: 2020-08-24 13:53
 */
public interface ActivityVisitRecordMapper extends BaseMapper<ActivityVisitRecord> {

    List<ActivityVisitRecordStatisticsVO> statisticsActivityVisitQty(Long activityId);
}
