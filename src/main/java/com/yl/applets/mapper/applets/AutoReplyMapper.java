package com.yl.applets.mapper.applets;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.AutoReplyQueryDto;
import com.yl.applets.entity.AutoReply;
import com.yl.applets.vo.AutoReplyVO;
import org.apache.ibatis.annotations.Param;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021<br>
 *
 * @description: 小程序自动回复规则
 * @author: caiyao
 * @create: 2021-05-20 10:11
 */
public interface AutoReplyMapper extends BaseMapper<AutoReply> {

    /**
     * 查看(1:关注回复 2:收到消息回复)自动回复
     *
     * @param replyType
     * @return
     */
    AutoReplyVO selectAutoReply( @Param("replyType") Integer replyType);

    /**
     * 查看关键词自动回复列表
     *
     * @param dto
     * @return
     */
    Page<AutoReplyVO> selectAutoReplyList(Page<AutoReply> page, @Param("dto") AutoReplyQueryDto dto);
}
