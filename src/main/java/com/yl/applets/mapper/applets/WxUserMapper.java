package com.yl.applets.mapper.applets;

import com.yl.applets.entity.WxUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 小程序用户 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2019-09-21
 */
public interface WxUserMapper extends BaseMapper<WxUser> {
    List<Long> getIdsByIdAndNumberId(Integer id);

    Integer maxNumberId();

    Integer maxJYUserId();

    Integer updateJyUserId(@Param("jyUserId") Integer jyUserId, @Param("ids")List<Long> ids);

    List<Long> getIdListByIdNumberId(@Param("numberId")Integer numberId);
}
