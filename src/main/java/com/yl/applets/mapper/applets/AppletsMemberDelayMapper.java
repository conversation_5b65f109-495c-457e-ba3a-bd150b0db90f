package com.yl.applets.mapper.applets;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-02-09 16:28
 * @Version 1.0
 */
public interface AppletsMemberDelayMapper {

    /**
     * 查询当前用户在指定时间、会员延长次数 是否存在数据
     *
     * @param memberId  会员id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param number    指定次数
     * @return
     */
    Integer qryMemberCount(@Param("memberId") String memberId, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("number") String number);
}
