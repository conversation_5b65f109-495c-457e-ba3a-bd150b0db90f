package com.yl.applets.mapper.oms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yl.applets.dto.CommonSearchApiDTO;
import com.yl.applets.entity.OmsWaybill;
import com.yl.applets.vo.OmsWaybillDetailVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 运单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-07-29
 */
@Repository
public interface OmsWaybillMapper extends BaseMapper<OmsWaybill> {


    List<OmsWaybillDetailVO> selectWaybillNoList(@Param("waybillNoList")List<String> waybillNoList);

    String selectF_encrypt(@Param("mobile")String mobile);
}
