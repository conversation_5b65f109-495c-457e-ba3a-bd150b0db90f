package com.yl.applets.mapper.market;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yl.applets.entity.NewCustomerActivitySp;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/3/9 11:48
 * @description
 */
public interface NewCustomerActivitySpMapper extends BaseMapper<NewCustomerActivitySp> {


    List<String> getInfoByPhone(@Param("phone")String phone);
}