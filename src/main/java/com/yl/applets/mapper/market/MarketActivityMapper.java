package com.yl.applets.mapper.market;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yl.applets.entity.MarketActivity;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/3/9 11:30
 * @description
 */
public interface MarketActivityMapper extends BaseMapper<MarketActivity> {

    List<MarketActivity>  getInfoById(@Param("ids") List<String> ids);
}