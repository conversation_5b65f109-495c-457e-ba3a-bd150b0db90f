package com.yl.applets.log;

import cn.binarywang.wx.miniapp.api.*;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.config.WxMaConfig;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.util.http.HttpType;
import me.chanjar.weixin.common.util.http.RequestExecutor;
import me.chanjar.weixin.common.util.http.RequestHttp;
import org.apache.http.HttpHost;
import org.apache.http.impl.client.CloseableHttpClient;

/**
 * @Title WxMaServiceImplLoggerWrapper
 * @Package com.yl.applets.log
 * @Description
 * @Version 1.0.0
 * @Date 2023/2/1 10:27 AM
 * @Created by mz
 */
@Slf4j
public class WxMaServiceImplLoggerWrapper extends WxMaServiceImpl {

    private WxMaServiceImpl wxMaService;

    public WxMaServiceImplLoggerWrapper(WxMaServiceImpl wxMaService) {
        this.wxMaService = wxMaService;
    }

    @Override
    public String post(String url, String postData) throws WxErrorException {
        String response = null;
        try {
            response = wxMaService.post(url, postData);
            log.info("请求微信url==>{}，data==>{}",url,postData);
            return response;
        } finally {
            StringBuilder stringBuilder = new StringBuilder("\n");
            stringBuilder.append("URI : {}").append("\n");
            stringBuilder.append("Method : post").append("\n");
            stringBuilder.append("request : {}").append("\n");
            stringBuilder.append("response : {}").append("\n");
            log.info(stringBuilder.toString(), url, postData, response);
        }
    }

    @Override
    public String get(String url, String queryParam) throws WxErrorException {
        String response = null;
        try {
            response = wxMaService.get(url, queryParam);
            log.info("请求微信url==>{}，data==>{}",url,queryParam);
            return response;
        } finally {
            StringBuilder stringBuilder = new StringBuilder("\n");
            stringBuilder.append("URI : {}").append("\n");
            stringBuilder.append("Method : get").append("\n");
            stringBuilder.append("request : {}").append("\n");
            stringBuilder.append("response : {}").append("\n");
            log.info(stringBuilder.toString(), url, queryParam, response);
        }
    }

    @Override
    public CloseableHttpClient getRequestHttpClient() {
        return wxMaService.getRequestHttpClient();
    }

    @Override
    public HttpHost getRequestHttpProxy() {
        return wxMaService.getRequestHttpProxy();
    }

    @Override
    public HttpType getRequestType() {
        return wxMaService.getRequestType();
    }

    @Override
    public void initHttp() {
        wxMaService.initHttp();
    }

    @Override
    public RequestHttp getRequestHttp() {
        return wxMaService.getRequestHttp();
    }

    @Override
    public String getAccessToken(boolean forceRefresh) throws WxErrorException {
        return wxMaService.getAccessToken(forceRefresh);
    }

    @Override
    public String getPaidUnionId(String openid, String transactionId, String mchId, String outTradeNo) throws WxErrorException {
        return wxMaService.getPaidUnionId(openid, transactionId, mchId, outTradeNo);
    }

    @Override
    public WxMaJscode2SessionResult jsCode2SessionInfo(String jsCode) throws WxErrorException {
        return wxMaService.jsCode2SessionInfo(jsCode);
    }

    @Override
    public boolean checkSignature(String timestamp, String nonce, String signature) {
        return wxMaService.checkSignature(timestamp, nonce, signature);
    }

    @Override
    public String getAccessToken() throws WxErrorException {
        return wxMaService.getAccessToken();
    }


    @Override
    public <T, E> T execute(RequestExecutor<T, E> executor, String uri, E data) throws WxErrorException {
        return wxMaService.execute(executor, uri, data);
    }

    @Override
    public WxMaConfig getWxMaConfig() {
        return wxMaService.getWxMaConfig();
    }

    @Override
    public void setWxMaConfig(WxMaConfig wxConfigProvider) {
        wxMaService.setWxMaConfig(wxConfigProvider);
    }

    @Override
    public void setRetrySleepMillis(int retrySleepMillis) {
        wxMaService.setRetrySleepMillis(retrySleepMillis);
    }

    @Override
    public void setMaxRetryTimes(int maxRetryTimes) {
        wxMaService.setMaxRetryTimes(maxRetryTimes);
    }

    @Override
    public WxMaMsgService getMsgService() {
        return wxMaService.getMsgService();
    }

    @Override
    public WxMaMediaService getMediaService() {
        return wxMaService.getMediaService();
    }

    @Override
    public WxMaUserService getUserService() {
        return wxMaService.getUserService();
    }

    @Override
    public WxMaQrcodeService getQrcodeService() {
        return wxMaService.getQrcodeService();
    }

    @Override
    public WxMaTemplateService getTemplateService() {
        return wxMaService.getTemplateService();
    }

    @Override
    public WxMaAnalysisService getAnalysisService() {
        return wxMaService.getAnalysisService();
    }

    @Override
    public WxMaCodeService getCodeService() {
        return wxMaService.getCodeService();
    }

    @Override
    public WxMaJsapiService getJsapiService() {
        return wxMaService.getJsapiService();
    }

    @Override
    public WxMaSettingService getSettingService() {
        return wxMaService.getSettingService();
    }

    @Override
    public WxMaShareService getShareService() {
        return wxMaService.getShareService();
    }

    @Override
    public WxMaRunService getRunService() {
        return wxMaService.getRunService();
    }

    @Override
    public WxMaSecCheckService getSecCheckService() {
        return wxMaService.getSecCheckService();
    }

    @Override
    public WxMaPluginService getPluginService() {
        return wxMaService.getPluginService();
    }
}
