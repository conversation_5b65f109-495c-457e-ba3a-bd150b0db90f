/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: MaterialSpecificationQueryDTO
 * Author:   luhong
 * Date:     2020-10-14 14:33
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yl.common.base.annotation.log.operation.OperationParamLog;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-14
 * @since 1.0.0
 */
@Data
public class MaterialSpecificationQueryDTO implements Serializable {
    @OperationParamLog(
            message = "物料编码"
    )
    @ApiModelProperty(
            name = "materialCode",
            value = "物料编码"
    )
    private String materialCode;
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @OperationParamLog(
            message = "物料id"
    )
    private Long materialId;
    @OperationParamLog(
            message = "物料名称"
    )
    @ApiModelProperty(
            name = "materialName",
            value = "物料名称"
    )
    private String materialName;
    @OperationParamLog(
            message = "物料小类编码"
    )
    @ApiModelProperty(
            name = "smallMaterialCode",
            value = "物料小类编码"
    )
    private String smallMaterialCode;
    @OperationParamLog(
            message = "物料小类名称"
    )
    @ApiModelProperty(
            name = "smallMaterialName",
            value = "物料小类名称"
    )
    private String smallMaterialName;
    @OperationParamLog(
            message = "是否启用"
    )
    @ApiModelProperty(
            name = "isEnable",
            value = "是否启用"
    )
    private Integer isEnable;
    @OperationParamLog(
            message = "开始时间"
    )
    @ApiModelProperty(
            name = "startTime",
            value = "开始时间"
    )
    private String startTime;
    @OperationParamLog(
            message = "截止时间"
    )
    @ApiModelProperty(
            name = "endTime",
            value = "截止时间"
    )
    private String endTime;
}