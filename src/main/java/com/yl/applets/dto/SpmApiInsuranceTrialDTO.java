package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class SpmApiInsuranceTrialDTO implements Serializable {
    @ApiModelProperty("返回MAP时的KEY")
    private String mapKey;


    @ApiModelProperty(
            name = "productTypeId",
            value = "产品类型id"
    )
    private Integer productTypeId;


    @ApiModelProperty(
            name = "productTypeCode",
            value = "产品类型Code"
    )
    private String productTypeCode;


    @ApiModelProperty(
            name = "goodsTypeId",
            value = "产品类型id"
    )
    private Integer goodsTypeId;


    @ApiModelProperty(
            name = "goodsTypeCode",
            value = "产品类型Code"
    )
    private String goodsTypeCode;
    @NotNull(
            message = "数值不能为空"
    )

    @ApiModelProperty(
            name = "value",
            value = "数值"
    )
    private BigDecimal value;
    @ApiModelProperty("客户ID")
    private Integer customerId;
    @ApiModelProperty("所属财务中心id")
    private Integer financialCenterId;
    @NotNull(
            message = "计算时间不能为空"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ApiModelProperty(
            name = "dateTime",
            value = "计算时间"
    )
    private LocalDateTime dateTime;
}
