/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: EsSearchDTO
 * Author:   luhong
 * Date:     2020-11-30 16:48
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto;

import com.alibaba.fastjson.JSON;
import com.yl.applets.entity.Sort;
import com.yl.applets.enums.RangeEnum;
import com.yl.applets.enums.SearchUnit;
import com.yl.applets.enums.SearchUnitConnectorEnum;
import com.yl.applets.enums.SortEnum;
import com.yl.applets.utils.EsShardingUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-11-30
 * @since 1.0.0
 */
@Data
public class EsSearchDTO {
    private static final Logger log = LoggerFactory.getLogger(EsSearchDTO.class);
    @NotBlank(
            message = "Parameter 'index' is blank!"
    )
    private String index;
    private String[] shardingValues;
    private List<SearchUnit> searchUnits;
    private SearchUnitConnectorEnum connectorEnum;
    private Integer page;
    private Integer size;
    private String[] includeFields;
    private List<Sort> sorts;

    public EsSearchDTO(String index, Integer page, Integer size) {
        this.connectorEnum = SearchUnitConnectorEnum.AND;
        this.page = 1;
        this.size = 20;
        this.sorts = new LinkedList();
        Asserts.notBlank(index, "index");
        this.index = index;
        if (page != null) {
            this.page = page;
        }

        if (size != null) {
            this.size = size;
        }

    }

    public EsSearchDTO(String index, Integer page, Integer size, SearchUnitConnectorEnum connectorEnum) {
        this.connectorEnum = SearchUnitConnectorEnum.AND;
        this.page = 1;
        this.size = 20;
        this.sorts = new LinkedList();
        Asserts.notBlank(index, "index");
        this.index = index;
        if (page != null) {
            this.page = page;
        }

        if (size != null) {
            this.size = size;
        }

        if (connectorEnum != null) {
            this.connectorEnum = connectorEnum;
        }

    }

    public EsSearchDTO(String index, Integer page, Integer size, SearchUnitConnectorEnum connectorEnum, String[] shardingValues) {
        this.connectorEnum = SearchUnitConnectorEnum.AND;
        this.page = 1;
        this.size = 20;
        this.sorts = new LinkedList();
        Asserts.notBlank(index, "index");
        this.index = index;
        if (page != null) {
            this.page = page;
        }

        if (size != null) {
            this.size = size;
        }

        if (connectorEnum != null) {
            this.connectorEnum = connectorEnum;
        }

        if (shardingValues != null && shardingValues.length > 0) {
            this.shardingValues = shardingValues;
        }

    }

    public EsSearchDTO sortBy(String field, SortEnum sortEnum) {
        Asserts.notBlank(field, "field");
        if (sortEnum == null) {
            sortEnum = SortEnum.ASC;
        }

        Sort sort = new Sort(field, sortEnum);
        this.sorts.add(sort);
        return this;
    }

    public EsSearchDTO distanceSortBy(String field, String unit, String coordinate, SortEnum sortEnum) {
        Asserts.notBlank(field, "field");
        Asserts.notBlank(field, "coordinate");
        if (sortEnum == null) {
            sortEnum = SortEnum.ASC;
        }

        if (StringUtils.isBlank(unit)) {
            unit = "m";
        }

        Sort sort = new Sort(field, sortEnum, true, unit, coordinate);
        this.sorts.clear();
        this.sorts.add(sort);
        return this;
    }

    public EsSearchDTO includeFields(String... includeFields) {
        this.includeFields = includeFields;
        return this;
    }

    public EsSearchDTO shardingValues(String... shardingValues) {
        this.shardingValues = shardingValues;
        return this;
    }

    public String[] getShardingValues() {
        if (this.shardingValues != null && this.shardingValues.length > 50) {
            log.warn("shardingValues too long.Ignored");
            this.shardingValues = null;
        }

        return this.shardingValues;
    }

    public EsSearchDTO appendSearchUnits(SearchUnit... searchUnit) {
        this.searchUnits = (List)Optional.ofNullable(this.searchUnits).orElse(new LinkedList());
        this.searchUnits.addAll(Arrays.asList(searchUnit));
        return this;
    }

    public static String handleTableName(String tableName) {
        if (StringUtils.isNotBlank(tableName)) {
            Matcher matcher = Pattern.compile("_\\d+$").matcher(tableName);
            if (matcher.find()) {
                tableName = matcher.replaceAll("");
            }
        }

        return tableName;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public EsSearchDTO shardingValueAutoCompletion(Map<String, String> routingConfig) {
        long start = System.currentTimeMillis();
        if (this.getShardingValues() != null && this.getShardingValues().length > 0) {
            return this;
        } else {
            List<SearchUnit> searchUnits = this.getSearchUnits();
            if (CollectionUtils.isEmpty(searchUnits)) {
                return this;
            } else {
                String table = handleTableName(this.getIndex());
                if (!routingConfig.containsKey(table)) {
                    return this;
                } else {
                    String routingKeyAll = (String)routingConfig.get(table);
                    String routingKey = routingKeyAll.replaceAll("date@", "");
                    Set<String> shardingValues = new TreeSet();
                    List<Object[]> rangeShardingValuesList = new ArrayList();
                    Iterator var10 = searchUnits.iterator();

                    while(true) {
                        Object[] values;
                        while(var10.hasNext()) {
                            SearchUnit searchUnit = (SearchUnit)var10.next();
                            String[] fields = searchUnit.getFields();
                            values = searchUnit.getValues();
                            RangeEnum[] ranges = searchUnit.getRanges();
                            switch(searchUnit.getSearchTypeEnum()) {
                                case TERM:
                                    if (routingKey.equals(fields[0])) {
                                        if (routingKeyAll.contains("date@")) {
                                            Optional.ofNullable(EsShardingUtils.dateShardingValue(values[0])).ifPresent(shardingValues::add);
                                        } else {
                                            shardingValues.add(values[0].toString());
                                        }
                                    }
                                    break;
                                case TERMS:
                                    if (routingKey.equals(fields[0])) {
                                        if (routingKeyAll.contains("date@")) {
                                            Arrays.stream(values).forEach((e) -> {
                                                Optional.ofNullable(EsShardingUtils.dateShardingValue(e)).ifPresent(shardingValues::add);
                                            });
                                        } else {
                                            Arrays.stream(values).forEach((e) -> {
                                                shardingValues.add(e.toString());
                                            });
                                        }
                                    }
                                    break;
                                case RANGE:
                                    for(int i = 0; i < fields.length; ++i) {
                                        String field = fields[i];
                                        if (routingKey.equals(field)) {
                                            Object[] preRangeShardingValues = new Object[2];
                                            if (ranges[i] != RangeEnum.GT && ranges[i] != RangeEnum.GTE) {
                                                if (preRangeShardingValues[1] != null) {
                                                    log.warn("sharding value parsing may be wrong.SearchUnit:{}", searchUnit);
                                                }

                                                if (!routingKeyAll.contains("date@") && values[i] instanceof String) {
                                                    preRangeShardingValues[1] = Long.parseLong(values[i].toString());
                                                } else {
                                                    preRangeShardingValues[1] = values[i];
                                                }
                                            } else {
                                                if (preRangeShardingValues[0] != null) {
                                                    log.warn("sharding value parsing may be wrong.SearchUnit:{}", searchUnit);
                                                }

                                                if (!routingKeyAll.contains("date@") && values[i] instanceof String) {
                                                    preRangeShardingValues[0] = Long.parseLong(values[i].toString());
                                                } else {
                                                    preRangeShardingValues[0] = values[i];
                                                }
                                            }

                                            rangeShardingValuesList.add(preRangeShardingValues);
                                        }
                                    }
                            }
                        }

                        if (!CollectionUtils.isEmpty(rangeShardingValuesList)) {
                            TreeSet<Object> lowerValues = new TreeSet();
                            TreeSet<Object> upperValues = new TreeSet();
                            Iterator var20 = rangeShardingValuesList.iterator();

                            while(var20.hasNext()) {
                                values = (Object[])var20.next();
                                Optional.ofNullable(values[0]).ifPresent(lowerValues::add);
                                Optional.ofNullable(values[1]).ifPresent(upperValues::add);
                            }

                            if (lowerValues.size() != upperValues.size()) {
                                log.warn("sharding value parsing may be wrong. table:{}, searchUnits:{}", table, searchUnits);
                                return this;
                            }

                            Object lowObj;
                            while((lowObj = lowerValues.pollFirst()) != null) {
                                if (routingKeyAll.contains("date@")) {
                                    Optional.ofNullable(EsShardingUtils.dateShardingValues(lowObj, upperValues.pollFirst())).ifPresent((e) -> {
                                        List<String> rangeShardingValues = Arrays.asList(e);
                                        shardingValues.addAll(rangeShardingValues);
                                    });
                                } else {
                                    Optional.ofNullable(EsShardingUtils.numberShardingValues(lowObj, upperValues.pollFirst())).ifPresent((e) -> {
                                        List<String> rangeShardingValues = Arrays.asList(e);
                                        shardingValues.addAll(rangeShardingValues);
                                    });
                                }
                            }
                        }

                        log.debug("sharding value parsing took:{} ms, table:{}, shardingValues:{}, searchUnits:{}", new Object[]{System.currentTimeMillis() - start, this.getIndex(), shardingValues, JSON.toJSONString(this.getSearchUnits())});
                        return this.shardingValues((String[])shardingValues.toArray(new String[0]));
                    }
                }
            }
        }
    }

}