package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-08-14 18:11 <br>
 * @Author: zhipeng.liu
 */
@Data
@ApiModel(value = "微信用户请求体",description = "微信用户请求体")
@Accessors(chain = true)
public class WxUserDTO {

    @ApiModelProperty(value = "性别",notes = "0:未知，1：男性，2女性")
    private Integer gender;

    @ApiModelProperty(value = "城市",notes = "城市")
    private String city;

    @ApiModelProperty(value = "昵称",notes = "昵称")
    private String nickName;

    @ApiModelProperty(value = "头像地址",notes = "头像地址")
    private String avatarUrl;

    @ApiModelProperty(value = "code",notes = "wx登录凭证")
    private String code;

    @ApiModelProperty(value = "手机号",notes = "手机号")
    private String mobile;

    @ApiModelProperty(value = "省份",notes = "省份")
    private String province;

    @ApiModelProperty(value = "区县",notes = "区县")
    private String zone;

    @ApiModelProperty(value = "设备信息",notes = "设备信息")
    private String equipmentInfo;

    @ApiModelProperty(value = "android/ios", notes = "android/ios")
    private String equipmentType;

}
