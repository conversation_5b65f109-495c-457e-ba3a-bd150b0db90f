package com.yl.applets.dto.consolidation;

import com.yl.common.base.valid.dto.BaseApiDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/11/21 10:38
 */
@Data
@ApiModel("新疆集运模版信息")
public class ConsolidationDTO extends BaseApiDTO {


    /**
     * id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long memberId;


    /**
     * 一段快递运单号
     */
    @NotBlank(message = "一段快递运单号不能为空")
    @Size(max = 50, message = "一段快递运单号不能超过50个字符")
    private String preOrderNo;


    /**
     * 快递公司编码
     */
    @NotBlank(message = "快递公司不能为空")
    private String expressCompany;

    /**
     * 寄件人姓名
     */
    private String senderName;


    /**
     * 寄件人手机号
     */
    private String senderMobilePhone;


    /**
     * 寄件省份ID
     */
    private Integer senderProvinceId;

    /**
     * 寄件省份名称
     */
    private String senderProvinceName;

    /**
     * 寄件城市名称
     */
    private String senderCityName;


    /**
     * 寄件城市ID
     */
    private Integer senderCityId;


    /**
     * 寄件区域名称
     */
    private String senderAreaName;


    /**
     * 寄件区域Id
     */
    private Integer senderAreaId;


    /**
     * 寄件详细地址
     */
    private String senderDetailedAddress;


    /**
     * 收件人姓名
     */
    @NotBlank(message = "收件人姓名不能为空")
    private String receiverName;


    /**
     * 收件人公司
     */
    private String receiverCompany;


    /**
     * 收件人手机号
     */
    @NotBlank(message = "收件人手机号不能为空")
    private String receiverMobilePhone;


    /**
     * 收件省份id
     */
    @NotNull(message = "收件省份id不能为空")
    private Integer receiverProvinceId;


    /**
     * 收件省份名称
     */
    @NotBlank(message = "收件省份名称不能为空")
    private String receiverProvinceName;


    /**
     * 收件城市id
     */
    @NotNull(message = "收件城市id不能为空")
    private Integer receiverCityId;


    /**
     * 收件城市名称
     */
    @NotBlank(message = "收件城市名称不能为空")
    private String receiverCityName;

    /**
     * 收件区域ID
     */
    @NotNull(message = "收件区域ID不能为空")
    private Integer receiverAreaId;

    /**
     * 收件区域名称
     */
    @NotBlank(message = "收件区域名称不能为空")
    private String receiverAreaName;


    /**
     * 收件详细地址
     */
    @NotBlank(message = "收件详细地址不能为空")
    private String receiverDetailedAddress;


    /**
     * 寄件网点编号
     */
    private String networkCode;


    /**
     * 寄件网点名称
     */
    private String networkName;


    /**
     * 寄件网点id
     */
    private Long networkId;


    /**
     * 注册人手机号不能为空
     */

    private String registerMobilePhone;

}
