package com.yl.applets.dto.consolidation;

import com.yl.common.base.valid.dto.BaseApiDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/11/21 10:38
 */
@Data
@ApiModel("新疆集运取消模版信息")
public class ConsolidationCancelDTO extends BaseApiDTO {

    /**
     * id
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 用户id
     */
    private Long memberId;


    /**
     * 一段快递运单号
     */
    @NotBlank(message = "一段快递运单号不能为空")
    private String preOrderNo;






}
