package com.yl.applets.dto.consolidation;

import com.yl.common.base.valid.dto.BaseApiDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/11/21 10:38
 */
@Data
@ApiModel("新疆集运分页模版信息")
public class ConsolidationPageDTO extends BaseApiDTO  implements Serializable {


    /**
     * 用户id
     */
    private Integer memberId;


    /**
     * 一段快递运单号
     */
    private String preOrderNo;


    /**
     * 订单状态, 0:待转运; 1:已取件; 2:已签收
     */
    private Integer orderStatus;

    /**
     * 支付状态, 0:未支付; 1:待支付; 2:支付成功;
     */
    private Integer payStatus;


    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Integer current;


    /**
     * 每页大小
     */
    @NotNull(message = "每页大小不能为空")
    private Integer size;

}
