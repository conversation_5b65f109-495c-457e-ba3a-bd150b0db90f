package com.yl.applets.dto.consolidation;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/11/21 15:34
 */
@Data
public class ConsolidationUnPayDTO {
    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private Long memberId;

    /**
     * 开始时间，不传默认查询近180天数据
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;


    /**
     * 结束时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDate;


}
