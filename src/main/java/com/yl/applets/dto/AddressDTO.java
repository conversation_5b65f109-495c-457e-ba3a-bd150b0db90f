package com.yl.applets.dto;

import com.yl.common.base.constant.UpdateGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * 地址管理
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AddressDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {UpdateGroup.class})
    private Long id;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    @Size(min = 1, max = 100, message = "姓名长度只能在1-100之间")
    private String name;

    /**
     * 手机号
     */
//    @Pattern(regexp = "^1\\d{10}$", message = "手机号码不合法")
    @NotBlank(message = "手机号码不能为空")
    private String mobile;

    /**
     * 省份id
     */
    @NotNull(message = "省份id不能为空")
    private Integer provinceId;

    /**
     * 省份名称
     */
    @Size(max = 60, message = "省份名称不能超过60个字符")
    @NotBlank(message = "省份名称不能为空")
    private String provinceName;

    /**
     * 城市id
     */
    @NotNull(message = "城市id不能为空")
    private Integer cityId;

    /**
     * 城市名称
     */
    @Size(max = 60, message = "城市名称不能超过60个字符")
    @NotBlank(message = "城市名称不能为空")
    private String cityName;

    /**
     * 区域id
     */
    @NotNull(message = "区域id不能为空")
    private Integer areaId;

    /**
     * 区域名称
     */
    @Size(max = 60, message = "区域名称不能超过60个字符")
    @NotBlank(message = "区域名称不能为空")
    private String areaName;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空")
    @Size(min = 4, max = 80, message = "详细地址长度只能在4-80之间")
    private String detailedAddress;

    /**
     * 是否默认地址:1是,2否
     */
    @Min(value = 1, message = "是否默认地址只能为1或者2")
    @Max(value = 2, message = "是否默认地址只能为1或者2")
    @NotNull(message = "是否默认地址不能为空")
    private Integer isDefault;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 地址类型：1寄件地址
     */
    @Min(value = 1, message = "地址类型只能为1或者2")
    @Max(value = 2, message = "地址类型只能为1或者2")
//    @NotNull(message = "地址类型不能为空")
    private Integer type;

    //地址标签
    private Integer addressSign;

}
