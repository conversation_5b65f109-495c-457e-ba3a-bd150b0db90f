package com.yl.applets.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title AppletsOrderStatusPathDTO
 * @Package com.yl.edi.lh.dto.applets
 * @Description 小程序订单状态轨迹
 * @date 2021/8/12 2:00 下午
 */
@Data
public class AppletsOrderStatusPathDTO {

    /**
     * 轨迹变化Unix时间戳	是	操作时间	采用Unix时间戳
     */
    @JsonProperty("action_time")
    @JSONField(name="action_time")
    private Long actionTime;

    /**
     * 轨迹变化类型，与普通单保持⼀致，参见附录action_type定义	是	扫描类型	需进行映射转换
     */
    @JsonProperty("action_type")
    @JSONField(name="action_type")
    private String actionType;

    /**
     * 轨迹变化具体信息说明，展示在快递轨迹详情页中。
     */
    @JsonProperty("action_msg")
    @JSONField(name="action_msg")
    private String actionMsg;

    /**
     * 取件员姓名	当分配取件员成功时返回	取件员姓名	订单调度至业务员时，必须返回
     */
    @JsonProperty("pickup_courier_name")
    @JSONField(name="pickup_courier_name")
    private String pickupCourierName;

    /**
     * 取件员电话	当分配取件员成功时返回	取件员电话	订单调度至业务员时，必须返回
     */
    @JsonProperty("pickup_courier_phone")
    @JSONField(name="pickup_courier_phone")
    private String pickupCourierPhone;

    /**
     * 派件员姓名	当分配派件员成功时返回	派件员姓名	出仓扫描时，必须返回
     */
    @JsonProperty("delivery_courier_name")
    @JSONField(name="delivery_courier_name")
    private String deliveryCourierName;

    /**
     * 派件员电话	当分配派件员成功时返回	派件员电话	（）出仓扫描时，必须返回
     */
    @JsonProperty("delivery_courier_phone")
    @JSONField(name="delivery_courier_phone")
    private String deliveryCourierPhone;

}