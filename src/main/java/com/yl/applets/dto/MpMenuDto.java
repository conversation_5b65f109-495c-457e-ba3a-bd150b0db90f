package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-09-03
 */

@Data
@ApiModel(value = "公众号菜单dto",description = "公众号菜单dto")
public class MpMenuDto {

    @ApiModelProperty(value = "创建人Id",notes = "创建人Id")
    private String createBy;

    @NotNull
    @ApiModelProperty(value = "菜单结构集合",notes = "菜单结构集合")
    private List<MyWxMenuButton> buttons;


    @ApiModelProperty(value = "发送消息",notes = "发送消息")
    private List<Map<String,String>> textContents;

//    @ApiModelProperty(value = "请求body",notes = "请求body")
//    private String body;


    @ApiModelProperty(value = "mediaJson串",notes = "mediaJson串")
    private List<MediaDto> mediaContents;

    /**
     * 用来回显素材
     */
    @Data
     public static class MediaDto{

        @ApiModelProperty(value = "mediaId",notes = "mediaId")
        private String mediaId;

        @ApiModelProperty(value = "时间",notes = "时间")
        private Date updateTime;

        @ApiModelProperty(value = "素材名字",notes = "素材名字")
        private String name;

        @ApiModelProperty(value = "素材url",notes = "素材url")
        private String url;

        @ApiModelProperty(value = "素材type",notes = "素材type")
        private String type;

        @ApiModelProperty(value = "图文内容跳转url",notes = "图文内容跳转url")
        private String jumpUrl;



    }


    @Data
    public static class MyWxMenuButton {

        private String code;
        private String type;
        private String name;
        private String key;
        private String url;
        private String mediaId;
        private String appId;
        private String pagePath;
        private List<MyWxMenuButton> subButtons = new ArrayList();

    }
}
