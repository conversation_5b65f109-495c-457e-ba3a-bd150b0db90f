package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-09-23 09:22 <br>
 * @Author: zhipeng.liu
 */
@Data
@ApiModel(value = "订单分享-查询页面请求体",description = "订单分享-查询页面请求体")
public class ShareQueryDTO {

    @ApiModelProperty(value = "waybillNo",notes="运单号")
    String waybillNo;

    @ApiModelProperty(value = "orderId",notes="订单号")
    String orderId;

    @ApiModelProperty(value = "times",notes="入参时间戳")
    Long times;

    @ApiModelProperty(value = "sign",notes="密文")
    String sign;

    @ApiModelProperty(value = "mobile",notes="手机号")
    String mobile;
}
