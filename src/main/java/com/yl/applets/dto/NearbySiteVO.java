package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class NearbySiteVO {
    /** 街道名称 */
    @ApiModelProperty(value = "街道名称")
    private String street;

    /** 详细地址 */
    @ApiModelProperty(value = "详细地址")
    private String address;

    /** 距离 */
    @ApiModelProperty(value = "距离")
    private Double distance;

    /** 省名称 */
    @ApiModelProperty(value = "省名称")
    private String province;

    /** 市名称 */
    @ApiModelProperty(value = "市名称")
    private String city;

    /** 区名称 */
    @ApiModelProperty(value = "区名称")
    private String area;

    /** 营业时间 */
    @ApiModelProperty(value = "营业时间")
    private String openingTime;

    /** 服务点名称 */
    @ApiModelProperty(value = "服务点名称")
    private String serviceSiteName;

    /** 服务点编号 */
    @ApiModelProperty(value = "服务点编号")
    private String serviceSiteCode;

    /** 服务点联系电话 */
    @ApiModelProperty(value = "服务点联系电话")
    private String serviceSitePhone;
}
