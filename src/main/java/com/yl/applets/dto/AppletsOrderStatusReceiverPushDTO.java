package com.yl.applets.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title AppletsOrderStatusReceiverPushDTO
 * @Package com.yl.edi.lh.dto.applets
 * @Description 小程序订单状态推送收件人
 * @date 2021/8/12 1:53 下午
 */
@Data
public class AppletsOrderStatusReceiverPushDTO {

    /**
     * 寄件人信息
     */

    @JSONField(name="sender")
    @JsonProperty("sender")
    private AppletsOrderStatusCustomerDTO sender;

    /**
     * 收件人信息
     */

    @JSONField(name="receiver")
    @JsonProperty("receiver")
    private AppletsOrderStatusCustomerDTO receiver;

    /**
     * 运单号
     */

    @JSONField(name="waybill_id")
    @JsonProperty("waybill_id")
    private String waybillId;

    /**
     * 当前需要推送消息的轨迹（轨迹文案）
     */

    @JSONField(name="path")
    @JsonProperty("path")
    private AppletsOrderStatusPathDTO path;

    /**
     * 运单创建时间，unix时间戳（秒）
     */

    @JSONField(name="create_time")
    @JsonProperty("create_time")
    private Long createTime;
}