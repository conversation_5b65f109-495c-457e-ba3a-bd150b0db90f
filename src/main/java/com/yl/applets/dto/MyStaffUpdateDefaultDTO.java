package com.yl.applets.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/2/24 16:36
 */
@Data
public class MyStaffUpdateDefaultDTO {

    @NotNull(message = "修改的数据id不存在")
    private String id;

    @NotNull(message = "默认修改值不存在")
    private Integer defaultNum;

    Integer numberId;


    private List<Long> userIds;

}
