package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 优惠劵领取使用信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "使用优惠券下单", description = "使用优惠券下单")
public class OrderPromotionDto implements Serializable {

    @NotNull(message = "寄件省份id不能为空")
    private Integer senderProvinceId;

    @NotBlank(message = "寄件省份名称不能为空")
    @Size(max = 60, message = "寄件省份名称不能超过60个字符")
    private String senderProvinceName;

    @NotNull(message = "寄件城市id不能为空")
    private Integer senderCityId;

    @NotBlank(message = "寄件城市名称不能为空")
    @Size(max = 60, message = "寄件城市名称不能超过60个字符")
    private String senderCityName;

    @NotNull(message = "寄件区域id不能为空")
    private Integer senderAreaId;

    @NotBlank(message = "寄件区域名称不能为空")
    @Size(max = 60, message = "寄件区域名称不能超过60个字符")
    private String senderAreaName;

    @NotBlank(message = "寄件详细地址不能为空")
    @Size(min = 4, max = 80, message = "寄件详细地址长度只能在4-80之间")
    private String senderDetailedAddress;


    @NotNull(message = "收件省份id不能为空")
    private Integer receiverProvinceId;

    @NotBlank(message = "收件省份名称不能为空")
    @Size(max = 60, message = "收件省份名称不能超过60个字符")
    private String receiverProvinceName;

    @NotNull(message = "收件城市id不能为空")
    private Integer receiverCityId;

    @NotBlank(message = "收件城市名称不能为空")
    @Size(max = 60, message = "收件城市名称不能超过60个字符")
    private String receiverCityName;

    @NotNull(message = "收件区域id不能为空")
    private Integer receiverAreaId;

    @NotBlank(message = "收件区域名称不能为空")
    @Size(max = 60, message = "收件区域名称不能超过60个字符")
    private String receiverAreaName;

    @NotBlank(message = "收件详细地址不能为空")
    @Size(min = 4, max = 80, message = "收件详细地址长度只能在4-80之间")
    private String receiverDetailedAddress;

    @Range(min = 0, max = 50000, message = "保价金额只能在0-50000之间")
    private BigDecimal declaredValue;

    @NotBlank(message = "物品类型编码不能为空")
    @Size(max = 30, message = "物品类型编码不能超过30个字符")
    private String goodsTypeCode;

    @ApiModelProperty(value = "物品类型名称")
    @Size(max = 30, message = "物品类型名称不能超过30个字符")
    @NotBlank(message = "物品类型名称不能为空")
    private String goodsTypeName;

    @Size(max = 30, message = "付款方式编码不能超过30个字符")
    private String paymentModeCode;

    @ApiModelProperty(value = "结算方式名称")
    @Size(max = 60, message = "结算方式名称不能超过60个字符")
    private String paymentModeName;

    @ApiModelProperty(value = "取件网点id")
    private Long pickNetworkId;

    @ApiModelProperty(value = "保价费")
    @Digits(integer = 10, fraction = 2, message = "保价费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal insuredValue;

    @ApiModelProperty(value = "标准运费")
    @Digits(integer = 10, fraction = 2, message = "标准运费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal standardValue;

    @ApiModelProperty(value = "总费用")
    @Digits(integer = 10, fraction = 2, message = "总费用超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal totalFreight;

   @ApiModelProperty(value = "查询类型 1已领取、2已使用、3已失效")
   private Integer queryType;

    private Long current;

    private Long size;

    private Long userId;

    @ApiModelProperty(value = "会员id")
    private Long numberId;
}
