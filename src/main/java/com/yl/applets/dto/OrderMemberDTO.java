package com.yl.applets.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OrderMemberDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank(message = "物品名称不能为空")
    @Size(max = 24, message = "物品名称不能超过24个字符")
    private String goodsName;

    @NotNull(message = "物品件数不能为空")
    @Range(min = 1, max = 999999, message = "物品件数只能在1-999999之间")
    private Integer goodsNumber;

    @NotNull(message = "物品重量不能为空")
    @Range(min = 1, max = 20, message = "物品重量只能在1-20之间")
    private BigDecimal goodsWeight;

}
