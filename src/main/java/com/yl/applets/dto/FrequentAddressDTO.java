package com.yl.applets.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/2/25 14:10
 */
@Data
public class FrequentAddressDTO {



    /**
     * 用户id
     */
    @NotNull(message = "用户id 不为空")
    private Integer userId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名 不为空")
    private String name;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号 不为空")
    private String mobile;

    /**
     * 省份id
     */
    @NotNull(message = "省份id 不为空")
    private Integer provinceId;

    /**
     * 省份名称
     */
    @NotBlank(message = "省份名称 不为空")
    private String provinceName;

    /**
     * 城市id
     */
    @NotNull(message = "城市id 不为空")
    private Integer cityId;

    /**
     * 城市名称
     */
    @NotBlank(message = "城市名称 不为空")
    private String cityName;

    /**
     * 区域id
     */
    @NotNull(message = "区域id 不为空")
    private Integer areaId;

    /**
     * 区域名称
     */
    @NotBlank(message = "区域名称 不为空")
    private String areaName;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址 不为空")
    private String detailedAddress;


    /**
     * 创建人ID
     */
    @NotNull(message = "创建人ID 不为空")
    private Integer createBy;

    /**
     * 最后更新人ID
     */
    @NotNull(message = "最后更新人ID 不为空")
    private Integer updateBy;


    private String createByName;

    /**
     * 最后修改人名称
     */
    private String updateByName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    /**
     * 地址hash值
     */
    private String addressHash;

    /**
     * 公司名称
     */
    private String companyName;

}
