/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: OmsOrderMemberDTO
 * Author:   luhong
 * Date:     2020-10-14 18:24
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto;

import com.yl.common.base.valid.group.EdiSaveGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-14
 * @since 1.0.0
 */
@ApiModel(
        value = "OmsOrderMember请求对象",
        description = "订单物品明细表请求实体"
)
@Data
public class OmsOrderMemberDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键id")
    private Long id;
    @ApiModelProperty("订单表主键id")
    private Long orderId;
    @ApiModelProperty("商品类型")
    @NotNull(
            message = "商品类型不能为空",
            groups = {EdiSaveGroup.class}
    )
    @Size(
            max = 200,
            message = "商品类型最大只能支持500个字符"
    )
    private String goodsTypeName;
    @ApiModelProperty("物品名称")
    @Size(
            max = 500,
            message = "物品名称最大只能支持500个字符"
    )
    private String goodsName;
    @ApiModelProperty("物品中文名称")
    @Size(
            max = 100,
            message = "物品中文名称最大只能支持100个字符"
    )
    private String chineseName;
    @ApiModelProperty("物品英文名称")
    @Size(
            max = 100,
            message = "物品英文名称最大只能支持100个字符"
    )
    private String englishName;
    @ApiModelProperty("物品数量")
    private Integer goodsNumber;
    @ApiModelProperty("物品重量")
    private BigDecimal goodsWeight;
    @ApiModelProperty("申报币别")
    @Size(
            max = 10,
            message = "申报币别最大只能支持10个字符"
    )
    private String pricecurrency;
    @ApiModelProperty("物品价值")
    private BigDecimal goodsValue;
    @ApiModelProperty("物品url")
    @Size(
            max = 200,
            message = "物品url最大只能支持200个字符"
    )
    private String goodsUrl;
    @ApiModelProperty("物品描述")
    @Size(
            max = 200,
            message = "物品描述最大只能支持200个字符"
    )
    private String goodsDesc;
    @ApiModelProperty("备注")
    private String remarks;
    @ApiModelProperty("录入时间")
    private LocalDateTime inputTime;
    @ApiModelProperty("是否删除,1未删除，2已删除")
    private Integer isDelete;
    private BigDecimal insuredAmount;
    private String extendFields;
}