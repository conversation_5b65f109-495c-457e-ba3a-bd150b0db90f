package com.yl.applets.dto.coupons;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-05-26 15:47
 * @Version 1.0
 */
@Data
public class TianyiPublicDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 由接口提供方统一分配的ID
     */
    private String appId;

    /**
     * 精确到秒，比如2014-09-11 14:26:44
     */
    private String timestamp;

    /**
     * 签名
     */
    @NotBlank(message = "签名不能为空")
    private String sign;

    /**
     * 签名方式，RSA，大写
     */
    @NotBlank(message = "签名方式不能为空")
    private String signType;

    private String charset;


}
