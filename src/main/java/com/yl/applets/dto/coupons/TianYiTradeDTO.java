package com.yl.applets.dto.coupons;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-05-25 15:01
 * @Version 1.0
 */
@Data
public class TianYiTradeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 由接口提供方统一分配的ID
     */
    private String appId;

    /**
     * 精确到秒，比如2014-09-11 14:26:44
     */
    private String timestamp;

    /**
     * 签名
     */
    @NotBlank(message = "签名不能为空")
    private String sign;

    /**
     * 签名方式，RSA，大写
     */
    @NotBlank(message = "签名方式不能为空")
    private String signType;

    private String charset;

    @ApiModelProperty(name = "service",value = "接口标识，trade")
    private String service;

    @ApiModelProperty(name = "cpCode",value = "CP标识")
    private String cpCode;

    @ApiModelProperty(name = "orderNo",value = "平台订单号")
    private String orderNo;

    @ApiModelProperty(name = "cpUserAccount",value = "CP用户账号")
    private String cpUserAccount;

    @ApiModelProperty(name = "cpPassportAccount",value = "通行证账号")
    private String cpPassportAccount;

    @ApiModelProperty(name = "cpUserAccountType",value = "充值类型")
    private String cpUserAccountType;

    @ApiModelProperty(name = "productCode",value = "CP数娱产品标识")
    private String productCode;

    @ApiModelProperty(name = "productRegionCode",value = "充值用户所选目标产品分区")
    private String productRegionCode;

    @ApiModelProperty(name = "goodCode",value = "CP商品标识")
    private String goodCode;

    @ApiModelProperty(name = "goodType",value = "商品类型CP商品型")
    private String goodType;

    @ApiModelProperty(name = "goodNumber",value = "商品数量")
    private String goodNumber;

    @ApiModelProperty(name = "originalPrice",value = "originalPrice")
    private String originalPrice;

    @ApiModelProperty(name = "settlePrice",value = "结算单价")
    private String settlePrice;

    @ApiModelProperty(name = "cpSharePart",value = "通过扫码/wap而获得的cp分成")
    private String cpSharePart;
}
