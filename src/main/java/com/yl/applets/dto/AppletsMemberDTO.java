package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/4/2 15:22
 */
@Data
public class AppletsMemberDTO {

    private Integer id;

    //下单id
    private Integer numberId;
    @ApiModelProperty(value = "性别",notes = "0:未知，1：男性，2女性")
    private Integer gender;

    @ApiModelProperty(value = "城市",notes = "城市")
    private String city;

    @ApiModelProperty(value = "省份",notes = "省份")
    private String province;

    @ApiModelProperty(value = "昵称",notes = "昵称")
    private String nickName;

    @ApiModelProperty(value = "头像地址",notes = "头像地址")
    private String avatarUrl;

    @ApiModelProperty(value = "手机号",notes = "手机号")
    private String mobile;

    @ApiModelProperty(value = "token",notes = "用户鉴权token")
    private String token;

    @ApiModelProperty(value = "openid",notes = "openid")
    private String openid;

    @ApiModelProperty(value = "unionid",notes = "unionid")
    private String unionid;

    @ApiModelProperty(value = "sessionKey",notes = "wx密钥")
    private String sessionKey;
    private List<Integer> userIds;

    private String channelSource;

    @ApiModelProperty(value = "渠道type",notes = "渠道type")
    private Integer type;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private String uuid;

    //会员过期时间
    private LocalDateTime memberExpiresTime;

    private Integer recordNum;


    /**
     * 集运的用户id
     */
    private Integer jyUserId;
}
