package com.yl.applets.dto;

import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021<br>
 *
 * @Description: 三段码请求的DTO
 * @Project: yl-ass-dispatchcode-api
 * @CreateDate: Created in 2021-04-28 10:59
 * @Author: linhaibo
 */
@Data
@ToString
public class DispatchCodeRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号或运单号，用于追踪问题
     */
    private String uniqueSequence;

    private Long provinceId;
    /**
     * 省中文
     */
    @NotBlank(message = "收件省不能为空")
    private String province;

    private Long cityId;
    /**
     * 市中文
     */
    @NotBlank(message = "收件市不能为空")
    private String city;

    private Long areaId;
    /**
     * 区中文
     */
    @NotBlank(message = "收件区不能为空")
    private String area;


    /**
     * 详细地址(不包含省市区乡镇)
     */
    @NotBlank(message = "详细地址")
    private String details;

    /**
     * 乡镇中文
     */
    private String town;

    /**
     * 寄件省ID
     */
    private Long senderProviceId;
    /**
     * 寄件市ID
     */
    private Long senderCityId;
    /**
     * 寄件区ID
     */
    private Long senderAreaId;

    /**
     * 寄件省
     */
    private String senderProvice;
    /**
     * 寄件市
     */
    private String senderCity;
    /**
     * 寄件区
     */
    private String senderArea;

    /**
     * 订单来源
     */
    private String bizSource;

    /**
     * 自定义超时时间（单位：ms）
     */
    private Long timeout;

}
