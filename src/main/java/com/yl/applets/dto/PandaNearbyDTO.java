package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class PandaNearbyDTO  implements Serializable {
    @ApiModelProperty(name = "lng", value = "经度(例:118.832936)")
    @NotNull(message = "经度不能为空")
    private String lng;

    @ApiModelProperty(name = "lat", value = "纬度(例:31.871466)")
    @NotNull(message = "纬度不能为空")
    private String lat;
}
