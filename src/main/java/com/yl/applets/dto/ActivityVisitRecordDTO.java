package com.yl.applets.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: x<PERSON>g<PERSON><PERSON>
 * @create: 2020-08-24 17:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ActivityVisitRecordDTO implements Serializable {
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 类型 1:曝光 2:访问
     */
    private Integer type;
    /**
     * 访问人
     */
    private String phone;
    /**
     * 访问时间
     */
    private LocalDateTime activityTime;
    /**
     * openId
     */
    private String openId;

    private String sort;

    private String actType;
}
