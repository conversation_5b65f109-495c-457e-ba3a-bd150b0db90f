package com.yl.applets.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: xiongweibin
 * @create: 2020-08-24 17:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VipFormRecordDTO implements Serializable {

    /**
     * 类型   VipActionSourceEnum
     */
    private Integer type;
    /**
     * 点击类型   VipActionSourceEnum
     */
    private Integer actionType;



}
