package com.yl.applets.dto;

import lombok.Data;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2024-04-22
 */
@Data
public class PreferencesCommonDto {

    /**
     * 工作日一级偏好编码
     */

    private String weekdayOneLevelCode;

    /**
     * 工作日一级偏好名称
     */
    private String weekdayOneLevelName;

    /**
     * 工作日二级偏好编码
     */
    private String weekdayTwoLevelCode;

    /**
     * 工作日二级偏好名称
     */
    private String weekdayTwoLevelName;

    /**
     * 休息日一级偏好编码
     */
    private String weekendOneLevelCode;

    /**
     * 休息日一级偏好名称
     */
    private String weekendOneLevelName;

    /**
     * 休息日二级偏好编码
     */
    private String weekendTwoLevelCode;

    /**
     * 休息日二级偏好名称
     */
    private String weekendTwoLevelName;




    private List<OrderMarkingStatus> markingStatusList;


    @Data
    public static class OrderMarkingStatus {

        private String code;//标识code
        private String name;//标识code
        private Boolean status;//标识状态
        private List<OrderMarkingStatus> child;

    }
}
