package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-11-26
 */
@Data
public class PandaOrderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    //新增订单才传
    @ApiModelProperty(value = "熊猫门店ID（新增订单时需要传）")
    private String stationId;

    //修改订单才传
    @ApiModelProperty(value = "订单ID（修改订单才需要传）")
    private String sendId;

    @ApiModelProperty(value = "极兔用户ID")
    private String jtUserId;

//    @ApiModelProperty(value = "极兔订单编号")
//    private Long id;


//    @ApiModelProperty(value = "门店名称")
//    @NotNull(message = "门店名称不能为空")
//    private String stationName;

//    @ApiModelProperty(value = "门店编码")
//    @NotNull(message = "门店编码不能为空")
//    private String stationCode;


//    @ApiModelProperty(value = "付款方式名称")
//    private String paidModeName;
//
//    @ApiModelProperty(value = "付款方式code")
//    private String paidModeCode;

    //寄件人实名
    private String realName;

    //寄件人身份证
    private String cardNo;

    @ApiModelProperty(value = "寄件人姓名")
    @Size(max = 100, message = "寄件人姓名不能超过100个字符")
    private String senderName;

//    @ApiModelProperty(value = "寄件人公司")
//    @Size(max = 100, message = "寄件人公司不能超过100个字符")
//    private String senderCompany;

    @ApiModelProperty(value = "寄件人手机号")
    @Size(max = 30, message = "寄件人手机号不能超过30个字符")
    @NotBlank(message = "寄件人手机号不能为空")
    private String senderMobilePhone;

//    @ApiModelProperty(value = "寄件人座机")
//    @Size(max = 30, message = "寄件人固话不能超过30个字符")
//    private String senderTelphone;
//
//    @ApiModelProperty(value = "寄件国家名称")
//    @Size(max = 60, message = "寄件国家名称不能超过60个字符")
//    private String senderCountryName;
//
//    @ApiModelProperty(value = "寄件国家Id")
//    private Integer senderCountryId;

    @ApiModelProperty(value = "寄件省份名称")
    @Size(max = 60, message = "寄件省份名称不能超过60个字符")
    @NotBlank(message = "寄件省份名称不能为空")
    private String senderProvinceName;

//    @ApiModelProperty(value = "寄件省份id")
//    @NotNull(message = "寄件省份id不能为空")
//    private Integer senderProvinceId;

    @ApiModelProperty(value = "寄件城市名称")
    @Size(max = 60, message = "寄件城市名称不能超过60个字符")
    @NotBlank(message = "寄件城市名称不能为空")
    private String senderCityName;
//
//    @ApiModelProperty(value = "寄件城市id")
//    @NotNull(message = "寄件城市id不能为空")
//    private Integer senderCityId;

    @ApiModelProperty(value = "寄件区域名称")
    @Size(max = 60, message = "寄件区域名称不能超过60个字符")
    @NotBlank(message = "寄件区域名称不能为空")
    private String senderAreaName;

//    @ApiModelProperty(value = "寄件区域Id")
//    @NotNull(message = "寄件区域Id不能为空")
//    private Integer senderAreaId;

//    @ApiModelProperty(value = "寄件乡镇")
//    @Size(max = 200, message = "寄件乡镇不能超过200个字符")
//    private String senderTownship;
//
//    @ApiModelProperty(value = "寄件街道")
//    @Size(max = 200, message = "不能超过200个字符")
//    private String senderStreet;

    @ApiModelProperty(value = "寄件详细地址")
    @Size(max = 255, message = "寄件详细地址不能超过255个字符")
    private String senderDetailedAddress;

//    @ApiModelProperty(value = "寄件邮编")
//    @Size(max = 60, message = "寄件邮编不能超过60个字符")
//    private String senderPostalCode;
//
//    @ApiModelProperty(value = "寄件人邮箱")
//    @Size(max = 150, message = "寄件人邮箱不能超过150个字符")
//    private String senderEmail;
//
//    @ApiModelProperty(value = "服务方式名称")
//    @NotBlank(message = "服务方式名称不能为空")
//    @Size(max = 30, message = "寄件服务方式名称不能超过30个字符")
//    private String sendName;
//
//    @ApiModelProperty(value = "服务方式code")
//    @NotBlank(message = "服务方式code不能为空")
//    @Size(max = 30, message = "寄件服务方式code不能超过30个字符")
//    private String sendCode;

    @ApiModelProperty(value = "收件人姓名")
    @Size(max = 100, message = "收件人姓名不能超过100个字符")
    private String receiverName;

//    @ApiModelProperty(value = "收件人公司")
//    @Size(max = 100, message = "收件人公司不能超过100个字符")
//    private String receiverCompany;

    @ApiModelProperty(value = "收件人手机号")
    @Size(max = 30, message = "收件人手机号不能超过30个字符")
    @NotBlank(message = "手机号不能为空")
    private String receiverMobilePhone;

//    @ApiModelProperty(value = "收件人座机")
//    @Size(max = 30, message = "收件人固话不能超过30个字符")
//    private String receiverTelphone;

//    @ApiModelProperty(value = "收件国家名称")
//    @Size(max = 60, message = "收件国家名称不能超过60个字符")
//    private String receiverCountryName;
//
//    @ApiModelProperty(value = "收件国家id")
//    private Integer receiverCountryId;

    @ApiModelProperty(value = "收件省份名称")
    @Size(max = 60, message = "收件省份名称不能超过60个字符")
    private String receiverProvinceName;

//    private Integer receiverProvinceId;

    @ApiModelProperty(value = "收件城市名称")
    @Size(max = 60, message = "收件城市名称不能超过60个字符")
    @NotBlank(message = "收件城市名称不能为空")
    private String receiverCityName;
//
//    @ApiModelProperty(value = "收件城市id")
//    @NotNull(message = "收件城市id不能为空")
//    private Integer receiverCityId;

    @ApiModelProperty(value = "收件区域名称")
    @Size(max = 60, message = "收件区域名称不能超过60个字符")
    @NotBlank(message = "收件区域名称不能为空")
    private String receiverAreaName;

//    @ApiModelProperty(value = "收件区域id")
//    @NotNull(message = "收件区域id不能为空")
//    private Integer receiverAreaId;

//    @ApiModelProperty(value = "收件乡镇")
//    @Size(max = 200, message = "收件乡镇不能超过200个字符")
//    private String receiverTownship;
//
//    @ApiModelProperty(value = "收件街道")
//    @Size(max = 200, message = "收件街道不能超过200个字符")
//    private String receiverStreet;

    @ApiModelProperty(value = "收件详细地址")
    @Size(max = 255, message = "收件详细地址不能超过255个字符")
    private String receiverDetailedAddress;

//    @ApiModelProperty(value = "收件邮编")
//    @Size(max = 60, message = "收件邮编不能超过60个字符")
//    private String receiverPostalCode;
//
//    @ApiModelProperty(value = "收件人邮箱")
//    private String receiverEmail;


    @ApiModelProperty(value = "备注")
    @Size(max = 200, message = "备注不能超过200个字符")
    private String remarks;


//    @ApiModelProperty(value = "需要保价1是，0否")
//    private Integer insured;
//
//    @ApiModelProperty(value = "保价金额")
//    private BigDecimal declaredValue;

    @ApiModelProperty(value = "保价费")
    private BigDecimal insuredValue;

    @ApiModelProperty(value = "标准运费")
    private BigDecimal standardValue;

    @ApiModelProperty(value = "包裹总重量,单位千克")
    @Digits(integer = 5, fraction = 2, message = "包裹总重量超出了允许范围(只允许在5位整数和2位小数范围内)")
    @DecimalMin(value = "0.01", message = "包裹总重量必须大于或等于0.01")
    private BigDecimal packageTotalWeight;

    @ApiModelProperty(value = "总费用")
    private BigDecimal totalFreight;

    /** 签回单 0否   1是  2回单标记*/
    private Integer signReceipt;

    /** 回单金额 */
    private BigDecimal receiptFreight;

    @ApiModelProperty(value = "物品类型名称")
    @Size(max = 60, message = "物品类型名称不能超过60个字符")
    private String goodsTypeName;

//    @ApiModelProperty(value = "物品类型id")
//    private Integer goodsTypeId;
//
//    @ApiModelProperty(value = "物品类型code")
//    @Size(max = 30, message = "物品类型编码不能超过30个字符")
//    @NotBlank(message = "物品类型编码不能为空")
//    private String goodsTypeCode;

//    @ApiModelProperty(value = "物品名称")
//    @Size(max = 200, message = "物品名称不能超过200个字符")
//    private String goodsName;
//
//    @ApiModelProperty(value = "订单录入时间，日期格式 yyyy-MM-dd HH:mm:ss")
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime inputTime;

//    @ApiModelProperty(value = "备注")
//    private String remark;

}
