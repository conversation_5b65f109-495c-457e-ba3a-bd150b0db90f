package com.yl.applets.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 微信支付参数 对应 https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_1.shtml 参数
 */
@Data
public class IpayLooseOrderWXDTO{

    /**
     * 应用ID
     */
    private String app_id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 寄件网点加盟商信息
     */
    private Integer franchiseeId;

    private String franchiseeCode;

    private String franchiseeName;

    /**
     * 寄件网点信息
     */
    private Integer networkId;

    private String networkCode;

    private String networkName;


    /**
     * 寄件网点财务中心信息
     */
    private Integer financialCenterId;

    private String financialCenterCode;

    private String financialCenterName;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 订单金额
     */
    private BigDecimal amount;


    private String payerClientIp;

    /**
     * 用户在服务商appid下的唯一标识。
     */
    private String spOpenid;

    /**
     * 用户在子商户appid下的唯一标识。若传sub_openid，那sub_appid必填
     */
    private String subOpenid;
    /**
     * 二级商户应用ID
     * 否
     * 二级商户申请的公众号或移动应用appid。若sub_openid有传的情况下，sub_appid必填，且sub_appid需与sub_openid对应
     */
    private String subAppid;
    /**
     * 运单号
     */
    private String waybillNo;
    /**
     * 揽收时间
     */
    private LocalDateTime collectTime;
}
