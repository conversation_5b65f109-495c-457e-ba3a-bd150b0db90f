package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-03-14 17:43
 * @Version 1.0
 */
@Data
@ApiModel(value = "CouponsCMCCDTO", description = "河南移动入参DTO")
@Accessors(chain = true)
public class CouponsCMCCDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "phone", name = "手机号，唯一键")
    @NotBlank(message = "手机号不能为空")
    @Length(min = 11, max = 11)
    private String phone;

    @ApiModelProperty(value = "openId", name = "openId，唯一键")
    @NotBlank(message = "openId不能为空")
    private String openId;

    @ApiModelProperty(value = "couponIds", name = "优惠券Id，唯一键")
    @NotEmpty(message = "优惠券Ids集合不能为空")
    @Size(min = 1, max = 2)
    private List<String> couponIds;

}
