package com.yl.applets.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-07-07
 */

@Data
public class MemberUserOffDto {

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户创建时间
     */
    private LocalDateTime createTime;

    /**
     * 注销时间
     */
    private LocalDateTime offTime;



    /**
     * 渠道类型（H5:1，微信：2，百度：3）
     */
    private Integer type;

    /**
     * 会员id
     */
    private Integer memberId;

    private String time;

    public void setOffTime(LocalDateTime offTime) {
        this.offTime = offTime;
        if(this.offTime!=null){
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            this.time = dateTimeFormatter.format(this.offTime);
        }
    }

}
