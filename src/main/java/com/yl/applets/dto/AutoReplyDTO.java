package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.File;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021<br>
 *
 * @description: 小程序自动回复规则
 * @author: caiyao
 * @create: 2021-05-20 10:11
 */
@Data
public class AutoReplyDTO {
    /**
     * id
     */
    private Long id;

    /**
     * 自动回复种类 1:关注回复 2:收到消息回复 3:关键词回复
     */
    @NotNull(message = "自动回复种类不能为空")
    @Range(min = 1, max = 3, message = "自动回复种类 1:关注回复 2:收到消息回复 3:关键词回复")
    @ApiModelProperty(name = "replyType", value = "自动回复种类 1:关注回复 2:收到消息回复 3:关键词回复")
    private Integer replyType;

    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空")
    @Size(min = 1, max = 60, message = "规则名称不能超过60个字符")
    @ApiModelProperty(name = "ruleName", value = "规则名称")
    private String ruleName;

    /**
     * 关键词json(ruleType:1全匹配,2半匹配，keyWord:关键词)
     */
    @NotNull(message = "关键词不能为空")
    @Size(min = 1, max = 10, message = "关键词不能超过10个")
    @ApiModelProperty(name = "keyWord", value = "关键词json(ruleType:1全匹配,2半匹配，keyWord:关键词)")
    private List<@Valid KeyWord> keyWordList;

    /**
     * 回复方式 1:回复全部 2:随机回复一条
     */
    @NotNull(message = "回复方式不能为空")
    @Range(min = 1, max = 2, message = "回复方式 1:回复全部 2:随机回复一条")
    @ApiModelProperty(name = "replyMode", value = "回复方式 1:回复全部 2:随机回复一条")
    private Integer replyMode;

    /**
     * 回复内容
     */
    @NotNull(message = "回复内容不能为空")
    @Size(min = 1, max = 5, message = "回复内容不能超过5条")
    @ApiModelProperty(name = "replyContentList", value = "回复内容")
    private List<@Valid ReplyContentDTO> replyContentList;


    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Data
    public static class ReplyContentDTO {
        /**
         * 回复类型:text文本，image图片，voice音频，video视频的内容的类型
         */
        @NotBlank(message = "回复类型不能为空")
        @ApiModelProperty(name = "type", value = "回复类型:text文本，image图片，voice音频，video视频")
        private String type;

        /**
         * 回复内容:text文本内容
         */
        @Size(max = 300, message = "回复内容不能超过300个字符")
        @ApiModelProperty(name = "text", value = "回复内容")
        private String text;

        /**
         * mediaId:微信返回的image图片，voice音频，video视频，新增的永久素材的media_id
         */
        @ApiModelProperty(name = "mediaId", value = "新增的永久素材的media_id")
        private String mediaId;

        /**
         * url:微信返回的，新增的图片素材的图片URL（仅新增图片素材时会返回该字段）
         */
        @ApiModelProperty(name = "url", value = "仅新增图片素材时会返回该字段")
        private String url;

        /**
         * name:上传图片，音频，视频时文件名字
         */
        @ApiModelProperty(name = "name", value = "上传图片，音频，视频时文件的名字")
        private String name;
    }


    @Data
    public static class KeyWord {
        /**
         * 规则类型:1全匹配,2半匹配
         */
        @NotNull(message = "规则类型不能为空")
        @Range(min = 1, max = 2, message = "规则类型:1全匹配,2半匹配")
        @ApiModelProperty(name = "ruleType", value = "规则类型:1全匹配,2半匹配")
        private Integer ruleType;
        /**
         * 关键词
         */
        @NotBlank(message = "关键词不能为空")
        @Size(min = 1, max = 30, message = "关键词不能超过30个字符")
        @ApiModelProperty(name = "keyWord", value = "关键词")
        private String keyWord;
    }


}
