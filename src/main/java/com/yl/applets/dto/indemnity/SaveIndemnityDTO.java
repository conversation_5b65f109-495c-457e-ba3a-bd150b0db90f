package com.yl.applets.dto.indemnity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： zhan<PERSON>hong
 * @Date： 2023/01/09
 */
@Data
public class SaveIndemnityDTO {

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @NotBlank
    @Size(max = 15,message = "客户电话超长")
    @ApiModelProperty(value = "客户电话")
    private String customerPhone;

    @NotNull
    @ApiModelProperty(value = "客户类型 1:寄件人 2:收件人 3:其他")
    private Integer customerType;

    @NotBlank
    @Size(max = 30,message = "运单号超长")
    @ApiModelProperty(value = "运单号")
    private String waybillNo;

    @NotBlank
    @Size(max = 500,message = "问题描述超长")
    @ApiModelProperty(value = "问题描述")
    private String problemDescription;

    @ApiModelProperty(value = "附件（价值证明）")
    private List<String> paths;

    @ApiModelProperty(value = "用户id")
    private Integer wxUserId;

//    @NotBlank(message = "验证码不能为空")
    private String verificationCode;

    @NotBlank
    @ApiModelProperty(value = "1.新增2.编辑")
    private String editOrAdd;

    @ApiModelProperty(value = "账户类型: 支付宝、微信、银行卡、现金")
    @NotBlank(message = "请输入账户类型")
    private String accountName;

    @NotBlank(message = "请输入收款人姓名")
    @Size(max = 10,message = "收款人姓名不超过10个字")
    @Pattern(regexp = "^(?:[a-zA-Z]+(?: [a-zA-Z]+)*|[\\u4e00-\\u9fa5]+)$", message = "请输入正确的收款人姓名")
    @ApiModelProperty(value = "收款人姓名")
    private String payeeName;

    @NotBlank(message = "请输入收款人账号")
    @Size(max = 254,message = "收款人账号不超过254个字符")
    @Pattern(regexp = "^1[3456789]\\d{9}$|^[A-Za-z0-9]+([_\\.][A-Za-z0-9]+)*@([A-Za-z0-9\\-]+\\.)+[A-Za-z]{2,6}$", message = "请输入正确的支付宝账号")
    @ApiModelProperty(value = "收款人账户")
    private String payeeAccount;

    @ApiModelProperty(value = "赔付对象: 寄件人、收件人、其他")
    private String compensationObj;

    /**
     * 二级类型编码
     */
    @NotBlank(message = "问题类型不能为空")
    @Size(max = 20, message = "二级类型编码不能超过20个字符")
    private String secondTypeCode;

}
