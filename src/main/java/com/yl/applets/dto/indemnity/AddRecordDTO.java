package com.yl.applets.dto.indemnity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： z<PERSON>
 * @Date： 2023/01/09
 */
@Data
public class AddRecordDTO {

    @ApiModelProperty(value = "工单id")
    private Long workOrderId;

    @ApiModelProperty(value = "附件地址")
    private List<String> paths;

    @ApiModelProperty(value = "回复内容")
    private String handlingOpinions;

    @ApiModelProperty(value = "客户评价\n" +
            "1:非常满意\n" +
            "2:满意\n" +
            "3:一般\n" +
            "4:不满意")
    private Integer evaluate;
}
