package com.yl.applets.dto.indemnity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.ss.formula.functions.T;

import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：   请求大数据入参
 * @Author： zhan<PERSON><PERSON>
 * @Date： 2023/01/10
 */
@Data
public class RequestDTO {

    @ApiModelProperty(value = "用户名")
    private String userId;

    @ApiModelProperty(value = "GUID,请求唯一ID")
    private String reqId;

    @ApiModelProperty(value = "当前时间戳")
    private Long timestamp;

    @ApiModelProperty(value = "MD5加密串 md5(userId=xxx&secretKey=xxx&reqId=xxx&timestamp=xxx)")
    private String sign;

    @ApiModelProperty(value = "数据参数")
    private Map<String,Object> p;

    @ApiModelProperty(value = "查询sql")
    private String sql;

}
