package com.yl.applets.dto.indemnity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： zhanzhihong
 * @Date： 2023/01/09
 */
@Data
public class IndemnityDetailVO {

    @ApiModelProperty(value = "状态 2:处理中，3:已关闭")
    private Integer status;

    @ApiModelProperty(value = "是否评价 1 是 0 否")
    private Integer wxUserEvaluateFlag;

    @ApiModelProperty(value = "运单号")
    private String waybillNo;

    @ApiModelProperty(value = "工单编号")
    private String workOrderNo;

    @ApiModelProperty(value = "工单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workOrderId;

    @ApiModelProperty(value = "问题描述")
    private String problemDescription;

    @ApiModelProperty(value = "价值证明")
    private List<String> urls;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户电话")
    private String customerPhone;

    @ApiModelProperty(value = "客户类型 1:寄件人 2:收件人 3:其他")
    private Integer customerType;

    @ApiModelProperty(value = "账户类型: 支付宝、微信、银行卡、现金")
    private String accountName;

    @ApiModelProperty(value = "收款人姓名")
    private String payeeName;

    @ApiModelProperty(value = "收款人账户")
    private String payeeAccount;

    @ApiModelProperty(value = "赔付对象: 寄件人、收件人、其他")
    private String compensationObj;

    /**
     * 二级类型编码
     */
    @ApiModelProperty(value = "二级类型编码")
    private String secondTypeCode;
    /**
     * 二级类型名称
     */
    @ApiModelProperty(value = "二级类型名称")
    private String secondTypeName;
}
