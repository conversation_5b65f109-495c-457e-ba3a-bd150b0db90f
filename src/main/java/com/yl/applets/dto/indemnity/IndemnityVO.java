package com.yl.applets.dto.indemnity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： zhanzhihong
 * @Date： 2023/01/09
 */
@Data
public class IndemnityVO {

    @ApiModelProperty(value = "客户附件上传 1 是 0 否")
    private Integer wxFileFlag;

    @ApiModelProperty(value = "是否评价 1 是 0 否")
    private Integer wxUserEvaluateFlag;

    @ApiModelProperty(value = "运单号")
    private String waybillNo;

    @ApiModelProperty(value = "工单编号")
    private String workOrderNo;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "工单id")
    private Long workOrderId;

    @ApiModelProperty(value = "寄件人姓名")
    private String senderName;

    @ApiModelProperty(value = "寄件省份名称")
    private String senderProvinceName;

    @ApiModelProperty(value = "寄件人手机号")
    private String senderMobilePhone;

    @ApiModelProperty(value = "收件人姓名")
    private String receiverName;

    @ApiModelProperty(value = "收件省份名称")
    private String receiverProvinceName;

    @ApiModelProperty(value = "收件人手机号")
    private String receiverMobilePhone;

    @ApiModelProperty(value = "用户id")
    private String memberId;

    @ApiModelProperty(value = "签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "GMT+8")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime signTime;

    @ApiModelProperty(value = "客户类型 1:寄件人 2:收件人 3:其他")
    private Integer customerType;
}
