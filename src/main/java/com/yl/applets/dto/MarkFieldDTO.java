package com.yl.applets.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


public class MarkFieldDTO implements Serializable {
    private String fieldName;
    private String fieldValue;
    /**
     * 是否有效
     */
    private boolean isEffective;

    /**
     * 子标签
     */
    private List<MarkFieldDTO> childMark;

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }

    public boolean isEffective() {
        return isEffective;
    }

    public void setEffective(boolean effective) {
        isEffective = effective;
    }

    public List<MarkFieldDTO> getChildMark() {
        return childMark;
    }

    public void setChildMark(List<MarkFieldDTO> childMark) {
        this.childMark = childMark;
    }
}
