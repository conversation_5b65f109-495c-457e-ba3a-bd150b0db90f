package com.yl.applets.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yl.applets.dto.neiborhood.MailAddressDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: k<PERSON>(liuzhinan)
 * @pcakage: com.yl.mail.service.api.dto.order.ExternalOrderPageVo
 * @date: 2022-07-25 16:13:02
 * @description:
 */
@Data
public class ExternalOrderPageVO implements Serializable {
    /** 订单号 */
    @ApiModelProperty("订单号")
    private String orderNo;

    /** 运单号 */
    @ApiModelProperty("运单号")
    private String waybillNo;

    /** 订单状态 （1.待打单 2.待寄出 3.运输中 4.已签收 5.派件异常 6.已取消 7.已取号） */
    @ApiModelProperty("订单状态 （1.待打单 2.待寄出 3.运输中 4.已签收 5.派件异常 6.已取消 7.已取号）")
    private Integer orderStatus;

    /** 寄件信息对象 */
    @ApiModelProperty(value = "寄件信息对象")
    private MailAddressDTO sender;

    /** 收件信息对象 */
    @ApiModelProperty(value = "收件信息对象")
    private MailAddressDTO receiver;

    /** 下单时间 */
    @ApiModelProperty("下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /** 取消时间 */
    @ApiModelProperty("取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date cancelTime;

    /** 重量 */
    @ApiModelProperty(value = "重量")
    private BigDecimal weight;

    /** 预估运费 */
    @ApiModelProperty(value = "预估运费")
    private BigDecimal estimatePrice;

    /** 实际运费 */
    @ApiModelProperty(value = "实际运费")
    private BigDecimal realPrice;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;
}