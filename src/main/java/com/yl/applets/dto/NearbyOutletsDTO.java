package com.yl.applets.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class NearbyOutletsDTO {

    @NotNull(message = "longitude 不能为空")
    private String longitude;

    @NotNull(message = "latitude 不能为空")
    private String latitude;

    //城市
    private String city;

    //城市id
    private Long cityId;

    //区id
    private Long areaId;

    //查询关键字
    private String keyWord;

    //显示多少条
    private Integer size;

    private String times;
    private String sign;
}
