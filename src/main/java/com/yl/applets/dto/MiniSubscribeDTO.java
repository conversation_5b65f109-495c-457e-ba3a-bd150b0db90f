/**
 * Copyright (C), 2015-2021, 云路供应链科技有限公司
 * FileName: MiniSubscribeDTO
 * Author:   luhong
 * Date:     2021-04-22 18:13
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2021-04-22
 * @since 1.0.0
 */
@Data
@ApiModel(value = "小程序订阅消息上报请求体",description = "小程序订阅消息上报DTO")
public class MiniSubscribeDTO {

    @ApiModelProperty(value = "订单id")
    private String orderId;

    @ApiModelProperty(value = "模板订阅状态信息",notes = "key:模板id,value:订阅状态")
    private Map<String,String> templateInfo;

//    @Data
//    class TemplateInfo{
//
//        @ApiModelProperty(value = "模板id")
//        private String templateId;
//
//        private String status;
//    }

}