/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: OpsDeliverTaskEDIQuery
 * Author:   luhong
 * Date:     2020-10-14 17:05
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-14
 * @since 1.0.0
 */
@ApiModel(
        value = "OmsDeliverTaskEDI请求对象",
        description = "OmsDeliverTaskEDI请求对象"
)
@Data
public class OpsDeliverTaskEDIQuery implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("任务状态1,已取消,2已派件,3待派件")
    private List<Integer> taskStatus;
    @ApiModelProperty("派件员编号")
    @NotBlank(
            message = "deliverStaffCode不能为空"
    )
    private String deliverStaffCode;
    @ApiModelProperty(
            name = "userNetworkId",
            value = "用户网点id"
    )
    @NotNull(
            message = "userNetworkId不能为空"
    )
    private Integer userNetworkId;
    @ApiModelProperty(
            name = "userInstitutionalLevelId",
            value = "用户机构级别"
    )
    @NotNull(
            message = "userInstitutionalLevelId不能为空"
    )
    private Integer userInstitutionalLevelId;
    @ApiModelProperty("运单编号")
    private String waybillNo;
    @NotNull(
            message = "size不能为空"
    )
    @ApiModelProperty(
            name = "size",
            value = "页码"
    )
    private Long size;
    @NotNull(
            message = "current不能为空"
    )
    @ApiModelProperty(
            name = "current",
            value = "当前页"
    )
    private Long current;
    @ApiModelProperty(
            name = "isAbnormal",
            value = "异常状态"
    )
    private Integer isAbnormal;
    @ApiModelProperty(
            name = "startScanTime",
            value = "开始扫描时间"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime startScanTime;
    @ApiModelProperty(
            name = "endScanTime",
            value = "结束扫描时间"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime endScanTime;
    @ApiModelProperty(
            name = "orderBy",
            value = "排序字段"
    )
    private Integer orderBy;
    @ApiModelProperty(
            name = "groupBy",
            value = "分组字段"
    )
    private Integer groupBy;
}