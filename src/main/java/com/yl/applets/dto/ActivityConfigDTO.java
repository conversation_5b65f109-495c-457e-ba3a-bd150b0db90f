package com.yl.applets.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动配置管理
 * @author: xiongweibin
 * @create: 2020-08-24 16:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ActivityConfigDTO implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    private String title;
    /**
     * 类型 1:小程序开屏 2:Banner
     */
    @NotNull(message = "类型不能为空")
    private Integer type;
    /**
     * 顺序位 1:一位 2:二位 3:三位 4:四位 5:五位 6:六位
     */
    private String sort;

    /**
     * 图片地址
     */
    private String imagePath;
    /**
     * 图片url
     */
    private String imageUrl;
    /**
     * 活动链接
     */
    private String activityLine;

    /**
     * 小程序appId
     */
    private String wxAppId;
    /**
     * 上线时间
     */
    @NotNull(message = "上线时间不能为空")
    private LocalDateTime onTime;
    /**
     * 下线时间
     */
    @NotNull(message = "下线时间不能为空")
    private LocalDateTime offTime;
    /**
     * 发布时间
     */
    @NotNull(message = "发布时间不能为空")
    private LocalDateTime releaseTime;
    /**
     * 创建人code
     */
    private String createByCode;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 发布时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人code
     */
    private String updateByCode;

    /**
     * 更新人名称
     */
    private String updateByName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * news文字内容
     */
    @Size(max = 12, message = "news文字内容不能超过12个字符")
    private String newsText;

    /**
     * news按钮内容
     */
    @Size(max = 4, message = "news按钮内容不能超过4个字符")
    private String buttonText;

    /**
     * 按钮跳转方式 1:链接 2:小程序页面
     */
    private Integer buttonWay;


    //适用地区 名字,id
    private List<Area> useArea;


    @Data
    public static class Area{
        private String provinceId;
        private String provinceName;
        private String cityId;
        private String cityName;
        private Integer type;//1.省2.市
    }
}
