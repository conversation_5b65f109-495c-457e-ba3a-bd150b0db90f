package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

@Data
public class OrderCancelDTO {

    @ApiModelProperty("订单编号集合")
    private List<Long> orderIds;

    @ApiModelProperty("原因")
    private String reason;

    @ApiModelProperty("原因code 1:客户取消订单")
    private Integer cancelReasonCode;
}
