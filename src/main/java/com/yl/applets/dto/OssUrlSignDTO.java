/**
 * Copyright (C), 2015-2021, 云路供应链科技有限公司
 * FileName: OssUrlSignDTO
 * Author:   luhong
 * Date:     2021-01-08 9:26
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto;

import com.yl.common.base.exception.FilePathException;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.regex.Pattern;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2021-01-08
 * @since 1.0.0
 */
@Data
public class OssUrlSignDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank(
            message = "项目名不能为空"
    )
    private String projectName;
    @NotBlank(
            message = "模块名不能为空"
    )
    private String moduleName;
    @NotBlank(
            message = "文件名不能为空"
    )
    private String fileName;

    public OssUrlSignDTO(String path) throws FilePathException {
        String pattern = "^(.)+(/)(.)+(/)(.)+(\\.)(.)+";
        if (!Pattern.matches(pattern, path)) {
            throw new FilePathException(String.format("文件路径[%s]不合法，正确示例：\"projectName/moduleName/fileName\", 如:a/b/c.jpg", path));
        } else {
            String[] names = path.split("/");
            this.projectName = names[0];
            this.moduleName = names[1];
            this.fileName = names[2];
        }
    }

    public OssUrlSignDTO(@NotBlank(message = "项目名不能为空") String projectName, @NotBlank(message = "模块名不能为空") String moduleName, @NotBlank(message = "文件名不能为空") String fileName) {
        this.projectName = projectName;
        this.moduleName = moduleName;
        this.fileName = fileName;
    }
}