package com.yl.applets.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class IpayWxPlaceOrderDTO{

    private String app_id;

    private String order_no;

    private Integer network_id;

    private String network_code;

    private String network_name;

    /**
     * 二级商户应用ID
     * 否
     * 二级商户申请的公众号或移动应用appid。若sub_openid有传的情况下，sub_appid必填，且sub_appid需与sub_openid对应
     */
    private String sub_appid;

    private String description;

    private BigDecimal order_amount;


    private String payer_Client_Ip;

    private String sp_openid;

    private String sub_openid;

    private String waybill_no;

    //寄件网点-加盟商信息
    private Integer franchisee_id;

    private String franchisee_code;

    private String franchisee_name;

    private String openId;

    /**
     * 优惠券code
     */
    private String coupon_code;

    /**
     * 优惠券ID
     */
    private Long coupon_id;

    /**
     * 优惠券优惠金额
     */
    private BigDecimal coupon_amount;

    /**
     * 原订单金额
     */
    private BigDecimal original_amount;

}
