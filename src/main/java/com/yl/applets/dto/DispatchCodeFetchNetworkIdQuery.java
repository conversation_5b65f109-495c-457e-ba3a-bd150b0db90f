package com.yl.applets.dto;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Shenzhen Yunlu Supply Chain Technology Co. Ltd © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019/10/30 16:47 <br>
 * @Author: <a href="<EMAIL>">hulilei</a>
 */
@Data
@Slf4j
public class DispatchCodeFetchNetworkIdQuery {
    //订单号或运单号，用于追踪问题
    private String uniqueSequence;

    @NotNull(message = "省ID不能为空")
    private Integer provinceId;
    @NotBlank(message = "省中文不能为空")
    private String province;

    @NotNull(message = "市ID不能为空")
    private Integer cityId;
    @NotBlank(message = "市ID不能为空")
    private String city;

    @NotNull(message = "区ID不能为空")
    private Integer areaId;
    @NotBlank(message = "区中文不能为空")
    private String area;

    private Integer townId;         //乡镇ID
    private String town;            //乡镇中文

    private String bizSource;               //订单来源

    private String details;         //详细地址(不包含省市区乡镇)

    private Integer deliverType;    //1. 收件 2. 派件


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public String fullAddress() {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(province)) {
            sb.append(province);
        }
        if (StringUtils.isNotBlank(city)) {
            sb.append(city);
        }
        if (StringUtils.isNotBlank(area)) {
            sb.append(area);
        } else {
            log.warn("地址area(区)为空，调用高德地图可能导致误差！");
        }
        if (StringUtils.isNotBlank(town)) {
            sb.append(town);
        }
        if (StringUtils.isNotBlank(details)) {
            sb.append(details);
        }
        return sb.toString();
    }

}
