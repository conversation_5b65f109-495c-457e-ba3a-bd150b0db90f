package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;


/**
 * 综合业务-系统运营-寄收地址设置 queryDTO
 *
 * <AUTHOR>
 * @date 2020年2月29日16:21:20
 */
@ApiModel(description = "系统区域表--queryDTO")
public class SysAreaOnlineConfigQueryDTO implements Serializable {
    @ApiModelProperty(name = "plateformType", value = "取值参考SysAreaOnlineConfigTypeEnum类.1:小程序,2:VIP大客户,3:官网,4:巴枪APP,5:JMSWEB")
    private Integer plateformType;

    @ApiModelProperty(name = "type", value = "区域类型1.国家,2.省,3.市,4区")
    private Integer type;

    @ApiModelProperty(name = "id", value = "")
    private Integer id;

    @ApiModelProperty(name = "name", value = "异常名称")
    private String name;

    @ApiModelProperty(name = "parentId", value = "父级ID")
    private Integer parentId;

    @ApiModelProperty(name = "countryId", value = "所属国家")
    private Integer countryId;

    @ApiModelProperty(name = "code", value = "区域编码")
    private String code;

    @ApiModelProperty(name = "internationalCode", value = "国际编码")
    private String internationalCode;

    @ApiModelProperty(name = "threeCode", value = "三字码")
    private String threeCode;

    @ApiModelProperty(name = "cnName", value = "中文名称")
    private String cnName;

    @ApiModelProperty(name = "enName", value = "英文名称")
    private String enName;

    @ApiModelProperty(name = "nativeName", value = "母语名称")
    private String nativeName;

    @ApiModelProperty(name = "areaNo", value = "区号")
    private String areaNo;

    @ApiModelProperty(name = "zipcode", value = "邮政编码")
    private String zipcode;

    @ApiModelProperty(name = "isEnable", value = "是否启用")
    private Integer isEnable;

    @ApiModelProperty(name = "isDelete", value = "是否删除:1未删除,2已删除")
    private Integer isDelete;

    @ApiModelProperty(name = "ids", value = "id集合")
    private List<Integer> ids;


    public Integer getPlateformType() {
        return plateformType;
    }

    public void setPlateformType(Integer plateformType) {
        this.plateformType = plateformType;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public Integer getCountryId() {
        return countryId;
    }

    public void setCountryId(Integer countryId) {
        this.countryId = countryId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getInternationalCode() {
        return internationalCode;
    }

    public void setInternationalCode(String internationalCode) {
        this.internationalCode = internationalCode;
    }

    public String getThreeCode() {
        return threeCode;
    }

    public void setThreeCode(String threeCode) {
        this.threeCode = threeCode;
    }

    public String getCnName() {
        return cnName;
    }

    public void setCnName(String cnName) {
        this.cnName = cnName;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public String getNativeName() {
        return nativeName;
    }

    public void setNativeName(String nativeName) {
        this.nativeName = nativeName;
    }

    public String getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(String areaNo) {
        this.areaNo = areaNo;
    }

    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    public Integer getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Integer isEnable) {
        this.isEnable = isEnable;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }
}
