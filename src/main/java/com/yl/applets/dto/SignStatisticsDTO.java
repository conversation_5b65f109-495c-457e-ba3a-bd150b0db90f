package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;

import java.io.Serializable;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: xiongweibin
 * @create: 2020-08-24 17:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "签到流量报表DTO", description = "签到流量报表DTO")
public class SignStatisticsDTO implements Serializable {

    /**
     * 类型   VipActionSourceEnum
     */
    @ApiModelProperty(value = "type",notes = "1.VIP 0.会员")
    @Range(min = 0, max = 1, message = "1.VIP 0.会员")
    private Integer isMember;
    /**
     * 点击类型   VipActionSourceEnum
     */
    @ApiModelProperty(value = "actionType",notes = "3.签到页分享 4.banner点击")
    @Range(min = 3, max = 4, message = "3.签到页分享 4.banner点击")
    private Integer actionType;


}
