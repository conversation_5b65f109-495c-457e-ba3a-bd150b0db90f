package com.yl.applets.dto;

import lombok.Data;

/**
 * 推送到微信物流助手的轨迹参数
 * 
 * <AUTHOR>
 * @since 2020-06-10
 * @version 1.0.0
 */
@Data
public class WxTraceDTO {

    /**
     * 商户侧下单事件中推送的 Token 字段
     */
    private String token;

    /**
     * 运单 ID
     */
    private String waybill_id;

    /**
     * 轨迹变化 Unix 时间戳
     */
    private Long action_time;

    /**
     * 轨迹变化类型
     */
    private Integer action_type;

    /**
     * 轨迹变化具体信息说明，展示在快递轨迹详情页中。若有手机号码，则直接写11位手机号码。使用UTF-8编码。
     */
    private String action_msg;

    /**
     * 取件员姓名 当分配取件员成功时返回
     */
    private String pickup_courier_name;

    /**
     * 取件员电话 当分配取件员成功时返回
     */
    private String pickup_courier_phone;

    /**
     * 派件员姓名 当分配派件员成功时返回
     */
    private String delivery_courier_name;

    /**
     * 派件员电话 当分配派件员成功时返回
     */
    private String delivery_courier_phone;


}
