package com.yl.applets.dto.neiborhood;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR> mybatis generator)
 * @since v1.0.0 2022-07-25
 */
@Data
@ApiModel("寄件收件人地址")
@NoArgsConstructor
@AllArgsConstructor
public class MailAddressDTO implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("unionId")
    private String unionId;

    @ApiModelProperty("站点编码")
    private String serviceSiteCode;

    /** 姓名 */
    @ApiModelProperty("姓名")
    @NotBlank(message = "寄件收件人姓名不能为空")
    @Length(max = 18,message = "姓名过长")
    private String name;

    /** 手机号 */
    @ApiModelProperty("手机号")
    @NotNull(message = "手机号不能为空")
    @Length(max = 20,message = "手机号过长")
    private String phone;

    /** 省 */
    @ApiModelProperty("省")
    @NotBlank(message = "省份不能为空")
    private String province;

    /** 市 */
    @ApiModelProperty("市")
    @NotBlank(message = "城市不能为空")
    private String city;

    /** 区/县 */
    @ApiModelProperty("区/县")
    @NotBlank(message = "县区不能为空")
    private String area;

    /** 乡镇 */
    @ApiModelProperty("乡镇")
    private String town;

    /** 街道 */
    @ApiModelProperty("街道")
    private String street;

    /** 详细地址 */
    @ApiModelProperty("详细地址")
    @NotBlank(message = "详细地址不能为空")
    @Length(max = 150,message = "详细地址过长")
    private String address;
}