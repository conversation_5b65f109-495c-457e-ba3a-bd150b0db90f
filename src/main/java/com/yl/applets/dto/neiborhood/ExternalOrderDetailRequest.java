package com.yl.applets.dto.neiborhood;

import com.yl.applets.valiate.Feign;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExternalOrderDetailRequest implements Serializable {
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    /**
     * 小程序下单用户unionId
     */
    @ApiModelProperty(value = "小程序下单用户unionId")
    @NotBlank(message = "小程序下单用户不能为空", groups = Feign.class)
    private String unionId;
}
