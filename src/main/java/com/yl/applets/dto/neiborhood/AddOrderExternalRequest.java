package com.yl.applets.dto.neiborhood;

import feign.Feign;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddOrderExternalRequest implements Serializable {

    /**
     * 小程序下单用户unionId
     */
    @ApiModelProperty(value = "小程序下单用户unionId")
    @NotBlank(message = "小程序下单用户不能为空", groups = {Feign.class})
    private String unionId;

    /**
     * 用户下单站点
     */
    @ApiModelProperty(value = "用户下单站点")
    @NotBlank(message = "用户下单站点不能为空")
    private String serviceSiteCode;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    @NotBlank(message = "用户身份证号不能为空", groups = {Feign.class})
    @Length(min = 15, max = 30, message = "身份证号码不合法", groups = {Feign.class})
    private String idCardNo;

    /**
     * 用户实际姓名
     */
    @ApiModelProperty(value = "用户实际姓名")
    @NotBlank(message = "用户实际姓名不能为空", groups = {Feign.class})
    @Length(max = 20, message = "姓名过长", groups = {Feign.class})
    private String realName;

    /**
     * 用户实际实际手机号
     */
    //    @NotBlank(message = "手机号不能为空")
    //    @Length(max = 20, message = "手机号过长")
    //    private String phone;

    /**
     * 寄件信息对象
     */
    @ApiModelProperty(value = "寄件信息对象")
    @Valid
    private MailAddressDTO sender;

    /**
     * 收件信息对象
     */
    @ApiModelProperty(value = "收件信息对象")
    @Valid
    private MailAddressDTO receiver;

    /**
     * 物品类型
     */
    @ApiModelProperty(value = "物品类型")
    private String goodsType;

    @ApiModelProperty(value = "预估重量")
    private BigDecimal estimateWeight;

    @ApiModelProperty(value = "预估运费")
    private BigDecimal estimatePrice;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Length(max = 30, message = "备注不得超过30个字")
    private String remark;
}
