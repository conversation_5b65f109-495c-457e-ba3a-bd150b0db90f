package com.yl.applets.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 深圳云路供应链科技有限公司 版权所有 Copyright 2018 - 2020 yl-scm. All Rights Reserved
 *
 * @ProjectName: yl-jms-applets
 * @ClassName: BatchOrderDTO
 * @Description: (云打印 - 批量订单)
 * @Author: liaoxiting
 * @Date: 2020/4/24 15:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BatchOrderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank(message = "打印设备码不能为空")
    private String printDeviceId;

    @NotBlank(message = "打印设备类型编码不能为空")
    private String printDeviceTypeCode;

    @NotBlank(message = "大客户ID不能为空")
    private String printCustomerId;

    @NotNull(message = "订单数据不能为空")
    @Valid
    private List<OrderDTO> orders;

    @NotNull(message = "printerEquipmentd 不能为空")
    private Integer printerEquipmentId;

}
