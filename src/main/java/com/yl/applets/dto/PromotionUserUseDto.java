package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 优惠劵领取使用信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PromotionUserUseDto", description = "优惠劵领取使用信息表")
public class PromotionUserUseDto implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "id")
    private Long proId;
    @ApiModelProperty(value = "分发id")
    private Long disId;

    @ApiModelProperty(value = "用户id")
    private Long userId;
    @ApiModelProperty(value = "会员id")
    private Long numberId;
    @ApiModelProperty(value = "记件")
    private Long sendNetworkId;
    @ApiModelProperty(value = "收件")
    private Long recCityId ;

    private    List<String> orderNos;



}
