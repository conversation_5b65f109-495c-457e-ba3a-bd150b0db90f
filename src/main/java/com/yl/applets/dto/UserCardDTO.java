package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "UserCardDTO", description = "实名制dto")
public class UserCardDTO {

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    @ApiModelProperty(value = "姓名",name = "name" )
    private String name;
    /**
     * 证件类型
     */
    @NotNull(message="证件类型不能为空")
    @ApiModelProperty(value = "证件类型",name = "cardType" )
    private Integer cardType;
    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @ApiModelProperty(value = "手机号",name = "mobile")
    private String mobile;

    /**
     * 证件号
     */
    @NotBlank(message = "证件号不能为空")
    @ApiModelProperty(value = "证件号",name = "cardNum")
    private String cardNum;

    private Long userId;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 查验方式 1手动输入2扫描上传
     */
    private Integer checkType;


    private Integer numberId;

//    @NotNull(message = "短信验证码不能为空")
    @ApiModelProperty(value = "短信验证码",name = "code")
    private String code;

    @NotNull(message = "是否需要校验验证码不能为空")
    @ApiModelProperty(value = "是否需要校验验证码",name = "isCheck")
    private Boolean isCheck;

}
