/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: SendMessageDTO
 * Author:   luhong
 * Date:     2020-08-31 17:12
 * Description: 新的短信接口验证码
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto;

import lombok.Data;

import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈Pdd短信接口〉
 *
 * <AUTHOR>
 * @create 2020-08-31
 * @since 1.0.0
 */
@Data
public class SendMessageDTO {

    private String templateCode;

    private List<PddMessage> list;

    @Data
    public static class PddMessage {

        /**
         * 快递签收点地址
         */
        private String address;

        /**
         * 取件码
         */
        private String code;

        /**
         * 网点联系电话
         */
        private String mobile;

        /**
         * 扫描网点编码
         */
        private String networkCode;

        /**
         * 扫描网点名称
         */
        private String networkName;

        /**
         * 订单类型，0为物流短信；1为批量短信
         */
        private Integer orderType;

        /**
         * 员工姓名
         */
        private String staffName;

        /**
         * 员工号
         */
        private String staffNo;

        /**
         * 运单号
         */
        private String waybillNo;

        /**
         * 接收短信的手机号。物流短信通过运单号去PDD获取，可为空。批量短信不能为空。
         */
        private String phoneNumber;

        /**
         * 接受模板中的参数  [验证码，过期时间（15mins）]
         */
        private String[] templateParam;


    }

}