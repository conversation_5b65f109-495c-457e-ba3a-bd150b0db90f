/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: ElectronicInvoiceDTO
 * Author:   luhong
 * Date:     2020-07-23 14:47
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 〈一句话功能简述〉<br> 
 * 〈电子发票三期DTO〉
 *
 * <AUTHOR>
 * @create 2020-07-23
 * @since 1.0.0
 */
@Data
public class ElectronicInvoiceDTO {

    private Integer memberId;

    private Integer current = 1;

    private Integer size = 20;

    private String phone;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startInputTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endInputTime;

}