package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class SpmApiTrialDTO implements Serializable {

    @ApiModelProperty("运单号ID")
    private Long waybillId;

    @ApiModelProperty(name = "productTypeId", value = "产品类型id")
    private Integer productTypeId;

    @ApiModelProperty(name = "productTypeCode", value = "产品类型CODE")
    private String productTypeCode;

    @ApiModelProperty(name = "serviceMethodId", value = "服务方式ID")
    private Integer serviceMethodId;

    @ApiModelProperty(name = "serviceMethodId", value = "服务方式CODE")
    private String serviceMethodCode;

    @NotNull(message = "始发区域ID不能为空")
    @ApiModelProperty(name = "startPointId", value = "始发区域ID")
    private Integer startPointId;

    @NotNull(message = "目的区域ID不能为空")
    @ApiModelProperty(name = "terminalPointId", value = "目的区域ID")
    private Integer terminalPointId;

    @NotNull(message = "数值不能为空")
    @ApiModelProperty(name = "value", value = "数值")
    private BigDecimal value;

    @NotNull(message = "计算时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "dateTime", value = "计算时间")
    private LocalDateTime dateTime;


    @ApiModelProperty(name = "networkId", hidden = true, value = "不需要传网点,直接取总部")
    private Integer networkId;


    @ApiModelProperty(name = "smMode", value = "结算方式1:寄件现结,2寄付月结,3:到付现结")
    private Integer smMode;

    private String sign;
    private String times;

    @ApiModelProperty(name = "senderProvinceName", value = "寄件省份名称")
    private String senderProvinceName;


    @ApiModelProperty(name = "senderCityName", value = "寄件城市名称")
    private String senderCityName;


    @ApiModelProperty(name = "receiverProvinceName", value = "收件省份名称")
    private String receiverProvinceName;

    @ApiModelProperty(name = "receiverCityName", value = "收件城市名称")
    private String receiverCityName;

    @ApiModelProperty(name = "orderLabelName", value = "网购退货标签")
    private String orderLabelName;

}
