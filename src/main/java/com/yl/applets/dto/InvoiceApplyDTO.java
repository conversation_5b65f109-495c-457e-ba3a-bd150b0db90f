package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
* 开票申请
*
* <AUTHOR> created on 2020-05-15
*/
@Data
@ApiModel(description = "开票申请dto")
public class InvoiceApplyDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotEmpty(message = "运单号列表不能为空")
    @ApiModelProperty("运单号列表, 必填")
    private List<String> waybillNos;

//    @NotNull(message = "客户ID不能为空")
//    @ApiModelProperty("客户ID, 必填")
    private Integer customerId;

//    @NotEmpty(message = "客户编码不能为空")
//    @ApiModelProperty("客户编码, 必填")
    private String customerCode;

    @NotEmpty(message = "客户开票名称不能为空")
    @ApiModelProperty("客户开票名称, 必填")
    private String customerName;

    @ApiModelProperty("客户开票税号")
    private String customerTaxNumber;

    @ApiModelProperty("客户地址")
    private String customerAddress;

    @ApiModelProperty("客户开户行")
    private String customerBankName;

    @ApiModelProperty("客户银行账号")
    private String customerBankNo;

    @ApiModelProperty("客户电话")
    private String customerTel;

    @NotEmpty(message = "客户手机号码不能为空")
    @ApiModelProperty("客户手机号码, 必填, 用于接收开票通知信息")
    private String customerMobile;

    @ApiModelProperty("客户邮箱地址, 必填, 用于接收开票通知信息")
    private String customerEmail;

    @NotNull(message = "客户抬头类型不能为空")
    @ApiModelProperty("客户抬头类型, 必填:[1,企业;2,个人]")
    private Integer customerTitleType;

    @ApiModelProperty("发票备注")
    private String invoiceRemark;

}

