/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: SendScanListMsgDto
 * Author:   luhong
 * Date:     2020-10-14 17:48
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-14
 * @since 1.0.0
 */
@Data
public class SendScanListMsgDto implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String waybillNo;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime deliveryTime;
    private String orderSourceCode;
    private Integer memberId;
    private String receiverMobilePhone;
    private String senderMobilePhone;
    private String senderName;
    private Integer pickNetworkId;
    private String receiveCityName;
    @ApiModelProperty("寄件城市")
    private String senderCityName;
    @ApiModelProperty("派件员名称")
    private String deliveryName;
    @ApiModelProperty("派件员手机号")
    private String deliveryMobile;
    @ApiModelProperty("1:快递发出通知,2:快递派送通知")
    private Integer type;
    @ApiModelProperty("1:寄件人通知,2:收件人通知")
    private Integer messageType;
}