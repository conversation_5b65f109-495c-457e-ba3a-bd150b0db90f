package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:  微信支付dto
 * @date 2021-09-08
 */

@Data
@ApiModel(value = "WxPayDto", description = "支付dto")
public class WxPayDto {

    @ApiModelProperty(value = "运单号不能为空",name = "waybillNo" )
    @NotNull(message = "运单号不能为空")
    private String waybillNo;

    @ApiModelProperty(value = "订单号不能为空",name = "orderNo" )
    @NotNull(message = "订单号不能为空")
    private String orderNo;

    @ApiModelProperty(value = "寄件网点id不能为空",name = "networkId" )
    @NotNull(message = "寄件网点id不能为空")
    private String networkId;

    @ApiModelProperty(value = "寄件网点code不能为空",name = "networkCode" )
    @NotNull(message = "寄件网点code不能为空")
    private String networkCode;

    @ApiModelProperty(value = "寄件网点名字不能为空",name = "networkName" )
    @NotNull(message = "寄件网点名字不能为空")
    private String networkName;

    @ApiModelProperty(value = "支付金额不能为空",name = "orderAmount" )
    @NotNull(message = "支付金额不能为空")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "描述不能为空",name = "description" )
    @NotNull(message = "描述不能为空")
    private String description;

    @ApiModelProperty(value = "openId",name = "openId")
    private String openId;


    /**
     * 优惠券code
     */
    @ApiModelProperty(value = "couponCode",name = "couponCode")
    private String couponCode;

    /**
     * 优惠券ID
     */
    @ApiModelProperty(value = "couponId",name = "couponId")
    private Long couponId;

    /**
     * 优惠券优惠金额
     */
    @ApiModelProperty(value = "couponAmount",name = "couponAmount")
    private BigDecimal couponAmount;

    /**
     * 原订单金额
     */
    @ApiModelProperty(value = "originalAmount",name = "originalAmount")
    private BigDecimal originalAmount;


}
