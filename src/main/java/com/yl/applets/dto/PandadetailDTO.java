package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PandadetailDTO  implements Serializable {
    @ApiModelProperty(name = "jtUserId", value = "用户ID")
    private String jtUserId;

    @ApiModelProperty(name = "sendId", value = "订单ID")
    private String sendId;

    @ApiModelProperty(name = "orderNum", value = "订单号")
    private String orderNum;

    @ApiModelProperty(name = "expressNum", value = "运单号")
    private String expressNum;
}
