/**
 * Copyright (C), 2015-2021, 云路供应链科技有限公司
 * FileName: WxMaterialDto
 * Author:   luhong
 * Date:     2021-05-21 17:36
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;

/**
 * 〈一句话功能简述〉<br> 
 * 〈微信素材分页列表〉
 *
 * <AUTHOR>
 * @create 2021-05-21
 * @since 1.0.0
 */
@Data
public class WxMaterialDto {

    @ApiModelProperty(name = "type", value = "素材的类型，图片（image）、视频（video）、语音 （voice）、图文（news）")
    private String type;

    @ApiModelProperty(name = "current",value = "当前页")
    private Integer current =1;

    @Max(value = 20)
    @ApiModelProperty(name = "size",value = "页面大小，最大20")
    private Integer size =20;
}