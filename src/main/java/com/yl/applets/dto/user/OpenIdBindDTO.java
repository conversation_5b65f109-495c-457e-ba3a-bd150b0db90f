package com.yl.applets.dto.user;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-09-15 15:24
 * @Version 1.0
 */
@Data
public class OpenIdBindDTO {

    @NotNull(message = "用户授权码不能为空")
    private String code;

    @NotNull(message = "时间戳不能为空")
    private String times;

    @NotNull(message = "签名不能为空")
    private String sign;
}
