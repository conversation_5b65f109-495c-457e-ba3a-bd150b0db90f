package com.yl.applets.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class CustomerPrintDataDTO implements Serializable {

    @NotNull(message = "customerId 不能为空!")
    private Integer customerId;

    @NotNull(message = "密码不能为空!")
    private String pwd;

    private Integer userId;

    @NotNull(message = "printerEquipmentId 不能为空!")
    private Integer printerEquipmentId;

}
