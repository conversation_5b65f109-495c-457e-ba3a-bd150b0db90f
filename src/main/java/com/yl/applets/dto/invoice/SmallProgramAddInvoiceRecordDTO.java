package com.yl.applets.dto.invoice;

import com.yl.applets.vo.OmsElectronicInvoice;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 添加票据记录DTO对象
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SmallProgramAddInvoiceRecordDTO对象", description="小程序添加票据记录DTO对象")
public class SmallProgramAddInvoiceRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
//    @NotNull(message = "用户id不能为空")
    private String userId;


    @ApiModelProperty(value = "申请日期")
//    @NotNull(message = "申请日期不能为空")
    private LocalDate applyDate;

    @ApiModelProperty(value = "发票类型:0增值税专用发票、8增值税电子专用发票、2增值税普通发票、51增值税电子普通发票、81数电专票、82数电普票")
    @NotNull(message = "发票类型不能为空")//01全电发票(专用发票)  02全电发票(普通发票)
    @Range(min = 1, max = 2, message = "发票类型不合法")
    private Integer invoiceType;


    @NotNull(message = "抬头类型不合法")//1 企业   2 个人/事业单位
    @Range(min = 1, max = 2, message = "抬头类型不合法")
    private Integer type;


    @ApiModelProperty(value = "购方名称")
    @NotNull(message = "抬头名称不能为空")
//    @Pattern(regexp = "[A-Za-z\\u4e00-\\u9fa5]+",message = "抬头名称只能填字母和汉字")
    private String buyerName;


    @ApiModelProperty(value = "纳税人识别号")
//    @NotNull(message = "纳税人识别号不能为空")
//    @Pattern(regexp = "^9[A-Z0-9]{17}$",message = "纳税人识别号不合法")
    private String buyerTaxNumber;


    @ApiModelProperty(value = "购方地址号码")
    private String buyerAddressPhone;

    @ApiModelProperty(value = "购方开户行")
    private String buyerBankName;


    @ApiModelProperty(value = "购方银行账号")
    private String buyerBankAccount;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "价税合计")
    private BigDecimal totalPriceAndTax;


    @ApiModelProperty(value = "手机号")
    private String invoicingTel;

    @ApiModelProperty(value = "邮箱")
    @NotNull(message = "电子邮箱不能为空")
    private String invoicingEmail;


    @ApiModelProperty(value = "数据来源")
    private Integer dataSource;


    @ApiModelProperty(value = "添加发票明细详情列表")
//    @NotNull(message = "发票明细详情不能为空")
//    @Size(min = 1, message = "发票明细详情不能为空")
    private List<SmallProgramAddInvoiceDetailsDTO>  invoiceRecordDetailsDTOS;

    @ApiModelProperty(value = "运单信息")
    private List<OmsElectronicInvoice>  omsElectronicInvoiceS;
}
