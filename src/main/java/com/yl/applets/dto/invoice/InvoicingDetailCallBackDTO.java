package com.yl.applets.dto.invoice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 添加票据记录DTO对象
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="InvoicingDetailCallBackDTO对象", description="开票详情回调DTO对象")
public class InvoicingDetailCallBackDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "申请单号")
    @NotNull(message = "开票申请号不能为空")
    private String applyNo;

    @ApiModelProperty(value = "开票申请子单号")
    private String invoicingChildNo;

    @ApiModelProperty(value = "开票日期")
    @NotNull(message = "开票日期不能为空")
    private LocalDate invoicingDate;

    @ApiModelProperty(value = "发票号码")
    @NotNull(message = "发票号码不能为空")
    private String invoiceNo;

    @ApiModelProperty(value = "发票代码")
    private String invoiceCode;


    @ApiModelProperty(value = "开票金额")
    @NotNull(message = "开票金额不能为空")
    private BigDecimal invoicingAmount;


    @ApiModelProperty(value = "税额")
    @NotNull(message = "税额不能为空")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "价税合计")
    @NotNull(message = "价税合计不能为空")
    private BigDecimal totalPriceAndTax;


    @ApiModelProperty(value = "原始发票代码")
    private String originalInvoiceCode;

    @ApiModelProperty(value = "原始发票号码")
    private String originalInvoiceNo;

    @ApiModelProperty(value = "红冲信息表号")
    private String redInvoiceNo;


    @ApiModelProperty(value = "发票类型:0增值税专用发票、8增值税电子专用发票、2增值税普通发票" +
            "、51增值税电子普通发票、81数电专票、82数电普票")
    @NotNull(message = "发票类型不能为空")
    private Integer invoiceType;

    @ApiModelProperty(value = "发票状态:默认为：0，枚举值：正常：0，红冲：2，作废：3，失败：-1")
    @NotNull(message = "发票状态不能为空")
    private Integer invoiceStatus;

    @ApiModelProperty(value = "发票pdf")
    private String invoicePdfUrl;


    @ApiModelProperty(value = "发票OFD")
    private String invoiceOfdUrl;


    @ApiModelProperty(value = "发票XML")
    private String invoiceXmlUrl;





}
