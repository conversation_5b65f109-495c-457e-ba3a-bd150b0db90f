package com.yl.applets.dto;

import com.yl.common.base.valid.dto.BaseApiDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: 云打印寄件请求参数对象<br>
 * @Project: <br>
 * @CreateDate: Created in 2019-10-24 16:45 <br>
 * @Author: <a href="<EMAIL>">sunQ</a>
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OmsCloudPrintApiDto云打印寄件请求对象", description = "云打印寄件请求参数对象")
public class OmsCloudPrintApiDto extends BaseApiDTO {

    @Valid
    private List<OmsOrderApiDTO> orders;

    /** 打印设备码 */
    @NotBlank(message = "打印设备码不能为空")
    private String printDeviceId;

    /** 打印设备类型CODE(具体取值参考PrintSourceEnum) */
    @NotBlank(message = "打印设备类型CODE不能为空")
    private String printDeviceTypeCode;

    /** 客户ID */
    @NotBlank(message = "客户ID不能为空")
    private String printCustomerId;

    /** 云打印设备id */
    @NotNull(message = "云打印设备id")
    private Integer printerEquipmentId;
}
