/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: SysSettlementDestinationBaseQueryDTO
 * Author:   luhong
 * Date:     2020-10-13 15:01
 * Description: 结算目的地
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto.lmdm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈结算目的地〉
 *
 * <AUTHOR>
 * @create 2020-10-13
 * @since 1.0.0
 */
@ApiModel(
        description = "结算目的地--queryDTO"
)
@Data
public class SysSettlementDestinationBaseQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(
            name = "id",
            value = ""
    )
    private Integer id;
    @ApiModelProperty(
            name = "name",
            value = "名称"
    )
    private String name;
    @ApiModelProperty(
            name = "code",
            value = "编码"
    )
    private String code;
    @ApiModelProperty(
            name = "countryId",
            value = "所属国家"
    )
    private Integer countryId;
    @ApiModelProperty(
            name = "providerId",
            value = "省份ID"
    )
    private Integer providerId;
    @ApiModelProperty(
            name = "cityId",
            value = "城市ID"
    )
    private Integer cityId;
    @ApiModelProperty(
            name = "regionalId",
            value = "大区ID"
    )
    private Integer regionalId;
    @ApiModelProperty(
            name = "areaId",
            value = "区/县ID"
    )
    private Integer areaId;
    @ApiModelProperty(
            name = "serviceOutletId",
            value = "服务网点ID"
    )
    private Integer serviceOutletId;
    @ApiModelProperty(
            name = "distributionId",
            value = "所属分拨ID"
    )
    private Integer distributionId;
    @ApiModelProperty(
            name = "isPay",
            value = "是否到付"
    )
    private Integer isPay;
    @ApiModelProperty(
            name = "isIdentificationId",
            value = "是否显示标识"
    )
    private Integer isIdentificationId;
    @ApiModelProperty(
            name = "isCollectionId",
            value = "是否代收"
    )
    private Integer isCollectionId;
    @ApiModelProperty(
            name = "isReceipt",
            value = "是否回单"
    )
    private Integer isReceipt;
    @ApiModelProperty(
            name = "expressType",
            value = "快递类型"
    )
    private Integer expressType;
    @ApiModelProperty(
            name = "isEnable",
            value = "是否启用:1启用,2不启用"
    )
    private Integer isEnable;
    @ApiModelProperty(
            name = "isDelete",
            value = "是否删除:1未删除,2已删除"
    )
    private Integer isDelete;
    @ApiModelProperty(
            name = "ids",
            value = "id集合"
    )
    private List<Integer> ids;
    @ApiModelProperty(
            name = "regionalIds",
            value = "区县id集合"
    )
    private List<Integer> areaIds;
    private String pcaSeparator;

    private String times;
    private String sign;

}