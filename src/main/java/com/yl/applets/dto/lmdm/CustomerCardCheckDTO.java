package com.yl.applets.dto.lmdm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2020
 * <p>
 * 证件号校验DTO
 *
 * <AUTHOR>
 * @since Created in 2020-07-29
 */
@Data
@ApiModel(value = "CustomerCardCheckDTO", description = "客户证件号校验DTO")
public class CustomerCardCheckDTO implements Serializable {

    private static final long serialVersionUID = 3209603846712993875L;

    @ApiModelProperty(name = "cardType", notes = "证件类型,CustomerCardTypeEnum")
    @NotNull
    private Integer cardType;

    @ApiModelProperty(name = "cardNumber", value = "证件号码")
    @NotEmpty
    private String cardNumber;

    @ApiModelProperty(name = "networkCode", value = "网点编码")
    private String networkCode;

}
