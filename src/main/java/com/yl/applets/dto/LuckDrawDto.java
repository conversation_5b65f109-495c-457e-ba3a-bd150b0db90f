package com.yl.applets.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yl.applets.entity.LuckDrawItem;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-08-13
 */
@Data
public class LuckDrawDto {

    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long itemId;

    /**
     * activity_name
     */
    private String activityName;

    /**
     * activity_code
     */
    private String activityCode;

    /**
     * start_time
     */
    private LocalDateTime startTime;

    /**
     * end_time
     */
    private LocalDateTime endTime;


    private List<LuckDrawItem> luckDrawItems;

    /**
     * 奖项等级
     */
    private Integer itemLevel;

    /**
     * 奖项类型
     */
    private Integer itemType;

    /**
     * 奖品名称
     */
    private String name;

    /**
     * 奖品数量
     */
    private Integer amount;


    /**
     * 奖品图片地址
     */
    private String imgUrl;

    private String winImgUrl;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 奖品剩余数
     */
    private Integer remainder;




}
