package com.yl.applets.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 优惠劵分发表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "优惠券一键领取", description = "优惠券一键领取")
public class CouponOneKeyDto implements Serializable {



    @ApiModelProperty(value = "类型1.小程序配置2.网点扫码")
    private Integer type;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户id")
    private Long numberId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "用户手机号")
    private String userPhone;

    @ApiModelProperty(value = "用户昵称")
    private String userAlias;

    @ApiModelProperty(value = "用户uuid")
    private String userUuid;

    @ApiModelProperty(value = "城市id")
    private String cityId;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "网点id")
    private Long networkId;

    @ApiModelProperty(value = "地址解析到的网点id")
    private String addressNetworkId;

    @ApiModelProperty(value = "网点名称")
    private String networkName;

    @ApiModelProperty(value = "网点code")
    private String networkCode;

    @ApiModelProperty(value = "分发员工code")
    private String grantStaffCode;

    @ApiModelProperty(value = "业务员code")
    private String pickStaffCode;

    @ApiModelProperty(value = "分发员工姓名")
    private String grantStaffName;


    @ApiModelProperty(value = "领取优惠券详情")
    private List<OneKeyList> oneKeyListList;

    @ApiModelProperty(value = "第三方交易单号")
    private String externalTrade;


    @Data
    public static class OneKeyList {

        @JsonSerialize(using = ToStringSerializer.class)
        @ApiModelProperty(value = "优惠劵id")
        private Long proId;

        @JsonSerialize(using = ToStringSerializer.class)
        @ApiModelProperty(value = "分发id")
        private Long disId;


    }




}
