package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:  下单优惠券信息
 * @date 2021-12-10
 */
@Data
public class OrderCouponDto  implements Serializable {


    @ApiModelProperty(value = "优惠券code")
    private String couponCode;

    @ApiModelProperty(value = "优惠券id")
    private String couponId;

    @ApiModelProperty("优惠金额")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "当前使用优惠券id")
    private String useProId;

    public OrderCouponDto(String couponCode, String couponId,BigDecimal couponValue, String useProId) {
        this.couponCode = couponCode;
        this.couponId = couponId;
        this.couponValue = couponValue;
        this.useProId = useProId;
    }
}
