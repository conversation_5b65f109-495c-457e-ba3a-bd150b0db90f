package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-11-08
 */
@Data
public class PandaNearStationDto {

    @ApiModelProperty(value = "省份")
    @NotNull(message = "省份不能为空")
    private String provinceName;

    @ApiModelProperty(value = "城市")
    @NotNull(message = "城市不能为空")
    private String cityName;

    @ApiModelProperty(value = "区县")
    @NotNull(message = "区县不能为空")
    private String zoneName;

    @ApiModelProperty(value = "乡镇")
    @NotNull(message = "乡镇不能为空")
    private String townName;

    @ApiModelProperty(value = "地址")
    @NotNull(message = "详细地址不能为空")
    private String address;

    private Long size;

    private Long current;


}
