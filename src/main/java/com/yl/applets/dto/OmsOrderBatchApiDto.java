package com.yl.applets.dto;

import com.yl.common.base.valid.dto.BaseApiDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-10-24 16:45 <br>
 * @Author: <a href="<EMAIL>">qiang.hu</a>
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OmsOrder批量请求对象", description = "订单请求实体")
public class OmsOrderBatchApiDto extends BaseApiDTO {

    @Valid
    private List<OmsOrderApiDTO> orders;

    /** 打印设备类型CODE(具体取值参考PrintSourceEnum) */
    private String printDeviceTypeCode;

    /** 客户ID */
    private String printCustomerId;

    private String myRequestId;
}
