package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/6/11 3:44 下午
 * Describe
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ChuangLanMinutesRequestDTO", description="创蓝短信后台请求实体,验证码有效时间")
public class ChuangLanMinutesRequestDTO {

    /** 请求的模板类型*/
//    @NotNull(message = "type不能为空")
    private String type;
    /** 接收短信的手机号*/
    @NotNull(message = "接收手机号不能为空")
    private String to;
    /** 接收的验证码*/
    @NotNull(message = "验证码code不能为空")
    private String code;
    /** 有效时间*/
    @NotNull(message = "有效时间不能为空")
    private String minutes;
    /** 网点编号*/
    private String networkCode;
    /** 网点名称*/
    private String networkName;
    /** 员工编号*/
    private String staffNo;
    /** 员工姓名*/
    private String staffName;

    public ChuangLanMinutesRequestDTO(String type, @NotNull(message = "接收手机号不能为空") String to, @NotNull(message = "验证码code不能为空") String code, @NotNull(message = "有效时间不能为空") String minutes, String networkCode, String networkName, String staffNo, String staffName) {
        this.type = type;
        this.to = to;
        this.code = code;
        this.minutes = minutes;
        this.networkCode = networkCode;
        this.networkName = networkName;
        this.staffNo = staffNo;
        this.staffName = staffName;
    }
}
