package com.yl.applets.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yl.common.base.constant.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-10-08
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class RecordPrivacyDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;

    /**
     * 渠道类型
     */
    private Integer type;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户ip
     */
    private String userIp;

    /**
     * 设备码
     */
    @ApiModelProperty(value = "equipmentInfo",notes = "设备信息")
    private String equipmentInfo;

    /**
     * user_sessionid
     */
    private String userSessionid;

    /**
     * 协议版本号
     */
    @ApiModelProperty(value = "version",notes = "协议版本号")
    @NotNull(message = "协议版本号不能为空")
    private String version;

    /**
     * useragent
     */
    @ApiModelProperty(value = "useragent",notes = "useragent")
    private String useragent;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 修改人id
     */
    private Long updateBy;




}
