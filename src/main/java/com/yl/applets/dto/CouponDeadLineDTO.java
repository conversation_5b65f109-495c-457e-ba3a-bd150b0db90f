package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-01-30 19:46
 * @Version 1.0
 */
@Data
@ApiModel(value = "CouponDeadLineDTO", description = "优惠券过期提醒DTO")
public class CouponDeadLineDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "userId", name = "用户ID")
    private String userId;

    @ApiModelProperty(value = "number", name = "优惠券过期张数")
    private Integer number;

    @ApiModelProperty(value = "invalidTime", name = "过期时间")
    private String invalidTime;
}
