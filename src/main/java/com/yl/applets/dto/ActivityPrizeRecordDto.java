package com.yl.applets.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-08-09
 */

@Data
public class ActivityPrizeRecordDto {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 活动ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 活动code
     */
    private String activityCode;
    /**
     *  奖项等级
     */
    private Integer winLevel;
    /**
     *  奖项id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long itemId;
    /**
     * 奖项名称
     */
    private String winName;

    private String winImgUrl;

    /**
     * 抽奖时间
     */
    private LocalDateTime winTime;

    /**
     * 抽奖人手机号
     */
    private String winMobile;
    /**
     * 抽奖人openid
     */
    private String openId;

    /**
     * 抽奖人会员id
     */
    private Integer numberId;

    /**
     * 是否中奖0否1是
     */
    private Integer isWin;

    /**
     * 收货人手机
     */
    private String userMobile;

    /**
     * 运单号
     */
    private String waybillNo;


}
