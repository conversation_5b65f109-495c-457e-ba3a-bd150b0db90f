package com.yl.applets.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@ApiModel(description = "发票抬头")
@Accessors(chain = true)
public class InvoiceTitleDTO implements Serializable {

    private Integer id;
    /**
     * 类型1企业2个人
     */
    @NotNull( message = "类型不能为空")
    private Integer type;

    /**
     * 税号
     */
//    @NotBlank(message = "纳税人识别号不能为空")
//    @Pattern( regexp = "^9[A-Z0-9]{17}$",message = "纳税人识别号不合法")
    private String taxNumber;

    /**
     * 手机号
     */
//    @NotBlank(message = "手机号不能为空")
    private String mobile;

    /**
     * 发票抬头
     */
    @NotBlank(message = "抬头不能为空")
    @Size(min = 1, max = 50, message = "抬头长度只能在1-50之间")
//    @Pattern(regexp = "[A-Za-z\\u4e00-\\u9fa5]+",message = "抬头名称只能填字母和汉字")
    private String name;

    /**
     * 邮箱
     */
//    @NotBlank(message = "邮箱不能为空")
    private String email;
    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 开户银行名字
     */
    private String buyerBankName;

    /**
     * 开户银行账号
     */
    @ApiModelProperty(value = "购方银行账号")
    private String buyerBankAccount;

    @ApiModelProperty(value = "用户id")
//    @NotNull(message = "用户id不能为空")
    private Integer userId;
}
