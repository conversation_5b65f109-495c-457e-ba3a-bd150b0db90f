package com.yl.applets.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2024-03-04
 */
@Data
public class CmsPageStatisticsDto {


    @NotEmpty(message = "页面ID不能为空")
    private String pageId;

    @NotEmpty(message = "埋点类型不能为空")
    private String eventType;//埋点类型 eventType  click 点击 | browse  浏览

    @NotEmpty(message = "埋点类型名称不能为空")
    private String eventName;//埋点类型名称

    private String userId;//用户id

}
