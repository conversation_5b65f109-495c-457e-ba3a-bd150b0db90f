package com.yl.applets.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2024-03-09
 */
@Data
public class OrderMarkingSignDto {


    private String memberId;

    @NotBlank(message = "订单号不能为空")
    private String orderId;

    @NotBlank(message = "运单号不能为空")
    private String waybillId;
    private String phone;
    private List<OrderMarkingStatus> orderMarkingStatusList;


    @Data
    public static class OrderMarkingStatus {

        private String code;//标识code
        private Boolean status;//标识状态

    }
}
