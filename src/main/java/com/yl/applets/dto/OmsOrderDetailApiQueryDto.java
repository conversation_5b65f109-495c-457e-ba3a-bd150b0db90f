/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: OmsOrderDetailApiQueryDto
 * Author:   luhong
 * Date:     2020-10-15 15:03
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto;

import com.yl.common.base.valid.dto.BaseApiDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-15
 * @since 1.0.0
 */
@Data
@ApiModel(
        value = "OmsOrderDetailApiQueryDto",
        description = "订单详情Api查询条件请求实体"
)
public class OmsOrderDetailApiQueryDto extends BaseApiDTO {
    @ApiModelProperty("订单id")
    @NotNull(
            message = "订单id不能为空"
    )
    private Long id;
    @ApiModelProperty("批量订单id")
    private List<Long> ids;
    private boolean needOrderExt;
}