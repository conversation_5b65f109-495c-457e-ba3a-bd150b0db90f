package com.yl.applets.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * @Author: songxg
 * @Description:
 * @Date: 2020-06-09 11:22
 */
@Data
public class SignScanListMsgDto implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;
    private String waybillId;
    private Integer memberId;
    private String receiverName;
    private String receiverMobilePhone;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime signTime;
    private Integer pickNetworkId;
    @ApiModelProperty("1:寄件人通知,2:收件人通知")
    private Integer messageType;
    private String orderSourceCode;

    @ApiModelProperty("寄件人手机号")
    private String senderMobilePhone;
    @ApiModelProperty("寄件城市名称")
    private String senderCityName;
    @ApiModelProperty("收件城市名称")
    private String receiverCityName;
}
