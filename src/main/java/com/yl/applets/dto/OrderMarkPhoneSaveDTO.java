package com.yl.applets.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-04-22 10:34
 */
@Data
public class OrderMarkPhoneSaveDTO implements Serializable {

    /**
     * 手机号
     */
    @NotNull(message = "手机号不能为空")
    private String mobilePhone;


    /**
     * 状态 0:启用 1：禁用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 数据来源
     */
    private String dataSource;
}
