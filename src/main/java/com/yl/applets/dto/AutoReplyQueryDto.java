package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021<br>
 *
 * @description: 小程序自动回复规则
 * @author: caiyao
 * @create: 2021-05-20 10:11
 */
@Data
public class AutoReplyQueryDto {

    /**
     * 规则名称/关键词
     */
    @Size(max = 60, message = "规则名称/关键词不能超过60个字符")
    @ApiModelProperty(name = "keyWord", value = "规则名称/关键词")
    private String keyWord;

    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Long current;

    /**
     * 页码
     */
    @NotNull(message = "数量不能为空")
    private Long size = 20L;
}
