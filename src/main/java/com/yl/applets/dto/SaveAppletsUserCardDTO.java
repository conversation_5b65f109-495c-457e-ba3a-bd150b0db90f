package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/19 17:46
 * @description
 */
@Data
public class SaveAppletsUserCardDTO implements Serializable {
    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String name;
    /**
     * 证件类型
     */
    @NotNull(message = "证件类型不能为空")
    private Integer cardType;
    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    /**
     * 证件号
     */
    @NotBlank(message = "证件号不能为空")
    private String cardNum;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 查验方式 1手动输入2扫描上传
     */
    private Integer checkType;


    /**
     * 来源: 主要是判断是哪个小程序过来的
     */
    private String source;

    @ApiModelProperty(value = "短信验证码", name = "code")
    private String code;

    @NotNull(message = "是否需要校验验证码不能为空")
    @ApiModelProperty(value = "是否需要校验验证码", name = "isCheck")
    private Boolean isCheck;

    private Long userId;
    private Integer numberId;
    private String uniqueLimitId;
    private String loginUserMobile;
}
