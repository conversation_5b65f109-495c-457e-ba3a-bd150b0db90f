package com.yl.applets.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 优惠劵领取使用信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustPromotionUseDto对象", description = "优惠劵领取使用信息表")
public class CustPromotionUseDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "id")
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "优惠劵id")
    private Long proId;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "分发id")
    private Long disId;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "用户手机号")
    private String userPhone;

    @ApiModelProperty(value = "用户昵称")
    private String userAlias;

    @ApiModelProperty(value = "用户uuid")
    private String userUuid;

    @ApiModelProperty(value = "使用时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime useTime;

    @ApiModelProperty(value = "代理区id")
    private Long agencyId;

    @ApiModelProperty(value = "代理区名称")
    private String agencyName;

    @ApiModelProperty(value = "网点id")
    private Long networkId;

    @ApiModelProperty(value = "网点名称")
    private String networkName;

    @ApiModelProperty(value = "优惠劵名称")
    private String proName;

    @ApiModelProperty(value = "优惠劵CODE")
    private String proCode;

    @ApiModelProperty(value = "领取时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime getTime;

    @ApiModelProperty(value = "优惠劵类型 折扣券  1 直减券 0")
    private Integer couponType;

    @ApiModelProperty(value = "优惠类型参数")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "单笔最高抵扣金额")
    private BigDecimal couponLimit;

    @ApiModelProperty(value = "优惠劵有效开始时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime couponValidTime;

    @ApiModelProperty(value = "优惠劵有效结束时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime couponInvalidTime;

    @ApiModelProperty(value = "领取方式 业务员 0      小程序 1")
    private Integer getChannel;

    @ApiModelProperty(value = "使用方式  折扣券  1 直减券 0 混合 2")
    private Integer useUsage;

    @ApiModelProperty(value = "是否过期   1 已过期 2未过期")
    private Integer isOvertime;

    @ApiModelProperty(value = "订单号")
    private Long orderId;

    @ApiModelProperty(value = "订单优惠劵分摊运费  存在1个订单使用多个优惠劵时使用/取平均，最后的用减法")
    private Long orderPraTraAmount;

    @ApiModelProperty(value = "订单优惠劵分摊优惠  存在1个订单使用多个优惠劵时使用")
    private BigDecimal orderPraDisAmount;

    @ApiModelProperty(value = "订单实付运费")
    private BigDecimal orderTraAmount;

    @ApiModelProperty(value = "订单优惠")
    private BigDecimal orderDisAmount;

    @ApiModelProperty(value = "订单状态  1待揽收  2 运输中 3 已运达")
    private Integer orderStatus;

    @ApiModelProperty(value = "代理区code")
    private String agencyCode;

    @ApiModelProperty(value = "网点code")
    private String networkCode;

    @ApiModelProperty(value = "申请编号")
    private String applyId;

    @ApiModelProperty(value = "状态 1未使用  2 未核销 3已核销 4回收")
    private Integer status;

    @ApiModelProperty(value = "核销时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime verificationTime;

    @ApiModelProperty(value = "核销网点id")
    private Long verificationNetworkId;

    @ApiModelProperty(value = "核销网点code")
    private String verificationNetworkCode;

    @ApiModelProperty(value = "核销网点名称")
    private String verificationNetworkName;

    @ApiModelProperty(value = "订单优惠前运费")
    private BigDecimal orderPriTraAmount;

    @ApiModelProperty(value = "运单号")
    private String waybillNo;

    @ApiModelProperty(value = "员工编号")
    private String staffCode;

    @ApiModelProperty(value = "员工姓名")
    private String staffName;

    @ApiModelProperty(value = "发放主体1.市场部2.品牌部0.其他")
    private Integer grantSubject;

    @ApiModelProperty(value = "会员id")
    private Long numberId;

    @ApiModelProperty(value = "城市id")
    private Long cityId;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "加盟商id")
    private Long joinId;

    @ApiModelProperty(value = "加盟商名称")
    private String joinName;

    @ApiModelProperty(value = "支付是否成功 0 待支付 1成功 2失败")
    private Integer isPay;

    @ApiModelProperty(value = "支付时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

   private Long current;

   private Long size;

   @ApiModelProperty(value = "查询类型 1已领取、2已使用、3已失效")
   private Integer queryType;
}
