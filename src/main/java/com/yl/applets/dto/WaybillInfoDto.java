package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2022-07-07
 */

@Data
public class WaybillInfoDto {

    @ApiModelProperty(value = "总运费",name = "totalFreight" )
    private BigDecimal totalFreight;
    @ApiModelProperty(value = "预付面单费",name = "tax" )
    private BigDecimal tax;
    @ApiModelProperty(value = "手工费",name = "handicraftFee" )
    private BigDecimal handicraftFee;
    @ApiModelProperty(value = "其他费",name = "otherFee" )
    private BigDecimal otherFee;
    @ApiModelProperty(value = "回单费",name = "receiptFee" )
    private BigDecimal receiptFee;
    @ApiModelProperty(value = "运费",name = "freight" )
    private BigDecimal freight;
    @ApiModelProperty(value = "包材费",name = "packageCost" )
    private BigDecimal packageCost;
    @ApiModelProperty(value = "优惠金额",name = "couponAmount" )
    private BigDecimal couponAmount;
    @ApiModelProperty(value = "代收货款手续费",name = "codFee" )
    private BigDecimal codFee;
    @ApiModelProperty(value = "保价费",name = "insuredFee" )
    private BigDecimal insuredFee;
    @ApiModelProperty(value = "代收货款",name = "codFee" )
    private BigDecimal codMoney;


}
