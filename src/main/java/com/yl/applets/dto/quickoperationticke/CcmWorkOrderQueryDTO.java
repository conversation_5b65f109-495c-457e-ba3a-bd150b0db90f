package com.yl.applets.dto.quickoperationticke;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/4/14 16:18
 */
@Data
public class CcmWorkOrderQueryDTO  {



    /**
     * 微信用户ID
     */
    private String wxUserId;


    @ApiModelProperty(value = "当前页码，默认1")
    private int current = 1;

    @ApiModelProperty(value = "每页数据大小，默认20")
    private int size = 20;

    public int getCurrent() {
        if (this.current <= 0) {
            return 1;
        }
        return current;
    }

    public int getSize() {
        if (this.size <= 0) {
            return 100;
        }
        return size;
    }

}
