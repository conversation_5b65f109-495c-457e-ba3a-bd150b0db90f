package com.yl.applets.dto.quickoperationticke;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/4/14 16:18
 */
@Data
public class CcmWorkOrderCreateDTO {

    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空")
    @Size(max = 30, message = "客户名称不能超过30个字符")
    private  String customerName;

    /**
     * 客户类型
     * 客户类型 1:寄件人 2:收件人 3:其他
     */
    private Integer customerType;

    /**
     * 客户电话
     */
    @NotBlank(message = "客户电话不能为空")
    @Size(max = 15, message = "客户电话不能超过15个字符")
    private String customerPhone;

    /**
     * 运单号
     */
    @NotBlank(message = "运单号不能为空")
    private String waybillNo;

    /**
     * 一级选项name
     */
    @NotBlank(message = "二级问题类型编码不能为空")
    private String optionOneName;

    /**
     * 二级选项name
     */
    @NotBlank(message = "二级问题类型编码不能为空")
    private String optionTwoName;

    /**
     * 三级选项name
     */
    private String optionThreeName;

    /**
     * 问题描述
     */
    @NotBlank(message = "问题描述不能为空")
    @Size(max = 200, message = "问题描述不能超过200个字符")
    private String problemDescription;

    /**
     * 附件
     */
    private List<String> filePaths = new ArrayList<>();

    /**
     * 工单登记人
     */
    private String registrantName="微信小程序";

    /**
     * 微信用户ID
     */
    private String wxUserId;

    /**
     * 当前登录人的手机号码
     */
    private String phone;

}
