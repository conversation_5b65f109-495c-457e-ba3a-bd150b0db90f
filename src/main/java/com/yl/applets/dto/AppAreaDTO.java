/**
 * Copyright (C), 2015-2021, 云路供应链科技有限公司
 * FileName: AppAreaDTO
 * Author:   luhong
 * Date:     2021-03-03 14:43
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2021-03-03
 * @since 1.0.0
 */
@Data
@ApiModel(value = "收寄件区域实体", description = "收寄件区域请求实体")
public class AppAreaDTO {

    @ApiModelProperty(value = "地址类型 1寄件，2收件，3公用")
    private Integer addressType;

    @ApiModelProperty(value = "父级ID:市和区上级id  非必填 如果要查某个省下面所有的市则type传3,parentId传该省id")
    private Integer parentId;

    @ApiModelProperty(value = "区域类型type:1.国家,2.省,3.市,4区")
    private Integer type;

    @ApiModelProperty(value = "模糊查询keyword")
    private String keyWord;

    @ApiModelProperty(value = "sign")
    private String sign;

    @ApiModelProperty(value = "times")
    private String times;


}