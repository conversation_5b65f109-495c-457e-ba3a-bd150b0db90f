/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: OmsOrderDispatchMsgDto
 * Author:   luhong
 * Date:     2020-10-15 11:44
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-15
 * @since 1.0.0
 */
@ApiModel(
        value = "订单消息推送实体",
        description = "订单消息推送实体"
)
@Data
public class OmsOrderDispatchMsgDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("订单编号")
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    private Long id;
    @ApiModelProperty("订单录入时间")
    private LocalDateTime inputTime;
    @ApiModelProperty("运单号")
    private String waybillId;
    @ApiModelProperty("取件业务员名称")
    private String pickStaffName;
    @ApiModelProperty("取件业务员code")
    private String pickStaffCode;
    @ApiModelProperty("客户下单时间")
    private LocalDateTime customerOrderTime;
    @ApiModelProperty("会员id")
    private Integer memberId;
    @ApiModelProperty("取件地址")
    private String address;

    private String orderSourceCode;
}