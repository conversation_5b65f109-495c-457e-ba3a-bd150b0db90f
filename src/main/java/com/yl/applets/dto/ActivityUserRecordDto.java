package com.yl.applets.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动管理配置
 * @author: xiongweibin
 * @create: 2020-08-24 11:48
 */
@Data
public class ActivityUserRecordDto implements Serializable {
    /**
     * 抽奖流水id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long prizeId;

    /**
     * 活动id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 活动code
     */
    private String activityCode;
    /**
     *  奖项等级
     */
    private Integer winLevel;
    /**
     *  奖项id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long itemId;
    /**
     * 奖项名称
     */
    private String winName;

    /**
     * 抽奖时间
     */
    private LocalDateTime winTime;

    /**
     * 抽奖人手机号
     */
    private String winMobile;
    /**
     * 抽奖人openid
     */
    private String openId;

    /**
     * 抽奖人会员id
     */
    private Integer numberId;

    /**
     * 是否中奖0否1是
     */
    private Integer isWin;
    /**
     * 类型（默认1）
     */
    private Integer type;

    /**
     * 中奖图片
     */
    private String winImgUrl;



}
