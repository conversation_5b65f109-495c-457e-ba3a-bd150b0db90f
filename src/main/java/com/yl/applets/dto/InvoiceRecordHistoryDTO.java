package com.yl.applets.dto;

import com.yl.applets.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* 开票历史
*
* <AUTHOR> created on 2020-05-15
*/
@Data
@ApiModel(description = "开票历史dto")
public class InvoiceRecordHistoryDTO extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("客户ID, [非必填, 客户ID和客户编码必须填一个]")
    private Integer customerId;

    @ApiModelProperty("客户编码, [非必填, 客户ID和客户编码必须填一个]")
    private String customerCode;

    @ApiModelProperty("数据来源: [1,小程序]")
    private Integer dataSource;


    @ApiModelProperty("客户开票名称, [非必填, 模糊查询], 用于开票抬头历史记录查询使用")
    private String customerName;

    @ApiModelProperty("客户开票税号, [非必填, 模糊查询], 用于开票抬头历史记录查询使用")
    private String customerTaxNumber;

}

