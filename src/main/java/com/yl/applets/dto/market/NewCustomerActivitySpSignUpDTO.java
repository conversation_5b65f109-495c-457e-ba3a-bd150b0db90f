package com.yl.applets.dto.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/9 11:51
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewCustomerActivitySpSignUpDTO implements Serializable {

    @ApiModelProperty(value = "渠道来源")
    @NotNull(message = "渠道来源不能为空")
    private String source;

    @ApiModelProperty(value = "活动编码")
    @NotBlank(message = "活动编码不能为空")
//    @Pattern(regexp = "^J00002$", message = "活动编码错误")
    private String marketActivityCode;

    @ApiModelProperty(value = "姓名")
    @Size(max = 30, message = "姓名超出长度")
    @NotBlank(message = "姓名不能为空")
    private String name;

    @ApiModelProperty(value = "手机号")
    @Size(max = 11, message = "手机号超出长度")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "省份id")
    @NotNull(message = "省份不能为空")
    private Integer provinceId;

    @ApiModelProperty(value = "省份名称")
    @NotBlank(message = "省份不能为空")
    private String provinceName;

    @ApiModelProperty(value = "城市id")
    @NotNull(message = "城市不能为空")
    private Integer cityId;

    @ApiModelProperty(value = "城市名称")
    @NotBlank(message = "城市不能为空")
    private String cityName;


    @ApiModelProperty(value = "区域id")
    @NotNull(message = "区域不能为空")
    private Integer areaId;


    @ApiModelProperty(value = "区域名称")
    @NotBlank(message = "区域不能为空")
    private String areaName;

    @ApiModelProperty(value = "详细地址")
    @NotBlank(message = "详细地址不能为空")
    private String address;

    @ApiModelProperty(value = "时间戳")
    @NotBlank(message = "时间戳不能为空")
    private String times;

    @ApiModelProperty(value = "验签")
    @NotBlank(message = "验签不能为空")
    private String sign;

    //    @NotBlank(message = "滑块参数ranstr为空")
    private String ticket;

    //    @NotBlank(message = "滑块参数ticket为空")
    private String randstr;

    @NotBlank(message = "captcha为空")
    private String captcha;
    @NotBlank(message = "ctoken为空")
    private String ctoken;

    @ApiModelProperty(value = "邀请人")
    private String inviter;

    @ApiModelProperty(value = "邀请人是否有效: 0否，1是")
    private Integer isInviter;

}

