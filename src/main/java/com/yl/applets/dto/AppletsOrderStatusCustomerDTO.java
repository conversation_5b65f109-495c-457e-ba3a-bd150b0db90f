package com.yl.applets.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title AppletsOrderStatusCustomerDTO
 * @Package com.yl.edi.lh.dto.applets
 * @Description 小程序收件 发件客户信息
 * @date 2021/8/12 1:54 下午
 */
@Data
public class AppletsOrderStatusCustomerDTO {

    /**
     * 姓名（收件⼈必传，寄件⼈非必传）
     */
    @JsonProperty("name")
    @JSONField(name="name")
    private String name;

    /**
     * 电话（收件⼈必传，寄件⼈非必传）
     */
    @JsonProperty("phone")
    @JSONField(name="phone")
    private String phone;

    /**
     * 寄件人信息填寄件人省
     * 收件人信息填收件人省
     */
    @JsonProperty("province")
    @JSONField(name="province")
    private String province;

    /**
     * 寄件人信息填寄件人市
     * 收件人信息填收件人市
     */
    @JsonProperty("city")
    @JSONField(name="city")
    private String city;

    /**
     * 寄件人信息填寄件人区
     * 收件人信息填收件人区
     */
    @JsonProperty("area")
    @JSONField(name="area")
    private String area;
}