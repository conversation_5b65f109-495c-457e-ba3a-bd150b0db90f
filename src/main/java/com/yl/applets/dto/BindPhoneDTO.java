package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-09-23 09:22 <br>
 * @Author: zhipeng.liu
 */
@Data
@ApiModel(value = "wx绑定手机号请求体", description = "wx绑定手机号请求体")
public class BindPhoneDTO {


    @ApiModelProperty(value = "encryptedData", notes = "encryptedData:wx加密数据")
    String encryptedData;

    @ApiModelProperty(value = "iv", notes = "iv:wx解密数据iv")
    String iv;

    @ApiModelProperty(value = "openid", notes = "openid")
    String openid;

    @ApiModelProperty(value = "unionid", notes = "unionid")
    String unionid;

    @ApiModelProperty(value = "times", notes = "入参时间戳")
    Long times;
    @ApiModelProperty(value = "sign", notes = "密文")
    String sign;

    //取sessionKey
    String uuid;

    //新版使用
    private String code;

    @ApiModelProperty(value = "省", notes = "省")
    private String province;

    @ApiModelProperty(value = "市", notes = "市")
    private String city;

    @ApiModelProperty(value = "区", notes = "区")
    private String zone;
}
