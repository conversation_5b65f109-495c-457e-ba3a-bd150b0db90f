package com.yl.applets.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 活动管理查询dto
 * @author: xiongweibin
 * @create: 2020-08-24 14:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ActivityConfigQueryDTO implements Serializable {
    /**
     * 标题
     */
    private String title;
    /**
     * 类型 1:小程序开屏 2:Banner
     */
    private Integer type;
    /**
     * 顺序位 1:一位 2:二位 3:三位
     */
    private String sort;
    /**
     * 状态 1:待发布 2:发布中 3:已下线
     */
    private Integer status;
    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Long current;

    /**
     * 页码
     */
    @NotNull(message = "数量不能为空")
    private Long size = 20L;

    //适用地区 名字,id
    private List<Area> useArea;

    private String address;

    @Data
    public static class Area{
        private String provinceId;
        private String provinceName;
        private String cityId;
        private String cityName;
        private Integer type;//1.省2.市
    }

}
