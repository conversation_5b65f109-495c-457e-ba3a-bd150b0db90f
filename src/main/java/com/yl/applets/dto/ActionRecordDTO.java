package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：用户行为记录
 * @Author： <PERSON><PERSON><PERSON><PERSON>
 * @Date： 2022/07/25
 */
@Data
public class ActionRecordDTO implements Serializable {

    @ApiModelProperty(value = "事件 code")
    private String elementCode;

    @ApiModelProperty(value = "事件内容")
    private String elementContent;

    @ApiModelProperty(value = "事件名称")
    private String elementEventName;

    @ApiModelProperty(value = "元素id")
    private String elementId;

    @ApiModelProperty(value = "事件类型")
    private String eventType;

    @ApiModelProperty(value = "机型")
    private String phoneModel;

    @ApiModelProperty(value = "上报地址")
    private String reportAddress;

    @ApiModelProperty(value = "上报经纬度")
    private String reportLocation;

    @ApiModelProperty(value = "页面路径")
    private String pagePath;

    @ApiModelProperty(value = "ip地址")
    private String ipAddress;

}
