package com.yl.applets.dto;


import lombok.Data;

@Data
public class InterceptOrderDto {


    private  SendAddress  sendAddress;

    private  ReceiverAddress  receiverAddress;

    private String source;

    @Data
    public static class SendAddress{

        private String province;
        private String city;
        private String area;
        private String detail;

    }


    @Data
    public static  class  ReceiverAddress{

        private String province;
        private String city;
        private String area;
        private String detail;

    }

}
