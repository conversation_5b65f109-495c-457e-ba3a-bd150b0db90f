package com.yl.applets.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 地址管理
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-20
 */
@Data
public class AppletsUserSignQueryDto {


    private Long id;


    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private Integer memberId;


    private String memberName;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String mobile;


    /**
     * 标签名称
     */
    private String signName;


    /**
     * 标签类型 1地址簿
     */
    private Integer signType;

    /**
     * 标签状态 1展示 2隐藏
     */
    private Integer signStatus;

    /**
     * 是否删除 1否 2是
     */
    private Integer isDelete;




}
