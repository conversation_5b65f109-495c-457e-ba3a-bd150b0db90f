package com.yl.applets.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 系统操作记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CustSystemOperationDto对象", description = "系统操作记录表")
public class CustSystemOperationDto implements Serializable {


    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "操作类型")
    private String operateType;

    @ApiModelProperty(value = "操作时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operateTime;

    @ApiModelProperty(value = "操作用户id")
    private Long userId;

    @ApiModelProperty(value = "操作用户")
    private String userName;

    @ApiModelProperty(value = "说明")
    private String explain;

    @ApiModelProperty(value = "操作页面")
    private String operatePage;

    @ApiModelProperty(value = "设备信息")
    private String equipmentInfo;

    @ApiModelProperty(value = "用户number id")
    private Long numberId;

    @ApiModelProperty(value = "经纬度")
    private String coordinate;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "操作天")
    private String operateDay;

   private Long current;

   private Long size;
}
