package com.yl.applets.dto;

import com.yl.applets.vo.OrderMarkExpandVO;
import com.yl.common.base.constant.SaveGroup;
import com.yl.common.base.constant.UpdateGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OrderDTO extends PreferencesCommonDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inputTime;

    @NotNull(message = "formId不能为空", groups = {SaveGroup.class})
    private String formId;

    @NotNull(message = "订单编号不能为空", groups = {UpdateGroup.class})
    private Long id;

    @NotBlank(message = "寄件人姓名不能为空")
    @Size(min = 1, max = 100, message = "寄件人姓名长度只能在1-100之间")
    private String senderName;

    @NotBlank(message = "寄件人手机号不能为空")
//    @Pattern(regexp = "^1\\d{10}$", message = "寄件人手机号不合法")
    private String senderMobilePhone;

    @NotBlank(message = "寄件省份名称不能为空")
    @Size(max = 60, message = "寄件省份名称不能超过60个字符")
    private String senderProvinceName;

    @NotNull(message = "寄件省份id不能为空")
    private Integer senderProvinceId;

    @NotBlank(message = "寄件城市名称不能为空")
    @Size(max = 60, message = "寄件城市名称不能超过60个字符")
    private String senderCityName;

    @NotNull(message = "寄件城市id不能为空")
    private Integer senderCityId;

    @NotBlank(message = "寄件区域名称不能为空")
    @Size(max = 60, message = "寄件区域名称不能超过60个字符")
    private String senderAreaName;

    @NotNull(message = "寄件区域id不能为空")
    private Integer senderAreaId;

    @NotBlank(message = "寄件详细地址不能为空")
    @Size(min = 4, max = 80, message = "寄件详细地址长度只能在4-80之间")
    private String senderDetailedAddress;

    @NotBlank(message = "收件人姓名不能为空")
    @Size(min = 1, max = 100, message = "收件人姓名长度只能在1-100之间")
    private String receiverName;

    @NotBlank(message = "收件人手机号不能为空")
//    @Pattern(regexp = "^1\\d{10}$", message = "收件人手机号不合法")
    private String receiverMobilePhone;

    @NotBlank(message = "收件省份名称不能为空")
    @Size(max = 60, message = "收件省份名称不能超过60个字符")
    private String receiverProvinceName;

    @NotNull(message = "收件省份id不能为空")
    private Integer receiverProvinceId;

    @NotBlank(message = "收件城市名称不能为空")
    @Size(max = 60, message = "收件城市名称不能超过60个字符")
    private String receiverCityName;

    @NotNull(message = "收件城市id不能为空")
    private Integer receiverCityId;

    @NotBlank(message = "收件区域名称不能为空")
    @Size(max = 60, message = "收件区域名称不能超过60个字符")
    private String receiverAreaName;

    @NotNull(message = "收件区域id不能为空")
    private Integer receiverAreaId;

    @NotBlank(message = "收件详细地址不能为空")
    @Size(min = 4, max = 80, message = "收件详细地址长度只能在4-80之间")
    private String receiverDetailedAddress;

    @Range(min = 0, max = 50000, message = "保价金额只能在0-50000之间")
    private BigDecimal declaredValue;

    @Size(max = 50, message = "备注长度只能在0-50之间")
    private String remarks;
    @ApiModelProperty(value = "需要保价1是，0否")
    @Min(value = 0, message = "需要保价只能为0或者1")
    @Max(value = 1, message = "需要保价只能为0或者1")
    private Integer insured;

    @NotBlank(message = "物品类型编码不能为空")
    @Size(max = 30, message = "物品类型编码不能超过30个字符")
    private String goodsTypeCode;

    @ApiModelProperty(value = "物品类型名称")
    @Size(max = 30, message = "物品类型名称不能超过30个字符")
    @NotBlank(message = "物品类型名称不能为空")
    private String goodsTypeName;

    @Size(max = 30, message = "付款方式编码不能超过30个字符")
    private String paymentModeCode;

    @ApiModelProperty(value = "结算方式名称")
    @Size(max = 60, message = "结算方式名称不能超过60个字符")
    private String paymentModeName;

    private String goodsName;

    @NotNull(message = "物品件数不能为空")
    @Range(min = 1, max = 999999, message = "物品件数只能在1-999999之间")
    private Integer packageNumber;

    @NotNull(message = "物品重量不能为空")
    @Range(min = 1, max = 30, message = "物品重量只能在1-30之间")
    private BigDecimal packageTotalWeight;

    @ApiModelProperty(value = "取件网点id")
    private Long pickNetworkId;

    @ApiModelProperty(value = "寄件人公司")
    @Size(max = 100, message = "寄件人公司不能超过100个字符")
    private String senderCompany;


    @ApiModelProperty(value = "收件人公司")
    @Size(max = 100, message = "收件人公司不能超过100个字符")
    private String receiverCompany;

    private BigDecimal settlementWeight;


    @ApiModelProperty(value = "保价费")
    @Digits(integer = 10, fraction = 2, message = "保价费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal insuredValue;

    @ApiModelProperty(value = "标准运费")
    @Digits(integer = 10, fraction = 2, message = "标准运费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal standardValue;

    @ApiModelProperty(value = "总费用")
    @Digits(integer = 10, fraction = 2, message = "总费用超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal totalFreight;

    /**
     * 折扣运费
     */
    private BigDecimal discountFreight;


    /**
     * 1 网购退货
     * 2 学生寄件
     * 3 内部员工寄件
     */
    private Integer orderChannelType;



    private String pickStaffCode;
    //运单号
    private String waybillId;

//    /** 签回单 0否   1是  2回单标记*/
//    private Integer signReceipt;
//
//    /** 回单金额 */
//    private BigDecimal receiptFreight;
    /** 回单运单号 */
    private String receiptWaybillNo;
    /**
     * 是否隐私面单 0:否 1:是
     */
    private Integer isPrivacy;

    @ApiModelProperty(value = "最佳取件开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //@NotNull(message = "最佳取件开始时间不能为空")
    private LocalDateTime bestPickTimeStart;

    @ApiModelProperty(value = "最佳取件结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //@NotNull(message = "最佳取件结束时间不能为空")
    private LocalDateTime bestPickTimeEnd;

    protected Integer from;

    private String orderSourceCode;

    @ApiModelProperty(value = "优惠券code")
    private String couponCode;

    @ApiModelProperty(value = "优惠券id")
    private String couponId;

    @ApiModelProperty("优惠金额")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "当前使用优惠券id")
    private String useProId;

    @ApiModelProperty(value = "优惠券分发id")
    private String disId;

    @ApiModelProperty(value = "三段码解析的网点id")
    private Long fetchNetworkId;

    private String myRequestId;

    @NotEmpty(message = "产品类型不能为空")
    private String expressTypeCode;

    /**
     * 是否专属
     * 1.是 0.否
     */
    private Integer isExclusive;

    /**
     * 订单标签列表
     */
    private List<OrderMarkExpandVO> markFields;

    /**
     * 月结账号 当支付类型为月结时需要填充
     *
     */
    private String account;

    /**
     * 小程序下单重算运费
     */
    private Boolean appletsReCalculate;


    /**
     *  修改时专属月结字段
     */
    private String pickStaffCodeUpdate;
    /**
     * 完整地址
     *
     * @return
     */
    public String getSenderAddressFull() {
        return this.senderProvinceName.concat("/").concat(this.senderCityName).concat("/").concat(this.senderAreaName).concat("/").concat(this.senderDetailedAddress);
    }
}
