package com.yl.applets.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-04-23 10:13
 */
@Data
public class OrderMarkingDTO extends PreferencesCommonDto implements Serializable {

    /**
     * 订单号
     */
    @NotNull(message = "订单号不能为空")
    private Long orderId;

    /**
     * 运单号
     */
//    @NotBlank(message = "运单号不能为空")
    private String waybillId;

    /**
     * 订单来源
     */
//    @NotNull(message = "订单来源不能为空")
    private String orderSourceCode;

    /**
     * 订单录入时间
     */
//    @NotNull(message = "订单录入时间不能为空")
    private LocalDateTime inputTime;

    /**
     * 数据来源
     */
//    @NotNull(message = "数据来源不能为空")
    private String dataSource;

    //校验权限
    private String phone;
    private String memberId;

}
