package com.yl.applets.dto;

import lombok.Data;
import java.io.Serializable;

@Data
public class MiniNotifyMsgDTO implements Serializable {
    /**
     * 运单号
     */
    private String waybillNo;

    /**
     * 寄件城市名称
     */
    private String senderCityName;

    /**
     * 派件员手机号
     */
    private String deliveryMobile;

    /**
     * 派件员名称
     */
    private String deliveryName;

    /**
     * 收件人手机号
     */
    private String receiverMobilePhone;

    /**
     * 寄件人手机号
     */
    private String senderMobilePhone;

    /**
     * 寄件人名称
     */
    private String senderName;

    /**
     * 收件城市名称
     */
    private String receiveCityName;

    /**
     * 订单来源
     */
    private String orderSourceCode;

}
