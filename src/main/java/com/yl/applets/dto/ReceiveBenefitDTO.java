package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： zhan<PERSON>hong
 * @Date： 2022/07/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ReceiveBenefitDTO", description = "福利申领记录")
public class ReceiveBenefitDTO {

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Size(max = 30,message = "姓名超出长度")
    @NotBlank(message = "姓名不能为空")
    private String name;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    @Size(max = 20,message = "手机号超出长度")
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    /**
     * 省份id
     */
    @ApiModelProperty(value = "省份id")
    @NotNull(message = "省份id不能为空")
    private Integer provinceId;

    /**
     * 省份名称
     */
    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    /**
     * 城市id
     */
    @ApiModelProperty(value = "城市id")
    @NotNull(message = "城市id不能为空")
    private Integer cityId;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String cityName;

    /**
     * 区域id
     */
    @ApiModelProperty(value = "区域id")
    private Integer areaId;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String areaName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    @NotBlank(message = "详细地址不能为空")
    private String address;

    /**
     * 渠道来源 ChannelSourceEnum
     */
    @ApiModelProperty(value = "渠道来源")
    private String source;


    @ApiModelProperty(value = "图片验证码token")
    private String cToken;


    @ApiModelProperty(value = "图片验证码")
    private String captcha;

    private String times;

    private String sign;

}
