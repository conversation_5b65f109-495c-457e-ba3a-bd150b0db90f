package com.yl.applets.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-04-23 10:13
 */
@Data
public class OrderRecvPrefDTO implements Serializable {

    /**
     * 工作日一级偏好编码
     */
    private String weekdayOneLevelCode;

    /**
     * 工作日一级偏好名称
     */
    private String weekdayOneLevelName;

    /**
     * 工作日二级偏好编码
     */
    private String weekdayTwoLevelCode;

    /**
     * 工作日二级偏好名称
     */
    private String weekdayTwoLevelName;

    /**
     * 休息日一级偏好编码
     */
    private String weekendOneLevelCode;

    /**
     * 休息日一级偏好名称
     */
    private String weekendOneLevelName;

    /**
     * 休息日二级偏好编码
     */
    private String weekendTwoLevelCode;

    /**
     * 休息日二级偏好名称
     */
    private String weekendTwoLevelName;

    /**
     * 数据来源
     */
    private String dataSource;

}
