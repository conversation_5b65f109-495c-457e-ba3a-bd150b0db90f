package com.yl.applets.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/2/24 19:42
 */
@Data
public class MyStaffCheckAddressDTO {
    /**
     * 省份
     */
    @NotNull(message = "省份信息为空")
    private Integer providerId;
    /**
     * 省份
     */
    @NotNull(message = "省份信息为空")
    private String providerDesc;

    /**
     * 城市
     */
    @NotNull(message = "城市为空")
    private Integer cityId;
    /**
     * 城市
     */
    @NotNull(message = "城市为空")
    private String cityDesc;
    /**
     * 区/县描述
     */
    @NotNull(message = "区/县描述为空")
    private Integer areaId;
    /**
     * 区/县描述
     */
    @NotNull(message = "区/县描述为空")
    private String areaDesc;


    @NotNull(message = "网点省份信息为空")
    private Integer networkProviderId;

    /**
     * 城市
     */
    @NotNull(message = "网点城市为空")
    private Integer networkCityId;

    /**
     * 区/县描述
     */
    @NotNull(message = "网点区/县描述为空")
    private Integer networkAreaId;

    /**
     * 区/县描述
     */
    @NotNull(message = "网点区/县描述为空")
    private String networkAreaDesc;


}
