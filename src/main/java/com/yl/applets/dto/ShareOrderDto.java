package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-08-06
 */

@Data
@ApiModel(value = "分享订单dto",description = "分享订单dto")
public class ShareOrderDto {


    @NotNull(message = "被分享人openid不能为空")
    @ApiModelProperty(value = "被分享人openid",notes = "被分享人openid")
    private String  beSharedOpenid;

//    @NotNull(message = "分享人openid不能为空")
//    @ApiModelProperty(value = "分享人openid",notes = "分享人openid")
//    private String  shareOpenid;


    @NotNull(message = "C端用户ID不能为空")
    @ApiModelProperty(value = "C端用户ID",notes = "C端用户ID")
    private String  memberId;


    @NotNull(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号",notes = "订单号")
    private String orderId;

    @NotNull(message = "分享时间戳不能为空")
    @ApiModelProperty(value = "分享时间戳",notes = "分享时间戳")
    private Long timestamp;


}
