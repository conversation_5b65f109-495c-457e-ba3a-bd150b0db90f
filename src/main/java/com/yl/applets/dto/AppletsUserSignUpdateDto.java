package com.yl.applets.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 地址管理
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-20
 */
@Data
public class AppletsUserSignUpdateDto {


    @NotNull(message = "id不能为空")
    private Long id;


    /**
     * 用户id
     */

    private Integer memberId;


    private String memberName;

    /**
     * 手机号
     */
    private String mobile;


    /**
     * 标签名称
     */
    private String signName;

    /**
     * 标签状态 1展示 2隐藏
     */
    private Integer signStatus;



}
