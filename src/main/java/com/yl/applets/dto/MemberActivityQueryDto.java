package com.yl.applets.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yl.common.base.constant.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 异常信息监控配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MemberActivityQueryDto对象", description = "MemberActivityQueryDto")
public class MemberActivityQueryDto implements Serializable {

   private Long current;

   private Long size;

   @ApiModelProperty(value = "ID")
   @JsonSerialize(using = ToStringSerializer.class)
   @NotNull(message = "id不能为空",groups = UpdateGroup.class)
   private Long id;

   @ApiModelProperty(value = "活动名称")
   @NotBlank(message = "活动名称不能为空")
   @Size(min = 1, max = 50, message = "活动名称长度只能在1-50之间")
   private String name;

   @ApiModelProperty(value = "活动类型")
   @NotNull(message = "活动类型不能为空")
   private Integer type;

   @ApiModelProperty(value = "赠品类型")
   @NotNull(message = "赠品类型不能为空")
   private Integer giftType;

   @ApiModelProperty(value = "赠品关联")
   @NotBlank(message = "赠品关联不能为空")
   private String giftRelation;

   @ApiModelProperty(value = "活动描述")
   @Size(min = 1, max = 200, message = "活动描述只能在1-200之间")
   private String activityDesc;

   @ApiModelProperty(value = "活动用户")
   @NotNull(message = "活动用户不能为空")
   private Integer activityUser;

   @ApiModelProperty(value = "是否删除")
   private Integer isDelete;

   @ApiModelProperty(value = "创建人id")
   private Long createBy;

   @ApiModelProperty(value = "创建人name")
   private String createByName;

   @ApiModelProperty(value = "更新人id")
   private Long updateBy;

   @ApiModelProperty(value = "更新人name")
   private String updateByName;

   @ApiModelProperty(value = "创建时间")
   private LocalDateTime createTime;

   @ApiModelProperty(value = "更新时间")
   private LocalDateTime updateTime;


}

