package com.yl.applets.dto;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yl.applets.vo.OrderMarkExpandVO;
import com.yl.common.base.annotation.log.operation.OperationParamLog;
import com.yl.common.base.enums.log.OperationParamTypeEnum;
import com.yl.common.base.valid.dto.BaseApiDTO;
import com.yl.common.base.valid.group.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-08-02 18:10 <br>
 * @Author: <a href="<EMAIL>">qiang.hu</a>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OmsOrder请求对象", description = "订单请求实体")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OmsOrderApiDTO extends PreferencesCommonDto {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户订单编号")
    @Size(max = 10000, message = "客户订单编号不能超过10000个字符")
    private String customerOrderId;

    @ApiModelProperty(value = "客户编号code")
    @Size(max = 30, message = "客户编号不能超过30个字符")
    private String customerCode;

    @ApiModelProperty(value = "订单编号")
    @NotNull(message = "id不能为空", groups = {JmsUpdateGroup.class})
    @Null(message = "编号不能有值", groups = {JmsSaveGroup.class, EdiSaveGroup.class, JmsUpdateGroup.class})
    private Long id;

    @ApiModelProperty(value = "运单号")
    @Size(max = 30, message = "运单号不能超过30个字符")
    private String waybillId;

    @ApiModelProperty(value = "订单类型code，1.散客 2.月结")
    @NotNull(message = "订单类型code不能为空", groups = {EdiSaveGroup.class})
    private Integer orderTypeCode;

    @ApiModelProperty(value = "订单来源名称")
    @NotBlank(message = "订单来源名称不能为空", groups = {JmsSaveGroup.class, JmsUpdateGroup.class})
    @Size(max = 30, message = "订单来源名称不能超过30个字符")
    private String orderSourceName;

    @ApiModelProperty(value = "订单来源code")
    @NotBlank(message = "订单来源code不能为空", groups = {JmsSaveGroup.class, JmsUpdateGroup.class})
    @Size(max = 30, message = "订单来源code不能超过30个字符")
    private String orderSourceCode;

    @ApiModelProperty(value = "最佳取件开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //@NotNull(message = "最佳取件开始时间不能为空")
    private LocalDateTime bestPickTimeStart;

    @ApiModelProperty(value = "最佳取件结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    //@NotNull(message = "最佳取件结束时间不能为空")
    private LocalDateTime bestPickTimeEnd;

    @ApiModelProperty(value = "取件时间")
    private LocalDateTime pickTime;

    @ApiModelProperty(value = "支付方式名称")
    @NotBlank(message = "支付方式名称不能为空", groups = {JmsSaveGroup.class, JmsUpdateGroup.class})
    @Size(max = 30, message = "支付方式名称不能超过30个字符")
    private String paidModeName;

    @ApiModelProperty(value = "支付方式code")
    @NotBlank(message = "支付方式code不能为空", groups = {JmsSaveGroup.class, JmsUpdateGroup.class})
    @Size(max = 30, message = "支付方式code不能超过30个字符")
    private String paidModeCode;

    @ApiModelProperty(value = "寄件人姓名")
    @Size(max = 100, message = "寄件人姓名不能超过100个字符")
    private String senderName;

    @ApiModelProperty(value = "寄件人公司")
    @Size(max = 100, message = "寄件人公司不能超过100个字符")
    private String senderCompany;

    @ApiModelProperty(value = "寄件人手机号")
    @Size(max = 30, message = "寄件人手机号不能超过30个字符")
    @NotBlank(message = "寄件人手机号不能为空", groups = {MiniAppSaveGroup.class, OWSaveGroup.class, CsSaveGroup.class})
    private String senderMobilePhone;

    @ApiModelProperty(value = "寄件人座机")
    @Size(max = 30, message = "寄件人固话不能超过30个字符")
    private String senderTelphone;

    @ApiModelProperty(value = "寄件国家名称")
    @Size(max = 60, message = "寄件国家名称不能超过60个字符")
    private String senderCountryName;

    @ApiModelProperty(value = "寄件国家Id")
    private Integer senderCountryId;

    @ApiModelProperty(value = "寄件省份名称")
    @Size(max = 60, message = "寄件省份名称不能超过60个字符")
    @NotBlank(message = "寄件省份名称不能为空", groups = {JmsSaveGroup.class, AliSaveGroup.class,
            JmsUpdateGroup.class, OWSaveGroup.class, CsSaveGroup.class})
    private String senderProvinceName;

    @ApiModelProperty(value = "寄件省份id")
    @NotNull(message = "寄件省份id不能为空", groups = {AliSaveGroup.class, OWSaveGroup.class, CsSaveGroup.class})
    private Integer senderProvinceId;

    @ApiModelProperty(value = "寄件城市名称")
    @Size(max = 60, message = "寄件城市名称不能超过60个字符")
    @NotBlank(message = "寄件城市名称不能为空", groups = {JmsSaveGroup.class, JmsUpdateGroup.class, OWSaveGroup.class, CsSaveGroup.class})
    private String senderCityName;

    @ApiModelProperty(value = "寄件城市id")
    @NotNull(message = "寄件城市id不能为空", groups = {OWSaveGroup.class, CsSaveGroup.class})
    private Integer senderCityId;

    @ApiModelProperty(value = "寄件区域名称")
    @Size(max = 60, message = "寄件区域名称不能超过60个字符")
    @NotBlank(message = "寄件区域名称不能为空", groups = {MiniAppSaveGroup.class, JmsUpdateGroup.class,
            OWSaveGroup.class, CsSaveGroup.class})
    private String senderAreaName;

    @ApiModelProperty(value = "寄件区域Id")
    @NotNull(message = "寄件区域Id不能为空",groups = {MiniAppSaveGroup.class,OWSaveGroup.class,CsSaveGroup.class})
    private Integer senderAreaId;

    @ApiModelProperty(value = "寄件乡镇")
    @Size(max = 200, message = "寄件乡镇不能超过200个字符")
    private String senderTownship;

    @ApiModelProperty(value = "寄件街道")
    @Size(max = 200, message = "不能超过200个字符")
    private String senderStreet;

    @ApiModelProperty(value = "寄件详细地址")
    @Size(max = 255, message = "寄件详细地址不能超过255个字符")
    private String senderDetailedAddress;

    @ApiModelProperty(value = "寄件邮编")
    @Size(max = 60, message = "寄件邮编不能超过60个字符")
    private String senderPostalCode;

    @ApiModelProperty(value = "寄件人邮箱")
    @Size(max = 150, message = "寄件人邮箱不能超过150个字符")
    private String senderEmail;

    @ApiModelProperty(value = "寄件服务方式名称")
    @NotBlank(message = "寄件服务方式名称不能为空", groups = {JmsSaveGroup.class, JmsUpdateGroup.class})
    @Size(max = 30, message = "寄件服务方式名称不能超过30个字符")
    private String sendName;

    @ApiModelProperty(value = "寄件服务方式code")
    @NotBlank(message = "寄件服务方式code不能为空", groups = {JmsSaveGroup.class, EdiSaveGroup.class, JmsUpdateGroup.class})
    @Size(max = 30, message = "寄件服务方式code不能超过30个字符")
    private String sendCode;

    @ApiModelProperty(value = "收件人姓名")
    @Size(max = 100, message = "收件人姓名不能超过100个字符")
    private String receiverName;

    @ApiModelProperty(value = "收件人公司")
    @Size(max = 100, message = "收件人公司不能超过100个字符")
    private String receiverCompany;

    @ApiModelProperty(value = "收件人手机号")
    @Size(max = 30, message = "收件人手机号不能超过30个字符")
    @NotBlank(message = "手机号不能为空", groups = {MiniAppSaveGroup.class, OWSaveGroup.class})
    private String receiverMobilePhone;

    @ApiModelProperty(value = "收件人座机")
    @Size(max = 30, message = "收件人固话不能超过30个字符")
    private String receiverTelphone;

    @ApiModelProperty(value = "收件国家名称")
    @Size(max = 60, message = "收件国家名称不能超过60个字符")
    private String receiverCountryName;

    @ApiModelProperty(value = "收件国家id")
    private Integer receiverCountryId;

    @ApiModelProperty(value = "收件省份名称")
    @Size(max = 60, message = "收件省份名称不能超过60个字符")
    private String receiverProvinceName;

    private Integer receiverProvinceId;

    @ApiModelProperty(value = "收件城市名称")
    @Size(max = 60, message = "收件城市名称不能超过60个字符")
    @NotBlank(message = "收件城市名称不能为空", groups = {JmsSaveGroup.class, MiniAppSaveGroup.class, JmsUpdateGroup.class,
            OWSaveGroup.class})
    private String receiverCityName;

    @ApiModelProperty(value = "收件城市id")
    @NotNull(message = "收件城市id不能为空", groups = {MiniAppSaveGroup.class, OWSaveGroup.class})
    private Integer receiverCityId;

    @ApiModelProperty(value = "收件区域名称")
    @Size(max = 60, message = "收件区域名称不能超过60个字符")
    @NotBlank(message = "收件区域名称不能为空",groups = {MiniAppSaveGroup.class,JmsUpdateGroup.class,OWSaveGroup.class})
    private String receiverAreaName;

    @ApiModelProperty(value = "收件区域id")
    @NotNull(message = "收件区域id不能为空",groups = {MiniAppSaveGroup.class,OWSaveGroup.class})
    private Integer receiverAreaId;

    @ApiModelProperty(value = "收件乡镇")
    @Size(max = 200, message = "收件乡镇不能超过200个字符")
    private String receiverTownship;

    @ApiModelProperty(value = "收件街道")
    @Size(max = 200, message = "收件街道不能超过200个字符")
    private String receiverStreet;

    @ApiModelProperty(value = "收件详细地址")
    @Size(max = 255, message = "收件详细地址不能超过255个字符")
    private String receiverDetailedAddress;

    @ApiModelProperty(value = "收件邮编")
    @Size(max = 60, message = "收件邮编不能超过60个字符")
    private String receiverPostalCode;

    @ApiModelProperty(value = "收件人邮箱")
    private String receiverEmail;

    @ApiModelProperty(value = "派件服务方式名称")
    @NotBlank(message = "派件服务方式名称不能为空", groups = {JmsSaveGroup.class, JmsUpdateGroup.class})
    @Size(max = 60, message = "派件服务方式名称不能超过60个字符")
    private String dispatchName;

    @ApiModelProperty(value = "派件服务方式code")
    @NotBlank(message = "派件服务方式code名称不能为空", groups = {JmsSaveGroup.class, JmsUpdateGroup.class})
    @Size(max = 30, message = "派件服务方式code不能超过30个字符")
    private String dispatchCode;

    @ApiModelProperty(value = "收件分拣码")
    @Size(max = 30, message = "收件分拣码不能超过30个字符")
    private String receiverSortingCode;

    @ApiModelProperty(value = "备注")
    @Size(max = 200, message = "备注不能超过200个字符")
    private String remarks;

    @ApiModelProperty(value = "快件类型code")
    @Size(max = 30, message = "快件类型编码不能超过30个字符")
    @NotBlank(message = "快件类型编码不能为空", groups = {JmsSaveGroup.class, JmsUpdateGroup.class})
    private String expressTypeCode;

    @ApiModelProperty(value = "快件类型名称")
    @Size(max = 30, message = "快件类型名称不能超过30个字符")
    @NotBlank(message = "快件类型名称不能为空", groups = {JmsSaveGroup.class, JmsUpdateGroup.class})
    private String expressTypeName;

    @ApiModelProperty(value = "需要保价1是，0否")
    @Min(value = 0, message = "需要保价只能为0或者1")
    @Max(value = 1, message = "需要保价只能为0或者1")
    @NotNull(groups = {JmsSaveGroup.class, JmsUpdateGroup.class})
    private Integer insured;

    @ApiModelProperty(value = "保价金额")
    @Digits(integer = 10, fraction = 2, message = "保价金额超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal declaredValue;

    @ApiModelProperty(value = "保价费")
    @Digits(integer = 10, fraction = 2, message = "保价费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal insuredValue;

    @ApiModelProperty(value = "标准运费")
    @Digits(integer = 10, fraction = 2, message = "标准运费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal standardValue;

    @ApiModelProperty(value = "总费用")
    @Digits(integer = 10, fraction = 2, message = "总费用超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal totalFreight;

    @ApiModelProperty(value = "需要代收货款,1是，0否")
    @Min(value = 0, message = "是否需要代收货款只能为0或者1")
    @Max(value = 1, message = "是否需要代收货款只能为0或者1")
    private Integer codNeed;

    @ApiModelProperty(value = "代收货款金额")
    @Digits(integer = 10, fraction = 2, message = "代收货款金额超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal codMoney;

    @ApiModelProperty(value = "代收货款手续费")
    @Digits(integer = 10, fraction = 2, message = "代收货款手续费超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal codFee;

    @ApiModelProperty(value = "包材规格名称")
    @Size(max = 60, message = "包材规格名称不能超过60个字符")
    private String boxStandardName;

    @ApiModelProperty(value = "包材数量")
    private Integer boxNumber;

    @ApiModelProperty(value = "包材规格code")
    private String boxStandardCode;

    @ApiModelProperty(value = "包材单价")
    @Digits(integer = 10, fraction = 2, message = "包材单价超出了允许范围(只允许在10位整数和2位小数范围内)")
    private BigDecimal boxPrice;

    @ApiModelProperty(value = "代收货款币别名称")
    @Size(max = 32, message = "代收货款币别名称不能超过32个字符")
    private String codCurrencyTypeName;

    @ApiModelProperty(value = "代收货款币别code")
    @Size(max = 32, message = "代收货款币别编码不能超过32个字符")
    private String codCurrencyTypeCode;

    @ApiModelProperty(value = "物品类型名称")
    @Size(max = 60, message = "物品类型名称不能超过60个字符")
    @NotBlank(message = "物品类型名称不能为空", groups = {JmsSaveGroup.class, JmsUpdateGroup.class})
    private String goodsTypeName;

    @ApiModelProperty(value = "物品类型id")
    private Integer goodsTypeId;

    @ApiModelProperty(value = "物品类型code")
    @Size(max = 30, message = "物品类型编码不能超过30个字符")
    @NotBlank(message = "物品类型编码不能为空", groups = {JmsSaveGroup.class, MiniAppSaveGroup.class,
            JmsUpdateGroup.class, OWSaveGroup.class, CsSaveGroup.class})
    private String goodsTypeCode;

    @ApiModelProperty(value = "物品名称")
    @Size(max = 200, message = "物品名称不能超过200个字符")
    private String goodsName;

    @ApiModelProperty(value = "应收运费")
    private BigDecimal receivableFreight;

    @ApiModelProperty(value = "件数,＞1表示子母件，如果业务上不支持子母件，则前端限制不显示，并默认为1")
    @Min(value = 1, message = "包裹数量不能小于1")
    @Max(value = 99, message = "包裹数量不能超过99")
    @NotNull(message = "件数不能为空", groups = {EdiSaveGroup.class, MiniAppSaveGroup.class, OWSaveGroup.class})
    private Integer packageNumber;

    @ApiModelProperty(value = "包裹总长,单位厘米")
    @Digits(integer = 5, fraction = 2, message = "包裹总长超出了允许范围(只允许在5位整数和2位小数范围内)")
    @NotNull(message = "包裹总长不能为空", groups = {JmsUpdateGroup.class})
    private BigDecimal packageLength;

    @ApiModelProperty(value = "包裹总宽,单位厘米")
    @Digits(integer = 5, fraction = 2, message = "包裹总宽超出了允许范围(只允许在5位整数和2位小数范围内)")
    @NotNull(message = "包裹总宽不能为空", groups = {JmsUpdateGroup.class})
    private BigDecimal packageWide;

    @ApiModelProperty(value = "包裹总高,单位厘米")
    @Digits(integer = 5, fraction = 2, message = "包裹总高超出了允许范围(只允许在5位整数和2位小数范围内)")
    @NotNull(message = "包裹总高不能为空", groups = {JmsUpdateGroup.class})
    private BigDecimal packageHigh;

    @ApiModelProperty(value = "包裹体积重,单位立方厘米")
    @Digits(integer = 5, fraction = 2, message = "包裹体积重超出了允许范围(只允许在5位整数和2位小数范围内)")
    @NotNull(message = "包裹体积重不能为空", groups = {JmsUpdateGroup.class})
    private BigDecimal packateVolume;

    @ApiModelProperty(value = "包裹计费重量,单位千克")
    @Digits(integer = 5, fraction = 2, message = "包裹计费重量,超出了允许范围(只允许在5位整数和2位小数范围内)")
    private BigDecimal packageChargeWeight;

    @ApiModelProperty(value = "包裹总重量,单位千克")
    @Digits(integer = 5, fraction = 2, message = "包裹总重量超出了允许范围(只允许在5位整数和2位小数范围内)")
    @DecimalMin(value = "0.01", message = "包裹总重量必须大于或等于0.01")
    private BigDecimal packageTotalWeight;

    @ApiModelProperty(value = "结算方式名称")
    @Size(max = 60, message = "结算方式名称不能超过60个字符")
    private String paymentModeName;

    @ApiModelProperty(value = "结算方式code")
    @Size(max = 30, message = "结算方式编码不能超过30个字符")
    private String paymentModeCode;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "客户名称")
    @Size(max = 60, message = "客户名称不能超过60个字符")
    private String customerName;

    @ApiModelProperty(value = "商品信息")
    private List<OmsOrderMemberDTO> items;

    @ApiModelProperty(value = "订单录入时间，日期格式 yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inputTime;

    @ApiModelProperty(value = "取件网点code")
    @Null(message = "取件网点code不能有值", groups = {EdiSaveGroup.class})
    private String pickNetworkCode;

    @ApiModelProperty(value = "取件网点id")
    @Null(message = "取件网点id不能有值", groups = {EdiSaveGroup.class, JmsSaveGroup.class})
    private Long pickNetworkId;

    /**
     * 是否有取件员信息
     */
    @ApiModelProperty(value = "是否有取件员信息")
    private boolean hasPickStaffInfo;

    @ApiModelProperty(value = "调度业务员时间")
    private LocalDateTime dispatchStaffTime;

    /**
     * 取件业务员名称
     */

    private String pickStaffName;

    /**
     * 取件业务员code
     */

    private String pickStaffCode;

    /**
     * 实际取件网点名称
     */

    private String realPickNetworkName;

    /**
     * 实际取件网点code
     */

    private String realPickNetworkCode;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private Integer orderStatusCode;

    @ApiModelProperty(value = "调度网点原因")
    private String dispatchNetworkReason;

    @ApiModelProperty(value = "取件网点名称")
    private String pickNetworkName;

    @ApiModelProperty(value = "调度网点时间")
    private LocalDateTime dispatchNetworkTime;

    @ApiModelProperty(value = "录入人ID")
    private Integer createBy;

    @ApiModelProperty(value = "录入人编码")
    private String createByCode;

    @ApiModelProperty(value = "录入人名称")
    private String createByName;

    @ApiModelProperty(value = "更新人id")
    private Integer updateBy;

    @ApiModelProperty(value = "更新人编码")
    private String updateByCode;

    @ApiModelProperty(value = "更新人名称")
    private String updateByName;

    @ApiModelProperty(value = "是否有网点信息")
    private boolean hasNetWorkInfo;

    /**
     * 始发地id
     */
    @ApiModelProperty(value = "始发地id")
    private Integer originId;

    /**
     * 始发地code
     */
    @ApiModelProperty(value = "始发地code")
    private String originCode;

    /**
     * 始发地名字
     */
    @ApiModelProperty(value = "始发地名字")
    private String originName;

    /**
     * 目的地id
     */
    @ApiModelProperty(value = "目的地id")
    private Integer destinationId;

    /**
     * 目的地code
     */
    @ApiModelProperty(value = "目的地code")
    private String destinationCode;

    /**
     * 目的地名字
     */
    @ApiModelProperty(value = "目的地名字")
    private String destinationName;

    /**
     * 客户下单时间
     */
    @ApiModelProperty(value = "客户下单时间")
    //@NotNull(message = "客户下单时间不能为空",groups = {DdwSaveGroup.class})
    private LocalDateTime customerOrderTime;

    /**
     * 菜鸟参数
     */
    @ApiModelProperty(value = "菜鸟参数")
    private String params;

    @ApiModelProperty(value = "VIP客户自定义订单id")
    private Long formIndex;

    /**
     * 取消原因code
     */
    @ApiModelProperty(value = "原因code 见OrderCancelEnum")
    private Integer cancelReasonCode;

    /**
     * 取消订单原因
     */
    private String cancelReason;

    /**
     * 取消订单说明
     */
    private String cancelExplain;

    private String operatorNumber;

    private String operatorName;

    /**
     * 三段码
     **/

    private String terminalDispatchCode;

    private BigDecimal settlementWeight;

    private Integer memberId;

    private Integer memberLevel;

    /**
     * 是否需要调度 1需要 2不需要
     */
    private Integer needDispatch;

    /**
     * 代理区code
     */
    private String proxyAreaCode;

    /**
     * 代理区name
     */
    private String proxyAreaName;

    /**
     * 调度代理区时间
     */
    private LocalDateTime dispatchProxyAreaTime;

    /**
     * 是否是代理区
     */
    private Boolean isProxyArea;

    /**
     * 商家id
     */
    private String mallId;

    /**
     * 实名姓名
     */
    private String realName;

    /**
     * 身份证号码
     */
    private String idNo;

    /**
     * 证件类型 DocumentTypeEnum
     */
    private Integer idNoType;

    /**
     * 是否实名
     */
    private Integer isRealName;

    private Integer sex;

    private boolean isRepeat;

    /**
     * 大头笔
     */
    private String datoubi;

    /**
     * 客户运单号
     */
    private String customerWaybillNo;


    /**
     * 包裹体积 单位立方厘米
     */

    private BigDecimal packageVolume;

    /**
     * 订单异常信息
     */
    private String exceptionMsg;


    /**
     * 物品价值
     */

    private BigDecimal goodsValue;

    /**
     * 第三方配送方式
     */
    private String thirdShipmentTypeCode;

    /**
     * 打印次数
     */

    private Integer printsNumber;


    /**
     * 外部商家标识
     */
    private String outerMallId;


    /**
     * 包裹提⽰码
     */
    private String packageHintCode;


    private String operationType;

    private String requestId;

    /**
     * 是否是商务件 1是 2不是
     */
    private Integer isBusiness;

    /**
     * 二段码命中原因
     */
    private String secondCodeHitDesc;

    /**
     * 派件网点id
     */
    private Integer dispatchNetworkId;
    
    /**
     * 派件网点编码
     */
    private String  dispatchNetworkCode;

    /**
     * 派件网点名称
     */
    private String  dispatchNetworkName;

    /** 是否自动调度 1自动调度  0不调度 */
    private String autoScheduling;
    /** 是否允许极兔取消订单 1不允许 0允许 */
    private String isCancelOrder;

    /**
     * 集包地编码
     */
    private String lastCenterCode;

    /**
     * 集包地名称
     */
    private String lastCenterName;

//    /** 签回单 0否   1是  2回单标记*/
//    private Integer signReceipt;
//
//    /** 回单金额 */
//    private BigDecimal receiptFreight;
    /** 回单运单号 */
    private String receiptWaybillNo;

    /**
     * 是否隐私面单 0:否 1:是
     */
    private Integer isPrivacy;

    @ApiModelProperty(value = "请求来源 1.jms 2.edi 3.巴枪app 4.PCCS")
    @OperationParamLog(message = "请求来源 1.jms 2.edi 3.巴枪app 4.PCCS", type = OperationParamTypeEnum.ID)
    protected Integer from;

    /**
     * 收货偏好
     */
    private OrderRecvPrefDTO orderRecvPrefDTO;


    /**
     * 订单标签列表
     */
    private List<OrderMarkExpandVO> markFields;


    /**
     * 月结账号 当支付类型为月结时需要填充
     *
     */
    private String account;

    /**
     * 小程序下单重算运费
     */
    private Boolean appletsReCalculate;


    /**
     * 客户标签（多个逗号组合）（1-务必电联，2-送货上门，3-放入驿站，4-工作日派送，5-休息日派送，6-共享客户无需收费，7-终端寄件无需收费，8-按需配送）
     */
    private String customerLabel;


    /**
     * 折扣运费
     */
    private BigDecimal discountFreight;
}
