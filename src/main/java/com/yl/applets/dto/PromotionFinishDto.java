package com.yl.applets.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-11-19 18:02
 */
@Data
public class PromotionFinishDto {
    @ApiModelProperty(value = "优惠劵id")
    private Long id;;

    @ApiModelProperty(value = "优惠劵配置id")
    private Long proId;

    @ApiModelProperty(value = "优惠劵分发id")
    private Long disId;

    @ApiModelProperty(value = "核销时间")
    private LocalDateTime verificationTime;

    @ApiModelProperty(value = "核销网点id")
    private Long verificationNetworkId;

    @ApiModelProperty(value = "核销网点code")
    private String verificationNetworkCode;

    @ApiModelProperty(value = "核销网点名称")
    private String verificationNetworkName;

    @ApiModelProperty(value = "订单优惠前运费")
    private BigDecimal orderPriTraAmount;

    @ApiModelProperty(value = "运单号")
    private String waybillNo;

    @ApiModelProperty(value = "订单优惠劵分摊运费  存在1个订单使用多个优惠劵时使用/取平均，最后的用减法")
    private Long orderPraTraAmount;

    @ApiModelProperty(value = "订单优惠劵分摊优惠  存在1个订单使用多个优惠劵时使用")
    private BigDecimal orderPraDisAmount;

    @ApiModelProperty(value = "订单实付运费")
    private BigDecimal orderTraAmount;

    @ApiModelProperty(value = "订单优惠")
    private BigDecimal orderDisAmount;

    @ApiModelProperty(value = "员工编号")
    private String staffCode;

    @ApiModelProperty(value = "员工姓名")
    private String staffName;

    @ApiModelProperty(value = "订单号")
    private Long orderId;
}
