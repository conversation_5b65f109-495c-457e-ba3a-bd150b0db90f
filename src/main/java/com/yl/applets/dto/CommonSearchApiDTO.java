package com.yl.applets.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 *  运单号
 * <AUTHOR>
 * @since Created in 2020/04/09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "CommonSearchApiDTO", description = "CommonSearchApiDTO")
public class CommonSearchApiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 运单号按需查询
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value = "运单号按需查询", description = "运单号按需查询")
    public static class GetWaybillDTO implements Serializable {

        @ApiModelProperty(value = "运单号")
        @NotNull
        private String waybillNo;

        @ApiModelProperty(value = "查询字段，示例 ：[\"waybillNo\", \"waybillStatusCode\",\"inputTime\"]   参考wiki:http://*************:8090/pages/viewpage.action?pageId=21725294")
        @NotNull
        @Size(min = 1,max = 500)
        private List<String> columns;
    }

    /**
     * 运单号按需批量查询
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value = "运单号按需批量查询", description = "运单号按需批量查询")
    public static class WaybillNosDTO implements Serializable {

        @ApiModelProperty(value = "运单号")
        @NotNull
        @Size(min = 1,max = 200)
        private List<String> waybillNos;

        @ApiModelProperty(value = "查询字段，示例 ：[\"waybillNo\", \"waybillStatusCode\",\"inputTime\"]   参考wiki:http://*************:8090/pages/viewpage.action?pageId=21725294")
        @NotNull
        @Size(min = 1,max = 500)
        private List<String> columns;
    }

}

