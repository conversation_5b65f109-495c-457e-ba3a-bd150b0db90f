package com.yl.applets.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yl.common.base.constant.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 异常信息监控配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MemberImageTextQueryDto", description = "MemberImageTextQueryDto")
public class MemberImageTextQueryDto implements Serializable {

   private Long current;

   private Long size;

   @ApiModelProperty(value = "ID")
   @JsonSerialize(using = ToStringSerializer.class)
   @NotNull(message = "id不能为空",groups = UpdateGroup.class)
   private Long id;

   @ApiModelProperty(value = "标题")
   @NotBlank(message = "标题不能为空")
   @Size(min = 1, max = 30, message = "标题长度只能在1-30之间")
   private String title;

   @ApiModelProperty(value = "类型 1.签到弹窗2.分享笔记")
   @NotNull(message = "类别不能为空")
   private Integer type;

   @ApiModelProperty(value = "编号")
   private String code;

   @ApiModelProperty(value = "1.上线2.下线")
   private Integer isEnable;

   @Size(max = 30, message = "文案长度只能在1-30之间")
   @ApiModelProperty(value = "文案")
   private String copyWriting;

   @ApiModelProperty(value = "图片path")
   @NotBlank(message = "图片不能为空")
   private String imagePath;

   @ApiModelProperty(value = "图片url")
   private String ImageUrl;

   @ApiModelProperty(value = "是否删除")
   private Integer isDelete;

   @ApiModelProperty(value = "创建人id")
   private Long createBy;

   @ApiModelProperty(value = "创建人name")
   private String createByName;

   @ApiModelProperty(value = "更新人id")
   private Long updateBy;

   @ApiModelProperty(value = "更新人name")
   private String updateByName;

   @ApiModelProperty(value = "创建时间")
   private LocalDateTime createTime;

   @ApiModelProperty(value = "更新时间")
   private LocalDateTime updateTime;

   @ApiModelProperty(value = "发布人id")
   private Long publishBy;

   @ApiModelProperty(value = "更新人name")
   private String publishByName;

   @ApiModelProperty(value = "更新时间")
   private LocalDateTime publishTime;

}

