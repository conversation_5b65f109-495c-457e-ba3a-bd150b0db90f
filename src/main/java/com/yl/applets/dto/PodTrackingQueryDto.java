/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: PodTrackingQueryDto
 * Author:   luhong
 * Date:     2020-10-14 16:47
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.dto;

import com.yl.applets.enums.TrackingTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-14
 * @since 1.0.0
 */
@Data
public class PodTrackingQueryDto {
    private List<String> keywordList;
    private TrackingTypeEnum typeEnum;
}