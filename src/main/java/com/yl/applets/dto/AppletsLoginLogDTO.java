package com.yl.applets.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2022/10/24 15:19
 * @Version 1.0
 */

@Data
@ApiModel(value = "微信小程序登录日志DTO", description = "微信小程序登录日志DTO")
public class AppletsLoginLogDTO {


    @ApiModelProperty(value = "省", notes = "省")
    private String province;

    @ApiModelProperty(value = "市", notes = "市")
    private String city;

    @ApiModelProperty(value = "区", notes = "区")
    private String zone;

    @ApiModelProperty(value = "签到时的设备品牌", notes = "签到时的设备品牌")
    private String equipmentBrand;

    @ApiModelProperty(value = "android/ios", notes = "android/ios")
    private String equipmentType;

    @ApiModelProperty(value = "型号", notes = "型号")
    private String equipmentModel;

}
