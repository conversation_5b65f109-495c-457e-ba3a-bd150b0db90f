package com.yl.applets.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 优惠劵领取使用信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PromotionUserGetNetworkDto", description = "优惠劵领取使用信息表")
public class PromotionUserGetNetworkDto implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "优惠劵id")
    private Long proId;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "分发id")
    private Long disId;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户id")
    private Long numberId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "用户手机号")
    private String userPhone;

    @ApiModelProperty(value = "用户昵称")
    private String userAlias;

    @ApiModelProperty(value = "用户uuid")
    private String userUuid;

    @ApiModelProperty(value = "网点id")
    private Long networkId;

    @ApiModelProperty(value = "网点名称")
    private String networkName;

    @ApiModelProperty(value = "分发员工code")
    private String grantStaffCode;

    @ApiModelProperty(value = "分发员工姓名")
    private String grantStaffName;
}
