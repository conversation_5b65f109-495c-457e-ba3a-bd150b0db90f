package com.yl.applets.feign.dto.citicmonthlysettlement;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/12/9 16:08
 */
@Data
public class CiticMonthlySettlementDTO {


    private String id;


    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户账号code
     */
    private String customerCode;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;



    /**
     * 是否默认账号 1 默认 2其他
     */
    @NotNull(message = "默认账号不能为空")
    private Integer defaultNum;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 用户id
     */
    private Integer memberId;


    /**
     * 1 绑定 2解绑
     */
    @NotNull(message = "绑定类型不能为空")
    private Integer type;


    /**
     * 账号
     */
    @NotBlank(message = "账号不能为空")
    private String account;
}
