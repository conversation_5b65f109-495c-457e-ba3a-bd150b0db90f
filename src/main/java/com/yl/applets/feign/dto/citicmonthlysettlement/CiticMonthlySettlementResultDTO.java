package com.yl.applets.feign.dto.citicmonthlysettlement;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/12/9 16:08
 */
@Data
public class CiticMonthlySettlementResultDTO {


    private String id;


    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户账号code
     */
    private String customerCode;

    private String customerName;

    /**
     * 是否默认账号 1 默认 2其他
     */
    private Integer defaultNum;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 1 绑定 2解绑
     */
    private Integer type;

    /**
     * 1可用 2不可用
     */
    private Integer enable;

    /**
     * 解绑时间
     */
    private LocalDateTime unbindTime ;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 账号
     */
    private String account;


}
