package com.yl.applets.feign.dto.citicmonthlysettlement;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/12/9 16:08
 */
@Data
public class CiticMonthlySettlementUpdateDTO {

    @NotBlank(message = "ID不能为空")
    private String id;

    /**
     * 是否默认账号 1 默认 2其他
     */
    @NotNull(message = "默认账号不能为空")
    private Integer defaultNum;

    /**
     * 用户id
     */
    private Integer memberId;


    /**
     * 1 绑定 2解绑
     */
    private Integer type;
}
