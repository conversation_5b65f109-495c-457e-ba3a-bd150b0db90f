package com.yl.applets.feign.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class OrderSensitiveQueryDTO {

    @NotEmpty(message = "运单号/订单号集合不能为空")
    @ApiModelProperty("运单号订单号集合")
    private List<String> noList;

    /**
     * 单号类型
     */
    @ApiModelProperty("1. 运单号 2. 订单号 不传则默认运单号，非枚举则报错")
    @Range(min = 1, max = 2)
    private Integer orderNumberType;

    @NotBlank
    @ApiModelProperty("查询事由编码")
    private String sensitiveCode;
    @NotBlank
    @ApiModelProperty("调用反查接口的应用名称")
    private String appName = "yl-jms-channel-api";

    @NotBlank(message = "业务员编码不能为空")
    @ApiModelProperty("业务员编码")
    private String staffNo = "postalCode";

    @NotBlank(message = "业务员名称不能为空")
    @ApiModelProperty("业务员名称")
    private String staffName = "端公共中台";

    @NotBlank
    @ApiModelProperty("网点code")
    private String siteCode = "8888888";

    @NotBlank
    @ApiModelProperty("网点name")
    private String siteName = "总部";
}