package com.yl.applets.feign.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br> 算费
 * @Project: <br> 结算管理平台
 * @CreateDate: Created in 2019/7/31 14:08 <br>
 * @Author: <a href="<EMAIL>">huangliang</a>
 */
@Data
@ApiModel(description = "算费")
public class SpmApiCustomerShippingQuoteTryCalcDTO implements Serializable {

    @ApiModelProperty(value = "运单号ID")
    private Long waybillId;

    @NotNull(message = "始发地不能为空")
    @ApiModelProperty(value = "始发地")
    private Integer startAddressId;

    @NotNull(message = "目的地不能为空")
    @ApiModelProperty(value = "目的地")
    private Integer endAddressId;

    @ApiModelProperty(value = "产品类型id")
    private Integer productTypeId;

    @ApiModelProperty(value = "产品类型CODE")
    private String productTypeCode = "EZ";

    @ApiModelProperty(value = "所属网点")
    private Integer networkId;

    @ApiModelProperty(value = "服务方式id")
    private Integer serviceMethodId;

    @ApiModelProperty(name = "serviceMethodId", value = "服务方式CODE")
    private String serviceMethodCode;

    @NotNull(message = "数量/件数不能为空")
    @ApiModelProperty(value = "数量/件数")
    private BigDecimal number;

    @NotNull(message = "下单时间不能为空")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "下单时间")
    private LocalDateTime currentTime;

    @NotNull(message = "客户id不能为空")
    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(name = "quoteCode", value = "客户报价方式01:标准报价,02:客户报价")
    private String quoteCode;

    @ApiModelProperty(value = "结算方式(1:寄件现结,2寄付月结,3:到付现结)")
    private Integer smMode;

    @ApiModelProperty(value = "唯一标识符UUID")
    private String uuid;

    @ApiModelProperty(value = "产品类型", required = true)
    @NotBlank(message = "产品类型不能为空")
    private String productType = "EZ";

    @ApiModelProperty(name = "senderProvinceName", value = "寄件省份名称")
    private String senderProvinceName;


    @ApiModelProperty(name = "senderCityName", value = "寄件城市名称")
    private String senderCityName;


    @ApiModelProperty(name = "receiverProvinceName", value = "收件省份名称")
    private String receiverProvinceName;

    @ApiModelProperty(name = "receiverCityName", value = "收件城市名称")
    private String receiverCityName;

    @NotBlank(message = "客户编码不能为空")
    private String customerCode;

    @NotNull(message = "数值不能为空")
    @ApiModelProperty(name = "value", value = "数值")
    private BigDecimal value;


}
