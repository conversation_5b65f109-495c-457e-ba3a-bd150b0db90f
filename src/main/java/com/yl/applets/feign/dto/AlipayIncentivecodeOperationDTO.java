package com.yl.applets.feign.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024-10-14
 **/
@Data
public class AlipayIncentivecodeOperationDTO {
    /**
     * 激励码值
     */
    @NotBlank(message = "激励码值不能为空")
    private String incentiveCode;
    /**
     * 商户标识
     */
    @NotBlank(message = "商户标识不能为空")
    private String logisticsCode;
    /**
     * 码值动销类型
     * 【枚举值】
     * 用户扫码: SCAN
     * 用户寄件下单: SEND_ORDER
     * 完成支付: PAY_SUCCESS
     * 揽收成功: RECEIVE_SUCCESS
     */
    @NotBlank(message = "码值动销类型不能为空")
    private String operationDynamicSalesType;

    /**
     * 渠道类别
     * 【枚举值】
     * 支付宝操作渠道: ALIPAY
     * 其他渠道: OTHER
     */
    @NotBlank(message = "渠道类别不能为空")
    private String operationSource;

    /**
     * 寄件支付宝用户标识
     */
    private String operationOpenId;

    /**
     * 订单号
     * 【必选条件】当传入operation_dynamic_sales_type,且operation_dynamic_sales_type =SEND_ORDER(用户寄件下单)
     * 或者 PAY_SUCCESS(完成支付)
     * 或者 RECEIVE_SUCCESS（揽收成功）此三种类型时，必选
     */
    private String orderNo;

    /**
     * 寄件用户操作时间
     */
    private Date operationTime;

    /**
     * 运单号
     * 【必选条件】当传入operation_dynamic_sales_type,且operation_dynamic_sales_type =PAY_SUCCESS(完成支付)时，必选
     */
    private String waybillNo;

    /**
     * 支付完成页面链接（支付宝小程序链接）
     * 【必选条件】当传入operation_dynamic_sales_type,且operation_dynamic_sales_type =PAY_SUCCESS(完成支付)时，必选
     */
    private String payFinishUrl;

    /**
     * 支付宝交易号
     * 【必选条件】当传入operation_dynamic_sales_type,且operation_dynamic_sales_type =PAY_SUCCESS(完成支付)时，必选
     */
    private String alipayTradeNo;
    /**
     * 支付宝交易成功状态
     * 【必选条件】当传入operation_dynamic_sales_type,且operation_dynamic_sales_type =PAY_SUCCESS(完成支付)时，必选
     */
    private String alipayTradeStatus;

}
