package com.yl.applets.feign;


import com.yl.applets.dto.SysAreaOnlineConfigQueryDTO;
import com.yl.applets.dto.lmdm.*;
import com.yl.applets.entity.lmdm.SysArea;
import com.yl.applets.entity.lmdm.SysNetwork;
import com.yl.applets.entity.lmdm.SysSettlementDestination;
import com.yl.applets.feign.fallback.OldLmdmFeignClientFallback;
import com.yl.applets.vo.SysCustomeAppletVO;
import com.yl.applets.vo.lmdm.*;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(
        name = "yllmdmapi",
        path = "/lmdmapi" ,
        fallback = OldLmdmFeignClientFallback.class
)
public interface OldLmdmFeignClient {


    /**
     * 通过打印设备项的id获取客户和打印设备信息
     *
     * @param id 主键
     * @return
     */
    @GetMapping(value = "/sysCustomerPrinterEquipment/getSysCustomerAppletInfo")
    Result<SysCustomeAppletVO> getSysCustomerAppletInfo(@RequestParam("id") Integer id);

    @ApiOperation(value = "通过省市区获取结算目的地-特殊需求", notes = "通过省市区获取结算目的地-特殊需求")
    @PostMapping({"/settlementDestination/getSettlementDestinationByProvinceCityAreaId"})
    Result<SysSettlementDestinationVO> getSettlementDestinationByProvinceCityAreaId(@RequestBody SysSettlementDestinationBaseQueryDTO var1);

    @ApiOperation(value = "获取redis缓存", notes = "获取redis缓存")
    @GetMapping({"/settlementDestination/redisAll"})
    Result<List<SysSettlementDestination>> redisAll();

    @PostMapping(value = "/sysArea/online/config/findOnLineAreas")
    @ApiOperation(value = "查询开通上线的区域详情", notes = "查询开通上线的区域详情")
    Result<List<SysArea>> findOnLineAreas(@RequestBody SysAreaOnlineConfigQueryDTO dto);

    @PostMapping({"/sysVersion/list"})
    Result<List<SysVersionVO>> list(@RequestBody SysVersionQueryDTO var1);


    @GetMapping({"/network/redisAll"})
    @ApiOperation(value = "获取所有的网点redis缓存", notes = "获取所有的网点redis缓存")
    Result<List<SysNetwork>> networkRedisAll();

    @GetMapping({"/network/detail"})
    @ApiOperation(value = "查询详细信息", notes = "查询详细信息")
    Result<SysNetworkVO> getDetailById(@ApiParam(name = "id",value = "网点id") @RequestParam(value = "id",required = false) Integer var1);

    @PostMapping({"/sysStaff/detail"})
    @ApiOperation(value = "查询员工详情", notes = "查询员工详情--xq")
    @ApiImplicitParam(name = "id", value = "员工id", dataType = "Integer")
    Result<SysStaffVO> getStaffDetail(@RequestBody SysStaffDTO var1);


    @PostMapping({"/sysArea/getByNames"})
    @ApiOperation(value = "根据名称查询省市区", notes = "根据名称查询省市区")
    Result<List<SysAreaNormalDTO>> getByNames(@RequestBody List<SysAreaPcaNamesDTO> var1);

    @GetMapping({"/app/baseData/list"})
    Result<AppBaseDataVO> getAppList(@RequestParam("type") Integer var1);

}
