package com.yl.applets.feign;

import com.alibaba.fastjson.JSONObject;
import com.yl.applets.dto.indemnity.RequestDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：大数据接口
 * @Author： <PERSON><PERSON><PERSON><PERSON>
 * @Date： 2023/01/10
 */
@FeignClient(value = "yl-bgapi-gateway-api")
public interface BgApiFeignClient {

    @RequestMapping(value = "/dataApiDoris/queryBySql", method = RequestMethod.POST)
    JSONObject queryDataBySql(RequestDTO requestDTO);
}
