package com.yl.applets.feign;

import com.yl.applets.entity.lmdm.SysNetwork;
import com.yl.applets.feign.fallback.CustomerPlatformFallback;
import com.yl.applets.vo.BasicAreaVO;
import com.yl.applets.vo.CpAreaByNetworkCodeVO;
import com.yl.applets.vo.GetProxyAndfranchiseeVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/17 14:27
 * @description
 */
@FeignClient(
        name = "ylnetworkapi",
        path = "/networkapi/web/customerplatform",
        fallback = CustomerPlatformFallback.class

)
public interface CustomerPlatformFeignClient {


    @GetMapping("/staff/getStaffExistByMobile")
    Result<Boolean> getStaffExistByMobile(@RequestParam("mobile") String mobile);


}
