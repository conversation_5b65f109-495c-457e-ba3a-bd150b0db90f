package com.yl.applets.feign;

import com.yl.applets.dto.CommonSearchApiDTO;
import com.yl.applets.vo.OmsWaybillDetailVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 *  通用操作 feign
 * <AUTHOR>
 * @since Created in 2020/05/23
 */
@Component
@FeignClient(name = "ylwaybillapi", path = "/waybillapi")
public interface CommonApiClient {

    /***
     * 根据运单号查询运单信息
     * @param dto
     * @return
     */
    @PostMapping(value = "/common/getContainPrivacy")
    Result<OmsWaybillDetailVO> commonWaybillGet(@RequestBody CommonSearchApiDTO.GetWaybillDTO dto);

    /***
     * 根据运单号批量查询运单信息
     * @param dto
     * @return
     */
    @PostMapping(value = "/common/listByWaybillNos")
    Result<List<OmsWaybillDetailVO>> commonWaybillListByWaybillNos(@RequestBody CommonSearchApiDTO.WaybillNosDTO dto);

}