package com.yl.applets.feign;

import com.yl.applets.dto.SendMessageDTO;
import com.yl.applets.dto.SmsCodeDTO;
import com.yl.applets.dto.SmsSendDTO;
import com.yl.applets.dto.SmsTemplateDTO;
import com.yl.applets.vo.SmsSendVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(name = "ylsms",path = "/sms")
public interface SmsFeignClient {

    /**
     * 功能描述:
     * 发送短信验证码
     * @param smsCodeDTO
     * @return:com.yl.common.base.model.vo.Result
     * @since: 1.0.0
     * @Author:luhuang
     * @Date: 2020-08-31 17:09
     */
    @PostMapping("/uni/sendMsg")
    Result sendCodeMsg(@Validated @RequestBody SmsCodeDTO smsCodeDTO);

    /**
     * 功能描述:
     * 基于模板发送短信
     * @param smsTemplateDTO
     * @return:com.yl.common.base.model.vo.Result
     * @since: 1.0.0
     * @Author:luhuang
     * @Date: 2020-08-31 17:09
     */
    @PostMapping("/uni/sendMsg")
    Result sendTemplateMsg(@Validated @RequestBody SmsTemplateDTO smsTemplateDTO);

    @PostMapping({"/v2/sendBatch"})
    public Result<SmsSendVO> sendBatch(@RequestBody @Valid SmsSendDTO smsSendDTO);
}