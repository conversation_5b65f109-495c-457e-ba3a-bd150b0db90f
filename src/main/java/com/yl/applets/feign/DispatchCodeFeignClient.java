package com.yl.applets.feign;


import com.yl.applets.dto.DispatchCodeFetchNetworkIdQuery;
import com.yl.applets.dto.DispatchCodeRequestDTO;
import com.yl.applets.vo.DispatchCodeResponseVO;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2020
 * <p>
 * 新三段码Feign
 *
 * <AUTHOR>
 * @since Created in 2020-10-15
 */
@Component
@FeignClient(name = "ylassdispatchcodeapi",
        //测试环境的url，本地测试的时候可以打开。
        //url = "http://192.168.1.221:30695",
        path = "/dispatchcodeapi/dispatchcode")
public interface DispatchCodeFeignClient {

    @PostMapping("/fetchCodesNew")
    @ApiOperation(value = "获取三段码接口", notes = "获取三段码接口")
    Result<DispatchCodeResponseVO> fetchCodesNew(@RequestBody DispatchCodeRequestDTO queryDTO);

    @ApiOperation(value = "批量获取三段码接口", notes = "批量获取三段码接口")
    @PostMapping("/batchFetchCodesNew")
    Result<Map<String, DispatchCodeResponseVO>> batchFetchCodesNew(@RequestBody List<DispatchCodeRequestDTO> queryList);


    //根据省市区详细地址获取网点信息，三段码信息
    @PostMapping("/fetchNetworkIds")
    @ApiOperation(value = "获取网点三段码接口", notes = "获取网点三段码接口")
    Result<Map<String, DispatchCodeResponseVO>> fetchNetworkIds(@RequestBody List<DispatchCodeFetchNetworkIdQuery> queryDTO);
}
