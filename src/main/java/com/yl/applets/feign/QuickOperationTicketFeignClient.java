package com.yl.applets.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sun.org.apache.xpath.internal.operations.Bool;
import com.yl.applets.dto.quickoperationticke.CcmWorkDetailQueryDTO;
import com.yl.applets.dto.quickoperationticke.CcmWorkOrderCreateDTO;
import com.yl.applets.dto.quickoperationticke.CcmWorkOrderQueryDTO;
import com.yl.applets.dto.quickoperationticke.WorkOrderjudgmentDTO;
import com.yl.applets.feign.fallback.ChannelApiFeignClientFallBack;
import com.yl.applets.vo.quickoperationticke.CcmWorkOrderVO;
import com.yl.applets.vo.quickoperationticke.WorkOrderCustomerOptionsSelectorVO;
import com.yl.common.base.model.vo.Result;
import org.apache.ibatis.annotations.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/6/14 11:52
 */
@FeignClient(
        name = "yljmschannelapi",
        path = "/channelapi",
        fallback = ChannelApiFeignClientFallBack.class
)
public interface QuickOperationTicketFeignClient {


    /**
     *  普通工单创建接口
     * @param dto
     * @return
     */
    @PostMapping("/quick/operation/ticket/workOrder/ccm/create")
    Result<Boolean> create(@RequestBody CcmWorkOrderCreateDTO dto);




    /**
     *  普通工单列表
     * @param dto
     * @return
     */
    @PostMapping("/quick/operation/ticket/workOrder/ccm/page")
    Result<Page<CcmWorkOrderVO>> page(@RequestBody CcmWorkOrderQueryDTO dto);





    /**
     * 普通工单详情查询接口
     */
    @PostMapping("/quick/operation/ticket/workOrder/ccm/detail")
    Result<CcmWorkOrderVO> detail(@RequestBody CcmWorkDetailQueryDTO dto);



    /**
     * 普通工单催单接口
     * @return
     */
    @PostMapping("/quick/operation/ticket/workOrder/ccm/reminder")
    Result<Boolean> reminder(@RequestBody CcmWorkDetailQueryDTO dto);



    /**
     * 普通工单是否超时效
     * @param waybillNo
     * @return
     * 1: 在时效内，2: 不在时效内
     */
    @PostMapping("/quick/operation/ticket/workOrder/ccm/isOverdue")
    Result<Integer> isOverdue(@RequestParam("waybillNo") String waybillNo);


    /**
     * 选项列表
     */
    @GetMapping("/quick/operation/ticket/workOrder/ccm/optionsSelector")
    Result<List<WorkOrderCustomerOptionsSelectorVO>> optionsSelector();


    /**
     * 判断是否满足创建的工单
     * @param dto
     * @return
     */
    @PostMapping("/quick/operation/ticket/workOrder/ccm/judgmentSelfWork")
    Result<Boolean> judgmentSelfWork(@RequestBody WorkOrderjudgmentDTO dto);
}
