package com.yl.applets.feign;

import cn.hutool.db.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.consolidation.ConsolidationCancelDTO;
import com.yl.applets.dto.consolidation.ConsolidationDTO;
import com.yl.applets.dto.consolidation.ConsolidationPageDTO;
import com.yl.applets.dto.consolidation.ConsolidationUnPayDTO;
import com.yl.applets.feign.fallback.ConsolidationFeignClientFallback;
import com.yl.applets.vo.consolidation.ConsolidationInfoVO;
import com.yl.applets.vo.consolidation.ConsolidationPageVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description 新疆集运远程调用
 * @date 2024/11/21 11:02
 */
@Component
@FeignClient(
        name = "ylorderapi",
        path  = "/orderapi",
        fallback = ConsolidationFeignClientFallback.class
)
public interface ConsolidationFeignClient {

    /**
     * 新增新建集运信息
     * @param dto
     * @return
     */
    @RequestMapping(value = "/consolidation/proOrder/save",method = RequestMethod.POST)
    Result<String> consolidationProOrderSave(@RequestBody ConsolidationDTO dto);


    /**
     * 修改新疆集运信息
     * @param dto
     * @return
     */
    @RequestMapping(value = "/consolidation/proOrder/update",method = RequestMethod.POST)
    Result<Boolean> consolidationProOrderUpdate(@RequestBody ConsolidationDTO dto);


    /**
     * 取消新疆集运信息
     * @param dto
     * @return
     */
    @RequestMapping(value = "/consolidation/proOrder/cancel",method = RequestMethod.POST)
    Result<Boolean> consolidationProOrderCancel(@RequestBody ConsolidationCancelDTO dto);


    /**
     * 删除新疆集运信息
     * @param dto
     * @return
     */
    @RequestMapping(value = "/consolidation/proOrder/delete",method = RequestMethod.POST)
    Result<Boolean> consolidationProOrderDelete(@RequestBody ConsolidationCancelDTO dto);


    /**
     * 获取新疆集运列表分页
     * @param dto
     * @return
     */
    @RequestMapping(value = "/consolidation/proOrder/getPages",method = RequestMethod.POST)
    Result<Page<ConsolidationPageVO>> consolidationProOrderPages(@RequestBody ConsolidationPageDTO dto);


    /**
     * 未支付订单查询
     * @param dto
     * @return
     */
    @RequestMapping(value = "/consolidation/proOrder/getCountUnpaidOrders",method = RequestMethod.POST)
    Result<Integer> getCountUnpaidOrders(@RequestBody ConsolidationUnPayDTO dto);


    /**
     * 一段单是否已登记
     * @param preOrderNo
     */
    @RequestMapping(value = "/consolidation/proOrder/isExistPreOrderNo",method = RequestMethod.GET)
    Result<Boolean> isExistPreOrderNo(@RequestParam(value = "preOrderNo") String preOrderNo);



    /**
     * 通过主键id查询集运详情
     * @param id
     */
    @RequestMapping(value = "/consolidation/proOrder/getDetailById",method = RequestMethod.GET)
    Result<ConsolidationInfoVO> consolidationProOrderInfoById(@RequestParam(value = "id") Long id);


    /**
     * 通过运单号查询集运详情
     * @param waybillNo
     */
    @RequestMapping(value = "/consolidation/proOrder/getDetailByWaybillNo",method = RequestMethod.GET)
    Result<ConsolidationInfoVO> consolidationProOrderInfoByNo(@RequestParam(value = "waybillNo") String waybillNo);

}
