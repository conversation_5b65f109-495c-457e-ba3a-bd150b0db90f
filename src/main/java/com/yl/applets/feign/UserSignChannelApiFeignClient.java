package com.yl.applets.feign;


import com.yl.applets.dto.AppletsUserSignQueryDto;
import com.yl.applets.dto.AppletsUserSignUpdateDto;
import com.yl.applets.feign.fallback.UserSignChannelApiFeignClientFallBack;
import com.yl.applets.vo.AppletsUserSignVo;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
        name = "yljmschannelapi",
        path = "/channelapi",
//        url = "http://localhost:8080",
        fallback = UserSignChannelApiFeignClientFallBack.class
)
public interface UserSignChannelApiFeignClient {



    @GetMapping("/user/sign/detail")
    public Result<AppletsUserSignVo> detail(@RequestParam("id") Long id);



    @PostMapping("/user/sign/get")
    public Result<List<AppletsUserSignVo>> getList(@RequestBody @Validated AppletsUserSignQueryDto dto);



    @PostMapping("/user/sign/update")
    public Result<Boolean> update(@RequestBody @Validated AppletsUserSignUpdateDto dto);


    @GetMapping("/user/sign/order/remarks")
    public Result<List<String>> remarks();

}
