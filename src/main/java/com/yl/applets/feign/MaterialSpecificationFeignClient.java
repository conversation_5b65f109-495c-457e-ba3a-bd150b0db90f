/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: MaterialSpecificationFeignClient
 * Author:   luhong
 * Date:     2020-10-14 11:47
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.feign;

import com.yl.applets.dto.MaterialSpecificationQueryDTO;
import com.yl.applets.feign.fallback.MaterialSpecificationFallback;
import com.yl.applets.vo.MaterialSpecificationDetailVO;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-14
 * @since 1.0.0
 */
@Component
@FeignClient(
        name = "ylmsdmapi",
        path = "/msdmapi/materialSpecification",
        fallback = MaterialSpecificationFallback.class
)
public interface MaterialSpecificationFeignClient {

    @ApiOperation(
            value = "查询列表",
            notes = "查询列表"
    )
    @PostMapping({"/list"})
    Result<List<MaterialSpecificationDetailVO>> list(@RequestBody MaterialSpecificationQueryDTO dto);

}