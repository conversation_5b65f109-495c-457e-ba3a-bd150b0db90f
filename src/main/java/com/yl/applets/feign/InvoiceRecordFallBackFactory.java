package com.yl.applets.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.InvoiceApplyDTO;
import com.yl.applets.dto.InvoiceRecordHistoryDTO;
import com.yl.applets.vo.InvoiceRecordHistoryVO;
import com.yl.applets.vo.InvoiceUrlVO;
import com.yl.applets.vo.InvoicingApplyVO;
import com.yl.common.base.model.vo.Result;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2020-05-18 13:41
 */
@Slf4j
@Component
public class InvoiceRecordFallBackFactory implements FallbackFactory<InvoiceRecordFeignClient> {

    @Override
    public InvoiceRecordFeignClient create(Throwable throwable) {
        return new InvoiceRecordFeignClient() {
            @Override
            public Result<InvoiceRecordHistoryVO> detailForOut(String applyNo) {
                log.warn("InvoiceRecordFeignClient detailForOut execute error", throwable);
                return null;
            }

            @Override
            public Result<InvoiceUrlVO> findUrlById(String applyNo) {
                log.warn("InvoiceRecordFeignClient findUrlById execute error", throwable);
                return null;
            }

            @Override
            public Result<List<String>> getInvoiceAbleWaybillNos(List<String> waybillNos) {
                log.warn("InvoiceRecordFeignClient getInvoiceAbleWaybillNos execute error", throwable);
                return null;
            }

            @Override
            public Result<List<InvoicingApplyVO>> apply(InvoiceApplyDTO applyDTO) {
                log.warn("InvoiceRecordFeignClient apply execute error", throwable);
                return null;
            }

            @Override
            public Result<Page<InvoiceRecordHistoryVO>> invoiceRecordHistory(InvoiceRecordHistoryDTO dto) {
                log.warn("InvoiceRecordFeignClient invoiceRecordHistory execute error", throwable);
                return null;
            }

            @Override
            public Result<Boolean> failure(String applyNo) {
                log.warn("InvoiceRecordFeignClient failure execute error", throwable);
                return null;
            }

            @Override
            public Result<Boolean> failureBatch(List<String> applyNos) {
                log.warn("InvoiceRecordFeignClient failureBatch execute error", throwable);
                return null;
            }
        };
    }
}