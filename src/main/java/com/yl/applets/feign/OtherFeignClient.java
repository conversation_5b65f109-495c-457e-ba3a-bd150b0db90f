package com.yl.applets.feign;


import com.yl.applets.dto.CpAreaIdDTO;
import com.yl.applets.feign.fallback.OtherFeignClientFallback;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@Component
@FeignClient(
        name = "ylotherapi",
        path = "/otherapi",
//        url = "http://10.21.72.66:30943",
        fallbackFactory = OtherFeignClientFallback.class
)
public interface OtherFeignClient {


    /**
     * 判断省市区id是否存在关系
     *
     * @param dto
     * @return
     */
    @PostMapping("/web/customerplatform/sysArea/isRelation")
    Result<Boolean> isRelation(@RequestBody @Valid CpAreaIdDTO dto);


}
