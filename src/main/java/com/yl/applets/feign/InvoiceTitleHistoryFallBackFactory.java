package com.yl.applets.feign;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2020-05-18 10:47
 */
@Slf4j
@Component
public class InvoiceTitleHistoryFallBackFactory implements FallbackFactory<InvoiceTitleHistoryFeignClient> {

    @Override
    public InvoiceTitleHistoryFeignClient create(Throwable throwable) {
        return dto -> {
            log.warn("InvoiceTitleHistoryFeignClient findListByCustomer execute error", throwable);
            return null;
        };
    }
}