package com.yl.applets.feign;

import com.yl.applets.vo.UserPageVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(name = "yloauthopenapi", path = "/oauthopenapi/sysUser")
public interface SysUserNewFeginClient {

    /**
     * 根据ID获取用户的信息
     * @param id
     * @return
     */
    @GetMapping({"/order/detail"})
    Result<UserPageVO> detail(@RequestParam Integer id);

    /**
     * 根据编号获取用户的信息
     * @param staffNo
     * @return
     */
    @GetMapping({"/order/detailByStaffNo"})
    Result<UserPageVO> detailByStaffNo(@RequestParam String staffNo);
}
