package com.yl.applets.feign;

import com.yl.applets.dto.EsSearchDTO;
import com.yl.applets.dto.SysAreaBookingDto;
import com.yl.applets.dto.SysAreaOnlineConfigQueryDTO;
import com.yl.applets.dto.lmdm.CustomerCardCheckDTO;
import com.yl.applets.entity.lmdm.SysArea;
import com.yl.applets.feign.fallback.NetworkFeighClientFallBack;
import com.yl.applets.feign.fallback.OldNetworkFeighClientFallBack;
import com.yl.applets.vo.EsSearchResult;
import com.yl.applets.vo.OrderExceptionNetworkVO;
import com.yl.applets.vo.SpmiFranchiseeNetworkVO;
import com.yl.applets.vo.SysAreaBookingVo;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-08-09 15:52
 */
@Component
@FeignClient(
        name = "ylnetworkapi",
      //  url = "http://*************:30278",
        path = "/networkapi",
        fallback = OldNetworkFeighClientFallBack.class)
public interface OldNetworkFeighClient {

    /**
     * @param dto
     * @return
     * @dto.plateformType 平台类型, 根据约定, 调用者自行传入自己系统对应的标识  必填
     * @dto.type 区域类型 1.国家,2.省,3.市,4区  非必填 不传查平台所有开通的省市区,传2则查所有开通的省,传2则查所有开通的市
     * @dto.parentId 市和区上级id  非必填 如果要查某个省下面所有的市则type传3,parentId传该省id
     */
    @PostMapping(value = "/web/edi/area/online/config/dispatch/findOnLineAreas")
    @ApiOperation(value = "查询开通上线的区域详情", notes = "查询开通上线的区域详情")
    Result<List<SysArea>> findOnLineAreas(SysAreaOnlineConfigQueryDTO dto);


    /**
     * @param dto
     * @return
     * @dto.plateformType 平台类型, 根据约定, 调用者自行传入自己系统对应的标识  必填
     * @dto.type 区域类型 1.国家,2.省,3.市,4区  非必填 不传查平台所有开通的省市区,传2则查所有开通的省,传2则查所有开通的市
     * @dto.parentId 市和区上级id  非必填 如果要查某个省下面所有的市则type传3,parentId传该省id
     */
    @PostMapping(value = "/web/edi/area/online/config/send/findOnLineAreas")
    @ApiOperation(value = "查询开通上线的区域详情", notes = "查询开通上线的区域详情")
    Result<List<SysArea>> findSendOnLineAreas(SysAreaOnlineConfigQueryDTO dto);

    @GetMapping("/web/spmi/network/getFranchiseeByCode")
    @ApiOperation(value = "根据网点code查询加盟商")
    Result<SpmiFranchiseeNetworkVO> getFranchiseeByCode(@RequestParam("networkCode") String networkCode);

    /**
     * 检查证件号码
     * @param dto
     * @return
     *
     */
    @PostMapping("/app/applets/customer/checkCardNo")
    @ApiOperation(value = "检查证件号码")
    Result<Boolean> checkCardNo(@RequestBody CustomerCardCheckDTO dto);


    /**
     * 获取禁寄限类目是否符合要求
     */
    @GetMapping("/app/applets/sysForbidden/getSysForbiddenInfo")
    Result<Boolean> getSysForbiddenInfo(@RequestParam String contents);


    @PostMapping({"/app/applets/booking/address/pickTime"})
    @ApiOperation(value = "返回省市区可预约时间段", notes = "返回省市区可预约时间段")
    Result<SysAreaBookingVo> pickTime(@RequestBody SysAreaBookingDto sysAreaBookingDto );

    @PostMapping("/common/network/geo/distanceSortSearch")
    Result<EsSearchResult> distanceSort(@RequestBody EsSearchDTO dto);

}
