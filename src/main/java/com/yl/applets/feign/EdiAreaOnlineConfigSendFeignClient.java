package com.yl.applets.feign;

import com.yl.applets.dto.SysAreaOnlineConfigQueryDTO;
import com.yl.applets.entity.lmdm.SysArea;
import com.yl.applets.feign.fallback.SysAreaOnlineConfigSendFallBack;
import com.yl.applets.feign.lmdm.BaseConstant;
import com.yl.applets.vo.lmdm.SysAreaIdVO;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 开放平台接口
 *
 * <AUTHOR>
 */
@Component
@FeignClient(
        name = BaseConstant.FEIGN_CLIENT_PATH,
        //url = "192.168.1.227:30169",
        path = BaseConstant.PLATFORM_EDI + "/area/online/config/send",
        fallback = SysAreaOnlineConfigSendFallBack.class)
public interface EdiAreaOnlineConfigSendFeignClient {

    @GetMapping(value = "/getByPlatformType")
    @ApiOperation(value = "根据平台类型查询该平台开通的省市区id", notes = "根据平台类型查询该平台开通的省市区")
    Result<SysAreaIdVO> getByPlatformType(@RequestParam("plateformType") Integer plateformType);

    /**
     * @param dto
     * @return
     * @dto.plateformType 平台类型, 根据约定, 调用者自行传入自己系统对应的标识  必填
     * @dto.type 区域类型 1.国家,2.省,3.市,4区  非必填 不传查平台所有开通的省市区,传2则查所有开通的省,传2则查所有开通的市
     * @dto.parentId 市和区上级id  非必填 如果要查某个省下面所有的市则type传3,parentId传该省id
     */
    @PostMapping(value = "/findOnLineAreas")
    @ApiOperation(value = "查询开通上线的区域详情", notes = "查询开通上线的区域详情")
    Result<List<SysArea>> findOnLineAreas(SysAreaOnlineConfigQueryDTO dto);


    @PostMapping(value = "/findOnLineAreasAll")
    @ApiOperation(value = "查询省市区", notes = "查询省市区")
    Result<List<SysArea>> findDistrict(SysAreaOnlineConfigQueryDTO dto);
}
