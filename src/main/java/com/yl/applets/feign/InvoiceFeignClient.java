package com.yl.applets.feign;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.*;
import com.yl.applets.dto.invoice.InvoicingEmailSendDTO;
import com.yl.applets.dto.invoice.SmallProgramAddInvoiceRecordDTO;
import com.yl.applets.dto.invoice.SmallProgramDetailsQueryDTO;
import com.yl.applets.dto.invoice.SmallProgramQueryDTO;
import com.yl.applets.entity.WxUser;
import com.yl.applets.feign.fallback.ChannelApiFeignClientFallBack;
import com.yl.applets.feign.fallback.InvoiceFeignClientFallBack;
import com.yl.applets.vo.*;
import com.yl.applets.vo.invoice.AddInvoiceRecordResultVO;
import com.yl.applets.vo.invoice.SmallProgramInvoicingDetailsRecordVO;
import com.yl.applets.vo.invoice.SmallProgramInvoicingRecordVO;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@FeignClient(
        name = "yljmschannelapi",
        path = "/channelapi",
//        url = "http://localhost:8080",
        fallback = InvoiceFeignClientFallBack.class
)
public interface InvoiceFeignClient {

    @PostMapping(value = "/invoiceRecord/pageMyElectronicInvoice")
    @ApiOperation(value = "小程序开票查询开票", notes = "小程序开票查询开票")
    Result<Page<OmsElectronicInvoice>> pageMyElectronicInvoice(@RequestBody ElectronicInvoiceDTO electronicInvoiceDTO);

    /**
     * 查询开票记录
     *
     * @param queryDTO 查询DTO
     * @return 查询结果
     */
    @PostMapping("/invoiceRecord/getRecord")
    @ApiOperation(value = "外部查询查询开票记录", notes = "外部查询查询开票记录")
    Result<Page<SmallProgramInvoicingRecordVO>> getRecord(@RequestBody SmallProgramQueryDTO queryDTO);


    /**
     * 查询开票记录详情
     *
     * @param queryDTO 查询DTO
     * @return 查询结果
     */
    @PostMapping("/invoiceRecord/getDetailRecord")
    @ApiOperation(value = "外部查询查询开票记录", notes = "外部查询查询开票记录")
    Result<Page<SmallProgramInvoicingDetailsRecordVO>> getDetailRecord(@RequestBody SmallProgramDetailsQueryDTO queryDTO);


    /**
     * 添加开票记录
     *
     * @param addInvoiceRecordDTO 添加开票记录DTO
     * @return 开票结果信息
     */
    @PostMapping(value = "/invoiceRecord/addInvoiceRecord")
    @ApiOperation(value = "添加开票记录", notes = "添加开票记录")
    Result<AddInvoiceRecordResultVO> addInvoiceRecord(@RequestBody SmallProgramAddInvoiceRecordDTO addInvoiceRecordDTO);


    /**
     * 开票详情回调
     * @param callBackDTO 回调DTO对象
     * @return 结果
     */
//    @PostMapping(value = "/invoicingDetailCallBack")
//    @ApiOperation(value = "开票详情回调", notes = "开票详情回调")
//    Result<Boolean> invoicingDetailCallBack(@RequestBody @Valid InvoicingDetailCallBackDTO callBackDTO);


    /**
     * 开票邮箱发送
     *
     * @param sendDTO 发送DTO对象
     * @return 结果对象
     */
    @PostMapping(value = "/invoiceRecord/invoicingEmailSend")
    @ApiOperation(value = "开票邮箱发送", notes = "开票邮箱发送")
    Result<Boolean> invoicingEmailSend(@RequestBody InvoicingEmailSendDTO sendDTO);


    /**
     * 添加抬头
     * @param invoiceTitleDTO
     * @return
     */
    @PostMapping("/invoice-title/save")
    Result<Boolean> save(@RequestBody InvoiceTitleDTO invoiceTitleDTO);

    /**
     * 修改抬头
     * @return
     */
    @PostMapping("/invoice-title/update")
    Result<Boolean> update(@RequestBody InvoiceTitleDTO invoiceTitleDTO);

    /**
     * 删除抬头
     * @param id
     * @return
     */
    @GetMapping("/invoice-title/del")
    Result<Boolean> del(@RequestParam("id") Integer id,@RequestParam("userId") Integer userId);


    /**
     * 抬头详情
     * @param id
     * @return
     */
    @GetMapping("/invoice-title/get")
    Result<InvoiceTitleVO> get(@RequestParam("id") Integer id,@RequestParam("userId") Integer userId);

    /**
     * 抬头列表
     * @return
     */
    @GetMapping("/invoice-title/list")
    Result<List<InvoiceTitleVO>> list(@RequestParam("userId") Integer userId);


    /**
     * 抬头列表
     * @return
     */
    @GetMapping("/invoice-title/listName")
    Result<List<InvoiceTitleVO>> listName(@RequestParam("name") String name,@RequestParam("userId") Integer userId);
}
