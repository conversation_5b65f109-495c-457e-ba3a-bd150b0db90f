package com.yl.applets.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.*;
import com.yl.applets.feign.fallback.PromotionFeighClientFallBack;
import com.yl.applets.vo.*;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-08-09 15:52
 */
@Component
@FeignClient(
        name = "ylcustpromotionapi",
        path = "/custpromotionapi",
        fallback = PromotionFeighClientFallBack.class)
public interface PromotionFeighClient {

    @ApiOperation(value = "小程序领取优惠劵", notes = "小程序领取优惠劵")
    @PostMapping("/coupon/applets/getApplets")
    Result<Integer> getApplets(@RequestBody PromotionUserGetAppletsDto appletsDto);

    @PostMapping("/coupon/applets/getActivityCoupon")
    @ApiOperation(value = "活动领取优惠劵", notes = "活动领取优惠劵")
    Result<Long> getActivityCoupon(@RequestBody PromotionUserGetAppletsDto appletsDto);


    @ApiOperation(value = "网点领取优惠劵", notes = "网点领取优惠劵")
    @PostMapping("/coupon/applets/getNetwork")
    Result<Integer> getNetwork(@RequestBody PromotionUserGetNetworkDto networkDto);

    @ApiOperation(value = "扫码领取优惠券列表", notes = "扫码领取优惠券列表")
    @RequestMapping(value = "/coupon/applets/userGetCouponList",method = RequestMethod.POST)
    Result<Page<CustUserPromotionVo>> userGetCouponList(@RequestBody CustUserPromotionVo vo);

    @ApiOperation(value = "河南移动用户领取优惠劵", notes = "河南移动用户领取优惠劵")
    @RequestMapping(value = "/coupon/applets/getCouponByCMCC",method = RequestMethod.POST)
    Result<List<Long>> getCouponByCMCC(@RequestBody List<PromotionUserGetAppletsDto> list);

    @ApiOperation(value = "根据后端配置领取优惠券列表", notes = "根据后端配置领取优惠券列表")
    @RequestMapping(value = "/coupon/applets/getCouponListByConfig",method = RequestMethod.POST)
    Result<CouponConfigVo> getCouponListByConfig(@RequestBody CustUserPromotionVo vo);


    @ApiOperation(value = "下单优惠券列表",notes = "下单优惠券列表")
    @PostMapping("/coupon/applets/getUserCouponToOrder")
    Result<Page<CustPromotionUseVo>> getCouponToOrder(@RequestBody OrderPromotionDto dto);


    @ApiOperation(value = "查询当前用户优惠券列表", notes = "查询当前用户优惠券列表")
    @PostMapping("/coupon/applets/getUserCouponByType")
    Result<Page<CustPromotionUseVo>> getCouponPageByType(@RequestBody CustPromotionUseDto dto);


    @ApiOperation(value = "根据优惠券查询可使用范围",notes = "根据优惠券查询可使用范围")
    @GetMapping("/coupon/applets/getRangeByCode")
    Result<List<String>> getRangeByCode(@RequestParam("id") String id, @RequestParam("type") String type);

    @ApiOperation(value = "根据优惠券查询优惠金额",notes = "根据优惠券查询优惠金额")
    @GetMapping("/coupon/ops/getCouponValueAndRule")
    Result<CustPromotionInfoVo> getCouponValueAndRule(@RequestParam("id") String id,@RequestParam("proCode") String proCode,@RequestParam("freight") String freight);


    /**
     *  锁定优惠劵
     */
    @PostMapping("/custPromotionUse/lockUserPromotion")
    @ApiOperation(value = "lockUserPromotion", notes = "锁定优惠劵")
    Result<Integer> useLockPromotion(@RequestBody PromotionUserUseDto userUseDto);


    /**
     *  使用优惠劵
     */
    @GetMapping("/custPromotionUse/usePromotion")
    @ApiOperation(value = "usePromotion", notes = "使用优惠劵")
    Result<Integer> usePromotion(@RequestParam("id") Long id,@RequestParam("orderId")Long orderId,@RequestParam("proId")Long proId,@RequestParam("disId")Long disId
        ,@RequestParam(value = "networkId",required = false) Long networkId,@RequestParam(value = "staffCode",required = false)String staffCode);


    /**
     * 解锁优惠券
     * @param id
     * @return
     */
    @GetMapping("/custPromotionUse/unlockUserPromotion")
    @ApiOperation(value = "unlockUserPromotion", notes = "解锁优惠劵")
    Result<Boolean> unlockUserPromotion(@RequestParam("id")Long id);

    /**
     *  核销
     */
    @PostMapping("/custPromotionUse/finishPromotion")
    @ApiOperation(value = "finishPromotion", notes = "核销")
    Result<Integer> finishPromotion(@RequestBody PromotionFinishDto finishDto );

    /**
     *  回退优惠劵
     */
    @GetMapping("/custPromotionUse/rollbackPromotion")
    @ApiOperation(value = "rollbackPromotion", notes = "回退优惠劵")
    Result<Integer> rollbackPromotion(@RequestParam("id")Long id,@RequestParam("proId")Long proId,@RequestParam("disId")Long disId);

    @PostMapping("/custPromotionUse/rollbackPromotionByOrderNo")
    @ApiOperation(value = "rollbackPromotionByOrderNo", notes = "根据订单号回退优惠劵")
    Result<Integer> rollbackPromotionByOrderNo(@RequestBody PromotionUserUseDto dto);

    @PostMapping("/coupon/applets/getCouponOnekey")
    @ApiOperation(value = "getCouponOnekey", notes = "优惠券一键领取")
    Result<Boolean> getCouponOnekey(@RequestBody CouponOneKeyDto dto);

    @PostMapping("/coupon/applets/getCouponByOutActive")
    @ApiOperation(value = "getCouponByOutActive", notes = "优惠券一键领取")
    Result<Boolean> getCouponByOutActive(@RequestBody CouponOneKeyDto dto);

    @PostMapping("/coupon/applets/getCouponByOutActiveValue")
    @ApiOperation(value = "getCouponByOutActiveValue", notes = "优惠券一键领取")
    Result<List<Long>> getCouponByOutActiveValue(@RequestBody List<PromotionUserGetAppletsDto> list);

    @ApiOperation(value = "queryCouponIdByTradeNo", notes = "根据三方订单查询优惠券使用明细")
    @PostMapping("/coupon/applets/queryCouponIdByTradeNo")
    Result<Long> bindUserAndTradeQuery(@RequestParam("tradeNo") String tradeNo);


    /**
     *  新增数据
     */
    @PostMapping("/custSystemOperation/add")
    @ApiOperation(value = "新增系统操作记录表", notes = "新增系统操作记录表")
    Result<Integer> addSystemOperation(@RequestBody CustSystemOperationDto dto);

    @GetMapping("/wxmember/activity/list")
    @ApiOperation(value = "查询活动配置", notes = "查询活动配置")
    Result<List<MemberActivityQueryDto>> activityList();


    //提供给小程序,签到/分享随机图文信息
    @GetMapping("/memberItc/applets/itInfoByType")
    @ApiOperation(value = "itInfo", notes = "随机图文信息")
    Result<MemberImageTextQueryDto> itInfoByType(@RequestParam("type") String type);

}
