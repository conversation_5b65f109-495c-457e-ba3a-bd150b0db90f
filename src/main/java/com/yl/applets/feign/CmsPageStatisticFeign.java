package com.yl.applets.feign;

import com.yl.applets.dto.CmsPageStatisticsDto;
import com.yl.applets.feign.fallback.CmsPageStatisticFallBack;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2024-03-04
 */
@FeignClient(
        name = "yl-jms-ccm-cms-api",
        path = "/cms",
//        url = "http://127.0.0.1:8080",
        fallbackFactory = CmsPageStatisticFallBack.class
)
public interface CmsPageStatisticFeign {


    @PostMapping(value = "/page/statistics")
    @ApiOperation(value = "CMS页面配置埋点", notes = "CMS页面配置埋点")
    public Result<Boolean> statistics(@RequestBody CmsPageStatisticsDto cmsPageStatisticsDto);


    /**
     * 配置页预览
     * @return
     */
    @GetMapping(value = "/page/detail")
    @ApiOperation(value = "配置页预览", notes = "配置页预览")
    public Result detail(@RequestParam("pageId") String pageId, @RequestParam("previewType") String previewType);
}
