/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: OmsPrintClient
 * Author:   luhong
 * Date:     2020-10-15 14:34
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.feign;

import com.yl.applets.feign.fallback.OmsPrintClientFallback;
import com.yl.applets.vo.CloudPrintStatusVo;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-15
 * @since 1.0.0
 */
@Component
@FeignClient(
        name = "ylord<PERSON><PERSON>",
        path = "/orderapi/print",
        fallback = OmsPrintClientFallback.class
)
public interface OmsPrintClient {

    @PostMapping({"queryCloudPrintStatus"})
    Result<List<CloudPrintStatusVo>> queryCloudPrintStatus(@RequestParam List<Long> orderIds);
}