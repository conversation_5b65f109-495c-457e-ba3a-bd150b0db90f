///**
// * Copyright (C), 2015-2021, 云路供应链科技有限公司
// * FileName: OssFallback
// * Author:   luhong
// * Date:     2021-01-08 9:16
// * Description:
// * History:
// * <author>          <time>          <version>          <desc>
// * 作者姓名           修改时间           版本号              描述
// */
//package com.yl.applets.feign.fallback;
//
//import com.yl.applets.dto.OssUrlSignDTO;
//import com.yl.applets.feign.OssApi;
//import com.yl.applets.utils.JsonUtils;
//import com.yl.applets.vo.OssUrlSignVO;
//import com.yl.common.base.enums.ResultCodeEnum;
//import com.yl.common.base.model.vo.Result;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.validation.Valid;
//import javax.validation.constraints.NotBlank;
//import javax.validation.constraints.NotNull;
//import java.util.List;
//import java.util.Map;
//
///**
// * 〈一句话功能简述〉<br>
// * 〈〉
// *
// * <AUTHOR>
// * @create 2021-01-08
// * @since 1.0.0
// */
//@Slf4j
//@Component
//public class OssFallback implements OssApi {
//
//    @Override
//    public Result<List<String>> getDownloadSignedUrl(List<String> paths) {
//        log.error("feign调用文件服务中台查询下载路径失败,传参：{}", JsonUtils.toJson(paths));
//        return Result.error(ResultCodeEnum.FAIL);
//    }
//
//    @Override
//    public Result<Map<String, String>> getDownloadSignedUrlDetail(List<String> paths) {
//        log.error("feign调用文件服务中台查询下载路径详情失败,传参：{}", JsonUtils.toJson(paths));
//        return Result.error(ResultCodeEnum.FAIL);
//    }
//
//    @Override
//    public Result<List<OssUrlSignVO>> getUploadSignedUrl(@Valid List<OssUrlSignDTO> list) {
//        log.error("feign调用文件服务中台查询上传路径失败,传参：{}", JsonUtils.toJson(list));
//        return Result.error(ResultCodeEnum.FAIL);
//    }
//
//    @Override
//    public Result<String> upload(@NotBlank(message = "项目名不能为空") String projectName, @NotBlank(message = "模块名不能为空") String moduleName, @NotNull(message = "文件不能为空") MultipartFile file) {
//        log.error("feign调用文件服务中台上传失败,传参：{}，{}", projectName,moduleName);
//        return Result.error(ResultCodeEnum.FAIL);
//    }
//}
