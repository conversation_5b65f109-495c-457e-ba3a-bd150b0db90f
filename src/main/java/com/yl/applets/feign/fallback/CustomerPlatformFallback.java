package com.yl.applets.feign.fallback;

import com.yl.applets.feign.CustomerPlatformFeignClient;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/6/12 10:49
 */
@Slf4j
@Component
public class CustomerPlatformFallback implements FallbackFactory<CustomerPlatformFeignClient> {
    @Override
    public CustomerPlatformFeignClient create(Throwable throwable) {
        return new CustomerPlatformFeignClient() {
            @Override
            public Result<Boolean> getStaffExistByMobile(String mobile) {
                log.info("feign调用基础资料服务，判断是否内部员工，参数：{}", mobile);
                return Result.error(ResultCodeEnum.FAIL);
            }
        };
    }
}
