package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.*;
import com.yl.applets.dto.invoice.InvoicingEmailSendDTO;
import com.yl.applets.dto.invoice.SmallProgramAddInvoiceRecordDTO;
import com.yl.applets.dto.invoice.SmallProgramDetailsQueryDTO;
import com.yl.applets.dto.invoice.SmallProgramQueryDTO;
import com.yl.applets.entity.WxUser;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.InvoiceFeignClient;
import com.yl.applets.vo.*;
import com.yl.applets.vo.invoice.AddInvoiceRecordResultVO;
import com.yl.applets.vo.invoice.SmallProgramInvoicingDetailsRecordVO;
import com.yl.applets.vo.invoice.SmallProgramInvoicingRecordVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-10-09
 */

@Slf4j
@Component
public class InvoiceFeignClientFallBack implements InvoiceFeignClient {
    @Override
    public Result<Page<OmsElectronicInvoice>> pageMyElectronicInvoice(ElectronicInvoiceDTO electronicInvoiceDTO) {
        log.warn("channel,pageMyElectronicInvoice失败.入参：{}", JSON.toJSONString(electronicInvoiceDTO));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Page<SmallProgramInvoicingRecordVO>> getRecord(SmallProgramQueryDTO queryDTO) {
        log.warn("channel,getRecord失败.入参：{}", JSON.toJSONString(queryDTO));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Page<SmallProgramInvoicingDetailsRecordVO>> getDetailRecord(SmallProgramDetailsQueryDTO queryDTO) {
        log.warn("channel,getDetailRecord失败.入参：{}", JSON.toJSONString(queryDTO));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<AddInvoiceRecordResultVO> addInvoiceRecord(SmallProgramAddInvoiceRecordDTO addInvoiceRecordDTO) {
        log.warn("channel,addInvoiceRecord失败.入参：{}", JSON.toJSONString(addInvoiceRecordDTO));
        return null;
    }

    @Override
    public Result<Boolean> invoicingEmailSend(InvoicingEmailSendDTO sendDTO) {
        log.warn("channel,invoicingEmailSend失败.入参：{}", JSON.toJSONString(sendDTO));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> save(InvoiceTitleDTO invoiceTitleDTO) {
        log.warn("channel,invoice-title,save失败.入参：{}", JSON.toJSONString(invoiceTitleDTO));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> update(InvoiceTitleDTO invoiceTitleDTO) {
        log.warn("channel,invoice-title,update失败.入参：{}", JSON.toJSONString(invoiceTitleDTO));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> del(Integer id, Integer userId) {
        log.warn("channel,invoice-title,del失败.入参id：{},userId：{}", id,userId);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<InvoiceTitleVO> get(Integer id, Integer userId) {
        log.warn("channel,invoice-title,get失败.入参id：{},userId：{}", id,userId);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<InvoiceTitleVO>> list(Integer userId) {
        log.warn("channel,invoice-title,list失败.入参：{}", userId);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<InvoiceTitleVO>> listName(String name, Integer userId) {
        log.warn("channel,invoice-title,listName失败.入参name：{},userId：{}", name,userId);
        return Result.error(ResultCodeEnum.FAIL);
    }
}
