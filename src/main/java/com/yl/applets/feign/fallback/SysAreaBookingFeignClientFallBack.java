//package com.yl.applets.feign.fallback;
//
//import com.yl.applets.dto.SysAreaBookingDto;
//import com.yl.applets.feign.SysAreaBookingFeignClient;
//import com.yl.applets.vo.SysAreaBookingVo;
//import com.yl.common.base.enums.ResultCodeEnum;
//import com.yl.common.base.model.vo.Result;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
///**
// * 云路供应链科技有限公司 版权所有 © Copyright 2020
// *
// * <AUTHOR>
// * @version 1.0
// * @Description: 异常处理
// * @date 2021-07-15 10:45
// */
//@Slf4j
//@Component
//public class SysAreaBookingFeignClientFallBack implements SysAreaBookingFeignClient {
//    @Override
//    public Result<SysAreaBookingVo> pickTime(SysAreaBookingDto sysAreaBookingDto) {
//        log.error("SysAreaBookingFeignClient 调用 [ pickTime ]失败");
//        return Result.error(ResultCodeEnum.FAIL);
//    }
//}
