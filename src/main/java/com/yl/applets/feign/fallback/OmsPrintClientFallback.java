/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: OmsPrintClientFallback
 * Author:   luhong
 * Date:     2020-10-15 14:35
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.feign.fallback;

import com.yl.applets.feign.OmsPrintClient;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.CloudPrintStatusVo;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-15
 * @since 1.0.0
 */
@Slf4j
@Component
public class OmsPrintClientFallback implements OmsPrintClient {

    @Override
    public Result<List<CloudPrintStatusVo>> queryCloudPrintStatus(List<Long> orderIds) {
        log.error("feign调用订单中台查询云打印状态失败,传参：{}", JsonUtils.toJson(orderIds));
        return Result.error(ResultCodeEnum.FAIL);
    }
}