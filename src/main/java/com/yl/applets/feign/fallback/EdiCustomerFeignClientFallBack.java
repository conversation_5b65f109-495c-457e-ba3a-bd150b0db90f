///**
// * Copyright (C), 2015-2021, 云路供应链科技有限公司
// * FileName: EdiCustomerFeignClientFallBack
// * Author:   luhong
// * Date:     2021-03-03 10:38
// * Description:
// * History:
// * <author>          <time>          <version>          <desc>
// * 作者姓名           修改时间           版本号              描述
// */
//package com.yl.applets.feign.fallback;
//
//import com.yl.applets.dto.SysAreaOnlineConfigQueryDTO;
//import com.yl.applets.entity.lmdm.SysArea;
//import com.yl.applets.feign.EdiAreaOnlineConfigDispatchFeignClient;
//import com.yl.applets.vo.lmdm.SysAreaIdVO;
//import com.yl.common.base.enums.ResultCodeEnum;
//import com.yl.common.base.model.vo.Result;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * 〈一句话功能简述〉<br>
// * 〈〉
// *
// * <AUTHOR>
// * @create 2021-03-03
// * @since 1.0.0
// */
//@Slf4j
//@Component
//public class EdiCustomerFeignClientFallBack implements EdiAreaOnlineConfigDispatchFeignClient {
//
//    @Override
//    public Result<SysAreaIdVO> getByPlatformType(Integer plateformType) {
//        log.warn("feign调用基础数据根据平台类型查询该平台开通的省市区id失败");
//        return Result.error(ResultCodeEnum.FAIL);
//    }
//
//    @Override
//    public Result<List<SysArea>> findOnLineAreas(SysAreaOnlineConfigQueryDTO dto) {
//        log.warn("feign调用基础数据查询开通上线的区域详情失败");
//        return Result.error(ResultCodeEnum.FAIL);
//    }
//}