/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: OmsOrderClientFallback
 * Author:   luhong
 * Date:     2020-10-15 14:40
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.feign.fallback;

import com.yl.applets.dto.OmsOrderCancelApiDTO;
import com.yl.applets.dto.OmsOrderUpdateApiDTO;
import com.yl.applets.feign.OmsOrderClient;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.OmsOrderCancelApiVO;
import com.yl.applets.vo.OmsOrderUpdateApiVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-15
 * @since 1.0.0
 */
@Slf4j
@Component
public class OmsOrderClientFallback implements OmsOrderClient {

    @Override
    public Result<OmsOrderUpdateApiVO> updateOrder(OmsOrderUpdateApiDTO omsOrderUpdateApiDTO) {
        log.error("feign调用订单中台修改订单失败,传参：{}", JsonUtils.toJson(omsOrderUpdateApiDTO));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<OmsOrderCancelApiVO> cancelOrder(OmsOrderCancelApiDTO omsOrderCancelApiDTO) {
        log.error("feign调用订单中台取消订单失败,传参：{}", JsonUtils.toJson(omsOrderCancelApiDTO));
        return Result.error(ResultCodeEnum.FAIL);
    }
}