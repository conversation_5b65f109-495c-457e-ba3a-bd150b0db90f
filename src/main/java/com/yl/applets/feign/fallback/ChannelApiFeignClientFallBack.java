package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.*;
import com.yl.applets.dto.appletsgeneralbusiness.AppletsGeneralBusinessDTO;
import com.yl.applets.entity.OmsWaybill;
import com.yl.applets.entity.WxUser;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.vo.*;
import com.yl.applets.vo.baidu.IdCardVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-10-09
 */

@Slf4j
@Component
public class ChannelApiFeignClientFallBack implements ChannelApiFeignClient {
    @Override
    public Result<Boolean> saveRecord(RecordPrivacyDto dto) {
        log.error("隐私政策记录接口，调用失败.入参：{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<AppOmsWaybillDetailVO> getDetailByNo(String waybillNo) {
        log.error("查询运单详情调用失败.入参：{}", JSON.toJSONString(waybillNo));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<PodTrackingListVO> getDetailByWaybillNoV2(String waybillNo) {
        log.error("查询轨迹接口调用失败.入参：{}", JSON.toJSONString(waybillNo));
        return Result.error(ResultCodeEnum.FAIL);
    }


    @Override
    public Result<InterceptOrderVo> orderIntercept(InterceptOrderDto dto) {
        log.error("channel筛单功能调用失败.入参：{}", JSON.toJSONString(dto));
        return  Result.error(ResultCodeEnum.FAIL);
    }


    @Override
    public Result<UserCardVo> userCardSave(UserCardDTO dto) {
        log.error("channel实名制接口调用失败.入参：{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<UserCardVo> saveAppletsUserCard(SaveAppletsUserCardDTO dto) {
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result infoBySecret(String numberId) {
        log.error("channel实名制信息查询调用失败.入参：{}", numberId);
        return  Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> logoff(WxUserVo vo) {
        log.warn("channel用户注销调用失败.入参：{}", JSON.toJSONString(vo));
        return  Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<MemberUserOffDto> isOff(String mobile) {
        log.error("channel查询用户是否注销调用失败.入参：{}", mobile);
        return  Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> cannelOff(WxUser wxUser) {
        log.error("channel用户取消注销调用失败.入参：{}", JSON.toJSONString(wxUser));
        return  Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<?> forward(ForwardRequest request) {
        log.error("channel,forward调用失败.入参：{}", JSON.toJSONString(request));
        return  Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<OmsCancelReasonVO>> qryCancelReasonVO(CancelReasonDTO dto) {
        log.error("channel,qryCancelReasonVO调用失败.入参：{}", JSON.toJSONString(dto));
        return  Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> checkWayBillNoExist(String waybillNo) {
        log.error("channel,checkWayBillNoExist失败.入参：{}", waybillNo);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<String> getToken(String code) {
        log.error("channel,getToken.入参：{}", code);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<IdCardVO> getIdCardInfo(MultipartFile file) {
        log.error("中台OCR解析身份证失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<AddressPCDVO> intelligentAddressRecognitionV2(String address) {
        log.error("中台百度地图解析失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result foreignIdCard(MultipartFile file) {
        log.error("中台OCR解析身份证失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> orderUserQuery(OrderUserQueryDto dto) {
        log.error("是否自己单子");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<OrderMarkingVo>> getResource() {
        log.error("订单标识");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> updateMarking(OrderMarkingSignDto dto) {
        log.error("订单打标[运单号]");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> updateMarkingByOrderId(OrderMarkingSignDto dto) {
        log.error("订单打标[订单号]");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<OrderMarkingVo>> queryMarking(OrderMarkingQueryDto queryDto) {
        log.error("查询订单标识[运单号]");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<OrderMarkingVo>> queryMarkingByOrderId(String orderId) {
        log.error("查询订单标识[订单号]");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> signRecord(List<OrderMarkingSignDto> dtos) {
        log.error("下单,记录打标");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<WaybillDetailVo> getWaybillDetail(String waybillNo) {
        log.error("中台运单轨迹详情失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<OmsWaybill>> batchDetail(BatchWaybillsDTO dto) {
        log.error("中台查询网点运单详情失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<String> intelligentAddressRecognitionFile(MultipartFile file) {
        log.error("中台解析文件失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<CustomerQueryByPhoneVO> getSingleOrderWarnCustomer(String loginPhone, String sendPhone, String receivePhone) {
        log.error("客户预警拦截失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<CustomerQueryByPhoneVO>> getOrderWarnCustomers(Set<String> phones) {
        log.error("客户预警拦截失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public  Result<Page<MyStaffVO>> list(MyStaffPageDTO dto) {
        log.error("我的专属小哥列表失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<String> updateDefaultStaff(MyStaffUpdateDefaultDTO dto) {
        log.error("更新默认专属失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> checkSendAddress(MyStaffCheckAddressDTO dto) {
        log.error("校验寄送信息失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<FrequentAddressVO>> getFrequentAddressByUserId(Integer userId) {
        log.error("获取客户常用地址信息失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Void> saveFrequentAddressInfo(FrequentAddressDTO dto) {
        log.error("保存常用地址信息失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<RandomStaffVo> getInfoByRandomCode(String randomCode) {
        log.error("根据随机码查业务员信息");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<MobilePhoneVO> getMobilePhoneByWaybillNo(String waybillNo) {
        log.error("查询订单详情失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<String> getChannelByCode(String code) {
        log.error("根据市场活动编码获取渠道错误");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<String> applyMember(AppletsMemberDTO dto) {
        log.error("申请登录绑定vip错误");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<String> appletsGeneralBusinessSave(AppletsGeneralBusinessDTO dto) {
        log.error("小程序通用业务信息异步处理错误");
        return Result.error(ResultCodeEnum.FAIL);
    }
}
