///**
// * Copyright (C), 2015-2020, 云路供应链科技有限公司
// * FileName: AppletsNetworkFeignClientFallBack
// * Author:   luhong
// * Date:     2020-11-30 14:38
// * Description:
// * History:
// * <author>          <time>          <version>          <desc>
// * 作者姓名           修改时间           版本号              描述
// */
//package com.yl.applets.feign.fallback;
//
//import com.yl.applets.dto.lmdm.CustomerCardCheckDTO;
//import com.yl.applets.feign.AppletsCustomerFeignClient;
//import com.yl.applets.vo.SpmiFranchiseeNetworkVO;
//import com.yl.common.base.enums.ResultCodeEnum;
//import com.yl.common.base.exception.BusinessException;
//import com.yl.common.base.model.vo.Result;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
///**
// * 〈一句话功能简述〉<br>
// * 〈〉
// *
// * <AUTHOR>
// * @create 2020-11-30
// * @since 1.0.0
// */
//@Slf4j
//@Component
//public class AppletsNetworkFeignClientFallBack implements AppletsCustomerFeignClient {
//
//    @Override
//    public  Result<SpmiFranchiseeNetworkVO>  getFranchiseeByCode(String networkCode) {
//        log.error("调用基础数据查询加盟商接口失败,传参：{}",networkCode);
//        throw new BusinessException(ResultCodeEnum.INTERFACE_INNER_INVOKE_ERROR);
//    }
//
//    @Override
//    public Result<Boolean> checkCardNo(CustomerCardCheckDTO dto) {
//        log.error("调用基础数据实名认证接口失败,传参：{}",dto);
//        throw new BusinessException(ResultCodeEnum.INTERFACE_INNER_INVOKE_ERROR);
//    }
//
//    @Override
//    public Result<Boolean> getSysForbiddenInfo(String contents) {
//        log.error("调用基础数据获取禁寄限类目是否符合要求接口失败,传参：{}",contents);
//        throw new BusinessException(ResultCodeEnum.INTERFACE_INNER_INVOKE_ERROR);
//    }
//}