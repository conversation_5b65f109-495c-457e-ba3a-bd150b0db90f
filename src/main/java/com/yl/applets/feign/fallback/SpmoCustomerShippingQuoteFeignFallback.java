package com.yl.applets.feign.fallback;

import com.yl.applets.feign.SpmoCustomerShippingQuoteFeignClient;
import com.yl.applets.feign.dto.SpmApiCustomerShippingQuoteTryCalcDTO;
import com.yl.applets.vo.CiticSpmCommonCostVO;
import com.yl.applets.vo.SpmCommonCostVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: xiongweibin
 * @create: 2020-10-14 15:52
 */
@Component
@Slf4j
public class SpmoCustomerShippingQuoteFeignFallback implements SpmoCustomerShippingQuoteFeignClient {
    @Override
    public Result<CiticSpmCommonCostVO> comCostAndWeight(SpmApiCustomerShippingQuoteTryCalcDTO dto) {
        log.error("========================SpmoCustomerShippingQuoteFeignClient服务调用[comCostAndWeight]失败========================");
        return Result.error(ResultCodeEnum.FAIL);
    }


}
