/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: SysCustomerExtFallback
 * Author:   luhong
 * Date:     2020-10-14 14:53
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.CpAreaIdDTO;
import com.yl.applets.feign.OtherFeignClient;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-14
 * @since 1.0.0
 */
@Slf4j
@Component
public class OtherFeignClientFallback implements FallbackFactory<OtherFeignClient> {


    @Override
    public OtherFeignClient create(Throwable throwable) {
        return new OtherFeignClient() {
            @Override
            public Result<Boolean> isRelation(CpAreaIdDTO dto) {
                log.warn("【根据iD校验省市区信息,参数为:{} ,异常{}！】", JSON.toJSONString(dto),throwable);
                return Result.error(ResultCodeEnum.FAIL);
            }
        };
    }
}