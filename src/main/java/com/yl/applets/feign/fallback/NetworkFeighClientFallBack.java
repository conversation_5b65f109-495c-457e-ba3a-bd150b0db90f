package com.yl.applets.feign.fallback;

import com.yl.applets.feign.NetworkFeighClient;
import com.yl.applets.vo.OrderExceptionNetworkVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-08-16 15:34
 */
@Slf4j
@Component
public class NetworkFeighClientFallBack implements NetworkFeighClient {
    @Override
    public Result<OrderExceptionNetworkVO> getNetworkError(String networkCode) {
        log.warn("NetworkFeighClientFallBack 调用 getNetworkError 失败，参数：{}",networkCode);
        return Result.error(ResultCodeEnum.FAIL);
    }
}
