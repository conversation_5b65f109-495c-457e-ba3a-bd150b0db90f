package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.IpayLooseOrderWXDTO;
import com.yl.applets.dto.IpayWxPlaceOrderDTO;
import com.yl.applets.feign.PayFeignClient;
import com.yl.applets.vo.IpayLooseTokenVO;
import com.yl.applets.vo.IpayTokenVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-08-18
 */
@Slf4j
@Component
public class IPayFeignClientFallback implements PayFeignClient {
    @Override
    public Result<String> placeOrder(IpayWxPlaceOrderDTO ipayWxPlaceOrderDTO) {
        log.warn("feign调用财务接口就，获取支付pay_id失败，参数：{}", JSON.toJSONString(ipayWxPlaceOrderDTO));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<String> placeOrder(IpayLooseOrderWXDTO ipayWxPlaceOrderDTO) {
        log.warn("feign调用财务接口新，获取支付pay_id失败，参数：{}", JSON.toJSONString(ipayWxPlaceOrderDTO));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<IpayLooseTokenVO> getToken(String body,String appid) {
        log.warn("feign调用财务接口，获取getToken失败，参数：{}", JSON.toJSONString(body));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<String> umsMiniPrePayId(IpayLooseOrderWXDTO ipayWxPlaceOrderDTO) {
        log.warn("feign调用财务接口新，获取支付umsMiniPrePayId失败，参数：{}", JSON.toJSONString(ipayWxPlaceOrderDTO));
        return Result.error(ResultCodeEnum.FAIL);
    }
}
