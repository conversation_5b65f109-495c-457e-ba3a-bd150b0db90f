package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.consolidation.ConsolidationCancelDTO;
import com.yl.applets.dto.consolidation.ConsolidationDTO;
import com.yl.applets.dto.consolidation.ConsolidationPageDTO;
import com.yl.applets.dto.consolidation.ConsolidationUnPayDTO;
import com.yl.applets.feign.ConsolidationFeignClient;
import com.yl.applets.vo.consolidation.ConsolidationInfoVO;
import com.yl.applets.vo.consolidation.ConsolidationPageVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/11/21 11:03
 */
@Component
@Slf4j
public class ConsolidationFeignClientFallback implements FallbackFactory<ConsolidationFeignClient> {


    @Override
    public ConsolidationFeignClient create(Throwable throwable) {

        return new ConsolidationFeignClient() {
            @Override
            public Result<String> consolidationProOrderSave(ConsolidationDTO dto) {
                log.error("【保存新疆集运信息失败,参数为:{} ,异常信息为:{}】", JSON.toJSONString(dto), throwable);
                return Result.error(ResultCodeEnum.FAIL.getCode(), throwable.getMessage());
            }

            @Override
            public Result<Boolean> consolidationProOrderUpdate(ConsolidationDTO dto) {
                log.error("【修改新疆集运信息失败,参数为:{} ,异常信息为:{}】", JSON.toJSONString(dto), throwable);
                return Result.error(ResultCodeEnum.FAIL.getCode(), throwable.getMessage());
            }

            @Override
            public Result<Boolean> consolidationProOrderCancel(ConsolidationCancelDTO dto) {
                log.error("【取消新疆集运信息失败,参数为:{} ,异常信息为:{}】", JSON.toJSONString(dto), throwable);
                return Result.error(ResultCodeEnum.FAIL.getCode(), throwable.getMessage());
            }

            @Override
            public Result<Boolean> consolidationProOrderDelete(ConsolidationCancelDTO dto) {
                log.error("【删除新疆集运信息失败,参数为:{} ,异常信息为:{}】", JSON.toJSONString(dto), throwable);
                return Result.error(ResultCodeEnum.FAIL.getCode(), throwable.getMessage());
            }

            @Override
            public Result<Page<ConsolidationPageVO>> consolidationProOrderPages(ConsolidationPageDTO dto) {
                log.error("【分页获取新疆集运信息失败,参数为:{} ,异常信息为:{}】", JSON.toJSONString(dto), throwable);
                return Result.error(ResultCodeEnum.FAIL.getCode(), throwable.getMessage());
            }

            @Override
            public Result<Integer> getCountUnpaidOrders(ConsolidationUnPayDTO dto) {
                log.error("【未支付订单查询信息失败,参数为:{} ,异常信息为:{}】", JSON.toJSONString(dto), throwable);
                return Result.error(ResultCodeEnum.FAIL.getCode(), throwable.getMessage());
            }

            @Override
            public Result<Boolean> isExistPreOrderNo(String preOrderNo) {
                log.error("【一段单是否已登记查询信息失败,参数为:{} ,异常信息为:{}】", preOrderNo, throwable);
                return Result.error(ResultCodeEnum.FAIL.getCode(), throwable.getMessage());
            }

            @Override
            public Result<ConsolidationInfoVO> consolidationProOrderInfoById(Long id) {
                log.error("【通过主键id查询集运详情,参数为:{} ,异常信息为:{}】", id, throwable);
                return Result.error(ResultCodeEnum.FAIL.getCode(), throwable.getMessage());
            }

            @Override
            public Result<ConsolidationInfoVO> consolidationProOrderInfoByNo(String waybillNo) {
                log.error("【通过运单号查询集运详情,参数为:{} ,异常信息为:{}】", waybillNo, throwable);
                return Result.error(ResultCodeEnum.FAIL.getCode(), throwable.getMessage());
            }
        };

    }
}
