package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.*;
import com.yl.applets.entity.WxUser;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.WayBillApiFeignClient;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.*;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-10-09
 */

@Slf4j
@Component
public class WaybillApiFeignClientFallBack implements WayBillApiFeignClient {

    @Override
    public Result<WaybillInfoDto> getOrderCostByWaybillNo(WaybillQueryDto waybillNo) {
        log.error("feign调用运单接口getOrderCostByWaybillNo失败，传参：{}", JsonUtils.toJson(waybillNo));
        return Result.error(ResultCodeEnum.FAIL);
    }
}
