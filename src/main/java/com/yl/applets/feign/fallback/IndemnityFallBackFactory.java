package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yl.applets.dto.indemnity.AddRecordDTO;
import com.yl.applets.dto.indemnity.IndemnityDetailVO;
import com.yl.applets.dto.indemnity.IndemnityVO;
import com.yl.applets.dto.indemnity.SaveIndemnityDTO;
import com.yl.applets.feign.IndemnityFeignClient;
import com.yl.applets.service.IndemnityService;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：
 * @Author： zhan<PERSON>hong
 * @Date： 2023/01/09
 */
@Slf4j
@Component
public class IndemnityFallBackFactory implements FallbackFactory<IndemnityFeignClient> {
    @Override
    public IndemnityFeignClient create(Throwable throwable) {
        return new IndemnityFeignClient() {
            @Override
            public Result saveIndemnity(SaveIndemnityDTO dto) {
                log.warn("feign调用服务质量，理赔工单登记，参数：{}", JSON.toJSONString(dto));
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<List<IndemnityVO>> wxQueryByUserId(Integer numberId, Integer status) {
                log.warn("feign调用服务质量，工单查询接口，参数：{}", numberId);
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<IndemnityDetailVO> wxQueryByWorkOrderId(Long workOrderId) {
                log.warn("feign调用服务质量，工单查询接口，参数：{}", workOrderId);
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result wxAddRecord(AddRecordDTO dto) {
                log.warn("feign调用服务质量，工单添加回复内容和附件，参数：{}", JSONObject.toJSONString(dto));
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result wxAddEvaluate(AddRecordDTO dto) {
                log.warn("feign调用服务质量，工单添加评价（满意度），参数：{}", JSONObject.toJSONString(dto));
                return Result.error(ResultCodeEnum.FAIL);
            }
        };
    }
}
