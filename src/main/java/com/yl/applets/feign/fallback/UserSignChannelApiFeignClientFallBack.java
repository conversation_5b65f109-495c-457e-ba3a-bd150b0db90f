package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.*;
import com.yl.applets.entity.OmsWaybill;
import com.yl.applets.entity.WxUser;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.UserSignChannelApiFeignClient;
import com.yl.applets.vo.*;
import com.yl.applets.vo.baidu.IdCardVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-10-09
 */

@Slf4j
@Component
public class UserSignChannelApiFeignClientFallBack implements UserSignChannelApiFeignClient {

    @Override
    public Result<AppletsUserSignVo> detail(Long id) {
        log.warn("中台[detail]失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<AppletsUserSignVo>> getList(AppletsUserSignQueryDto dto) {
        log.warn("中台[getList]失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> update(AppletsUserSignUpdateDto dto) {
        log.warn("中台[update]失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<String>> remarks() {
        log.warn("中台[remarks]失败");
        return Result.error(ResultCodeEnum.FAIL);
    }
}
