/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: SysCustomerExtFallback
 * Author:   luhong
 * Date:     2020-10-14 14:53
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.feign.fallback;

import com.yl.applets.dto.SysAreaOnlineConfigQueryDTO;
import com.yl.applets.dto.lmdm.*;
import com.yl.applets.entity.lmdm.SysArea;
import com.yl.applets.entity.lmdm.SysNetwork;
import com.yl.applets.entity.lmdm.SysSettlementDestination;
import com.yl.applets.feign.OldLmdmFeignClient;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.SysCustomeAppletVO;
import com.yl.applets.vo.lmdm.*;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-14
 * @since 1.0.0
 */
@Slf4j
@Component
public class OldLmdmFeignClientFallback implements OldLmdmFeignClient {

    @Override
    public Result<SysCustomeAppletVO> getSysCustomerAppletInfo(Integer id) {
        log.error("feign调用基础数据中台通过打印设备项的id获取客户和打印设备信息失败,传参：{}", id);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<SysSettlementDestinationVO> getSettlementDestinationByProvinceCityAreaId(SysSettlementDestinationBaseQueryDTO dto) {
        log.error("feign调用基础数据中台通过省市区匹配结算目的地失败，传参：{}", JsonUtils.toJson(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<SysSettlementDestination>> redisAll() {
        log.error("feign调用基础数据中台获取redis缓存失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<SysArea>> findOnLineAreas(SysAreaOnlineConfigQueryDTO dto) {
        log.error("feign调用基础数据中台查询开通上线的区域详情失败,传参：{}", JsonUtils.toJson(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<SysVersionVO>> list(SysVersionQueryDTO var1) {
        log.error("feign调用基础数据中台App版本号管理列表失败，传参：{}", JsonUtils.toJson(var1));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<SysNetwork>> networkRedisAll() {
        log.error("feign调用基础数据中台获取所有的网点redis缓存失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<SysNetworkVO> getDetailById(Integer var1) {
        log.error("feign调用基础数据中台获取查询网点详细信息失败,传参：{}",var1);
        return Result.error(ResultCodeEnum.FAIL);
    }
    @Override
    public Result<SysStaffVO> getStaffDetail(SysStaffDTO var1) {
        log.error("feign调用基础数据中台查询员工详情失败，传参：{}", JsonUtils.toJson(var1));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<SysAreaNormalDTO>> getByNames(List<SysAreaPcaNamesDTO> var1) {
        log.error("feign调用基础数据中台根据名称查询省市区失败,传参：{}", JsonUtils.toJson(var1));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<AppBaseDataVO> getAppList(Integer integer) {
        log.error("调用基础数据getAppList接口失败,传参：{}",integer);
        throw new BusinessException(ResultCodeEnum.INTERFACE_INNER_INVOKE_ERROR);
    }

}