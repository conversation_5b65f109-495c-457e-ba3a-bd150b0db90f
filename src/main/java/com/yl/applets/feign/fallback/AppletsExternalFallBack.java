package com.yl.applets.feign.fallback;

import com.yl.applets.feign.AppletsExternalFeign;
import com.yl.applets.vo.CheckWxPhoneVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: wwx
 * @create: 2021-08-12 14:50
 */

@Component
@Slf4j
public class AppletsExternalFallBack implements AppletsExternalFeign {


    @Override
    public Result<CheckWxPhoneVO> checkPhoneIsBind(String phone) {
        log.error("根据手机号获取vip客户类型信息失败,请求参数为==>{}", phone);
        return Result.error(ResultCodeEnum.FAIL);
    }
}
