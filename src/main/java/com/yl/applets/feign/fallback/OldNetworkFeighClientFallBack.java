package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.EsSearchDTO;
import com.yl.applets.dto.SysAreaBookingDto;
import com.yl.applets.dto.SysAreaOnlineConfigQueryDTO;
import com.yl.applets.dto.lmdm.CustomerCardCheckDTO;
import com.yl.applets.entity.lmdm.SysArea;
import com.yl.applets.feign.OldNetworkFeighClient;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.EsSearchResult;
import com.yl.applets.vo.SpmiFranchiseeNetworkVO;
import com.yl.applets.vo.SysAreaBookingVo;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-08-16 15:34
 */
@Slf4j
@Component
public class OldNetworkFeighClientFallBack implements OldNetworkFeighClient {
    @Override
    public Result<List<SysArea>> findOnLineAreas(SysAreaOnlineConfigQueryDTO dto) {
        log.error("feign调用基础数据中台查询开通上线的寄件区域详情失败,传参：{}", JsonUtils.toJson(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<SysArea>> findSendOnLineAreas(SysAreaOnlineConfigQueryDTO dto) {
        log.error("feign调用基础数据中台查询开通上线的寄件区域详情失败,传参：{}", JsonUtils.toJson(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public  Result<SpmiFranchiseeNetworkVO>  getFranchiseeByCode(String networkCode) {
        log.error("调用基础数据查询加盟商接口失败,传参：{}",networkCode);
        throw new BusinessException(ResultCodeEnum.INTERFACE_INNER_INVOKE_ERROR);
    }

    @Override
    public Result<Boolean> checkCardNo(CustomerCardCheckDTO dto) {
        log.error("调用基础数据实名认证接口失败,传参：{}",dto);
        throw new BusinessException(ResultCodeEnum.INTERFACE_INNER_INVOKE_ERROR);
    }

    @Override
    public Result<Boolean> getSysForbiddenInfo(String contents) {
        log.error("调用基础数据获取禁寄限类目是否符合要求接口失败,传参：{}",contents);
        throw new BusinessException(ResultCodeEnum.INTERFACE_INNER_INVOKE_ERROR);
    }

    @Override
    public Result<SysAreaBookingVo> pickTime(SysAreaBookingDto sysAreaBookingDto) {
        log.error("SysAreaBookingFeignClient 调用 [ pickTime ]失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<EsSearchResult> distanceSort(EsSearchDTO dto) {
        log.error("调用网点es查询附近网点接口失败:{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }
}
