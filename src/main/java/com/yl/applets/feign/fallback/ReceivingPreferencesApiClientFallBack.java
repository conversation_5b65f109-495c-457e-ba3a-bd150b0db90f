package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.*;
import com.yl.applets.feign.ReceivingPreferencesApiClient;
import com.yl.applets.vo.OrderMarkPhoneVO;
import com.yl.applets.vo.OrderMarkingVo;
import com.yl.applets.vo.OrderRecvPrefVO;
import com.yl.applets.vo.RecvPrefConfigVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-10-09
 */

@Slf4j
@Component
public class ReceivingPreferencesApiClientFallBack implements ReceivingPreferencesApiClient {


    @Override
    public Result<List<OrderMarkingVo>> getResource(String orderId) {
        log.error("feign请求[getResource]方法出错了");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Page<RecvPrefConfigVO>> preferencesPage(PreferencesQueryDto dto) {
        log.error("feign请求[preferencesPage]方法出错了，参数{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> preferencesAdd(PreferencesAddDto dto) {
        log.error("feign请求[preferencesAdd]方法出错了，参数{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> preferencesUpdate(PreferencesUpdateDto dto) {
        log.error("feign请求[preferencesUpdate]方法出错了，参数{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> preferencesDelete(String id, String mobile,String source) {
        log.error("feign请求[preferencesDelete]方法出错了，参数{}-{}", id,mobile);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Void> save(OrderMarkPhoneSaveDTO dto) {
        log.error("feign请求[save]方法出错了，参数{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<OrderMarkPhoneVO> detail(OrderMarkPhoneQueryDTO dto) {
        log.error("feign请求[detail]方法出错了，参数{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<OrderRecvPrefVO> queryByOrderId(String orderId) {
        log.error("feign请求[queryByOrderId]方法出错了，参数{}", orderId);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Void> accept(OrderMarkingDTO dto) {
        log.error("feign请求[accept]方法出错了，参数{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> userSelf(OrderUserQueryDto dto) {
        log.error("feign请求[userSelf]方法出错了，参数{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }


    @Override
    public Result<Boolean> userSelfOrder(OrderUserQueryDto dto) {
        log.error("feign请求[userSelfOrder]方法出错了，参数{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }
}
