package com.yl.applets.feign.fallback;

import com.yl.applets.entity.CustomizedQrcode;
import com.yl.applets.feign.QrcodeFeignClient;
import com.yl.common.base.model.vo.Result;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021
 *
 * @Description:
 * @CreateDate: Created in {2021/7/12 17:26}
 * @Author: hhf
 */
@Component
@Slf4j
public class QrcodeFeignClientFallbackFactory implements FallbackFactory<QrcodeFeignClient> {


    @Override
    public QrcodeFeignClient create(Throwable throwable) {
        return new QrcodeFeignClient() {



            @Override
            public Result<CustomizedQrcode> getQrDetailInfo(Long id) {
                log.warn("【根据iD查询二维码详情信息,参数为:{} ,异常信息为:{}】", id, throwable);
                return Result.error(1, throwable.getMessage());
            }

            @Override
            public Result<Boolean> qrIsScan(Long id) {
                log.warn("【根据iD更新二维码扫码次数,参数为:{} ,异常信息为:{}】", id, throwable);
                return Result.error(1, throwable.getMessage());
            }

        };
    }
}
