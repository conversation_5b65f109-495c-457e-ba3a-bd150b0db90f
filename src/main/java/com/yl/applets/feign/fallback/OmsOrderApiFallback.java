/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: OmsOrderApiFallback
 * Author:   luhong
 * Date:     2020-10-15 9:45
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.feign.fallback;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.ElectronicInvoiceDTO;
import com.yl.applets.dto.OmsCloudPrintApiDto;
import com.yl.applets.dto.OmsOrderApiDTO;
import com.yl.applets.dto.OmsOrderBatchApiDto;
import com.yl.applets.feign.OrderFeigntClient;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.*;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-15
 * @since 1.0.0
 */
@Slf4j
@Component
public class OmsOrderApiFallback implements OrderFeigntClient {

    @Override
    public Result<OmsOrderBatchApiVo> batchSave(OmsOrderBatchApiDto param) {
        log.error("feign调用订单中台批量新增失败,传参：{}", JsonUtils.toJson(param));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Page<OmsElectronicInvoice>> myElectronicInvoice(Integer memberId, Integer current, Integer size) {
        log.error("feign调用订单中台开票列表失败,传参：{},{},{}", memberId,current,size);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Page<OmsElectronicInvoice>> pageMyElectronicInvoice(ElectronicInvoiceDTO electronicInvoiceDTO) {
        log.error("feign调用订单中台开票列表失败,传参：{}", JsonUtils.toJson(electronicInvoiceDTO));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<OmsOrderApiVO> getOrderInfoByWaybillId(String waybillId) {
        log.error("feign调用订单中台订单详情失败,传参：{}",waybillId);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<OmsOrderApiVO> saveOrder(OmsOrderApiDTO omsOrderApiDTO) {
        log.error("feign调用订单中台保存订单失败,传参：{}",JsonUtils.toJson(omsOrderApiDTO));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<OmsOrderBatchApiVo> cloudPrintOrder(OmsCloudPrintApiDto param) {
        log.error("feign调用订单中台云打印失败,传参：{}",JsonUtils.toJson(param));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result cloudPrint(Long orderId) {
        log.error("feign调用订单中台打印失败,传参：{}",orderId);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<OmsOrderApiVO> detailApp(Long id) {
        log.error("feign调用订单中台订单详情失败,传参：{}",id);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<OmsOrderConventionVo> queryOrderConventionByOrderId(long orderId) {
        log.error("feign调用queryOrderConventionByOrderId失败,传参：{}",orderId);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<OmsOrderPayRecordVO> getPayRecordByWaybillNo(String couponCode) {
        log.error("feign调用getPayRecordByCouponCode失败,传参：{}",couponCode);
        return Result.error(ResultCodeEnum.FAIL);
    }
}