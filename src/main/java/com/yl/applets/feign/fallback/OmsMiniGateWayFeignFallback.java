/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: OmsMiniGateWayFeignFallback
 * Author:   luhong
 * Date:     2020-10-15 14:31
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.feign.fallback;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.ThirdExpressApiDTO;
import com.yl.applets.feign.OmsMiniGateWayFeign;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.ThirdExpressListVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-15
 * @since 1.0.0
 */
@Slf4j
@Component
public class OmsMiniGateWayFeignFallback implements OmsMiniGateWayFeign {

    @Override
    public Result<Boolean> delete(@NotNull(message = "订单编号不能为空") Long orderId) {
        log.error("feign调用订单中台删除订单失败：{}",orderId);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Page<ThirdExpressListVO>> getMyExpressPage(ThirdExpressApiDTO queryDto) {
        log.error("feign调用订单中台收寄件列表失败,传参：{}", JsonUtils.toJson(queryDto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Map<String, Long>> getMyExpressCnt(ThirdExpressApiDTO queryDto) {
        log.error("feign调用订单中台收寄件统计失败,传参：{}",JsonUtils.toJson(queryDto));
        return Result.error(ResultCodeEnum.FAIL);
    }
}