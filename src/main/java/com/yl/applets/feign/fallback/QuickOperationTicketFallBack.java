package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.quickoperationticke.CcmWorkDetailQueryDTO;
import com.yl.applets.dto.quickoperationticke.CcmWorkOrderCreateDTO;
import com.yl.applets.dto.quickoperationticke.CcmWorkOrderQueryDTO;
import com.yl.applets.dto.quickoperationticke.WorkOrderjudgmentDTO;
import com.yl.applets.feign.IndemnityFeignClient;
import com.yl.applets.feign.QuickOperationTicketFeignClient;
import com.yl.applets.vo.quickoperationticke.CcmWorkOrderVO;
import com.yl.applets.vo.quickoperationticke.WorkOrderCustomerOptionsSelectorVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/6/14 12:00
 */
@Slf4j
@Component
public class QuickOperationTicketFallBack implements FallbackFactory<QuickOperationTicketFeignClient> {
    @Override
    public QuickOperationTicketFeignClient create(Throwable throwable) {
        return new QuickOperationTicketFeignClient() {
            @Override
            public Result<Boolean> create(CcmWorkOrderCreateDTO dto) {
                log.error("QuickOperationTicketFeignClient create error入参：{}", JSON.toJSONString(dto));
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<Page<CcmWorkOrderVO>> page(CcmWorkOrderQueryDTO dto) {
                log.error("QuickOperationTicketFeignClient page error：{}", JSON.toJSONString(dto));
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<CcmWorkOrderVO> detail(CcmWorkDetailQueryDTO dto) {
                log.error("QuickOperationTicketFeignClient detail error：{}", JSON.toJSONString(dto));
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<Boolean> reminder(CcmWorkDetailQueryDTO dto) {
                log.error("QuickOperationTicketFeignClient reminder error：{}", JSON.toJSONString(dto));
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<Integer> isOverdue(String waybillNo) {
                log.error("QuickOperationTicketFeignClient isOverdue error：{}", waybillNo);
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<List<WorkOrderCustomerOptionsSelectorVO>> optionsSelector() {
                log.error("QuickOperationTicketFeignClient optionsSelector error");
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<Boolean> judgmentSelfWork(WorkOrderjudgmentDTO dto) {
                log.error("QuickOperationTicketFeignClient judgmentSelfWork error dto:{}", JSONObject.toJSONString(dto));
                return Result.error(ResultCodeEnum.FAIL);
            }
        };
    }
}
