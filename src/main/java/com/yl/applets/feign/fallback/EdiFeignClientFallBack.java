package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.yl.applets.feign.EdiFeignClient;
import com.yl.applets.feign.dto.AlipayIncentivecodeOperationDTO;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2022-12-10 16:56
 * @Version 1.0
 */
@Component
@Slf4j
public class EdiFeignClientFallBack implements EdiFeignClient {


    @Override
    public Result<Boolean> codePin(AlipayIncentivecodeOperationDTO dto) {
        log.warn("调用EDI-码动销接口失败.入参：{}", JSON.toJSONString(dto));
        return null;
    }
}
