package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.lmdm.SysStaffDTO;
import com.yl.applets.feign.NewStaffFeignClient;
import com.yl.applets.vo.lmdm.SysStaffVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * StaffFeignClient兜底策略
 */
@Slf4j
@Component
public class StaffFeignClientFallback implements NewStaffFeignClient {

    /**************************************************************
     *
     * staff
     *
     **************************************************************/
    @Override
    public Result<SysStaffVO> getStaffDetail(SysStaffDTO var1) {
        log.error("feign请求ylstaffapi的getStaffDetail方法出错了，参数；[{}]", JSON.toJSONString(var1));
        return Result.error(ResultCodeEnum.FAIL);
    }
}
