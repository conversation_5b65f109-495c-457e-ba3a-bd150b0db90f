package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.*;
import com.yl.applets.feign.PromotionFeighClient;
import com.yl.applets.vo.*;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description:
 * @author: wwx
 * @create: 2021-08-12 14:50
 */

@Component
@Slf4j
public class PromotionFeighClientFallBack implements PromotionFeighClient {


    @Override
    public Result<Integer> getApplets(PromotionUserGetAppletsDto appletsDto) {
        log.error("小程序调用优惠券接口失败==>{}", JSON.toJSONString(appletsDto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Long> getActivityCoupon(PromotionUserGetAppletsDto appletsDto) {
        log.error("小程序调用优惠券活动领取接口失败==>{}", JSON.toJSONString(appletsDto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Integer> getNetwork(PromotionUserGetNetworkDto networkDto) {
        log.error("网点领取优惠券接口失败==>{}", JSON.toJSONString(networkDto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Page<CustUserPromotionVo>> userGetCouponList(CustUserPromotionVo vo) {
        log.error("扫码领取优惠券列表接口失败==>{}", JSON.toJSONString(vo));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<CouponConfigVo> getCouponListByConfig(CustUserPromotionVo vo) {
        log.error("根据后端配置领取优惠券接口失败==>{}",JSON.toJSONString(vo));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Page<CustPromotionUseVo>> getCouponToOrder(OrderPromotionDto dto) {
        log.error("下单优惠券列表接口失败==>{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Page<CustPromotionUseVo>> getCouponPageByType(CustPromotionUseDto dto) {
        log.error("查询当前用户优惠券列表接口失败==>{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<String>> getRangeByCode(String id, String type) {
        log.error("根据优惠券查询可使用范围,失败==>{}{}", id,type);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<CustPromotionInfoVo> getCouponValueAndRule(String id, String proCode, String freight) {
        log.error("根据优惠券查询优惠金额,失败==>{}{}{}", id,proCode,freight);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Integer> useLockPromotion(PromotionUserUseDto userUseDto) {
        log.error("锁定优惠券,失败==>{}", JSON.toJSONString(userUseDto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Integer> usePromotion(Long id, Long orderId, Long proId, Long disId,Long network,String staffCode) {
        log.error("使用优惠券,失败==>{}{}{}{}{}{}", id,orderId,proId,disId,network,staffCode);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> unlockUserPromotion(Long id) {
        log.error("解锁优惠券,失败==>{}", id);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Integer> finishPromotion(PromotionFinishDto finishDto) {
        log.error("核销优惠券,失败==>{}", JSON.toJSONString(finishDto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Integer> rollbackPromotion(Long id, Long proId, Long disId) {
        log.error("回滚优惠券,失败==>{}{}{}", id,proId,disId);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Integer> rollbackPromotionByOrderNo(PromotionUserUseDto dto) {
        log.error("根据订单号回滚优惠券,失败==>{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> getCouponOnekey(CouponOneKeyDto dto) {
        log.error("优惠券一键领取,失败==>{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Boolean> getCouponByOutActive(CouponOneKeyDto dto) {
        log.error("优惠券一键领取,失败==>{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<Long>> getCouponByOutActiveValue(List<PromotionUserGetAppletsDto> list) {
        log.error("优惠券一键领取,失败==>{}", JSON.toJSONString(list));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Long> bindUserAndTradeQuery(String tradeNo) {
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Integer> addSystemOperation(CustSystemOperationDto dto) {
        log.error("PromotionFeighClientFallBack addSystemOperation,失败==>{}", JSON.toJSONString(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<MemberActivityQueryDto>> activityList() {
        log.error("查询活动劵,失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<MemberImageTextQueryDto> itInfoByType(String type) {
        log.error("图文随机获取,失败");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<Long>> getCouponByCMCC(List<PromotionUserGetAppletsDto> list) {
        log.error("PromotionFeighClientFallBack getCouponByCMCC,失败==>{}", JSON.toJSONString(list));
        return Result.error(ResultCodeEnum.FAIL);
    }
}
