package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.feign.CiticMonthlySettlementClient;
import com.yl.applets.feign.dto.citicmonthlysettlement.*;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/12/12 10:42
 */
@Slf4j
@Component
public class CiticMonthlySettlementClienntFallBack implements FallbackFactory<CiticMonthlySettlementClient> {

    @Override
    public CiticMonthlySettlementClient create(Throwable throwable) {
        return new CiticMonthlySettlementClient() {
            @Override
            public Result<Integer> bindInfo(CiticMonthlySettlementDTO dto) {
                log.error("月结信息:绑定月结账号信息，调用失败.入参：{}", JSON.toJSONString(dto));
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<Integer> unBindInfo(CiticMonthlySettlementUpdateDTO dto) {
                log.error("月结信息:解除绑定，调用失败.入参：{}", JSON.toJSONString(dto));
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<Integer> updateDefaultAccount(CiticMonthlySettlementUpdateDTO dto) {
                log.error("月结信息:修改默认账号，调用失败.入参：{}", JSON.toJSONString(dto));
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<Page<CiticMonthlySettlementResultDTO>> getMonthlySettlementList(CiticMonthlySettlementQueryDTO dto) {
                log.error("月结信息:查询当前用户下的月付账号列表，调用失败.入参：{}", JSON.toJSONString(dto));
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<Boolean> judgeRabbitDelivery(String account) {
                log.error("月结信息:判断兔优达，调用失败.入参：{}", account);
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<Boolean> judgeAccount(String account) {
                log.error("月结信息:判断账号是否可用，调用失败.入参：{}", account);
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<MonthlySettlementShareCustomerDTO> findShareCustomer(String account) {
                log.error("月结信息:查询共享客户信息，调用失败.入参：{}", account);
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<MonthlySettlementShareCustomerDTO> findShareCustomerByCode(String customerCode) {
                log.error("月结信息:查询共享客户信息code，调用失败.入参：{}", customerCode);
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<CiticMonthlySettlementResultDTO> findDefaultAccount(String memberId) {
                log.error("月结信息:查询默认客户信息，调用失败.入参：{}", memberId);
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<CiticMonthlySettlementResultDTO> findAccountByCusCode(String memberId,String customerCode) {
                log.error("月结信息:查询客户信息通过客户code，调用失败.入参：memberId {} customerCode {}",memberId ,customerCode);
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result<MonthlySettlementCustomerDTO> findCustomerAccount(String account) {
                log.error("月结信息:查询客户信息完成通过客户账号，调用失败.入参： customerCode {} ",account);
                return Result.error(ResultCodeEnum.FAIL);
            }
        };
    }
}
