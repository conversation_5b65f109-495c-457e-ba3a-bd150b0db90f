package com.yl.applets.feign.fallback;

import com.yl.applets.feign.OmsWaybillGateWayFeign;
import com.yl.applets.vo.OmsWaybillDetailVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class OmsWaybillApiFallback implements OmsWaybillGateWayFeign {

    @Override
    public Result<OmsWaybillDetailVO> getDetailByNo(String waybillNo) {
        log.info("=====调用运单详情接口报错====");
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<OmsWaybillDetailVO> detailByNo(String waybillNo, Integer pickNetworkId) {
        log.info("=====调用运单详情接口报错====");
        return Result.error(ResultCodeEnum.FAIL);
    }
}
