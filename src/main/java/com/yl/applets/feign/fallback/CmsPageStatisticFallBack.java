package com.yl.applets.feign.fallback;

import com.yl.applets.dto.CmsPageStatisticsDto;
import com.yl.applets.feign.CmsPageStatisticFeign;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2024-03-04
 */
@Slf4j
public class CmsPageStatisticFallBack implements FallbackFactory<CmsPageStatisticFeign> {

    @Override
    public CmsPageStatisticFeign create(Throwable throwable) {
        return new CmsPageStatisticFeign() {
            @Override
            public Result<Boolean> statistics(CmsPageStatisticsDto cmsPageStatisticsDto) {
                log.warn("cms页面埋点调用失败==>{}", throwable);
                return Result.error(ResultCodeEnum.FAIL);
            }

            @Override
            public Result detail(String pageId, String previewType) {
                log.warn("cms页面detail调用失败==>{}", throwable);
                return Result.error(ResultCodeEnum.FAIL);
            }
        };
    }
}
