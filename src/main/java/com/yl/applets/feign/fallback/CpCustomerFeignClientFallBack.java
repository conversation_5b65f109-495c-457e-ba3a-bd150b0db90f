package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.yl.applets.feign.CpCustomerFeignClient;
import com.yl.applets.feign.IndemnityFeignClient;
import com.yl.applets.feign.dto.SysCustomerDTO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/12/18 17:21
 */
@Slf4j
@Component
public class CpCustomerFeignClientFallBack implements FallbackFactory<CpCustomerFeignClient> {
    @Override
    public CpCustomerFeignClient create(Throwable throwable) {
        return new CpCustomerFeignClient() {
            @Override
            public Result<SysCustomerDTO> getSysCustomerByCode(String code) {
                log.info("feign调用客户服务，参数：{}", code);
                return Result.error(ResultCodeEnum.FAIL);
            }


        };
    }
}
