package com.yl.applets.feign.fallback;

import com.yl.applets.dto.SysAreaOnlineConfigQueryDTO;
import com.yl.applets.entity.lmdm.SysArea;
import com.yl.applets.feign.EdiAreaOnlineConfigSendFeignClient;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.lmdm.SysAreaIdVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Auther: zhuzhengjie
 * @Date: 2020年2月24日20:37:35
 * @Description: 作用描述
 */
@Slf4j
@Component
public class SysAreaOnlineConfigSendFallBack implements EdiAreaOnlineConfigSendFeignClient {


    @Override
    public Result<SysAreaIdVO> getByPlatformType(Integer plateformType) {
        log.error("feign调用基础数据中台根据平台类型查询该平台开通的寄件省市区id失败,传参：{}", plateformType);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<SysArea>> findOnLineAreas(SysAreaOnlineConfigQueryDTO dto) {
        log.error("feign调用基础数据中台查询开通上线的寄件区域详情失败,传参：{}", JsonUtils.toJson(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<List<SysArea>> findDistrict(SysAreaOnlineConfigQueryDTO dto) {
        log.error("feign调用基础数据中台查询省市区失败,传参：{}", JsonUtils.toJson(dto));
        return Result.error(ResultCodeEnum.FAIL);
    }
}
