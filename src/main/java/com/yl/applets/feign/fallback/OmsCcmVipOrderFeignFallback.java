package com.yl.applets.feign.fallback;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.ThirdExpressApiDTO;
import com.yl.applets.feign.OmsCcmVipOrderFeign;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.ThirdExpressListVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2024-01-29 18:08
 * @Version 1.0
 */
@Component
@Slf4j
public class OmsCcmVipOrderFeignFallback implements OmsCcmVipOrderFeign {
    @Override
    public Result<Boolean> delete(@NotNull(message = "订单编号不能为空") Long orderId) {
        log.error("feign调用订单中台删除订单失败：{}", orderId);
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Page<ThirdExpressListVO>> getMyExpressPage(ThirdExpressApiDTO queryDto) {
        log.error("feign调用订单中台收寄件列表失败,传参：{}", JsonUtils.toJson(queryDto));
        return Result.error(ResultCodeEnum.FAIL);
    }

    @Override
    public Result<Map<String, Long>> getMyExpressCnt(ThirdExpressApiDTO queryDto) {
        log.error("feign调用订单中台收寄件统计失败,传参：{}",JsonUtils.toJson(queryDto));
        return Result.error(ResultCodeEnum.FAIL);
    }
}
