package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.yl.applets.feign.CouponStaffBuryingFeignClient;
import com.yl.applets.feign.dto.CouponStaffBuryingRecordDTO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/12/4 18:19
 */
@Component
@Slf4j
public class CouponStaffBuryingFeignClientFallBack implements FallbackFactory<CouponStaffBuryingFeignClient> {
    @Override
    public CouponStaffBuryingFeignClient create(Throwable throwable) {
        return new CouponStaffBuryingFeignClient() {
            @Override
            public Result saveCouponStaffBurying(CouponStaffBuryingRecordDTO dto) {
                log.error("员工优惠券埋点保存 dto:[{}] error:[{}]", JSON.toJSONString(dto),throwable.getMessage());
                return Result.error(ResultCodeEnum.FAIL);
            }
        };
    }
}
