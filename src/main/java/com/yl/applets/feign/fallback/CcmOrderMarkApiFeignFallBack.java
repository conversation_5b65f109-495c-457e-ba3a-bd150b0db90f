package com.yl.applets.feign.fallback;

import com.alibaba.fastjson.JSON;
import com.yl.applets.feign.CcmOrderMarkApiFeignClient;
import com.yl.applets.feign.CiticMonthlySettlementClient;
import com.yl.applets.vo.OrderMarkCodeVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/3/26 18:26
 */
@Slf4j
@Component
public class CcmOrderMarkApiFeignFallBack implements FallbackFactory<CcmOrderMarkApiFeignClient> {
    @Override
    public CcmOrderMarkApiFeignClient create(Throwable throwable) {
        return new CcmOrderMarkApiFeignClient() {
            @Override
            public Result<List<OrderMarkCodeVO>> queryMarkCodes(List<Long> orderIds) {
                log.error("小程序列表查询订单标签，调用失败.入参：{}", orderIds);
                return Result.error(ResultCodeEnum.FAIL);
            }
        };
    }
}
