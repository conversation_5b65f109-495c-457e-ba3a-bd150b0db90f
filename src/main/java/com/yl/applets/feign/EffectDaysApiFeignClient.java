package com.yl.applets.feign;

import com.yl.applets.dto.AppletsEffectQueryDTO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2024-07-22 13:35
 * @Version 1.0
 */
@FeignClient(value = "ylccmvipdatacenter",path = "/ylccmvipdatacenter")
public interface EffectDaysApiFeignClient {

    @PostMapping("/effect/day/query")
    Result<String> effectTime(@RequestBody AppletsEffectQueryDTO dto);
}
