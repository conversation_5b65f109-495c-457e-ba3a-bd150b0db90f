package com.yl.applets.feign;

import com.yl.applets.feign.dto.SysCustomerDTO;
import com.yl.applets.vo.SysCustomerGroupByMallVO;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(
//        url = "http://10.33.102.14:30623",
        name = "ylcustomerapi",
        path = "/customerapi")
public interface CpCustomerFeignClient {

    /**
     * 根据code获取单个客户
     *
     * @param code
     * @return
     */
    @GetMapping("/app/applets/customer/get/customer/byCode")
    @ApiOperation(value = "根据code获取单个客户")
    Result<SysCustomerDTO> getSysCustomerByCode(@RequestParam("code") String code);




}
