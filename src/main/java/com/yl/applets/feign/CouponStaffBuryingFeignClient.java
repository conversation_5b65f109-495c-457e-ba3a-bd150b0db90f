package com.yl.applets.feign;

import com.yl.applets.feign.dto.CouponStaffBuryingRecordDTO;
import com.yl.applets.feign.fallback.CouponStaffBuryingFeignClientFallBack;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/12/4 18:18
 */
@FeignClient(
        name = "yljmschannelapi",
        path = "/channelapi",
        fallback = CouponStaffBuryingFeignClientFallBack.class
)
public interface CouponStaffBuryingFeignClient {

    @PostMapping(value = "/burying/event/coupon/staff/save")
    @ApiOperation(value = "员工优惠券埋点保存", notes = "员工优惠券埋点保存")
    Result saveCouponStaffBurying(@RequestBody CouponStaffBuryingRecordDTO dto);


}
