package com.yl.applets.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.ThirdExpressApiDTO;
import com.yl.applets.feign.fallback.OmsCcmVipOrderFeignFallback;
import com.yl.applets.vo.ThirdExpressListVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2024-01-29 18:06
 * @Version 1.0
 */
@FeignClient(name = "yl-jms-ccm-vip-order-api", path = "/vip-order/omsMini", fallback = OmsCcmVipOrderFeignFallback.class)
public interface OmsCcmVipOrderFeign {

    /**
     * 删除我的快递
     *
     * @param orderId
     * @return
     **/
    @PostMapping(value = "/delete")
    Result<Boolean> delete(@RequestParam("orderId") @NotNull(message = "订单编号不能为空") Long orderId);

    /**
     * 分页查询我的寄收快件
     *
     * @param queryDto
     * @return
     */
    @PostMapping(value = "/myExpress")
    Result<Page<ThirdExpressListVO>> getMyExpressPage(@RequestBody ThirdExpressApiDTO queryDto);

    /**
     * 查询我的寄收快件数量
     *
     * @param queryDto
     * @return
     */
    @PostMapping(value = "/myExpressCount")
    Result<Map<String, Long>> getMyExpressCnt(@RequestBody ThirdExpressApiDTO queryDto);
}
