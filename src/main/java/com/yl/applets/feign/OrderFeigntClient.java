package com.yl.applets.feign;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.ElectronicInvoiceDTO;
import com.yl.applets.dto.OmsCloudPrintApiDto;
import com.yl.applets.dto.OmsOrderApiDTO;
import com.yl.applets.dto.OmsOrderBatchApiDto;
import com.yl.applets.feign.fallback.OmsOrderApiFallback;
import com.yl.applets.vo.*;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Component
@FeignClient(
        name = "ylorderapi",
        path = "/orderapi/",
//        url = "192.168.1.229:30278",
//        url = "127.0.0.1:8081",
        fallback = OmsOrderApiFallback.class
)
public interface OrderFeigntClient {

    /**
     * 批量下单
     * @param param
     * @return
     */
    @PostMapping({"/omsOrder/batch/save"})
    Result<OmsOrderBatchApiVo> batchSave(@Validated @RequestBody OmsOrderBatchApiDto param);

    /**
     * 获取满足开票的运单
     * @param memberId
     * @param current
     * @param size
     * @return
     */
    @GetMapping({"/omsMini/myElectronicInvoice"})
    Result<Page<OmsElectronicInvoice>>  myElectronicInvoice(@RequestParam("memberId")Integer memberId,@RequestParam("current")Integer current,@RequestParam("size")Integer size);

    /**
     * 功能描述:
     * 电子发票三期
     * @param electronicInvoiceDTO
     * @return:com.yl.common.base.model.vo.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.yl.order.api.vo.OmsElectronicInvoice>>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2020-07-23 15:23
     */
    @PostMapping({"/omsMini/pageElectronicInvoice"})
    Result<Page<OmsElectronicInvoice>> pageMyElectronicInvoice(@RequestBody ElectronicInvoiceDTO electronicInvoiceDTO);


    @GetMapping(value = "/omsOrder/getOrder/info/{waybill_id}")
    Result<OmsOrderApiVO> getOrderInfoByWaybillId(@PathVariable(value = "waybill_id") String waybillId);

    /**
     * 下订单
     * @param omsOrderApiDTO
     * @return
     */
    @PostMapping(value = "/omsOrder")
    Result<OmsOrderApiVO> saveOrder(@RequestBody OmsOrderApiDTO omsOrderApiDTO);


    /**
     * 云打印订单
     * @param param
     * @return
     */
    @PostMapping("/omsOrder/cloudPrintOrder")
    Result<OmsOrderBatchApiVo> cloudPrintOrder(@Validated @RequestBody OmsCloudPrintApiDto param);


    /**
     * 云打印 复打
     * @param orderId 订单号
     * @return
     */
    @PostMapping(value = "/print/cloudPrint")
    Result cloudPrint(@RequestParam Long orderId);

    /**
     * 根据订单id查询订单详情
     *
     * @param id 订单id
     */
    @GetMapping("/omsOrder/detailApp")
    Result<OmsOrderApiVO> detailApp(@RequestParam Long id);

    /**
     * 查询预约时间
     * @param orderId
     * @return
     */
    @PostMapping(value = "/orderConvention/queryOrderConventionByOrderId")
    Result<OmsOrderConventionVo> queryOrderConventionByOrderId(@RequestParam(value = "orderId") long orderId);

    @GetMapping(value = "/omsOrder/open/getPayRecordByWaybillNo")
    Result<OmsOrderPayRecordVO> getPayRecordByWaybillNo(@RequestParam("waybillNo") String waybillNo);

}
