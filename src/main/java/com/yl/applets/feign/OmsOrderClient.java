/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: OmsOrderClient
 * Author:   luhong
 * Date:     2020-10-15 14:37
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.feign;

import com.yl.applets.dto.OmsOrderCancelApiDTO;
import com.yl.applets.dto.OmsOrderUpdateApiDTO;
import com.yl.applets.feign.fallback.OmsOrderClientFallback;
import com.yl.applets.vo.OmsOrderCancelApiVO;
import com.yl.applets.vo.OmsOrderUpdateApiVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-15
 * @since 1.0.0
 */
@Component
@FeignClient(
        name = "ylorderapi",
        path = "/orderapi/omsOrder",
//        url = "127.0.0.1:8081",
//         url = "*************:30278",
        fallback = OmsOrderClientFallback.class
)
public interface OmsOrderClient {


    @PostMapping({"/updateAppletsOrder"})
    Result<OmsOrderUpdateApiVO> updateOrder(@RequestBody OmsOrderUpdateApiDTO omsOrderUpdateApiDTO);



    @PostMapping({"/cancel"})
    Result<OmsOrderCancelApiVO> cancelOrder(@RequestBody OmsOrderCancelApiDTO omsOrderCancelApiDTO);

}