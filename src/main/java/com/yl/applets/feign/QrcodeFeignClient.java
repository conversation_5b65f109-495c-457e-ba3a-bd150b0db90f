package com.yl.applets.feign;


import com.yl.applets.entity.CustomizedQrcode;
import com.yl.applets.feign.fallback.QrcodeFeignClientFallbackFactory;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        name = "yljmschannelapi",
        path = "/channelapi/api/qrcode",
        fallback = QrcodeFeignClientFallbackFactory.class
//        , url = "http://localhost:9611"

)
public interface QrcodeFeignClient {



    @GetMapping("/getQrDetailInfo")
    Result<CustomizedQrcode> getQrDetailInfo(@RequestParam("id") Long id);



    @GetMapping("/qrIsScan")
    Result<Boolean> qrIsScan(@RequestParam("id") Long id);


}
