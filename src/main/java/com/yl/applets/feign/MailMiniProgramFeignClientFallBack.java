package com.yl.applets.feign;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/1/17 11:26
 * @description
 */
@Slf4j
@Component
public class MailMiniProgramFeignClientFallBack implements FallbackFactory<MailMiniProgramFeignClient> {

    @Override
    public MailMiniProgramFeignClient create(Throwable throwable) {
        log.warn("MailMiniProgramFeignClient error", throwable);
        return null;
    }
}
