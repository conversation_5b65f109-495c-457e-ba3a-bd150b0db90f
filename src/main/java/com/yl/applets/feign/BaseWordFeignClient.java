package com.yl.applets.feign;

import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        name = "ylsensitivewordsapi"
        ,path = "/sensitivewordsapi"
//        ,url = ""
)
public interface BaseWordFeignClient {

    /**
     * 功能描述:
     * 敏感词
     */
    @GetMapping("/sensitive/word/cp/checkWord")
    Result<Boolean> checkWord(@RequestParam("word") String word);


}