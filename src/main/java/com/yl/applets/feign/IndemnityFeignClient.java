package com.yl.applets.feign;

import com.yl.applets.dto.indemnity.AddRecordDTO;
import com.yl.applets.dto.indemnity.IndemnityDetailVO;
import com.yl.applets.dto.indemnity.IndemnityVO;
import com.yl.applets.dto.indemnity.SaveIndemnityDTO;
import com.yl.applets.feign.fallback.IndemnityFallBackFactory;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：理赔中心feign接口
 * @Author： zhanzhihong
 * @Date： 2023/01/09
 */
@FeignClient(name = "ylcssworkorderapi",
        path = "/cssworkorderapi/workOrder",
        fallbackFactory = IndemnityFallBackFactory.class)
public interface IndemnityFeignClient {


    @ApiOperation(value = "理赔工单登记", notes = "理赔工单登记")
    @PostMapping("/wxSave")
    Result saveIndemnity(@RequestBody @Valid SaveIndemnityDTO dto);

    @ApiOperation(value = "工单查询接口", notes = "工单查询接口")
    @GetMapping("/wxQueryByUserId")
    Result<List<IndemnityVO>> wxQueryByUserId(@RequestParam(value = "wxUserId") Integer wxUserId, @RequestParam(value = "status", required = false) Integer status);

    @ApiOperation(value = "工单详情查询", notes = "工单详情查询")
    @GetMapping("/wxQueryByWorkOrderId")
    Result<IndemnityDetailVO> wxQueryByWorkOrderId(@RequestParam(value = "workOrderId") Long workOrderId);

    @ApiOperation(value = "工单添加回复内容和附件", notes = "工单添加回复内容和附件")
    @PostMapping("/wxAddRecord")
    Result wxAddRecord(@RequestBody  AddRecordDTO dto);

    @ApiOperation(value = "工单添加评价（满意度）", notes = "工单添加评价（满意度）")
    @PostMapping("/wxAddEvaluate")
    Result wxAddEvaluate(@RequestBody AddRecordDTO dto);
}
