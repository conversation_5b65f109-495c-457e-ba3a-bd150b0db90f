package com.yl.applets.feign;


import com.yl.applets.feign.dto.AlipayIncentivecodeOperationDTO;
import com.yl.applets.feign.fallback.EdiFeignClientFallBack;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(
        name = "ccm-pay-inner",
        path = "/ccm-pay-inner",
//        url = "http://***********:32728",
        fallback = EdiFeignClientFallBack.class
)
public interface EdiFeignClient {

    @RequestMapping(value = "/api/alipay/codePin",method = RequestMethod.POST)
    Result<Boolean> codePin(@RequestBody AlipayIncentivecodeOperationDTO dto);



}
