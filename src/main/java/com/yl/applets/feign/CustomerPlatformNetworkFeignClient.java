package com.yl.applets.feign;

import com.yl.applets.entity.lmdm.SysNetwork;
import com.yl.applets.vo.BasicAreaVO;
import com.yl.applets.vo.CpAreaByNetworkCodeVO;
import com.yl.applets.vo.GetProxyAndfranchiseeVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/17 14:27
 * @description
 */
@FeignClient(
        name = "ylnetworkapi",
        path = "/networkapi/web/customerplatform"
//        url = "http://************:30580"
)
@Component
public interface CustomerPlatformNetworkFeignClient {

    @PostMapping("/network/getProxyAndfranchiseeById")
    Result<List<GetProxyAndfranchiseeVO>> getProxyAndfranchiseeById(@RequestBody List<Long> networkIds);

    @PostMapping("/network/getAreaByNetworkCode")
    Result<List<CpAreaByNetworkCodeVO>> getAreaByNetworkCode(@RequestBody List<String> codes);

    @PostMapping("/staff/getAreaByAreaIds")
    Result<List<BasicAreaVO>> getAreaByAreaIds(@RequestBody List<Long> networkIds);


    @PostMapping("/network/getByIds")
    Result<List<SysNetwork>> getByIds(@RequestBody List<Integer> ids);

}
