/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: EsGeoFeignClient
 * Author:   luhong
 * Date:     2020-11-30 16:22
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.feign;

import com.yl.applets.dto.EsSearchDTO;
import com.yl.applets.vo.EsSearchResult;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-11-30
 * @since 1.0.0
 */
@FeignClient(name = "ylesapi", path = "/esapi/geo")
public interface EsGeoFeignClient {

    @PostMapping({"/distanceSortSearch"})
    Result<EsSearchResult> distanceSort(@RequestBody EsSearchDTO var1);

}