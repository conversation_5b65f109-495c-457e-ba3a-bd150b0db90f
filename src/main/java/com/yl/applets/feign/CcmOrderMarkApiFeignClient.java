package com.yl.applets.feign;

import com.yl.applets.feign.fallback.CcmOrderMarkApiFeignFallBack;
import com.yl.applets.vo.OrderMarkCodeVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/3/26 17:57
 */

@FeignClient(name = "yl-jms-ccm-order-mark-api",
        path = "/order-mark-api",fallbackFactory = CcmOrderMarkApiFeignFallBack.class)
public interface CcmOrderMarkApiFeignClient {

    @PostMapping("/orderMarkExpandQuery/queryMarkCodes")
    Result<List<OrderMarkCodeVO>> queryMarkCodes(@RequestBody List<Long> orderIds);


}
