/**
 * Copyright (C), 2015-2021, 云路供应链科技有限公司
 * FileName: OssApi
 * Author:   luhong
 * Date:     2021-01-08 9:11
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.feign;

import com.yl.applets.dto.OssUrlSignDTO;
import com.yl.applets.vo.OssUrlSignVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 〈一句话功能简述〉<br> 
 * 〈Oss文件上传〉
 *
 * <AUTHOR>
 * @create 2021-01-08
 * @since 1.0.0
 */
public interface OssApi {

    Result<List<String>> getDownloadSignedUrl(@RequestBody List<String> paths);

    Result<Map<String, String>> getDownloadSignedUrlDetail(@RequestBody List<String> paths);

    Result<List<OssUrlSignVO>> getUploadSignedUrl(@RequestBody @Valid List<OssUrlSignDTO> list);

    Result<String> upload(@RequestParam("projectName") @NotBlank(message = "项目名不能为空") String projectName,
                          @RequestParam("moduleName") @NotBlank(message = "模块名不能为空") String moduleName,
                          @RequestPart @NotNull(message = "文件不能为空") MultipartFile file);

    Result<String> uploadFile(@RequestParam("projectName") @NotBlank(message = "项目名不能为空") String projectName,
                          @RequestParam("moduleName") @NotBlank(message = "模块名不能为空") String moduleName,
                          @RequestPart @NotNull(message = "文件不能为空") MultipartFile file);
}
