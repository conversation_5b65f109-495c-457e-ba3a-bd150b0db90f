package com.yl.applets.feign;

import com.yl.applets.feign.fallback.OmsMiniGateWayFeignFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-08-06 20:47
 * @Author: <a href="<EMAIL>">z<PERSON><PERSON><PERSON><PERSON></a>
 */
@Component
@FeignClient(name = "ylordera<PERSON>",path = "/orderapi/omsMini", fallback = OmsMiniGateWayFeignFallback.class)
public interface OmsMiniGateWayFeign extends OmsMiniClient {
}
