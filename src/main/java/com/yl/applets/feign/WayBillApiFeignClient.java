package com.yl.applets.feign;


import com.yl.applets.dto.*;
import com.yl.applets.entity.WxUser;
import com.yl.applets.feign.fallback.ChannelApiFeignClientFallBack;
import com.yl.applets.feign.fallback.WaybillApiFeignClientFallBack;
import com.yl.applets.vo.*;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(
        name = "ylwaybillouterapi",
        path = "/waybillouterapi",
//        url = "http://localhost:8080",
        fallback = WaybillApiFeignClientFallBack.class
)
public interface WayBillApiFeignClient {

    @RequestMapping(value = "/order/getOrderCostByWaybillNo",method = RequestMethod.POST)
    Result<WaybillInfoDto> getOrderCostByWaybillNo(@RequestBody WaybillQueryDto dto);


}
