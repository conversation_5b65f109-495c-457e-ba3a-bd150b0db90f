package com.yl.applets.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.*;
import com.yl.applets.feign.fallback.ReceivingPreferencesApiClientFallBack;
import com.yl.applets.vo.OrderMarkPhoneVO;
import com.yl.applets.vo.OrderMarkingVo;
import com.yl.applets.vo.OrderRecvPrefVO;
import com.yl.applets.vo.RecvPrefConfigVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2024-04-16
 */

@FeignClient(
        name = "yljmschannelapi",
        path = "/channelapi",
//        url = "http://127.0.0.1:8080",
        fallbackFactory = ReceivingPreferencesApiClientFallBack.class
//        ,url = ""
)
public interface ReceivingPreferencesApiClient {


    /**
     * 查询订单 打标(回显)
     * @param orderId
     * @return
     */
    @GetMapping("/order/preferences/resource")
    public Result<List<OrderMarkingVo>> getResource(@RequestParam("orderId")String orderId);

    @PostMapping("/order/preferences/page")
    public Result<Page<RecvPrefConfigVO>> preferencesPage(@RequestBody PreferencesQueryDto dto);



    @PostMapping("/order/preferences/add")
    public Result<Boolean> preferencesAdd(@RequestBody PreferencesAddDto dto);



    @PostMapping("/order/preferences/update")
    public Result<Boolean> preferencesUpdate(@RequestBody PreferencesUpdateDto dto);


    @GetMapping("/order/preferences/delete")
    public Result<Boolean> preferencesDelete(@RequestParam("id") String id,@RequestParam("mobile") String mobile,@RequestParam("source") String source);


    /**
     * 派前电联
     * @param dto
     * @return
     */
    @PostMapping("/order/preferences/mark/phone/save")
    public Result<Void> save(@RequestBody OrderMarkPhoneSaveDTO dto);


    /**
     * 派前电联-详情
     */
    @PostMapping(value = "/order/preferences/mark/phone/detail")
    public Result<OrderMarkPhoneVO> detail(@RequestBody OrderMarkPhoneQueryDTO dto);


    /**
     * 查询打标
     * @param orderId
     * @return
     */
    @GetMapping("/order/preferences/queryByOrderId")
    public Result<OrderRecvPrefVO> queryByOrderId(@RequestParam(name = "orderId") String orderId);


    /**
     * 订单打标
     */
    @PostMapping(value = "/order/preferences/accept")
    public Result<Void> accept(@RequestBody @Valid OrderMarkingDTO dto);


    /**
     *
     * @param dto
     * @return
     */
    @PostMapping("/order/preferences/userSelf")
    public Result<Boolean> userSelf(@RequestBody OrderUserQueryDto dto);


    /**
     *
     * @param dto
     * @return
     */
    @PostMapping("/order/preferences/userSelfOrder")
    public Result<Boolean> userSelfOrder(@RequestBody OrderUserQueryDto dto);

}
