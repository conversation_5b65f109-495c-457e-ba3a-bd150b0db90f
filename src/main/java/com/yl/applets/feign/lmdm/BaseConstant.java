package com.yl.applets.feign.lmdm;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-08-17 15:41 <br>
 * @Author: zhoujw
 */

public class BaseConstant {
    public static final String FEIGN_CLIENT_PATH = "ylnetworkapi";
    private static final String APP_FCN = "/networkapi/app";
    private static final String WEB_FCN = "/networkapi/web";
    private static final String THIRD_FCN = "/networkapi/third";

    /*****************************WEB****************************/
    //企业银行定时任务
    public static final String PLATFORM_IBKSCHEDULE = WEB_FCN + "/ibkschedule";
    //企业银行
    public static final String PLATFORM_IBK = WEB_FCN + "/ibk";
    //物料平台
    public static final String PLATFORM_MSDM = WEB_FCN + "/msdm";
    //权限平台
    public static final String PLATFORM_OAUTH = WEB_FCN + "/oauth";
    //单证模块
    public static final String PLATFORM_OMS = WEB_FCN + "/oms";
    //操作模块
    public static final String PLATFORM_OPS = WEB_FCN + "/ops";
    //内部结算账单
    public static final String PLATFORM_SPMIBILL = WEB_FCN + "/spmibill";
    //内部结算报价
    public static final String PLATFORM_SPMI = WEB_FCN + "/spmi";
    //客户结算报价
    public static final String PLATFORM_SPMO = WEB_FCN + "/spmo";
    public static final String PLATFORM_SPMN = WEB_FCN + "/spmn";
    //第三方平台
    public static final String PLATFORM_THIRD = WEB_FCN + "/third";
    //通用edi
    public static final String PLATFORM_GENERAL_EDI = WEB_FCN + "/general/edi";
    //网点经营
    public static final String PLATFORM_NM = WEB_FCN + "/networkmanagement";
    //经营指标
    public static final String PLATFORM_BUSINESS_INDICATOR = WEB_FCN + "/businessindicator";
    //客户平台
    public static final String PLATFORM_CUSTOMER_PLATFORM = WEB_FCN + "/customerplatform";
    //订单模块
    public static final String PLATFORM_ORDER = WEB_FCN + "/order";
    //公告平台
    public static final String PLATFORM_NOTICE = WEB_FCN + "/notice";
    //短信平台
    public static final String PLATFORM_SMS = WEB_FCN + "/sms";
    //消息平台
    public static final String PLATFORM_SCM = WEB_FCN + "/scm";
    //智能设备
    public static final String PLATFORM_SMART_DEVICE = WEB_FCN + "/smartdevice";
    //isv
    public static final String PLATFORM_ISV = WEB_FCN + "/isv";
    //运力
    public static final String PLATFORM_TMS = WEB_FCN + "/tms";
    //服务质量
    public static final String PLATFORM_SQ = WEB_FCN + "/servicequality";
    //edi
    public static final String PLATFORM_EDI = WEB_FCN + "/edi";
    //VIP
    public static final String PLATFORM_VIP = WEB_FCN + "/vip";
    //客服系统
    public static final String PLATFORM_CSS = WEB_FCN + "/css";
    //实名制
    public static final String PLATFORM_OCR = WEB_FCN + "/ocr";
    //开放平台
    public static final String PLATFORM_OPF = WEB_FCN + "/opf";

    //社区
    public static final String PLATFORM_COMMUNITY = WEB_FCN + "/community";




    /*****************************APP****************************/
    //巴枪APP
    public static final String PLATFORM_APPBC = APP_FCN + "/appbc";
    //delphi
    public static final String PLATFORM_DELPHI = APP_FCN + "/delphi";
    //小程序
    public static final String PLATFORM_APPLETS = APP_FCN + "/networkapi/app/applets";



    /*****************************THIRD****************************/
    //丰巢
    public static final String PLATFORM_FCBOX = THIRD_FCN + "/fcbox";
    //循环贷
    public static final String PLATFORM_LOOPLOAN = THIRD_FCN + "/looploan";


}
