//package com.yl.applets.feign;
//
//import com.yl.applets.dto.lmdm.CustomerCardCheckDTO;
//import com.yl.applets.feign.fallback.AppletsNetworkFeignClientFallBack;
//import com.yl.applets.vo.SpmiFranchiseeNetworkVO;
//import com.yl.common.base.model.vo.Result;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.stereotype.Component;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestParam;
//
///**
// * 客户资料api
// *
// * <AUTHOR>
// */
//@Component
//@FeignClient(
//        name = "ylnetworkapi",
////        path = "/networkapi/app/applets/customer",
////        url="",
//        fallback = AppletsNetworkFeignClientFallBack.class)
//public interface AppletsCustomerFeignClient {
//
//
//    @GetMapping("/networkapi/web/spmi/network/getFranchiseeByCode")
//    @ApiOperation(value = "根据网点code查询加盟商")
//    Result<SpmiFranchiseeNetworkVO> getFranchiseeByCode(@RequestParam("networkCode") String networkCode);
//
//      /**
//     * 检查证件号码
//     * @param dto
//     * @return
//       *
//     */
//    @PostMapping("/networkapi/app/applets/customer/checkCardNo")
//    @ApiOperation(value = "检查证件号码")
//    Result<Boolean> checkCardNo(@RequestBody CustomerCardCheckDTO dto);
//
//
//    /**
//     * 获取禁寄限类目是否符合要求
//     */
//    @GetMapping("/networkapi/app/applets/sysForbidden/getSysForbiddenInfo")
//    Result<Boolean> getSysForbiddenInfo(@RequestParam String contents);
//
//
//
//}
