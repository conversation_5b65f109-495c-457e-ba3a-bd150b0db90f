package com.yl.applets.feign;

import com.yl.applets.dto.SpmApiInsuranceTrialDTO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;

@Component
@FeignClient(
        name = "ylspmoapi",
        path = "/spmoapi/spmInsuranceFee"
)
public interface SpmoBusinessInsuranceFeeFeignClient {

    /**
     * 保价费计算
     * @param spmApiInsuranceTrialDTO
     * @return
     */
    @PostMapping({"/comCost"})
    Result<BigDecimal> computationCost(@RequestBody SpmApiInsuranceTrialDTO spmApiInsuranceTrialDTO);
}
