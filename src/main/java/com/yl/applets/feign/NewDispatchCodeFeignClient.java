package com.yl.applets.feign;

import com.yl.applets.dto.DispatchCodeFetchNetworkIdQuery;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/6 18:27
 * @description
 */
@Component
@FeignClient(name = "ylassapi",
        //测试环境的url，本地测试的时候可以打开。
//        url = "http://***********:30264",
        path = "/assapi")
public interface NewDispatchCodeFeignClient {
    /**
     * key  uniqueSequence(请求入参唯一标识),
     * value  派件网点ID或取件网点ID
     */
    @PostMapping("/dispatchcode/queryNetworkIds")
    Result<Map<String, Object>> fetchNetworkIds(@RequestBody @Validated List<DispatchCodeFetchNetworkIdQuery> queryList);
}
