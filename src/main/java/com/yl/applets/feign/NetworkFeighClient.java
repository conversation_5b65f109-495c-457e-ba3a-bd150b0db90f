package com.yl.applets.feign;

import com.yl.applets.feign.fallback.NetworkFeighClientFallBack;
import com.yl.applets.vo.OrderExceptionNetworkVO;
import com.yl.common.base.model.vo.Result;
import io.lettuce.core.dynamic.annotation.Param;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @version 1.0
 * @Description: TODO
 * @date 2021-08-09 15:52
 */
@Component
@FeignClient(
        name = "ylorderapi",
      //  url = "http://*************:30278",
        path = "/orderapi/exceptionNetwork",
        fallback = NetworkFeighClientFallBack.class)
public interface NetworkFeighClient {

    /**
     * 查询异常网点，没有返回null
     * @param networkCode
     * @return
     */
    @GetMapping(value = "/invalidNetwork")
    @ApiOperation(value = "查询异常网点", notes = "查询异常网点")
    Result<OrderExceptionNetworkVO>  getNetworkError(@RequestParam String  networkCode);
}
