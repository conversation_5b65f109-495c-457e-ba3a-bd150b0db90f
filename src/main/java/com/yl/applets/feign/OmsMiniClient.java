package com.yl.applets.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.ThirdExpressApiDTO;
import com.yl.applets.vo.ThirdExpressListVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br>
 * @Project: <br>
 * @CreateDate: Created in 2019-09-21 14:11 <br>
 * @Author: <a href="<EMAIL>">qiang.hu</a>
 */

public interface OmsMiniClient {

    /**
     * 删除我的快递
     *
     * @param orderId
     * @return
     **/
    @PostMapping(value = "/delete")
    Result<Boolean> delete(@RequestParam("orderId") @NotNull(message = "订单编号不能为空") Long orderId);

    /**
     * 分页查询我的寄收快件
     *
     * @param queryDto
     * @return
     */
    @PostMapping(value = "/myExpress")
    Result<Page<ThirdExpressListVO>> getMyExpressPage(@RequestBody ThirdExpressApiDTO queryDto);

    /**
     * 查询我的寄收快件数量
     *
     * @param queryDto
     * @return
     */
    @PostMapping(value = "/myExpressCount")
    Result<Map<String, Long>> getMyExpressCnt(@RequestBody ThirdExpressApiDTO queryDto);

}
