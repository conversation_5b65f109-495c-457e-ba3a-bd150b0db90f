package com.yl.applets.feign;

import com.yl.applets.dto.SpmApiTrialDTO;
import com.yl.applets.vo.SpmCommonCostVO;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@Component
@FeignClient(
        name = "ylspmoapi",
        path = "/spmoapi/spmStandardShippingQuote"
)
public interface SpmoStandardShippingQuoteFeignClient {

    @PostMapping({"/business/comCostAndWeight"})
    @ApiOperation(
            value = "计算费用与结算重量",
            notes = "计算费用与结算重量"
    )
    Result<SpmCommonCostVO> comCostAndWeight(@RequestBody SpmApiTrialDTO spmApiTrialDTO);

}
