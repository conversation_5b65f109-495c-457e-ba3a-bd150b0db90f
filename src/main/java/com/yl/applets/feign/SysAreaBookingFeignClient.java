//package com.yl.applets.feign;
//
//import com.yl.applets.dto.SysAreaBookingDto;
//import com.yl.applets.feign.fallback.SysAreaBookingFeignClientFallBack;
//import com.yl.applets.feign.lmdm.BaseConstant;
//import com.yl.applets.vo.SysAreaBookingVo;
//import com.yl.common.base.model.vo.Result;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.stereotype.Component;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//
///**
// * 云路供应链科技有限公司 版权所有 © Copyright 2020
// *
// * <AUTHOR>
// * @version 1.0
// * @Description: 省市区可预约时间段接口
// * @date 2021-07-15 10:43
// */
//@Component
//@FeignClient(
//        name = "ylnetworkapi",
//        //url = "http://localhost:8369",
//        path = "/networkapi" ,
//        fallback = SysAreaBookingFeignClientFallBack.class)
//public interface SysAreaBookingFeignClient {
//
//    @PostMapping({"/app/applets/booking/address/pickTime"})
//    @ApiOperation(value = "返回省市区可预约时间段", notes = "返回省市区可预约时间段")
//    Result<SysAreaBookingVo> pickTime(@RequestBody SysAreaBookingDto sysAreaBookingDto );
//}
