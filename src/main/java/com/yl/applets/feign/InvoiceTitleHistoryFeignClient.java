package com.yl.applets.feign;

import com.yl.applets.dto.InvoiceRecordHistoryDTO;
import com.yl.applets.vo.InvoiceTitleHistoryVO;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2020-05-18 10:46
 */
@Component
@FeignClient(name = "ylinvoiceapi",
        path = "/invoiceapi/invoiceTitleHistory",
        fallbackFactory = InvoiceTitleHistoryFallBackFactory.class)
public interface InvoiceTitleHistoryFeignClient {

    @PostMapping("/findListByCustomer")
    @ApiOperation(value = "客户开票抬头历史记录查询", notes = "客户开票抬头历史记录查询")
    Result<List<InvoiceTitleHistoryVO>> findListByCustomer(@RequestBody InvoiceRecordHistoryDTO dto);
}
