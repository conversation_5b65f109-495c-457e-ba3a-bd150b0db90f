package com.yl.applets.feign;

import com.yl.applets.feign.dto.SpmApiCustomerShippingQuoteTryCalcDTO;
import com.yl.applets.feign.fallback.SpmoCustomerShippingQuoteFeignFallback;
import com.yl.applets.vo.CiticSpmCommonCostVO;
import com.yl.applets.vo.SpmCommonCostVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * @Description: <br> 客户运费报价 controller
 * @Project: <br> 结算管理平台
 * @CreateDate: Created in 2019/08/13 10:11 <br>
 * @Author: <a href="<EMAIL>">huangliang</a>
 */
@Component
@FeignClient(name = "ylspmoapi", path = "/spmoapi/spmCustomerShippingQuote", fallback = SpmoCustomerShippingQuoteFeignFallback.class)
public interface SpmoCustomerShippingQuoteFeignClient {

    /**
     * 计算客户运费与结算重量
     *
     * @param dto
     * @return
     */
    @PostMapping("/comCostAndWeight")
    Result<CiticSpmCommonCostVO> comCostAndWeight(@RequestBody SpmApiCustomerShippingQuoteTryCalcDTO dto);


}
