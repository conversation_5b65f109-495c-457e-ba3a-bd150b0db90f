package com.yl.applets.feign;

import com.yl.applets.vo.OmsWaybillDetailVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface OmsWaybillClient {

    @GetMapping({"/waybillDetailByNo"})
    Result<OmsWaybillDetailVO> getDetailByNo(@RequestParam("waybillNo") String waybillNo);

    @GetMapping({"/detailByNo"})
    Result<OmsWaybillDetailVO> detailByNo(@RequestParam("waybillNo") String waybillNo, @RequestParam("pickNetworkId") Integer pickNetworkId);


}
