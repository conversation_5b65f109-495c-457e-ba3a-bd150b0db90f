package com.yl.applets.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.InvoiceApplyDTO;
import com.yl.applets.dto.InvoiceRecordHistoryDTO;
import com.yl.applets.vo.InvoiceRecordHistoryVO;
import com.yl.applets.vo.InvoiceUrlVO;
import com.yl.applets.vo.InvoicingApplyVO;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2020-05-18 13:40
 */
@Component
@FeignClient(name = "ylinvoiceapi",
        path = "/invoiceapi/invoiceRecord",
        fallbackFactory = InvoiceRecordFallBackFactory.class)
public interface InvoiceRecordFeignClient {

    @GetMapping("/detailForOut")
    @ApiOperation(value = "小程序-开票详情", notes = "小程序-开票详情")
    Result<InvoiceRecordHistoryVO> detailForOut(@RequestParam("applyNo") String applyNo);

    @GetMapping("/findUrlById")
    @ApiOperation(value = "小程序-开票PDF、图片查询下载接口", notes = "小程序-开票PDF、图片查询下载接口")
    Result<InvoiceUrlVO> findUrlById(@RequestParam("applyNo") String applyNo);

    @GetMapping("/getInvoiceAbleWaybillNos")
    @ApiOperation(value = "小程序-获取可开票运单号", notes = "小程序-获取可开票运单号")
    Result<List<String>> getInvoiceAbleWaybillNos(@RequestParam("waybillNos") List<String> waybillNos);

    @PostMapping("/apply")
    @ApiOperation(value = "小程序-开票申请", notes = "小程序-开票申请")
    Result<List<InvoicingApplyVO>> apply(@RequestBody InvoiceApplyDTO applyDTO);

    @PostMapping("/invoiceRecordHistory")
    @ApiOperation(value = "小程序-开票历史查询", notes = "小程序-开票历史查询")
    Result<Page<InvoiceRecordHistoryVO>> invoiceRecordHistory(@RequestBody InvoiceRecordHistoryDTO dto);

    @PostMapping("/failure")
    @ApiOperation(value = "小程序-冲销申请", notes = "小程序-冲销申请")
    Result<Boolean> failure(@RequestParam("applyNo") String applyNo);

    @PostMapping("/failureBatch")
    @ApiOperation(value = "小程序-批量冲销申请", notes = "小程序-批量冲销申请")
    Result<Boolean> failureBatch(@RequestParam("applyNos") List<String> applyNos);
}
