/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: OpsPodTrackingClient
 * Author:   luhong
 * Date:     2020-10-14 16:46
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.feign;

import com.yl.applets.dto.PodTrackingQueryDto;
import com.yl.applets.vo.PodTrackingListVO;
import com.yl.applets.vo.ops.PodTrackingVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-10-14
 * @since 1.0.0
 */
@Component
@FeignClient(
        name = "ylopsapi",
        path = "/opsapi/opsPodTracking"
)
public interface OpsPodTrackingClient {

    @PostMapping({"/query/customer/inner"})
    Result<List<PodTrackingListVO>> innerPodTrackingList(@RequestBody PodTrackingQueryDto dto);

    @GetMapping({"query/latest"})
    Result<PodTrackingVO> getLatest(@RequestParam String waybillId);

}