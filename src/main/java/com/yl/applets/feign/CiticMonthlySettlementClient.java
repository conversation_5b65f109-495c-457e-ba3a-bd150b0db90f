package com.yl.applets.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.feign.dto.citicmonthlysettlement.*;
import com.yl.applets.feign.fallback.CiticMonthlySettlementClienntFallBack;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/12/12 10:42
 */
@FeignClient(
        name = "yljmschannelapi",
        path = "/channelapi",
        fallback = CiticMonthlySettlementClienntFallBack.class
)
public interface CiticMonthlySettlementClient {

    /**
     * 绑定月结账号信息
     * @param dto
     * @return
     */
    @PostMapping("/citic/monthlySettlement/bind/info")
    Result<Integer> bindInfo(@RequestBody CiticMonthlySettlementDTO dto);


    /**
     * 解除绑定
     * @param dto
     * @return
     */
     @PostMapping("/citic/monthlySettlement/unbind/info")
     Result<Integer> unBindInfo( @RequestBody CiticMonthlySettlementUpdateDTO dto);

    /**
     * 修改默认账号
     * @param dto
     * @return
     */
    @PostMapping("/citic/monthlySettlement/update/default/info")
    Result<Integer> updateDefaultAccount(@Validated @RequestBody CiticMonthlySettlementUpdateDTO dto);

    /**
     * 查询当前用户下的月付账号列表
     * @param memberId
     * @return
     */
    @PostMapping("/citic/monthlySettlement/getMonthlySettlement/list")
    Result<Page<CiticMonthlySettlementResultDTO>> getMonthlySettlementList(@RequestBody CiticMonthlySettlementQueryDTO dto);

    /**
     * 判断兔优达
     * @param account
     * @return
     */
    @GetMapping("/citic/monthlySettlement/judge/rabbitDelivery")
    Result<Boolean> judgeRabbitDelivery(@RequestParam("account") String account);


    /**
     * 判断账号是否可用
     * @param account
     * @return
     */
    @GetMapping("/citic/monthlySettlement/judge/account")
    Result<Boolean> judgeAccount(@RequestParam("account") String account);


    /**
     * 查询共享客户信息
     * @param account
     * @return
     */
    @GetMapping("/citic/monthlySettlement/find/share/info")
    Result<MonthlySettlementShareCustomerDTO> findShareCustomer(@RequestParam("account")String account);


    /**
     * 查询共享客户信息根据客户code
     * @param customerCode
     * @return
     */
    @GetMapping("/citic/monthlySettlement/find/share/code")
    Result<MonthlySettlementShareCustomerDTO> findShareCustomerByCode(@RequestParam("customerCode")String customerCode);



    /**
     * 查询默认客户信息
     * @param memberId
     * @return
     */
    @GetMapping("/citic/monthlySettlement/find/default/info")
    Result<CiticMonthlySettlementResultDTO> findDefaultAccount(@RequestParam("memberId")String memberId);


    /**
     * 查询客户信息通过客户code
     * @param customerCode
     * @return
     */
    @GetMapping("/citic/monthlySettlement/find/info/by/cusCode")
    Result<CiticMonthlySettlementResultDTO> findAccountByCusCode(@RequestParam("memberId")String memberId,@RequestParam("customerCode")String customerCode);

    /**
     * 查询客户信息完整
     * @param account
     * @return
     */
    @GetMapping("/citic/monthlySettlement/find/cus/info")
    Result<MonthlySettlementCustomerDTO> findCustomerAccount(@RequestParam("account")String account);

}
