package com.yl.applets.feign;


import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "yl-jms-ops-outer-read-api",path = "/ops/outer/read/")
public interface OpsOuterFeigntClient {


    @GetMapping({"/signingScanList/getEleSignPicUrl"})
    Result<String> getEleSignPicUrl(@RequestParam("waybillNo") String waybillNo);

}
