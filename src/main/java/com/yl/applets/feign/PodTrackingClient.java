package com.yl.applets.feign;

import com.yl.applets.vo.PodTrackingListVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2019<br>
 *
 * <AUTHOR>
 * @date ：Created in 2020-05-12 16:36
 * @description：
 * @modified By：
 * @version: $version$
 */
@FeignClient(name = "yl-jms-ops-pod-api",path = "/ops/pod/opsPodTracking")
public interface PodTrackingClient {

    @PostMapping("/outer/keywordList")
    Result<List<PodTrackingListVO>> outerLeywordList(String []waybillNo);
}
