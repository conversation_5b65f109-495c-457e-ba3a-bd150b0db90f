package com.yl.applets.feign;

import com.yl.applets.vo.VIPLoginVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(name = "yljmschannelapi", path = "/channelapi")
public interface VIPSystemFeigntClient {

    @GetMapping("/api/vip/getAccountPwd")
    Result<VIPLoginVO> getAccountPwd(@RequestParam("account") String account) ;

}
