package com.yl.applets.feign;

import com.yl.applets.dto.IpayLooseOrderWXDTO;
import com.yl.applets.dto.IpayWxPlaceOrderDTO;
import com.yl.applets.feign.fallback.IPayFeignClientFallback;
import com.yl.applets.vo.IpayLooseTokenVO;
import com.yl.applets.vo.IpayTokenVO;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-08-18
 */
@FeignClient(
        name = "ylipayapi",
        path = "/ipayapi",
//          url = "http://***********:10811/ipayapi/ipayWxOrder",
//        configuration = IbkApiFeignConfiguration.class,
        fallbackFactory = IPayFeignClientFallback.class)
public interface PayFeignClient {
    //旧
    @PostMapping("/ipayWxOrder/placeOrder")
    Result<String> placeOrder(@RequestBody IpayWxPlaceOrderDTO ipayWxPlaceOrderDTO);

    //新
    @PostMapping("/ipayLooseOrder/pay/wx")
    Result<String> placeOrder(@RequestBody IpayLooseOrderWXDTO ipayWxPlaceOrderDTO);

    @GetMapping("/ipayLooseOrder/pay/getToken")
    @ApiOperation(value = "微信支付getToken", notes = "微信支付getToken")
    Result<IpayLooseTokenVO> getToken(@RequestParam String body ,@RequestParam String appid);


    @PostMapping("/ipayLooseOrder/pay/ums_mini/wx")
    Result<String> umsMiniPrePayId(@RequestBody IpayLooseOrderWXDTO ipayWxPlaceOrderDTO);

}
