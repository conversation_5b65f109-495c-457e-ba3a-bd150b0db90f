package com.yl.applets.feign;

import com.yl.applets.dto.lmdm.SysStaffDTO;
import com.yl.applets.feign.fallback.StaffFeignClientFallback;
import com.yl.applets.vo.lmdm.SysStaffVO;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * staffapi Feign接口定义
 */
@FeignClient(name = "ylstaffapi",
        path = "/staffapi",
        fallback = StaffFeignClientFallback.class)
public interface NewStaffFeignClient {

    /**************************************************************
     *
     * staff
     *
     **************************************************************/
    @PostMapping("/web/customerplatform/staff/detail")
    @ApiOperation(value = "查询员工详情", notes = "查询员工详情--xq")
    @ApiImplicitParam(name = "id", value = "员工id", dataType = "Integer")
    Result<SysStaffVO> getStaffDetail(@RequestBody SysStaffDTO var1);
}
