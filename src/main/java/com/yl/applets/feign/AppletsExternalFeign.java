package com.yl.applets.feign;

import com.yl.applets.constant.BaseConstant;
import com.yl.applets.feign.fallback.AppletsExternalFallBack;
import com.yl.applets.vo.CheckWxPhoneVO;
import com.yl.common.base.model.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 功能描述:
 * 小程序对外接口feign
 * @since: 1.0.0
 * @Author:wwx
 * @Date: 2021-08-12 11:10
 */
@Component
@FeignClient(
        name = BaseConstant.APPLETS_FEIGN_CLIENT_NAME,
        path = BaseConstant.APPLETS_FEIGN_CLIENT_PATH + "/external",
        fallback = AppletsExternalFallBack.class)
public interface AppletsExternalFeign {


    /**
     * 通过手机号调用微信校验是否绑定
     * @param phone
     * @return
     */
    @PostMapping("/checkPhoneIsBind")
    Result<CheckWxPhoneVO> checkPhoneIsBind(@RequestParam("phone") String phone);



}
