package com.yl.applets.feign;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.*;
import com.yl.applets.dto.neiborhood.*;
import com.yl.applets.valiate.Feign;
import com.yl.common.base.model.vo.NeighborhoodResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "yl-tmp-mail-service-api",
        path = "/tmp-mail-service-api" + "/miniProgram",
//        url = "***********:30616",
        fallback = MailMiniProgramFeignClientFallBack.class)
@Component
public interface MailMiniProgramFeignClient {
    /**
     * 附近驿站
     */
    @PostMapping("/nearbySite")
    @ApiOperation("附近驿站")
    NeighborhoodResult<List<NearbySiteVO>> nearbySite(@Validated @RequestBody NearbySiteRequest request);

    /**
     * 新增外部订单
     */
    @PostMapping("/addOrderExternal")
    @ApiOperation("新增外部订单")
    NeighborhoodResult<AddOrderExternalResponse> addOrderExternal(@Validated(Feign.class) @RequestBody AddOrderExternalRequest request);

    /**
     * 取消外部订单
     */
    @PostMapping("/cancelOrderExternal")
    @ApiOperation("取消外部订单")
    NeighborhoodResult<CancelOrderExternalResponse> cancelOrderExternal(@Validated(Feign.class) @RequestBody CancelOrderExternalRequest request);

    /**
     * 订单分页查询
     */
    @PostMapping("/externalOrderPage")
    @ApiOperation("订单分页查询")
    NeighborhoodResult<Page<ExternalOrderPageVO>> externalOrderPage(@Validated(Feign.class) @RequestBody ExternalOrderPageRequest request);

    /**
     * 订单详情
     */
    @PostMapping("/externalOrderDetail")
    @ApiOperation("订单详情")
    NeighborhoodResult<ExternalOrderDetailResponse> externalOrderDetail(@Validated(Feign.class) @RequestBody ExternalOrderDetailRequest request);

    /**
     * 删除订单
     */
    @PostMapping("/deleteOrderExternal")
    @ApiOperation("删除外部订单")
    NeighborhoodResult<?> deleteOrderExternal(@Validated(Feign.class) @RequestBody DeleteOrderExternalRequest request);
}