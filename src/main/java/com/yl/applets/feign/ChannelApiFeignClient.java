package com.yl.applets.feign;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.*;
import com.yl.applets.dto.appletsgeneralbusiness.AppletsGeneralBusinessDTO;
import com.yl.applets.entity.OmsWaybill;
import com.yl.applets.entity.WxUser;
import com.yl.applets.feign.fallback.ChannelApiFeignClientFallBack;
import com.yl.applets.vo.*;
import com.yl.applets.vo.baidu.IdCardVO;
import com.yl.common.base.model.vo.Result;
import feign.Headers;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;

@FeignClient(
        name = "yljmschannelapi",
        path = "/channelapi",
        fallback = ChannelApiFeignClientFallBack.class
)
public interface ChannelApiFeignClient {

    @RequestMapping(value = "/api/recordPrivacy/saveRecord", method = RequestMethod.POST)
    Result<Boolean> saveRecord(@RequestBody RecordPrivacyDto dto);

    /**
     *
     * @param waybillNo
     * @return
     */
    @GetMapping(value = "/waybillOrder/getDetailByNo")
    Result<AppOmsWaybillDetailVO> getDetailByNo(@RequestParam("waybillNo") String waybillNo);

    /**
     * 轨迹查询
     *
     * @param waybillNo
     * @return
     */
    @GetMapping(value = "/logisticsTracking/v2/getDetailByWaybillNo")
    Result<PodTrackingListVO> getDetailByWaybillNoV2(@RequestParam("waybillNo") String waybillNo);

    @PostMapping("/channel/order/intercept")
    Result<InterceptOrderVo> orderIntercept(@RequestBody InterceptOrderDto dto);


    @PostMapping("/api/forward")
    Result<?> forward(@RequestBody ForwardRequest request);


    @PostMapping("/channel/usercard/save")
    Result<UserCardVo> userCardSave(@RequestBody UserCardDTO dto);


    @PostMapping("/channel/usercard/applets/save")
    Result<UserCardVo> saveAppletsUserCard(@RequestBody SaveAppletsUserCardDTO dto);

    @GetMapping("/channel/usercard/infoBySecret")
    Result<UserCardVo> infoBySecret(@RequestParam("numberId") String numberId);

    @PostMapping("/channel/user/logoff")
    Result<Boolean> logoff(@RequestBody WxUserVo vo);

    @GetMapping("/channel/user/isOff")
    Result<MemberUserOffDto> isOff(@RequestParam("mobile") String mobile);

    @PostMapping("/channel/user/cannelOff")
    Result<Boolean> cannelOff(@RequestBody WxUser wxUser);

    @PostMapping("/cancel-reason/list")
    Result<List<OmsCancelReasonVO>> qryCancelReasonVO(@RequestBody CancelReasonDTO dto);

    @GetMapping("/waybillOrder/checkWayBillNo")
    Result<Boolean> checkWayBillNoExist(@RequestParam("waybillNo") String waybillNo);

    @GetMapping("/token/get")
    Result<String> getToken(@RequestParam("code") String code);

    @PostMapping(
            value = {"/ocr/resolve"},
            produces = {"application/json;charset=UTF-8"},
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE
    )
    @Headers("Content-Type: multipart/form-data")
    Result getIdCardInfo(@RequestPart("file") MultipartFile file);

    @ApiOperation("百度地图-地址解析")
    @GetMapping("/address/resolve")
    Result<AddressPCDVO> intelligentAddressRecognitionV2(@RequestParam("address") String address);

    @PostMapping(
            value = {"/ocr/foreign/resolve"},
            produces = {"application/json;charset=UTF-8"},
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE
    )
    @Headers("Content-Type: multipart/form-data")
    Result<IdCardVO> foreignIdCard(@RequestPart("file") MultipartFile file);




    @PostMapping("/order/mark/user/query")
    public Result<Boolean> orderUserQuery(@RequestBody OrderUserQueryDto dto);


    /**
     * 获取订单标识
     *
     * @return
     */
    @GetMapping("/order/mark/resource")
    public Result<List<OrderMarkingVo>> getResource();


    /**
     * 打标
     * @param dto
     * @return
     */
    @PostMapping("/order/mark/sign")
    public Result<Boolean> updateMarking(@RequestBody OrderMarkingSignDto dto);


    @PostMapping("/order/mark/signOrderId")
    public Result<Boolean> updateMarkingByOrderId(@RequestBody OrderMarkingSignDto dto);


    /**
     * 查询
     * @param queryDto
     * @return
     */
    @PostMapping("/order/mark/query")
    public Result<List<OrderMarkingVo>> queryMarking(@RequestBody OrderMarkingQueryDto queryDto);


    @GetMapping("/waybillOrder/getWaybillDetail")
    public Result<WaybillDetailVo> getWaybillDetail(@RequestParam("waybillNo") String waybillNo);


    @GetMapping("/order/mark/queryByOrderId")
    public Result<List<OrderMarkingVo>> queryMarkingByOrderId(@RequestParam("orderId") String orderId);


    @PostMapping("/order/mark/signRecord")
    public Result<Boolean> signRecord(@RequestBody List<OrderMarkingSignDto> dtos);

    @PostMapping("/waybillOut/api/detail")
    Result<List<OmsWaybill>> batchDetail(@RequestBody BatchWaybillsDTO dto);

    @PostMapping(
            value = {"/address/resolve/file"},
            produces = {"application/json;charset=UTF-8"},
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE
    )
    @Headers("Content-Type: multipart/form-data")
    Result<String> intelligentAddressRecognitionFile(@RequestPart("file") MultipartFile file);


    @GetMapping("/random/getInfoByRandomCode")
    public Result<RandomStaffVo> getInfoByRandomCode(@RequestParam("randomCode")String randomCode);

    /**
     * 一个订单的登录人手机号，寄件人手机号，收件人手机号来查询预警客户信息
     *
     * @param loginPhone   登录人手机号
     * @param sendPhone    寄件人手机号
     * @param receivePhone 收件人手机号
     * @return
     */
    @GetMapping("/warn/customer/bySingleOrder")
    Result<CustomerQueryByPhoneVO> getSingleOrderWarnCustomer(@RequestParam("loginPhone") String loginPhone,
                                                              @RequestParam("sendPhone") String sendPhone,
                                                              @RequestParam("receivePhone") String receivePhone);

    /**
     * 根据手机号批量查询预警客户信息
     * @param phones  手机号集合
     * @return
     */
    @PostMapping("/warn/customer/byBatchPhone")
    Result<List<CustomerQueryByPhoneVO>> getOrderWarnCustomers(@RequestBody Set<String> phones);

    /**
     * 我的专属快递员列表
     * @param dto
     * @return
     */
    @PostMapping("/myStaff/list")
    Result<Page<MyStaffVO>> list(@RequestBody MyStaffPageDTO dto) ;

    /**
     * 修改默认专属快递员
     * @param dto
     * @return
     */
    @PostMapping("/myStaff/update/default/staff")
     Result<String> updateDefaultStaff(@RequestBody  MyStaffUpdateDefaultDTO dto) ;


    /**
     * 检验寄件地址跟网点信息是否相同
     * @param dto
     * @return
     */
    @PostMapping("/myStaff/check/send/address")
     Result<Boolean> checkSendAddress(@RequestBody  MyStaffCheckAddressDTO dto) ;



    /**
     * 获取客户常用地址
     * @return
     */
    @GetMapping("/frequent/address/by/userId")
    Result<List<FrequentAddressVO>> getFrequentAddressByUserId(@RequestParam("userId") Integer userId) ;

    /**
     * 保存常用地址
     * @param dto
     * @return
     */
    @PostMapping("/frequent/address/save")
     Result<Void> saveFrequentAddressInfo(@RequestBody  FrequentAddressDTO dto);



    /**
     * 根据市场活动编码获取渠道
     * @param code
     * @return
     */
    @GetMapping("/marketActivity/channel/by/code")
    Result<String> getChannelByCode(@RequestParam("code") String code);

    /**
     * 根据运单号反查明文收寄件人手机号
     * @param waybillNo
     * @return
     */
    @GetMapping("/waybillOrder/getMobilePhoneByWaybillNo")
    Result<MobilePhoneVO> getMobilePhoneByWaybillNo(@RequestParam("waybillNo") String waybillNo);





    /**
     * 微信登录时开通绑定vip
     * @param dto
     * @return
     */
    @PostMapping("/user/applyMember")
    Result<String> applyMember(@RequestBody AppletsMemberDTO dto);



    /**
     * 小程序通用业务信息异步处理
     * @param dto
     * @return
     */
    @PostMapping("/api/general/business/handle")
    Result<String> appletsGeneralBusinessSave(@RequestBody AppletsGeneralBusinessDTO dto);




}
