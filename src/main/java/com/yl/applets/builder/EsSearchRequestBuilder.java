/**
 * Copyright (C), 2015-2020, 云路供应链科技有限公司
 * FileName: EsSearchRequestBuilder
 * Author:   luhong
 * Date:     2020-11-30 17:04
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.builder;

import com.yl.applets.dto.EsSearchDTO;
import com.yl.applets.enums.*;
import org.apache.http.util.Asserts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2020-11-30
 * @since 1.0.0
 */
public class EsSearchRequestBuilder {
    private static final Logger log = LoggerFactory.getLogger(EsSearchRequestBuilder.class);
    private EsSearchDTO esSearchDTO;

    private EsSearchRequestBuilder(EsSearchDTO esSearchDTO) {
        this.esSearchDTO = esSearchDTO;
    }

    public static EsSearchRequestBuilder requestBuilder(String tableName) {
        EsSearchDTO esSearchDTO = new EsSearchDTO(tableName, (Integer)null, (Integer)null);
        EsSearchRequestBuilder esSearchRequestBuilder = new EsSearchRequestBuilder(esSearchDTO);
        return esSearchRequestBuilder;
    }

    public static EsSearchRequestBuilder requestBuilder(String tableName, Integer page, Integer size) {
        EsSearchDTO esSearchDTO = new EsSearchDTO(tableName, page, size);
        EsSearchRequestBuilder esSearchRequestBuilder = new EsSearchRequestBuilder(esSearchDTO);
        return esSearchRequestBuilder;
    }

    public static EsSearchRequestBuilder requestBuilder(String tableName, Integer page, Integer size, SearchUnitConnectorEnum connectorEnum) {
        EsSearchDTO esSearchDTO = new EsSearchDTO(tableName, page, size, connectorEnum);
        EsSearchRequestBuilder esSearchRequestBuilder = new EsSearchRequestBuilder(esSearchDTO);
        return esSearchRequestBuilder;
    }

    public static EsSearchRequestBuilder requestBuilder(String tableName, SearchUnitConnectorEnum connectorEnum) {
        EsSearchDTO esSearchDTO = new EsSearchDTO(tableName, (Integer)null, (Integer)null, connectorEnum, (String[])null);
        EsSearchRequestBuilder requestBuilder = new EsSearchRequestBuilder(esSearchDTO);
        return requestBuilder;
    }

    public static EsSearchRequestBuilder requestBuilder(String tableName, SearchUnitConnectorEnum connectorEnum, String[] shardingValues) {
        EsSearchDTO esSearchDTO = new EsSearchDTO(tableName, (Integer)null, (Integer)null, connectorEnum, shardingValues);
        EsSearchRequestBuilder requestBuilder = new EsSearchRequestBuilder(esSearchDTO);
        return requestBuilder;
    }

    public static EsSearchRequestBuilder requestBuilder(String tableName, Integer page, Integer size, SearchUnitConnectorEnum connectorEnum, String[] shardingValues) {
        EsSearchDTO esSearchDTO = new EsSearchDTO(tableName, page, size, connectorEnum, shardingValues);
        EsSearchRequestBuilder requestBuilder = new EsSearchRequestBuilder(esSearchDTO);
        return requestBuilder;
    }

    public static EsSearchRequestBuilder requestBuilder(String tableName, Integer page, Integer size, SearchUnitConnectorEnum connectorEnum, String[] shardingValues, String... includeFields) {
        EsSearchDTO esSearchDTO = (new EsSearchDTO(tableName, page, size, connectorEnum, shardingValues)).includeFields(includeFields);
        EsSearchRequestBuilder requestBuilder = new EsSearchRequestBuilder(esSearchDTO);
        return requestBuilder;
    }

    public static EsSearchRequestBuilder requestBuilder(String tableName, SearchUnitConnectorEnum connectorEnum, String[] shardingValues, String... includeFields) {
        EsSearchDTO esSearchDTO = (new EsSearchDTO(tableName, (Integer)null, (Integer)null, connectorEnum, shardingValues)).includeFields(includeFields);
        EsSearchRequestBuilder requestBuilder = new EsSearchRequestBuilder(esSearchDTO);
        return requestBuilder;
    }

    public EsSearchRequestBuilder sort(String field, SortEnum sortEnum) {
        this.esSearchDTO.sortBy(field, sortEnum);
        return this;
    }

    public EsSearchRequestBuilder distanceSort(String field, String coordinate, @Nullable String unit, @Nullable SortEnum sortEnum) {
        this.esSearchDTO.distanceSortBy(field, unit, coordinate, sortEnum);
        return this;
    }

    public EsSearchRequestBuilder equal(String field, Object value) {
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.TERM).fields(new String[]{field}).values(new Object[]{value});
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder equal(String[] fields, Object[] values) {
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.TERM).fields(fields).values(values);
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder match(String field, String value, Integer minimumShouldMatch) {
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.MATCH).fields(new String[]{field}).values(new Object[]{value}).minimumShouldMatch(minimumShouldMatch);
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder in(String field, Object... values) {
        if (values != null && values.length == 1 && values[0] instanceof Collection) {
            values = ((Collection)values[0]).toArray(new Object[0]);
        }

        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.TERMS).fields(new String[]{field}).values(values);
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder multiMatch(String[] fields, Object value) {
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.MULTI_MATCH).fields(fields).values(new Object[]{value});
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    /** @deprecated */
    @Deprecated
    public EsSearchRequestBuilder or(String[] fields, Object[] values) {
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.SHOULD).fields(fields).values(values);
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder multiEqualInnerOr(String[] fields, Object[] values) {
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.SHOULD).fields(fields).values(values);
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder range(String field, Object value, RangeEnum rangeType) {
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.RANGE).fields(new String[]{field}).values(new Object[]{value}).ranges(new RangeEnum[]{rangeType});
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder range(String[] fields, Object[] values, RangeEnum[] rangeTypes) {
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.RANGE).fields(fields).values(values).ranges(rangeTypes);
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder isNull(String... fields) {
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.NULL).fields(fields);
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder notNull(String... fields) {
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.NOT_NULL).fields(fields);
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder notEqual(String field, Object value) {
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.NOT_EQUAL).fields(new String[]{field}).values(new Object[]{value});
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder notEqual(String field, Object... values) {
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.NOT_EQUAL).values(values);
        String[] fields = new String[values.length];

        for(int i = 0; i < values.length; ++i) {
            fields[i] = field;
        }

        searchUnit.fields(fields);
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder notEqual(String[] fields, Object... values) {
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.NOT_EQUAL).fields(fields).values(values);
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder prefix(String field, Object value) {
        Asserts.check(field != null && value != null, "Illegal parameters");
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.PREFIX).fields(new String[]{field}).values(new Object[]{value});
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder prefix(String[] fields, Object... values) {
        Asserts.check(fields != null && values != null, "Illegal parameters");
        Asserts.check(fields.length == values.length, "Illegal parameters");
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.NOT_EQUAL).fields(fields).values(values);
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder script(Object... values) {
        Asserts.check(values != null && values.length > 0, "Illegal parameters");
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.SCRIPT).values(values);
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder wildcard(String field, String pattern) {
        Asserts.check(field != null && !StringUtils.isEmpty(pattern), "Illegal parameters");
        SearchUnit searchUnit = (new SearchUnit()).setSearchTypeEnum(SearchTypeEnum.WILDCARD).fields(new String[]{field}).values(new Object[]{pattern});
        this.esSearchDTO.appendSearchUnits(new SearchUnit[]{searchUnit});
        return this;
    }

    public EsSearchRequestBuilder includeFields(String... fields) {
        Asserts.check(fields != null || fields.length == 0, "Illegal parameter 'fields'");
        this.esSearchDTO.includeFields(fields);
        return this;
    }

    public EsSearchRequestBuilder shardingValues(String... shardingValues) {
        Asserts.check(shardingValues != null || shardingValues.length == 0, "Illegal parameter 'shardingValues'");
        this.esSearchDTO.shardingValues(shardingValues);
        return this;
    }

    public EsSearchRequestBuilder limit(int current, int size) {
        this.esSearchDTO.setPage(current);
        this.esSearchDTO.setSize(size);
        return this;
    }

    public EsSearchDTO build() {
        return this.esSearchDTO;
    }

    public Boolean hasConditions() {
        return !CollectionUtils.isEmpty(this.esSearchDTO.getSearchUnits());
    }
}