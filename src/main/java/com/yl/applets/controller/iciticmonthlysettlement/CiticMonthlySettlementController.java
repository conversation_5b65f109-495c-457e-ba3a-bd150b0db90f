package com.yl.applets.controller.iciticmonthlysettlement;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.controller.AppBaseController;
import com.yl.applets.dto.SpmApiInsuranceTrialDTO;
import com.yl.applets.enums.DispatchServiceTypeEnum;
import com.yl.applets.feign.dto.SpmApiCustomerShippingQuoteTryCalcDTO;
import com.yl.applets.feign.dto.citicmonthlysettlement.*;
import com.yl.applets.service.CiticMonthlySettlementService;
import com.yl.applets.vo.CiticSpmCommonCostVO;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.yl.common.base.model.vo.Result.success;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2024/12/12 10:37
 */
@Api(value = "中信银行月结", tags = {"中信银行月结"})
@RestController
@RequestMapping("/citic/monthlySettlement")
@Slf4j
public class CiticMonthlySettlementController extends AppBaseController {


    @Autowired
    private CiticMonthlySettlementService citicMonthlySettlementService;


    @PostMapping(value = "/bind/info")
    @ApiOperation(value = "绑定月结账号信息", notes = "绑定月结账号信息")
    public Result<Integer> bindInfo(@Validated @RequestBody CiticMonthlySettlementDTO dto) {
        WxUserVo user = getUser();
        dto.setMemberId(user.getNumberId());
        dto.setPhone(user.getMobile());
        return citicMonthlySettlementService.bindInfo(dto);
    }

    @PostMapping(value = "/unbind/info")
    @ApiOperation(value = "解除绑定月结账号信息", notes = "解除绑定月结账号信息")
    public Result<Integer> unBindInfo(@Validated @RequestBody CiticMonthlySettlementUpdateDTO dto) {
        WxUserVo user = getUser();
        dto.setMemberId(user.getNumberId());
        return citicMonthlySettlementService.unBindInfo(dto);
    }


    @PostMapping(value = "/update/default/info")
    @ApiOperation(value = "修改默认账号", notes = "修改默认账号")
    public Result<Integer> updateDefaultAccount(@Validated @RequestBody CiticMonthlySettlementUpdateDTO dto) {
        WxUserVo user = getUser();
        dto.setMemberId(user.getNumberId());
        return citicMonthlySettlementService.updateDefaultAccount(dto);
    }


    @PostMapping(value = "/getMonthlySettlement/list")
    @ApiOperation(value = "查询当前用户下的月付账号列表", notes = "查询当前用户下的月付账号列表")
    public Result<Page<CiticMonthlySettlementResultDTO>> getMonthlySettlementList(@RequestBody CiticMonthlySettlementQueryDTO dto) {
        WxUserVo user = getUser();
        Integer memberId=user.getNumberId();
        dto.setMemberId(memberId);
        return citicMonthlySettlementService.getMonthlySettlementList( dto);
    }



    @GetMapping(value = "/judge/rabbitDelivery")
    @ApiOperation(value = "判断兔优达", notes = "判断兔优达")
    public Result<Boolean> judgeRabbitDelivery(@RequestParam("account") String account) {
        return citicMonthlySettlementService.judgeRabbitDelivery(account);
    }


    @GetMapping(value = "/judge/account")
    @ApiOperation(value = "判断账号是否可用", notes = "判断账号是否可用")
    public Result<Boolean> judgeAccount(@RequestParam("account") String account) {
        return citicMonthlySettlementService.judgeAccount(account);
    }


    @GetMapping(value = "/find/share/info")
    @ApiOperation(value = "查询共享客户信息", notes = "查询共享客户信息")
    public Result<MonthlySettlementShareCustomerDTO> findShareCustomer(@RequestParam("account") String account) {
        return citicMonthlySettlementService.findShareCustomer(account);
    }


    @GetMapping(value = "/find/default/info")
    @ApiOperation(value = "查询默认客户信息", notes = "查询默认客户信息")
    public Result<CiticMonthlySettlementResultDTO> findDefaultAccount() {
        WxUserVo user = getUser();
        String memberId=user.getNumberId()+"";
        return citicMonthlySettlementService.findDefaultAccount(memberId);
    }

    @GetMapping(value = "/find/info/by/cusCode")
    @ApiOperation(value = "查询默认客户信息", notes = "查询默认客户信息")
    public Result<CiticMonthlySettlementResultDTO> findAccountByCusCode(@RequestParam("customerCode")String customerCode) {
        WxUserVo user = getUser();
        String memberId=user.getNumberId()+"";
        return citicMonthlySettlementService.findAccountByCusCode(memberId,customerCode);
    }

    /**
     * 运费计算
     *
     * @param spmApiCustomerShippingQuoteTryCalcDTO
     * @return
     */
    @ApiOperation(value = "中信银行月结-运费计算", notes = "中信银行月结-运费计算")
    @PostMapping("/spmCustomerShippingQuote/comCost")
    public Result<CiticSpmCommonCostVO> spmCustomerShippingQuoteCost(@RequestBody SpmApiCustomerShippingQuoteTryCalcDTO spmApiCustomerShippingQuoteTryCalcDTO) {

        spmApiCustomerShippingQuoteTryCalcDTO.setProductTypeCode(spmApiCustomerShippingQuoteTryCalcDTO.getProductTypeCode());
        spmApiCustomerShippingQuoteTryCalcDTO.setServiceMethodCode(DispatchServiceTypeEnum.DISPATCH_DOOR.getCode());
        spmApiCustomerShippingQuoteTryCalcDTO.setCurrentTime(LocalDateTime.now());
        log.info("中信银行月结-客户运费计算,请求参数：【{}】", JSON.toJSONString(spmApiCustomerShippingQuoteTryCalcDTO));
        CiticSpmCommonCostVO res = citicMonthlySettlementService.spmCustomerShippingQuoteCost(spmApiCustomerShippingQuoteTryCalcDTO);
        log.info("中信银行月结-客户运费计算结果: [{}]", JSON.toJSONString(res));
        return success(res);
    }

}
