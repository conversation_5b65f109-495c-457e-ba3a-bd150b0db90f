package com.yl.applets.controller;

import com.sun.org.apache.xpath.internal.operations.Bool;
import com.yl.applets.dto.WxPayDto;
import com.yl.applets.service.IPayService;
import com.yl.applets.vo.IpayTokenVO;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-08-18
 */

@Api(value = "微信支付", tags = {"微信支付"})
@RestController
@RequestMapping("/wxpay")
@Slf4j
public class PayController extends AppBaseController {

    //微信支付商户号
    @Value("${wx.pay.commercial:1613021961}")
    private String wxPayCommercialNum;

    //商户类型
    @Value("${wx.pay.type:微信支付服务商}")
    private String wxPayType;
    @Autowired
    private IPayService payService;

    //云闪付支付按钮
    @Value("${ums.pay.show:true}")
    private Boolean umsPayShow;



    @PostMapping("/getPrePayId")
    @ApiOperation(value = "获取prepay_id接口", notes = "获取prepay_id接口")
    public Object getPrePayId(@RequestBody @Validated WxPayDto dto) {
        Object vo = payService.getPrePayId(dto);
        return vo;
    }


    @GetMapping("/getValue")
    public Result<Map> getValue() {
        Map<String, Object> map = new HashMap<>();
        map.put("wxPayCommercialNum", wxPayCommercialNum);
        map.put("wxPayType", wxPayType);
        return Result.success(map);
    }

//    @GetMapping("/getToken")
//    @ApiOperation(value = "微信支付getToken", notes = "微信支付getToken")
//    Result<IpayTokenVO> getToken(){
//      return   Result.success(payService.getToken());
//    }


    /**
     * 云闪付财务接口
     * @param dto
     * @return
     */
    @PostMapping("/get/ums/mini/PrePayId")
    @ApiOperation(value = "获取prepay_id云闪付接口", notes = "获取prepay_id云闪付接口")
    public Object getUmsMiniPrePayId(@RequestBody @Validated WxPayDto dto) {
        return payService.getUmsMiniPrePayId(dto);
    }



    /**
     * 云闪付支付按钮展示
     * @return
     */
    @GetMapping("/get/ums/pay/show")
    @ApiOperation(value = "云闪付支付按钮展示", notes = "云闪付支付按钮展示")
    public Result<Boolean> getUmsPayShow() {
        return Result.success(umsPayShow);
    }


}
