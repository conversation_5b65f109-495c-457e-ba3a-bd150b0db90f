package com.yl.applets.controller.market;

import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.market.NewCustomerActivitySpSignUpDTO;
import com.yl.applets.service.NewCustomerActivitySpService;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2023/3/9 11:36
 * @description 新用户拉新活动
 */
@Slf4j
@RestController
@RequestMapping("/newCustomerActivitySp")
public class NewCustomerActivitySpController {
    @Resource
    private NewCustomerActivitySpService newCustomerActivitySpService;


    /**
     * 报名
     */
    @PostMapping("/signUp")
    public Result<Boolean> signUp(@RequestBody @Valid NewCustomerActivitySpSignUpDTO dto) {
        log.info("新用户拉新活动，用户报名==>入参: {}", JSON.toJSONString(dto));
        return Result.success(newCustomerActivitySpService.signUp(dto));
    }
}
