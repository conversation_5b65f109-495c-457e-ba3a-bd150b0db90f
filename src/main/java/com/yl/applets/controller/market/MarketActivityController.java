package com.yl.applets.controller.market;

import com.yl.applets.service.MarketActivityService;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/3/9 11:36
 * @description 市场活动
 */
@Slf4j
@RestController
@RequestMapping("/marketActivity")
public class MarketActivityController {
    @Resource
    private MarketActivityService marketActivityService;


    /**
     * 刷新浏览量
     */
    @GetMapping("/refreshPageViews")
    public Result<Boolean> refreshPageViews(@RequestParam String marketActivityCode, @RequestParam String source) {
        log.info("市场活动刷新浏览量==>入参: marketActivity=>{},source=>{}", marketActivityCode, source);
        return Result.success(marketActivityService.refreshPageViews(marketActivityCode, source));
    }

}
