package com.yl.applets.controller;

import com.yl.applets.feign.AppletsExternalFeign;
import com.yl.applets.service.IExternalService;
import com.yl.applets.vo.CheckWxPhoneVO;
import com.yl.common.base.annotation.log.operation.ExcludeInterceptor;
import com.yl.common.base.model.vo.Result;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description: 小程序对外提供服务入口
 * @Project:
 * @CreateDate: Created in 2019-10-15 11:31 <br>
 * @Author: wwx
 */
@RestController
@RequestMapping("/external")
@Slf4j
public class AppletsExternalController implements AppletsExternalFeign {

    @Autowired
    private IExternalService externalService;

    @Override
    @PostMapping("/checkPhoneIsBind")
    @ExcludeInterceptor
    public Result<CheckWxPhoneVO> checkPhoneIsBind(@RequestParam("phone")  @NotNull(message = "手机号不能为空") String phone) {
        return Result.success(externalService.checkPhoneIsBind(phone));
    }
}
