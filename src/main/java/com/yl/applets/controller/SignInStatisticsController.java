package com.yl.applets.controller;

import com.yl.applets.dto.SignStatisticsDTO;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.WxUserVo;
import com.yl.redis.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.UUID;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2022/10/18 20:49
 * @Version 1.0
 */
@RestController
@RequestMapping("/sign/in")
@Slf4j
@Api(value = "微信小程序签到/浏览统计", tags = "微信小程序签到/浏览统计")
public class SignInStatisticsController extends AppBaseController {


    public static final int INT_THREE = 3;
    public static final int INT_THOUR = 4;

    @PostMapping("/statisticspvuv")
    @ApiOperation(value = "页面浏览PV/UV", notes = "页面浏览PV/UV统计")
    public void upLoadStatisticsPvUvData(@RequestParam("isMember") Integer isMember) {

        WxUserVo user = getUser();

        if (isMember >= 2 || isMember < 0) {
            log.info("是否会员参数错误：{}", isMember);
            return;
        }
        //保存到redis 初始化时间
        LocalDate now = LocalDate.now();
        //拿到月份
        int monthValue = now.getMonthValue();
        String PVKEY = "";
        String UVKEY = "";
        if (isMember == 1) {
            //会员
            PVKEY = BcRedisKeyEnum.STATISTICS_SIGN_VIP_PV_MONTH.keyBuilder(String.valueOf(monthValue));
            UVKEY = BcRedisKeyEnum.STATISTICS_SIGN_VIP_UV_MONTH.keyBuilder(String.valueOf(monthValue));
        } else {
            PVKEY = BcRedisKeyEnum.STATISTICS_SIGN_COMMON_PV_MONTH.keyBuilder(String.valueOf(monthValue));
            UVKEY = BcRedisKeyEnum.STATISTICS_SIGN_COMMON_UV_MONTH.keyBuilder(String.valueOf(monthValue));
        }
        log.info("页面浏览PV统计,key==>{}", PVKEY);
        log.info("页面浏览UV统计,key==>{}", UVKEY);
        //设置PV自增
        RedisUtil.incrBy(PVKEY, 1);
        if (RedisUtil.getExpire(PVKEY) == -1) {
            //到下个月
            RedisUtil.expire(PVKEY, getTimeNextMonth());
        }

        //设置UV自增
        RedisUtil.hIncrBy(UVKEY, user.getId() + "", 1);
        if (RedisUtil.getExpire(UVKEY) == -1) {
            //到下个月
            RedisUtil.expire(UVKEY, getTimeNextMonth());
        }

        log.info("页面浏览PV/UV点击统计,保存成功");

    }

    @PostMapping("/upLoadShareStatistics")
    @ApiOperation(value = "页面签到分享/Banner点击统计", notes = "页面签到分享/Banner点击统计")
    public void upLoadStatisticsData(@RequestBody @Valid SignStatisticsDTO dto) {

        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id==>{}页面签到分享/Banner点击统计,入参：{}", requestId, JsonUtils.toJson(dto));

        //保存到redis 初始化时间
        LocalDate now = LocalDate.now();
        //拿到月份
        int monthValue = now.getMonthValue();
        String shareKey = "";
        String bannerKey = "";
        if (dto.getIsMember() == 1) {
            //会员
            shareKey = BcRedisKeyEnum.STATISTICS_SIGN_VIP_SHARE_MONTH.keyBuilder(String.valueOf(monthValue));
            bannerKey = BcRedisKeyEnum.STATISTICS_SIGN_VIP_BANNER_MONTH.keyBuilder(String.valueOf(monthValue));
        } else {
            shareKey = BcRedisKeyEnum.STATISTICS_SIGN_COMMON_SHARE_MONTH.keyBuilder(String.valueOf(monthValue));
            bannerKey = BcRedisKeyEnum.STATISTICS_SIGN_COMMON_BANNER_MONTH.keyBuilder(String.valueOf(monthValue));
        }

        if (dto.getActionType() == INT_THREE) {
            log.info("页面签到分享统计,key==>{}", shareKey);
            //设置shareKey自增
            RedisUtil.incrBy(shareKey, 1);
            if (RedisUtil.getExpire(shareKey) == -1) {
                //到下个月
                RedisUtil.expire(shareKey, getTimeNextMonth());
            }
        } else if (dto.getActionType() == INT_THOUR) {
            log.info("页面Banner点击统计,key==>{}", bannerKey);
            //设置bannerKey自增
            RedisUtil.incrBy(bannerKey, 1);
            if (RedisUtil.getExpire(bannerKey) == -1) {
                //到下个月
                RedisUtil.expire(bannerKey, getTimeNextMonth());
            }
        } else {
            return;
        }

        log.info("页面签到分享/Banner点击统计,保存成功");

    }

    private long getTimeNextMonth() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime localDateTime = now.plusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(1);
        long seconds = Duration.between(now, localDateTime).getSeconds();
        log.info("失效时间==>{}", seconds);
        return seconds;
    }
}
