package com.yl.applets.controller;

import com.alibaba.fastjson.JSON;
import com.yl.applets.config.SourceProperties;
import com.yl.applets.dto.ReceiveBenefitDTO;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.service.IReceiveBenefitService;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.vo.SourceVO;
import com.yl.common.base.annotation.log.operation.ExcludeInterceptor;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.redis.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：福利申领控制器
 * @Author： zhanzhihong
 * @Date： 2022/07/12
 */
@RestController
@RequestMapping("/api/receiveBenefit")
@Api(value = "福利申领记录", tags = {"福利申领记录"})
@Slf4j
public class ReceiveBenefitController extends AppBaseController {
    @Autowired
    private IReceiveBenefitService receiveBenefitService;

    @Autowired
    private SourceProperties sourceProperties;

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    static final String RECEIVEBENEFIT_MD5_KEY = "hsaNJj@addReceiveBenefit";


    /**
     * 添加福利申领记录
     *
     * @param receiveBenefitDTO
     * @return
     */
    @PostMapping("/addReceiveBenefit")
    @ExcludeInterceptor
    @ApiOperation(value = "添加福利申领记录", notes = "添加福利申领记录")
    public Result<Boolean> addReceiveBenefit(@Valid @RequestBody(required = false) ReceiveBenefitDTO receiveBenefitDTO) {
        if (receiveBenefitDTO == null) {
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id{}，参数：{}", requestId, JSON.toJSONString(receiveBenefitDTO));
        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(receiveBenefitDTO.getSource() + receiveBenefitDTO.getTimes() + RECEIVEBENEFIT_MD5_KEY));
        log.info("请求id{}，后端MD5加密之后{}", requestId, sign1);
        if (!sign1.equals(receiveBenefitDTO.getSign())) {
            log.info("请求id{},验签失败！", requestId, receiveBenefitDTO.getSign());
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }
        String capcha_key = BcRedisKeyEnum.APPLETS_CAPTCHA.keyBuilder(receiveBenefitDTO.getCToken());
        //校验验证码
        if (!RedisUtil.hasKey(capcha_key)) {
            throw new BusinessException(ServiceErrCodeEnum.CAPTCHA_ERROR);
        }
        if (!receiveBenefitDTO.getCaptcha().equalsIgnoreCase((String) RedisUtil.get(capcha_key))) {
            throw new BusinessException(ServiceErrCodeEnum.CAPTCHA_ERROR);
        }

        //验证码使用一次后销毁
        RedisUtil.delete(capcha_key);
        Boolean result = receiveBenefitService.addReceiveBenefit(receiveBenefitDTO, requestId);
        return success(result);
    }

    @GetMapping("/getSource")
    @ExcludeInterceptor
    @ApiOperation(value = "活动来源", notes = "活动来源")
    public Result<List<SourceVO>> getSource() {
        //来源转换
        List<SourceProperties.source> sourceList = sourceProperties.getList();
        List<SourceVO> sourceVOS = orikaBeanMapper.mapAsList(sourceList, SourceVO.class);
        return success(sourceVOS);
    }


    /**
     * #350492 #341008 “极兔欢乐送”H5报名落地页开发
     * https://ones.jtexpress.com.cn/project/#/team/5BXYuw3B/task/FtBqyn5o6Dcw5kQX
     * 圣诞报名活动
     *
     * @param receiveBenefitDTO
     * @return
     */
    @PostMapping("/christmasRegistrationActivity")
    @ExcludeInterceptor
    @ApiOperation(value = "圣诞报名活动", notes = "圣诞报名活动")
    public Result<Boolean> christmasRegistrationActivity(@Valid @RequestBody(required = false) ReceiveBenefitDTO receiveBenefitDTO) {
        if (receiveBenefitDTO == null) {
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        log.info("请求参数：{}", JSON.toJSONString(receiveBenefitDTO));
        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(receiveBenefitDTO.getTimes() + RECEIVEBENEFIT_MD5_KEY));
        log.info("后端MD5加密之后{}", sign1);
        if (!sign1.equals(receiveBenefitDTO.getSign())) {
            log.info("验签失败！{}", receiveBenefitDTO.getSign());
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }
//        String capcha_key = BcRedisKeyEnum.APPLETS_CAPTCHA.keyBuilder(receiveBenefitDTO.getCToken());
//        //校验验证码
//        if (!RedisUtil.hasKey(capcha_key)) {
//            throw new BusinessException(ServiceErrCodeEnum.CAPTCHA_ERROR);
//        }
//        if (!receiveBenefitDTO.getCaptcha().equalsIgnoreCase((String) RedisUtil.get(capcha_key))) {
//            throw new BusinessException(ServiceErrCodeEnum.CAPTCHA_ERROR);
//        }
//        //验证码使用一次后销毁
//        RedisUtil.delete(capcha_key);
        Boolean result = receiveBenefitService.christmasRegistrationActivity(receiveBenefitDTO);
        return success(result);
    }

}
