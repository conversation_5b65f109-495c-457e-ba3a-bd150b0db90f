package com.yl.applets.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.ActivityConfigDTO;
import com.yl.applets.dto.ActivityConfigQueryDTO;
import com.yl.applets.dto.ActivityVisitRecordDTO;
import com.yl.applets.entity.ActivityConfig;
import com.yl.applets.service.IActivityService;
import com.yl.applets.stream.ActivityConfigPushOutputProcessor;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.vo.ActivityConfigVO;
import com.yl.applets.vo.ActivityVisitRecordStatisticsVO;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.annotation.log.operation.ExcludeInterceptor;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import com.yl.redis.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 小程序活动管理
 * @author: xiongweibin
 * @create: 2020-08-24 11:41
 */
@Api(value = "活动管理", tags = {"活动管理"})
@RestController
@RequestMapping("/activity")
@Slf4j
public class ActivityController extends AppBaseController {

    @Autowired
    private IActivityService activityService;

    @Autowired
    private ActivityConfigPushOutputProcessor activityConfigPushOutputProcessor;

    /**
     * 分页查询
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "活动配置分页", notes = "活动配置分页")
    @ExcludeInterceptor
    public Result<Page<ActivityConfigVO>> getPages(@RequestBody(required = false) @Valid ActivityConfigQueryDTO queryDTO) {
        if(queryDTO==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        log.info("活动配置分页查询,入参==>{}", JSON.toJSONString(queryDTO));
        return success(activityService.getPages(queryDTO));
    }

    /**
     * 活动配置集合查询
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/list")
    @ApiOperation(value = "活动配置集合查询", notes = "活动配置集合查询")
    public Result<List<ActivityConfigVO>> list(@RequestBody(required = false) ActivityConfigQueryDTO queryDTO) {
        if(queryDTO==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        log.info("活动配置查询,入参==>{}", JSON.toJSONString(queryDTO));
        return success(activityService.list(queryDTO));
    }

    /**
     * 新增活动配置
     *
     * @param dto
     * @return
     */
    @PostMapping("/add")
    @ApiOperation(value = "活动配置新增", notes = "活动配置新增")
    @ExcludeInterceptor
    public Result add(@RequestBody(required = false) @Valid ActivityConfigDTO dto) {
        if(dto==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        log.info("活动配置新增,入参==>{}", JSON.toJSONString(dto));
        Result<ActivityConfig> result = activityService.add(dto);
        if(result.isSucc()){
            activityConfigPushOutputProcessor.sendActivityConfigAsynMessage(result.getData());
        }
        return result;
    }


    /**
     * 删除活动配置
     *
     * @param dto
     * @return
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除活动配置", notes = "删除活动配置")
    @ExcludeInterceptor
    public Result delete(@RequestBody(required = false) ActivityConfigDTO dto) {
        if(dto==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        return activityService.delete(dto);
    }

    /**
     * 下线
     *
     * @param dto
     * @return
     */
    @PostMapping("/offline")
    @ApiOperation(value = "下线", notes = "下线")
    @ExcludeInterceptor
    public Result offline(@RequestBody(required = false) ActivityConfigDTO dto) {
        if(dto==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        return activityService.offline(dto);
    }

    /**
     * 更新活动配置
     *
     * @param dto
     * @return
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新活动配置", notes = "更新活动配置")
    @ExcludeInterceptor
    public Result<ActivityConfig> update(@RequestBody(required = false) @Valid ActivityConfigDTO dto) {
        if(dto==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        log.info("活动配置更新,入参==>{}", JSON.toJSONString(dto));
        Result<ActivityConfig> result = activityService.update(dto);
        if(result.isSucc()){
            activityConfigPushOutputProcessor.sendActivityConfigAsynMessage(result.getData());
        }
        return result;
    }

    /**
     * 查询活动详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询活动详情", notes = "查询活动详情")
    @ExcludeInterceptor
    public Result<ActivityConfigVO> getDetailById(@RequestParam("id") Long id) {
        return activityService.getDetailById(id);
    }

    /**
     * 统计活动访问次数
     *
     * @param id
     * @return
     */
    @GetMapping("/statisticsActivityVisitQty")
    @ApiOperation(value = "统计活动访问次数", notes = "统计活动访问次数")
    @ExcludeInterceptor
    public Result<List<ActivityVisitRecordStatisticsVO>> statisticsActivityVisitQty(@RequestParam("id") Long id) {
        return activityService.statisticsActivityVisitQty(id);
    }

    /**
     * 上传访问/曝光记录
     *
     * @param dto
     */
    @PostMapping("/uploadAccessRecord")
    @ApiOperation(value = "上传访问/曝光记录", notes = "上传访问/曝光记")
    public Result uploadAccessRecord(@RequestBody(required = false) @Valid ActivityVisitRecordDTO dto) {
        if(dto==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        return activityService.uploadAccessRecord(dto);
    }


    /**
     * 页面访问记录
     *
     * @param dto
     */
    @PostMapping("/uploadIndexAccessRecord")
    @ApiOperation(value = "页面访问记录", notes = "页面访问记录")
    public void uploadIndexAccessRecord(@RequestBody ActivityVisitRecordDTO dto) {
        WxUserVo user = getUser();
        //UV:   key   ：   大key，小key(userId)，值，取大key的size，
        //PV:   key   ：   自增
        //保存到redis
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate now = LocalDate.now();
        String day = dateTimeFormatter.format(now);
        String PVKEY = BcRedisKeyEnum.INDEX_COUNT_PV_DAY.keyBuilder(day);
        RedisUtil.incrBy(PVKEY,1);
        if(RedisUtil.getExpire(PVKEY)==-1){
            RedisUtil.expire(PVKEY,3600*24*2);
        }
        String UVKEY = BcRedisKeyEnum.INDEX_COUNT_UV_DAY.keyBuilder(day);
        RedisUtil.hIncrBy(UVKEY,user.getId()+"",1);
        if(RedisUtil.getExpire(UVKEY)==-1){
            RedisUtil.expire(UVKEY,3600*24*2);
        }
    }

    /**
     * 功能描述:
     * 春节不打烊接口（前端用来判断活动是否进行中）
     * @return:com.yl.common.base.model.vo.Result
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-01-14 13:52
     */
    @PostMapping("/springActivity/isValid")
    @ApiOperation(value = "春节不打烊活动是否（true有效，false过期）", notes = "春节不打烊活动是否")
    public Result<Boolean> isValid() {
        return Result.success(activityService.springActivityIsValid());
    }

}
