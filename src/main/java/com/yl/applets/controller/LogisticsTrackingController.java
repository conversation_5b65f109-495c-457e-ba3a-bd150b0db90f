package com.yl.applets.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.PodTrackingClient;
import com.yl.applets.service.ILogisticsTrackingService;
import com.yl.applets.vo.AppOmsWaybillDetailVO;
import com.yl.applets.vo.PodTrackingListVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.YlPreconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-08-13
 */
@Api(value = "快件跟踪", tags = {"快件跟踪"})
@RestController
@RequestMapping("/logisticsTracking")
@Slf4j
public class LogisticsTrackingController extends AppBaseController {

    @Autowired
    private ILogisticsTrackingService logisticsTrackingService;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClientl;

    static final String WAYBILL_MD5_KEY_ALIPAY = "h3saNnlogisAPPL@e1tejasig";

    @Value("${track.enable:true}")
    private Boolean switchFlag;

    /**
     * 快件跟踪
     *
     * @param waybillNo
     * @return
     */
    @ApiOperation(value = "快件跟踪", notes = "快件跟踪")
    @GetMapping(value = "/getDetailByWaybillNo")
    public Result<Object> getDetailByWaybillNo(@RequestParam("waybillNo") String waybillNo) {
        YlPreconditions.checkArgument(StringUtils.isNotBlank(waybillNo), new ServiceException(ServiceErrCodeEnum.WAYBILLNO_ISNULL));
        return success(switchFlag ? "" : logisticsTrackingService.logisticsTracking(waybillNo));
    }

    /**
     * 快件跟踪V2k
     *
     * @param waybillNo
     * @return
     */
    @ApiOperation(value = "快件跟踪", notes = "快件跟踪")
    @GetMapping(value = "/v2/getDetailByWaybillNo")
    public Result<Object> getDetailByWaybillNoV2(@RequestParam("waybillNo") String waybillNo, @RequestParam("sign") String sign,
                                                 @RequestParam("time") String time, @RequestParam("phone") String phone) {
        if (StrUtil.isBlank(phone) || phone.length() != 4){
            throw new ServiceException(ServiceErrCodeEnum.PHONE_VERIFY_IS_NULL);
        }

        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        YlPreconditions.checkArgument(StringUtils.isNotBlank(waybillNo), new ServiceException(ServiceErrCodeEnum.WAYBILLNO_ISNULL));
        log.info("请求id{}，[轨迹跟踪]参数：waybillNo{}times{}sign{}", requestId, waybillNo, time, sign);
        YlPreconditions.checkArgument(org.apache.commons.lang.StringUtils.isNotBlank(waybillNo), new ServiceException(ServiceErrCodeEnum.WAYBILLNO_ISNULL));
        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(waybillNo + time + WAYBILL_MD5_KEY_ALIPAY));
        log.info("请求id{}，[轨迹跟踪]后端MD5加密之后{}", requestId, sign1);
        if (!sign1.equals(sign)) {
            log.info("请求id{},验签失败！", requestId, sign);
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }
        log.info("请求id:{},[轨迹跟踪]远程调用入参：{}", requestId, waybillNo);
        PodTrackingListVO podTrackingListVO = channelApiFeignClientl.getDetailByWaybillNoV2(waybillNo).result();
        log.info("请求id:{},[轨迹跟踪]远程调用返回：{}", requestId, JSON.toJSONString(podTrackingListVO));

        //查询运单详情轨迹  鉴权
        AppOmsWaybillDetailVO appOmsWaybillDetailVO = channelApiFeignClientl.getDetailByNo(waybillNo).result();
        log.info("请求id{},[轨迹跟踪]查询订单/运单详情出参：{}", requestId, JSON.toJSONString(appOmsWaybillDetailVO));
        String receiverTelphone = appOmsWaybillDetailVO.getReceiverTelphone();
        String senderTelphone = appOmsWaybillDetailVO.getSenderTelphone();

        if ((org.apache.commons.lang.StringUtils.isNotBlank(appOmsWaybillDetailVO.getReceiverMobilePhone()) && appOmsWaybillDetailVO.getReceiverMobilePhone().endsWith(phone))
                || (org.apache.commons.lang.StringUtils.isNotBlank(appOmsWaybillDetailVO.getSenderMobilePhone()) && appOmsWaybillDetailVO.getSenderMobilePhone().endsWith(phone)
                || (org.apache.commons.lang.StringUtils.isNotBlank(receiverTelphone) && receiverTelphone.endsWith(phone))
                || (org.apache.commons.lang.StringUtils.isNotBlank(senderTelphone) && senderTelphone.endsWith(phone)))) {
            return success(podTrackingListVO);
        }
        throw new BusinessException(ServiceErrCodeEnum.PHONE_VERIFY_ERROR);


    }


}

