package com.yl.applets.controller;


import com.yl.applets.config.WxInvoiceProperties;
import com.yl.applets.dto.InvoiceTitleDTO;
import com.yl.applets.feign.InvoiceFeignClient;
import com.yl.applets.vo.InvoiceTitleVO;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <p>
 * 开票抬头表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-21
 */
@Slf4j
@RestController
@RequestMapping("/invoice-title")
public class InvoiceTitleController extends AppBaseController {

    @Resource
    private OrikaBeanMapper orikaBeanMapper;

    @Autowired
    private InvoiceFeignClient invoiceFeignClient;

    @Autowired
    private WxInvoiceProperties wxInvoiceProperties;


    /**
     * 添加抬头
     *
     * @param invoiceTitleDTO
     * @return
     */
    @PostMapping("/save")
    public Result<Boolean> save(@RequestBody @Valid InvoiceTitleDTO invoiceTitleDTO) {
        checkData(invoiceTitleDTO);
        invoiceTitleDTO.setUserId(getUser().getNumberId());
        return success(invoiceFeignClient.save(invoiceTitleDTO).result());
    }
    
    private void checkData(InvoiceTitleDTO invoiceTitleDTO) {
        Integer type = invoiceTitleDTO.getType();
        String taxNumber = invoiceTitleDTO.getTaxNumber();// 纳税人识别号
        if(type==2){
            if(StringUtils.isNotBlank(taxNumber)){
                Pattern regex = Pattern.compile("^9[A-Z0-9]{17}$") ;
                if (!regex.matcher(taxNumber).matches()) {
                    throw new BusinessException(ResultCodeEnum.INVOICE_TAX_ERROR);
                }
            }
        } else if(type==1){
            if(StringUtils.isBlank(taxNumber)){
                throw new BusinessException(ResultCodeEnum.INVOICE_TAX_ERROR2);
            }
            if(StringUtils.isNotBlank(taxNumber)){
                Pattern regex = Pattern.compile("^9[A-Z0-9]{17}$") ;
                if (!regex.matcher(taxNumber).matches()) {
                    throw new BusinessException(ResultCodeEnum.INVOICE_TAX_ERROR);
                }
            }
        }
        String name = invoiceTitleDTO.getName();//抬头名称
        if(name.contains(" ")){
            throw new BusinessException(ResultCodeEnum.INVOICE_TAX_ERROR8);
        }
        String buyerBankAccount = invoiceTitleDTO.getBuyerBankAccount();//开户行账号
        String mobile = invoiceTitleDTO.getMobile();//电话
        if(StringUtils.equals(wxInvoiceProperties.getCompanyName(),name)||StringUtils.equals(wxInvoiceProperties.getTaxNumber(),taxNumber)
                ||StringUtils.equals(wxInvoiceProperties.getBankAccount(),buyerBankAccount)||StringUtils.equals(wxInvoiceProperties.getMobile(),mobile)){
            throw new BusinessException(ResultCodeEnum.INVOICE_INFO_ERROR);
        }
    }

    /**
     * 修改抬头
     *
     * @return
     */
    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody @Valid InvoiceTitleDTO invoiceTitleDTO) {
        checkData(invoiceTitleDTO);
        invoiceTitleDTO.setUserId(getUser().getNumberId());
        return success(invoiceFeignClient.update(invoiceTitleDTO).result());
    }

    /**
     * 删除抬头
     *
     * @param id
     * @return
     */
    @GetMapping("/del")
    public Result<Boolean> del(@RequestParam("id") Integer id) {
        Integer userId = getUser().getNumberId();
        return success(invoiceFeignClient.del(id, userId).result());
    }


    /**
     * 抬头详情
     *
     * @param id
     * @return
     */
    @GetMapping("/get")
    public Result<InvoiceTitleVO> get(@RequestParam("id") Integer id) {
        Integer userId = getUser().getNumberId();
        return success(invoiceFeignClient.get(id, userId).result());
    }

    /**
     * 抬头列表
     *
     * @return
     */
    @GetMapping("/list")
    public Result<List<InvoiceTitleVO>> list() {
        Integer userId = getUser().getNumberId();
        return success(invoiceFeignClient.list(userId).result());
    }

    /**
     * 抬头列表
     *
     * @return
     */
    @GetMapping("/listName")
    public Result<List<InvoiceTitleVO>> listName(@RequestParam("name") String name) {
        Integer userId = getUser().getNumberId();
        return success(invoiceFeignClient.listName(name, userId).result());
    }

}
