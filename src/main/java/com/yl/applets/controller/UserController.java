package com.yl.applets.controller;


import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yl.applets.config.WxMaConfiguration;
import com.yl.applets.config.WxMaProperties;
import com.yl.applets.constant.RedisKeyConstants;
import com.yl.applets.dto.*;
import com.yl.applets.dto.user.OpenIdBindDTO;
import com.yl.applets.entity.MemberScoreConfig;
import com.yl.applets.entity.MemberUser;
import com.yl.applets.entity.WxUser;
import com.yl.applets.enums.BaseDictionaryEnums;
import com.yl.applets.enums.ChannelMemberEnum;
import com.yl.applets.enums.ChannelSourceEnum;
import com.yl.applets.enums.MemberUserActionSourceEnum;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.PromotionFeighClient;
import com.yl.applets.feign.SmsFeignClient;
import com.yl.applets.service.*;
import com.yl.applets.stream.MemberScoreOutputProcessor;
import com.yl.applets.stream.dto.MemberScoreMqDto;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.utils.IpUtils;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.utils.YlStringUtils;
import com.yl.applets.vo.*;
import com.yl.common.base.annotation.log.operation.ExcludeInterceptor;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.IpAddressUtil;
import com.yl.common.base.util.YlPreconditions;
import com.yl.redis.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.*;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-20
 */
@Api(value = "用户管理", tags = {"用户管理"})
@RestController
@RequestMapping("/user")
@Slf4j
@Validated
public class UserController extends AppBaseController {

    @Autowired
    private IWxUserService wxUserService;


    @Autowired
    private SmsFeignClient smsFeignClient;

    @Autowired
    private WxMaProperties wxMaProperties;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private MemberScoreOutputProcessor memberScoreOutputProcessor;

    @Autowired
    private IMemberUserService memberUserService;

    @Autowired
    private IMemberLevelService memberLevelService;

    @Autowired
    private IMemberScoreConfigService memberScoreConfigService;

    @Autowired
    private IMemberScoreRecordService memberScoreRecordService;

    @Autowired
    private PromotionFeighClient promotionFeighClient;

    @Autowired
    private IMemberUserService iMemberUserService;


    @Value("${sms.source.code:SMS20221229111614037}")
    private String smsSourceCode;

    @Value("${sms.template.code:default_verify_2}")
    private String templateCode;
    @Value("${sms.code.time:2}")
    private String smsCodeTime;
    @Value("${wx.white:136736}")
    private String whiteMobile;

    @Value("${wx.coupon.sign:1}")
    private String couponSign;

    @Autowired
    private DefaultKaptcha producer;
    @Autowired
    private CaptchaService captchaService;

    @Autowired
    private SmsCheckService smsCheckService;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private OrikaBeanMapper beanMapper;

    @Autowired
    private ChapterCheckService chapterCheckService;

    /**
     * 绑定/修改手机号
     */
    @PostMapping("/bindMobile")
    @ApiOperation(value = "换绑手机号", notes = "换绑手机号")
    public Result<WxUserVo> bindMobile(@RequestParam("mobile")
                                       @NotBlank(message = "手机号不能为空")
                                       @Pattern(regexp = "^1\\d{10}$", message = "手机号不合法")
                                       @ApiParam(value = "手机号")
                                       String mobile,
                                       @RequestParam("verificationCode")
                                       @NotBlank(message = "验证码不能为空")
                                       @ApiParam(value = "验证码")
                                       String verificationCode,
                                       @RequestParam("type")
                                       @NotNull(message = "验证码类型不能为空")
                                       @ApiParam(value = "类型:1绑定手机号,2修改手机号获取旧手机，3修改手机号获取新手机，4解绑")
                                       Integer type,
                                       @RequestParam(value = "openid",required = false)
                                           @ApiParam(value = "微信openid")
                                                   String openid,
                                       @RequestParam(value = "unionid",required = false)
                                           @ApiParam(value = "微信unionid")
                                                   String unionid) {
        return success(wxUserService.bindMobile(mobile,verificationCode,type,openid,unionid));
    }
    /**
     * 绑定/修改手机号
     */
    @PostMapping("/loginByVerificationCode")
    @ApiOperation(value = "通过手机号验证码登陆", notes = "通过手机号验证码登陆")
    @ExcludeInterceptor
    public Result<WxUserVo> bindMobileLogin(@RequestParam("mobile")
                                       @NotBlank(message = "手机号不能为空")
                                       @Pattern(regexp = "^1\\d{10}$", message = "手机号不合法")
                                       @ApiParam(value = "手机号")
                                               String mobile,
                                       @RequestParam("verificationCode")
                                       @NotBlank(message = "验证码不能为空")
                                       @ApiParam(value = "验证码")
                                               String verificationCode,
                                       @RequestParam("type")
                                       @NotNull(message = "验证码类型不能为空")
                                       @ApiParam(value = "类型:1绑定手机号,2修改手机号获取旧手机，3修改手机号获取新手机，4解绑")
                                               Integer type,
                                       @RequestParam(value = "openid",required = false)
                                       @ApiParam(value = "微信openid")
                                               String openid,
                                       @RequestParam(value = "unionid",required = false)
                                       @ApiParam(value = "微信unionid")
                                               String unionid) {
        return success(wxUserService.bindMobile(mobile,verificationCode,type,openid,unionid));
    }
    /**
     * 登录时，发送验证码
     */
    @PostMapping("/sendLoginVerificationCode")
    @ApiOperation(value = "登录验证码", notes = "登录验证码")
    @ExcludeInterceptor
//    @SmsAccessLimit(seconds = 60,maxCount = 10)
    public Result<Boolean> sendLoginVerificationCode(@RequestParam("mobile")
                                                     @NotBlank(message = "手机号不能为空")
                                                     @Pattern(regexp = "^1\\d{10}$", message = "手机号不合法")
                                                     @ApiParam(value = "手机号")
                                                             String mobile,
                                                     @RequestParam("type")
                                                     @NotNull(message = "验证码类型不能为空")
                                                     @ApiParam(value = "类型:1")
                                                             Integer type,
                                                     @RequestParam("ticket") @NotBlank(message = "前端回调函数返回的用户验证票据") String ticket

    ) {
        String key;
        switch (type) {
            //登录手机号
            case 1:
                key = BcRedisKeyEnum.APPLETS_VERIFYCODE_BINDMOBILE.keyBuilder(mobile);
                break;
            default:
                throw new ServiceException(ServiceErrCodeEnum.VERIFICATIONCODE_TYPE_ERROR);
        }
        //判断手机号是否已注销，注销的不允许登录（临时2022-6-7 09:11:38）
        if(isAlreadyOff(mobile)){
            log.info("该手机号已经注销,无法登录==>{}",mobile);
            throw new BusinessException(ResultCodeEnum.IS_NOT_LOGIN_MOBILE_ERROR);
        }
//        String capcha_key =BcRedisKeyEnum.APPLETS_CAPTCHA.keyBuilder(cToken);
//        //校验验证码
//        if(!RedisUtil.hasKey(capcha_key) ){
//            throw new BusinessException(ServiceErrCodeEnum.CAPTCHA_ERROR);
//        }
//        if(!captcha.equalsIgnoreCase((String) RedisUtil.get(capcha_key)) ){
//            throw new BusinessException(ServiceErrCodeEnum.CAPTCHA_ERROR);
//        }
//        //验证码使用一次后销毁
//        RedisUtil.delete(capcha_key);
        //校验滑动验证码
        boolean isChapter=true;
        try{
            isChapter=chapterCheckService.captchaCheckApplets(ticket, IpAddressUtil.getIpAdrress(request));
        }catch (Exception ex){
            log.error("验证码出现异常:{}",ex);
        }
        if(!isChapter){
            throw new BusinessException(ResultCodeEnum.USER_CODE_ERROR);
        }
        String verifyCode = String.valueOf((int) (Math.random() * 900000 + 100000));
        SmsSendDTO sendDTO = new SmsSendDTO();
        sendDTO.setSourceCode(smsSourceCode);
        sendDTO.setTemplateCode(templateCode);
        sendDTO.setIp(IpAddressUtil.getIpAdrress(request));
        List<SmsSendDTO.SendDetail> sendDetails = new ArrayList<>();
        SmsSendDTO.SendDetail sendDetail = new SmsSendDTO.SendDetail();
        Map<String, String> map = new HashMap<>();
        map.put("code",verifyCode);//验证码
        map.put("date",smsCodeTime);//分钟
        sendDetail.setParams(map);
        sendDetail.setPhone(mobile);
        sendDetails.add(sendDetail);
        sendDTO.setSendDetails(sendDetails);
        log.info("登录调用短信接口传参：{}", JsonUtils.toJson(sendDTO));
        Result<?> result = smsFeignClient.sendBatch(sendDTO);
        log.info("调用短信接口结果：{}", JsonUtils.toJson(result));
        RedisUtil.setEx(key, verifyCode, 900);
        return success(Boolean.TRUE);
    }


    private boolean isAlreadyOff(String phoneNumber) {
        return Lists.newArrayList(whiteMobile.split(",")).contains(phoneNumber);
    }

    /**
     * 解绑手机号
     */
    @PostMapping("/unbindMobile")
    @ApiOperation(value = "解绑手机号", notes = "解绑手机号")
    public Result<Boolean> unbindMobile(@RequestParam("verificationCode") @NotBlank(message = "验证码不能为空") @ApiParam(value = "验证码") String verificationCode) {
        String key = BcRedisKeyEnum.APPLETS_VERIFYCODE_UNBINDMOBILE.keyBuilder(getUser().getMobile());
        YlPreconditions.checkArgument(verificationCode.equals(RedisUtil.get(key)), new ServiceException(ServiceErrCodeEnum.VERIFICATIONCODE_ERROR));
        RedisUtil.delete(key);
        return success(wxUserService.unbindMobile());
    }

    /**
     * 发送验证码
     */
    @PostMapping("/sendVerificationCode")
    @ApiOperation(value = "发送验证码", notes = "发送验证码")
//    @SmsAccessLimit(seconds = 60,maxCount = 10)
    public Result<Boolean> sendVerificationCode(@RequestParam("mobile")
                                                @NotBlank(message = "手机号不能为空")
                                                @Pattern(regexp = "^1\\d{10}$", message = "手机号不合法")
                                                @ApiParam(value = "手机号")
                                                String mobile,
                                                @RequestParam("type")
                                                @NotNull(message = "验证码类型不能为空")
                                                @ApiParam(value = "类型:1绑定手机号,2修改手机号获取旧手机，3修改手机号获取新手机，4解绑，5绑定实名信息 6理赔发送验证码")
                                                Integer type,
                                                @RequestParam("ticket") @NotBlank(message = "前端回调函数返回的用户验证票据") String ticket
    ) {
        String key;
        switch (type) {
            //绑定手机号
            case 1:
                key = BcRedisKeyEnum.APPLETS_VERIFYCODE_BINDMOBILE.keyBuilder(mobile);
                break;
            //修改手机号时获取旧手机号的验证码
            case 2:
                key = BcRedisKeyEnum.APPLETS_VERIFYCODE_MODIFYMOBILEOLD.keyBuilder(mobile);
                break;
            //修改手机号时获取新手机号的验证码
            case 3:
                key = BcRedisKeyEnum.APPLETS_VERIFYCODE_MODIFYMOBILENEW.keyBuilder(mobile);
                break;
            //解绑手机号
            case 4:
                key = BcRedisKeyEnum.APPLETS_VERIFYCODE_UNBINDMOBILE.keyBuilder(mobile);
                break;
            // 实名制
            case 5:
                // @update: 微信小程序端前缀 实名制统一更换
                key = RedisKeyConstants.SAVE_APPLETS_USER_CARD_CODE_CHECK_KEY + mobile;
                break;
            // 理赔发送验证码
            case 6:
                key = BcRedisKeyEnum.APPLETS_VERIFYCODE_INDEMNITY.keyBuilder(mobile);
                break;
            default:
                throw new ServiceException(ServiceErrCodeEnum.VERIFICATIONCODE_TYPE_ERROR);
        }
        //校验滑动验证码
        boolean isChapter = true;
        try {
            isChapter = chapterCheckService.captchaCheckApplets(ticket, IpAddressUtil.getIpAdrress(request));
        } catch (Exception ex) {
            log.error("验证码出现异常:", ex);
        }
        if (!isChapter) {
            throw new BusinessException(ResultCodeEnum.USER_CODE_ERROR);
        }
        smsCheckService.smsSendCheck(request.getContextPath(), IpAddressUtil.getIpAdrress(request), type + "", mobile, 10, 60);
        String verifyCode = String.valueOf((int) (Math.random() * 900000 + 100000));
        SmsSendDTO sendDTO = new SmsSendDTO();
        sendDTO.setSourceCode(smsSourceCode);
        sendDTO.setTemplateCode(templateCode);
        sendDTO.setIp(IpAddressUtil.getIpAdrress(request));
        List<SmsSendDTO.SendDetail> sendDetails = new ArrayList<>();
        SmsSendDTO.SendDetail sendDetail = new SmsSendDTO.SendDetail();
        Map<String, String> map = new HashMap<>();
        map.put("code", verifyCode);//验证码
        map.put("date", smsCodeTime);//分钟
        sendDetail.setParams(map);
        sendDetail.setPhone(mobile);
        sendDetails.add(sendDetail);
        sendDTO.setSendDetails(sendDetails);
        log.info("登录调用短信接口传参：{}", JsonUtils.toJson(sendDTO));
        Result<?> result = smsFeignClient.sendBatch(sendDTO);
        log.info("调用短信接口结果：{}", JsonUtils.toJson(result));
        RedisUtil.setEx(key, verifyCode, 900);

        return success(Boolean.TRUE);
    }

    /**
     * 校验验证码
     */
    @PostMapping("/checkVerificationCode")
    @ApiOperation(value = "校验验证码", notes = "校验验证码")
    public Result<Boolean> checkVerificationCode(@RequestParam("verificationCode") @NotBlank(message = "验证码不能为空") String verificationCode) {
        String key = BcRedisKeyEnum.APPLETS_VERIFYCODE_MODIFYMOBILEOLD.keyBuilder(getUser().getMobile());
        YlPreconditions.checkArgument(verificationCode.equals(RedisUtil.get(key)), new ServiceException(ServiceErrCodeEnum.VERIFICATIONCODE_ERROR));
        RedisUtil.delete(key);
        return success(Boolean.TRUE);
    }

    /**
     * 功能描述:
     * 是否关注公众号
     * @param wxUserDTO
     * @return:com.yl.common.base.model.vo.Result<java.lang.Boolean>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2020-12-18 14:24
     */
    @PostMapping("/isSubscribe")
    @ApiOperation(value = "关注公众号V1", notes = "关注公众号V1")
    public Result<Boolean> isSubscribe(@RequestBody WxUserDTO wxUserDTO) {
        if (StringUtils.isBlank(wxUserDTO.getCode())) {
            return Result.error(ResultCodeEnum.FAIL);
        }
        Boolean isSubscribe = false;
        String appId = wxMaProperties.getConfigs().get(0).getAppid();
        final WxMaService wxService = WxMaConfiguration.getMaService(appId);
        WxMaJscode2SessionResult session = null;
        try {
            session = wxService.getUserService().getSessionInfo(wxUserDTO.getCode());
        } catch (WxErrorException e) {
            log.error(e.getMessage(), e);
            return Result.error(ResultCodeEnum.FAIL);
        }
        //存在unionid，则已关注公众号
        if(StringUtils.isNotEmpty(session.getUnionid())){
            isSubscribe = true;
        }
        return Result.success(isSubscribe);
    }

    /**
     * 功能描述:
     * 是否关注公众号
     * @param wxUserDTO
     * @return:com.yl.common.base.model.vo.Result<java.lang.Boolean>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2020-12-18 14:24
     */
    @PostMapping("/v2/isSubscribe")
    @ApiOperation(value = "关注公众号V2", notes = "关注公众号V2")
    public Result<SubscribeVO> isSubscribeV2(@RequestBody WxUserDTO wxUserDTO) {
        return Result.success(wxUserService.isSubscribeV2(wxUserDTO));
    }

   /**
    * 功能描述:
    * 小程序一次性订阅消息上报
    * @param miniSubscribeDTO
    * @return:com.yl.common.base.model.vo.Result<com.yl.applets.vo.SubscribeVO>
    * @since: 1.0.0
    * @Author:luhong
    * @Date: 2021-04-22 18:22
    */
    @PostMapping("/subscribe/report")
    @ApiOperation(value = "小程序一次性订阅消息上报", notes = "小程序一次性订阅消息上报")
    public Result miniSubscribeReport(@RequestBody MiniSubscribeDTO miniSubscribeDTO) {
        log.info("小程序订阅消息==>{}",JSON.toJSONString(miniSubscribeDTO));
        wxUserService.miniSubscribeReport(miniSubscribeDTO,getUser());
        return Result.success();
    }


    /**
     * 签到
     * 返回连签次数和是否领取了优惠券
     */
    @PostMapping("/sign")
    @ApiOperation(value = "小程序-签到", notes = "小程序-签到")
    public Result<Map<String,Object>> sign(){
        String requestId = UUID.randomUUID().toString().replace("-", "");
        Map<String,Object> map= Maps.newHashMap();
        map.put("coupon","0");//优惠券
        map.put("score","0");//积分
        map.put("grow","0");//成长值
        WxUserVo user = getUser();
        log.info("请求id==>{}当前用户签到==>{}",requestId, JSON.toJSONString(user));
        //是否已经签到
        //加分之后，记录连签次数   memberId,次数,截止明天23:59:59的时间
        RedisAtomicLong entityIdCounter = new RedisAtomicLong("WX:APPLETS:SIGN"+user.getNumberId()+"", redisTemplate.getConnectionFactory());
        if (isSign().getData()) {
            log.info("请求id==>{},用户已经签到",requestId);
            map.put("day",entityIdCounter.get());
            return Result.success(map);
        }
        //1.发起签到
        MemberScoreMqDto dto=new MemberScoreMqDto();
        dto.setId(user.getId().longValue());
        dto.setMemberId(user.getNumberId());
        dto.setMemberAction(MemberUserActionSourceEnum.QD.getCode());
        dto.setType(ChannelSourceEnum.WX.getKey());
//        //发送mq，获取签到的积分和成长值
        memberScoreOutputProcessor.sendScorePushMessage(dto);
        //2.天数+1
        entityIdCounter.getAndIncrement();
        entityIdCounter.expire(getNextDayMaxTime(LocalDateTime.now()), TimeUnit.SECONDS);
        long count = entityIdCounter.get();
        if(count>7){
            log.info("请求id==>{}连签天数超过7天,重置为第一天==>{}",requestId,count);
            entityIdCounter.set(1);
            count = entityIdCounter.get();
        }else {
            log.info("请求id==>{}连签天数==>{}",requestId,count);
        }
        map.put("day",count);
        //3.查询活动配置。
        log.info("请求id==>{}查询活动配置,入参==>{}",requestId);
        Result<List<MemberActivityQueryDto>> listResult = promotionFeighClient.activityList();
        log.info("请求id==>{}查询活动配置,出参==>{}",requestId,JSON.toJSONString(listResult));
        List<MemberActivityQueryDto> result = listResult.result();
        if(result.isEmpty()){
            log.info("请求id==>{}当前没有查询到活动配置",requestId);
        }
        List<MemberActivityQueryDto> isRight=Lists.newArrayList();
        for (MemberActivityQueryDto memberActivityQueryDto : result) {
            //判断是否到达天数
            log.info("请求id==>{}当前活动配置==>{}",requestId,JSON.toJSONString(memberActivityQueryDto));
            if (BaseDictionaryEnums.ACTIVITY_TYPE_1.getCode().equals(memberActivityQueryDto.getType())&&
                count==BaseDictionaryEnums.ACTIVITY_TYPE_1.getCount().longValue()){
                log.info("请求id==>{}【连续签到3天】,符合活动配置",requestId);
                isRight.add(memberActivityQueryDto);
            }
            if (BaseDictionaryEnums.ACTIVITY_TYPE_2.getCode().equals(memberActivityQueryDto.getType())&&
                    count==BaseDictionaryEnums.ACTIVITY_TYPE_2.getCount().longValue()){
                log.info("请求id==>{}【连续签到7天】,符合活动配置",requestId);
                isRight.add(memberActivityQueryDto);
            }
        }
        //达到天数
        if(!isRight.isEmpty()){
            Set<Boolean> isSuccess= Sets.newHashSet();
            for (MemberActivityQueryDto queryDto : isRight) {
                if(BaseDictionaryEnums.GIFT_TYPE_1.getCode().equals(queryDto.getGiftType())){//积分
                    handleScore(user,queryDto.getGiftRelation(),requestId);
                    map.put("score",queryDto.getGiftRelation());//积分
                }
                if(BaseDictionaryEnums.GIFT_TYPE_2.getCode().equals(queryDto.getGiftType())){//成长值
                    handleGrow(user,queryDto.getGiftRelation(),requestId);
                    map.put("grow",queryDto.getGiftRelation());//成长值
                }
                if(BaseDictionaryEnums.GIFT_TYPE_3.getCode().equals(queryDto.getGiftType())){//签到券
                    Integer integer = handleCoupon(user, queryDto.getGiftRelation(), requestId);
                    //只要有成功的，就是领取成功
                    isSuccess.add(integer==1);
                }
            }
            if(!isSuccess.contains(true)){
                map.put("coupon","-1");//领取失败
            }else {
                map.put("coupon","1");//领取成功
            }
        }else {
            log.info("请求id==>{}当前没有查询到活动配置",requestId);
        }
        long day =(long) map.get("day");
        //设置签到流量统计key
        extracted(requestId, user, day);

        return Result.success(map);
    }

    /***
     * 设置签到流量统计key
     *
     * @param requestId 请求id
     * @param user  用户
     * @param day 签到几天
     */
    private  void extracted(String requestId, WxUserVo user ,long day) {
        LocalDate now = LocalDate.now();
        int monthValue = now.getMonthValue();
        String sevenKey;
        String threeKey;
        //等于null 说明可能就是Null 或者 是之前的缓存中没有存过期时间导致转换为null  需要再查一次
        if (user.getMemberExpiresTime() == null){
           //重新查库，判断过期时间
            long start = System.currentTimeMillis();

            MemberUser memberUser = iMemberUserService.getOne(new QueryWrapper<MemberUser>().eq("member_id", user.getNumberId()));
            long end = System.currentTimeMillis();
            log.info("更新签到流量数据，MemberExpiresTime为空，重新查库 入参：{}，出参：{} 耗时：{}", JsonUtils.toJson(user), JsonUtils.toJson(memberUser), (end-start));
            if (memberUser != null) {
                user.setMemberExpiresTime(memberUser.getMemberExpiresTime());
            }
        }
        if (day == 3) {
            //VIP
            if (user.getMemberExpiresTime() != null && LocalDateTime.now().isBefore(user.getMemberExpiresTime())) {
                threeKey = BcRedisKeyEnum.STATISTICS_SIGN_VIP_THREE_MONTH.keyBuilder(String.valueOf(monthValue));
            } else {
                //会员
                threeKey = BcRedisKeyEnum.STATISTICS_SIGN_COMMON_THREE_MONTH.keyBuilder(String.valueOf(monthValue));
            }
            log.info("请求id==>{}连续签到三天，签到流量数据加1：{}", requestId, day);
            //设置自增
            RedisUtil.incrBy(threeKey, 1);
        }
        if (day ==7){
            //流量签到报表
            if (user.getMemberExpiresTime() != null && LocalDateTime.now().isBefore(user.getMemberExpiresTime())) {
                //VIP
                sevenKey = BcRedisKeyEnum.STATISTICS_SIGN_VIP_SEVEN_MONTH.keyBuilder(String.valueOf(monthValue));
            }else {
                //会员
                sevenKey = BcRedisKeyEnum.STATISTICS_SIGN_COMMON_SEVEN_MONTH.keyBuilder(String.valueOf(monthValue));
            }
            log.info("请求id==>{}连续签到七天，签到流量数据加1：{}", requestId, day);
            //设置自增
            RedisUtil.incrBy(sevenKey, 1);
        }
    }


    /**
     * 签到
     * 返回连签次数和是否领取了优惠券
     */
    @PostMapping("/coupon/sign")
    @ApiOperation(value = "小程序-签到", notes = "小程序-签到")
    public Result<Map<String,Object>> couponSign(String numberId){
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("请求id==>{}用户补签,领券入参==>{}",requestId,numberId);
        if(StringUtils.equals("0",couponSign)){
            log.info("请求id==>{},当前不可补签==>{}",requestId,couponSign);
            return null;
        }
        Map<String,Object> map= Maps.newHashMap();
//        WxUserVo user = getUser();
        map.put("coupon", "0");//不该领取
        QueryWrapper<WxUser> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("number_id", numberId).eq("is_delete",0).eq("type",4);
        WxUser one = wxUserService.getOne(queryWrapper);
        WxUserVo user = beanMapper.map(one, WxUserVo.class);
        log.info("请求id==>{}当前用户签到==>{}",requestId, JSON.toJSONString(user));
        if(user==null){
            log.info("用户为空");
            return null;
        }
        //是否已经签到3天
        RedisAtomicLong entityIdCounter = new RedisAtomicLong("WX:APPLETS:SIGN"+user.getNumberId()+"", redisTemplate.getConnectionFactory());
        //1.发起签到
        long count = entityIdCounter.get();
        if(count==3L){
            //3.查询活动配置。
            log.info("请求id==>{}查询活动配置,入参==>{}",requestId);
            Result<List<MemberActivityQueryDto>> listResult = promotionFeighClient.activityList();
            log.info("请求id==>{}查询活动配置,出参==>{}",requestId,JSON.toJSONString(listResult));
            List<MemberActivityQueryDto> result = listResult.result();
            if(result.isEmpty()){
                log.info("请求id==>{}当前没有查询到活动配置",requestId);
            }
            List<MemberActivityQueryDto> isRight=Lists.newArrayList();
            for (MemberActivityQueryDto memberActivityQueryDto : result) {
                //判断是否到达天数
                if (BaseDictionaryEnums.ACTIVITY_TYPE_1.getCode().equals(memberActivityQueryDto.getType())&&
                        count==BaseDictionaryEnums.ACTIVITY_TYPE_1.getCount().longValue()){
                    log.info("请求id==>{}【连续签到3天】,符合活动配置==>{}",requestId,JSON.toJSONString(memberActivityQueryDto));
                    isRight.add(memberActivityQueryDto);
                }
            }
            //达到天数
            if(!isRight.isEmpty()) {
                Set<Boolean> isSuccess = Sets.newHashSet();
                for (MemberActivityQueryDto queryDto : isRight) {
                    if (BaseDictionaryEnums.GIFT_TYPE_3.getCode().equals(queryDto.getGiftType())) {//签到券
                        Integer integer = handleCoupon(user, queryDto.getGiftRelation(), requestId);
                        //只要有成功的，就是领取成功
                        isSuccess.add(integer == 1);
                    }
                }
                if (!isSuccess.contains(true)) {
                    map.put("coupon", "-1");//领取失败
                } else {
                    map.put("coupon", "1");//领取成功
                }
            }
        }
        return Result.success(map);
    }

    /**
     * 功能描述:
     * 是否第一次点击兔优达
     */
    @PostMapping("/isFirstTYD")
    @ApiOperation(value = "是否第一次点击兔优达", notes = "是否第一次点击兔优达")
    public Result<Integer> isFirstTYD() {
        return Result.success(wxUserService.isFirstTYD());
    }

    /**
     * 功能描述:
     * 是否第一次点击兔优达
     */
    @PostMapping("/updateFirstTYD")
    @ApiOperation(value = "是否第一次点击兔优达", notes = "是否第一次点击兔优达")
    public Result updateFirstTYD() {
        wxUserService.updateFirstTYD();
        return Result.success();
    }


    /**
     * 给用户发送优惠券
     * @param user
     * @param giftRelation
     */
    private Integer handleCoupon(WxUserVo user, String giftRelation,String requestId) {
        PromotionUserGetAppletsDto appletsDto=new PromotionUserGetAppletsDto();
        appletsDto.setUserAlias(user.getNickName());
        appletsDto.setUserId(user.getId().longValue());
        appletsDto.setUserUuid(user.getOpenid());
        appletsDto.setUserPhone(user.getMobile());
        appletsDto.setNumberId(user.getNumberId().longValue());
        appletsDto.setProId(Long.parseLong(giftRelation));
        log.info("请求id==>{},用户连签,领取优惠券,入参==>{}",requestId,JSON.toJSONString(appletsDto));
        Result<Long> activityCoupon = promotionFeighClient.getActivityCoupon(appletsDto);
        log.info("请求id==>{},用户连签,领取优惠券,出参==>{}",requestId,JSON.toJSONString(activityCoupon));
        if(activityCoupon.isSucc()){
            return 1;
        }else {
            log.info("请求id==>{},用户连签,领取优惠券失败",requestId);
            return -1;
        }
    }


    /**
     * 增加用户成长值
     * @param user
     * @param giftRelation
     */
    private void handleGrow(WxUserVo user, String giftRelation,String requestId) {
        MemberScoreMqDto dto=new MemberScoreMqDto();
        dto.setId(user.getId().longValue());
        dto.setMemberId(user.getNumberId());
        dto.setMemberAction(MemberUserActionSourceEnum.HDQD.getCode());
        dto.setType(ChannelSourceEnum.WX.getKey());
        dto.setGrowValue(Integer.parseInt(giftRelation));
        memberScoreOutputProcessor.sendScoreActivityMessage(dto);
        log.info("请求id==>{},增加增加用户成长值mq发送成功==>{}",requestId,JSON.toJSONString(dto));
    }

    /**
     * 增加用户积分
     * @param user
     * @param giftRelation
     */
    private void handleScore(WxUserVo user, String giftRelation,String requestId) {
        MemberScoreMqDto dto=new MemberScoreMqDto();
        dto.setId(user.getId().longValue());
        dto.setMemberId(user.getNumberId());
        dto.setMemberAction(MemberUserActionSourceEnum.HDQD.getCode());
        dto.setType(ChannelSourceEnum.WX.getKey());
        dto.setScoreValue(Integer.parseInt(giftRelation));
        memberScoreOutputProcessor.sendScoreActivityMessage(dto);
        log.info("请求id==>{},增加用户积分mq发送成功==>{}",requestId,JSON.toJSONString(dto));
    }


    /**
     * 获取连签截止秒数
     * @param now
     * @return
     */
    private Long getNextDayMaxTime(LocalDateTime now) {
        LocalDateTime end = now.with(LocalTime.MAX).plusDays(1);
        log.info("连签截止时间==>{}",end);
        long seconds = Duration.between(now, end).getSeconds();
        log.info("连签剩余{}s",seconds);
        return seconds;
    }

    @GetMapping("/isSign")
    @ApiOperation(value = "是否签到",notes = "是否签到")
    public Result<Boolean> isSign(){
        String key = ChannelMemberEnum.MemberUserRedis.CHANNEL_PRE + ":" + ChannelSourceEnum.WX.getKey() + ":" + MemberUserActionSourceEnum.QD.getCode();
        Integer numberId = getUser().getNumberId();
        log.info("微信签到key：{},{}",key,numberId);
//        Object score = RedisUtil.hGet(key, ChannelMemberEnum.MemberUserRedis.SCORE_LIMIT + numberId);
        Object grow = RedisUtil.hGet(key, ChannelMemberEnum.MemberUserRedis.GROW_LIMIT + numberId);
        String channelMemberScoreConfig = ChannelMemberEnum.MemberUserRedis.CHANNEL_MEMBER_SCORE_CONFIG;
        //获取积分配置规则 redis
        List<MemberScoreConfig> configs= Lists.newArrayList();
        Object o = RedisUtil.get(channelMemberScoreConfig);
        if(o!=null){
            log.info("从缓存中获取积分成长配置");
            configs = (List<MemberScoreConfig>) o;
        }else {
            log.info("从DB查询积分成长配置，set到redis");
            configs = memberScoreConfigService.list();
            if(!configs.isEmpty()){
                RedisUtil.set(ChannelMemberEnum.MemberUserRedis.CHANNEL_MEMBER_SCORE_CONFIG,configs);
            }
        }
        List<MemberScoreConfig> collect = configs.stream().filter(one -> ChannelSourceEnum.WX.getKey().equals(one.getType()) && MemberUserActionSourceEnum.QD.getCode().equals(one.getMemberAction())).collect(Collectors.toList());
        if(collect!=null&&!collect.isEmpty()){
            boolean b=false;
            MemberScoreConfig config = collect.get(0);
            log.info("微信小程序签到,配置==>{}",JSON.toJSONString(config));
            //是否为空，且不小于配置限制
            if(grow!=null){
                long lgrow = Long.parseLong(grow + "");
                b=lgrow>=config.getGrowLimit().longValue();
                log.info("微信小程序签到,成长值缓存==>{},配置上限==>{}",lgrow,config.getGrowLimit());
            }
            return Result.success(b);
        }else {
            //true，达到限制
            log.info("微信小程序是否签到,签到配置为空,或成长值缓存不为空");
            return Result.success(grow!=null||collect.isEmpty());
        }
    }

    @GetMapping("/qureyMemberLevel")
    @ApiOperation(value = "查询会员等级配置",notes = "查询会员等级配置")
    public Result<List<MemberLevelDto>> qureyMemberLevel(){
        List<MemberLevelVO> levelVOList = memberLevelService.listLevel();
        log.info("查询会员等级配置,原始配置==>{}",JSON.toJSONString(levelVOList));
        List<MemberLevelDto> result = Lists.newArrayList();
        levelVOList.forEach(o->{
            MemberLevelDto dto=new MemberLevelDto();
            dto.setLevel(o.getMemberLevel());
            dto.setName(o.getLevelName());
            dto.setStart(o.getGrowScoreStart());
            dto.setEnd(o.getGrowScoreEnd());
            result.add(dto);
        });
        return Result.success(result);
    }


    @GetMapping("/qureyMyselfGrow")
    @ApiOperation(value = "查询个人成长值以及对应会员信息",notes = "查询个人成长值以及对应会员信息")
    public Result<MemberUserDetailVo> qureyMyselfGrow(){
        return memberUserService.queryMemberUser(getUser());
    }

    @GetMapping("/queryMyselfScoreRecord")
    @ApiOperation(value = "查询个人成长值明细",notes = "查询个人成长值明细")
    public Result<IPage<MemberScoreRecordVo>> queryScoreRecord(Page page){
        return memberScoreRecordService.queryScoreRecord(getUser() ,page);
    }

    @GetMapping("/qureyGrowConfig")
    @ApiOperation(value = "查询成长值计划列表",notes = "查询成长值计划列表")
    public Result<List<MemberScoreConfigVo>> qureyGrowConfig(){
        return memberScoreConfigService.qureyGrowConfigByChannel(getUser());
    }

    @PostMapping("/shareOrder")
    @ApiOperation(value = "分享订单",notes = "分享订单")
    public Result<Boolean> shareOrder(@RequestBody(required = false) @Valid ShareOrderDto dto){
        if(dto==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        log.info("用户分享订单，链接被打开。入参：{}：当前用户{}", JSON.toJSONString(dto));
        //是否过期
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime dateTime =LocalDateTime.ofEpochSecond(dto.getTimestamp()/1000,0, ZoneOffset.ofHours(8));
        if(now.isBefore(dateTime)){
            log.info("分享时间有误，舍弃数据");
            return Result.success(false);
        }
        if(dateTime.isBefore(now.minusMonths(1))){
            log.info("超过一个月过期分享，该用户不再加分");
            return Result.success(false);
        }
        if (!YlStringUtils.isNumeric(dto.getMemberId())) {
            log.info("memberId,入参异常，分享用户不存在：{}；当前登录人信息：{}",dto.getMemberId(),JSON.toJSONString(getUser()));
            return Result.success(false);
        }
        //根据openid查用户信息
        QueryWrapper<WxUser> queryOpenid = new QueryWrapper<>();
        queryOpenid.eq("number_id", dto.getMemberId());
        queryOpenid.eq("is_delete",0);
        queryOpenid.eq("type", ChannelSourceEnum.WX.getKey());
        WxUser one = wxUserService.getOne(queryOpenid);
        if(one==null){
            log.info("分享者不存在");
            return Result.success(false);
        }
        if(StringUtils.equals(dto.getBeSharedOpenid(),one.getOpenid())){
            log.info("分享者打开自己的分享时，不加分");
            return Result.success(false);
        }
        //判断reids里缓存的数据、是否重复:分享者会员id+订单
        Object o = RedisUtil.get(BcRedisKeyEnum.CHANNEL_SHARE_ORDER.keyBuilder(dto.getMemberId() + ":" + dto.getOrderId()));
        if(o!=null){
            log.info("已经被分享，此用户{}不能重复加分",dto.getMemberId());
            return Result.success(false);
        }
        MemberScoreMqDto MQdto=new MemberScoreMqDto();
        MQdto.setId(one.getId().longValue());//分享人的id
        MQdto.setMemberId(one.getNumberId());//分享人的memberId
        MQdto.setMemberAction(MemberUserActionSourceEnum.FX.getCode());
        MQdto.setType(ChannelSourceEnum.WX.getKey());
        MQdto.setDto(dto);
        memberScoreOutputProcessor.sendScorePushMessage(MQdto);
        return Result.success(true);
    }


    @PostMapping("/isLogin")
    @ApiOperation(value = "用户免登录",notes = "用户免登录")
    public Result<Boolean> isLogin(){
        WxUserVo user = getUser();
        return success(wxUserService.isLogin(user));
    }

    @ResponseBody
    @ExcludeInterceptor
    @PostMapping("/captcha")
    public Result captcha(HttpServletResponse response) throws IOException {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("生成验证码-requestId==>{}", requestId);
        // 生成文字验证码
        String text = producer.createText();
        log.info("图片验证码：{}",text);
        // 生成图片验证码
        ByteArrayOutputStream outputStream = null;
        BufferedImage image = producer.createImage(text);

        outputStream = new ByteArrayOutputStream();
        ImageIO.write(image, "jpg", outputStream);

        // 对字节数组Base64编码
        BASE64Encoder encoder = new BASE64Encoder();

        // 生成captcha的token
        Map<String, Object> map =captchaService.createToken(text);
        String start = "data:image/jpeg;base64,";
        map.put("img",start + encoder.encode(outputStream.toByteArray()).replace("\n", "").replace("\r", ""));
        log.info("生成验证码-requestId==>{}, 返回值==>{}", requestId, map);
        return success(map);
    }


    @PostMapping("/queryMobile")
    @ApiOperation(value = "用户手机号",notes = "用户手机号")
    public Result<String> queryMobile(){
        WxUserVo user = getUser();
        return success(user.getMobile());
    }

    @PostMapping("/applyMember")
    @ApiOperation(value = "会员申请", notes = "会员申请")
    public Result<Boolean> applyMember() throws InterruptedException {
        return Result.success(wxUserService.applyMember());
    }

    @PostMapping("/addActionRecord")
    @ApiOperation(value = "用户行为记录", notes = "用户行为记录")
    public Result addActionRecord(@RequestBody ActionRecordDTO dto) {
        //获取ip地址
        String ipAddr = IpUtils.getIpAddr(request);
        dto.setIpAddress(ipAddr);
        wxUserService.addActionRecord(dto);
        return Result.success();
    }


    //提供给小程序,签到/分享随机图文信息
    @GetMapping("/itInfoByType")
    @ApiOperation(value = "itInfo", notes = "随机图文信息")
    public Result<MemberImageTextQueryDto> itInfoByType(@RequestParam("type") String type){
        log.info("分享随机图文信息,入参==>{}",type);
        Result<MemberImageTextQueryDto> memberImageTextQueryDtoResult = promotionFeighClient.itInfoByType(type);
        log.info("分享随机图文信息,出参==>{}",JSON.toJSONString(memberImageTextQueryDtoResult));
        return memberImageTextQueryDtoResult;
    }


    @GetMapping("/clearUser")
    @ExcludeInterceptor
    public Result<String> clearUser(@RequestParam("userId") String userId){
        WxUserVo user = getUser();
        log.info("清除信息,入参==>{}", JSON.toJSONString(user));
        RedisUtil.delete(BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(user.getNumberId().toString()));
        return Result.success("success");
    }

    @PostMapping("/checkToken")
    public Result<Boolean> checkToken(){
        WxUserVo user = getUser();
        return Result.success(true);
    }

    @PostMapping("/expire/extension")
    public Result<Boolean> extension()  throws InterruptedException{
        WxUserVo user = getUser();
        return Result.success(wxUserService.extension(user));
    }

    @PostMapping("/getTokenByOpen")
    @ExcludeInterceptor
    public Result<WxUserVo> getToken(@RequestBody @Valid OpenIdBindDTO dto){
        log.info("用户获取缓存数据,入参:{}", JSON.toJSONString(dto));
        String md5Hex = DigestUtils.md5Hex(DigestUtils.md5Hex(dto.getCode() + dto.getTimes() + "JT@2023AppLets"));
        log.info("后端加密校验md5:{}", md5Hex);

        if (!StrUtil.equals(dto.getSign(), md5Hex)) throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        return Result.success(wxUserService.getUser(dto.getCode()));

    }

}
