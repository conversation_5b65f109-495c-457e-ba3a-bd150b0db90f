package com.yl.applets.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yl.applets.dto.MyStaffCheckAddressDTO;
import com.yl.applets.dto.MyStaffPageDTO;
import com.yl.applets.dto.MyStaffUpdateDefaultDTO;
import com.yl.applets.entity.BasePage;
import com.yl.applets.entity.MyStaff;
import com.yl.applets.enums.StaffExclusiveTypeEnum;
import com.yl.applets.service.IMyStaffService;
import com.yl.applets.service.IWxUserService;
import com.yl.applets.vo.FrequentAddressVO;
import com.yl.applets.vo.MyStaffVO;
import com.yl.applets.vo.RandomStaffVo;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 专属会员 前端控制器
 * </p>
 *
 * <AUTHOR> @since 2020-01-07
 */
@RestController
@RequestMapping("/myStaff")
public class MyStaffController extends AppBaseController{
    @Autowired
    private IMyStaffService myStaffService;

    @Autowired
    private IWxUserService wxUserService;

    @ApiOperation(value = "获取专属业务员", notes = "获取专属业务员")
    @GetMapping("/list")
    public Result<List<MyStaff>> list() {

        List<Long> ids = wxUserService.getIdsByIdAndNumberId(getUser().getNumberId());
        LambdaQueryWrapper<MyStaff> lambdaQueryWrapper =  new LambdaQueryWrapper<MyStaff>().in(MyStaff::getUserId,ids).orderByDesc(MyStaff::getUpdateTime);
        List<MyStaff> list = myStaffService.list(lambdaQueryWrapper);
        //去重
        Set<String> code= Sets.newHashSet();
        List<MyStaff> result= Lists.newArrayList();
        for (MyStaff record : list) {
            if(!code.contains(record.getStaffNo())){
                result.add(record);
            }
            code.add(record.getStaffNo());
        }
        return success(result);
    }


    @ApiOperation(value = "获取专属业务员", notes = "获取专属业务员")
    @GetMapping("/list/new")
    public  Result<Page<MyStaffVO>> listNew(MyStaffPageDTO dto) {
        WxUserVo user = getUser();
        dto.setNumberId(user.getNumberId());
        return myStaffService.getMyStaffList(dto);
    }

    @ApiOperation(value = "删除专属业务员", notes = "删除专属业务员")
    @PostMapping("/remove")
    public Result<Boolean> remove(@RequestParam("id")Long id) {
        List<Long> ids = wxUserService.getIdsByIdAndNumberId(getUser().getNumberId());
        return success(myStaffService.remove(new LambdaQueryWrapper<MyStaff>().in(MyStaff::getUserId,ids).eq(MyStaff::getId,id)));
    }


    @ApiOperation(value = "绑定专属业务员",notes = "绑定专属业务员")
    @GetMapping("/bindStaff")
    public Result<Boolean> bindStaff(@RequestParam("staffCode")String staffCode) {
        return success(myStaffService.bindStaff(staffCode,getUser(), StaffExclusiveTypeEnum.NEW.getCode()));
    }


    /**
     * 根据随机码查询业务员信息,并且通知支付宝
     * @param randomCode
     * @return
     */
    @ApiOperation(value = "根据随机码查询业务员信息",notes = "根据随机码查询业务员信息")
    @GetMapping("/getStaffAndBinding")
    public Result<RandomStaffVo> getStaffAndBinding(@RequestParam("randomCode")String randomCode) {
        return success(myStaffService.getStaffAndBinding(randomCode,getUser()));
    }


    @ApiOperation(value = "查询专属业务员",notes = "查询专属业务员")
    @GetMapping("/queryStaff")
    public Result<Boolean> queryStaff(@RequestParam("staffCode")String staffCode) {
        return success(myStaffService.queryStaff(staffCode,getUser()));
    }

    @ApiOperation(value = "修改默认专属业务员", notes = "修改默认专属业务员")
    @PostMapping("/update/default/staff")
    public Result<String> updateDefaultStaff(@RequestBody @Validated MyStaffUpdateDefaultDTO dto) {
        WxUserVo user = getUser();
        dto.setNumberId(user.getNumberId());
        return myStaffService.updateDefaultStaff(dto);
    }


    @ApiOperation(value = "检查寄件信息是否符合标准", notes = "检查寄件信息是否符合标准")
    @PostMapping("/check/send/address")
    public Result<Boolean> checkSendAddress(@RequestBody  @Validated MyStaffCheckAddressDTO dto) {
        return myStaffService.checkSendAddress(dto);
    }


    @ApiOperation(value = "获取客户常用地址", notes = "获取客户常用地址")
    @GetMapping("/address/by/userId")
    public Result<List<FrequentAddressVO>> getFrequentAddressByUserId() {
        WxUserVo user = getUser();
        return myStaffService.getFrequentAddressByUserId(user.getNumberId());
    }


}
