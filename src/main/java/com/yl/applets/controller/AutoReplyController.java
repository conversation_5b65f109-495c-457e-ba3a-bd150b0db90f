package com.yl.applets.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.AutoReplyDTO;
import com.yl.applets.dto.AutoReplyQueryDto;
import com.yl.applets.dto.WxMaterialDto;
import com.yl.applets.service.IAutoReplyService;
import com.yl.applets.vo.AutoReplyVO;
import com.yl.applets.vo.WxFreePublishVo;
import com.yl.common.base.annotation.log.operation.ExcludeInterceptor;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import me.chanjar.weixin.mp.bean.material.WxMpMaterialFileBatchGetResult;
import me.chanjar.weixin.mp.bean.material.WxMpMaterialNewsBatchGetResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2021<br>
 *
 * @description: 小程序自动回复规则
 * @author: caiyao
 * @create: 2021-05-20 10:11
 */
@Api(value = "自动回复规则", tags = {"自动回复规则"})
@RestController
@RequestMapping("/autoreply")
public class AutoReplyController {

    @Autowired
    private IAutoReplyService iAutoReplyService;

    /**
     * 新增、更新(1:关注回复 2:收到消息回复)自动回复规则
     *
     * @param dto
     * @return
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增、更新(1:关注回复 2:收到消息回复)自动回复规则", notes = "新增、更新(1:关注回复 2:收到消息回复)自动回复规则")
    @ExcludeInterceptor
    public Result add(@RequestBody(required = false) AutoReplyDTO dto) {
        if(dto==null){
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        return iAutoReplyService.add(dto);
    }

    /**
     * 新增关键词自动回复规则
     *
     * @param dto
     * @return
     */
    @PostMapping("/addKeyWordReply")
    @ApiOperation(value = "新增关键词自动回复规则", notes = "新增关键词自动回复规则")
    @ExcludeInterceptor
    public Result addKeyWordReply(@RequestBody(required = false) @Valid AutoReplyDTO dto) {
        if(dto==null){
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        return iAutoReplyService.addKeyWordReply(dto);
    }

    /**
     * 修改(关键词回复)自动回复规则
     *
     * @param dto
     * @return
     */
    @PostMapping("/updateKeyWordReply")
    @ApiOperation(value = "修改(关键词回复)自动回复规则", notes = "修改(关键词回复)自动回复规则")
    @ExcludeInterceptor
    public Result updateKeyWordReply(@RequestBody(required = false) @Valid AutoReplyDTO dto) {
        if(dto==null){
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        return iAutoReplyService.updateKeyWordReply(dto);
    }

    /**
     * 删除单条关键词自动回复规则
     *
     * @param id
     * @return
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除自动回复规则", notes = "删除自动回复规则")
    @ExcludeInterceptor
    public Result delete(@RequestParam("id") Long id) {
        return iAutoReplyService.delete(id);
    }

    /**
     * 查看单条关键词自动回复规则
     *
     * @param id
     * @return
     */
    @PostMapping("/selectKeyWordReply")
    @ExcludeInterceptor
    @ApiOperation(value = "查看单条关键词自动回复规则", notes = "查看单条关键词自动回复规则")
    public Result<AutoReplyVO> selectKeyWordReply(@RequestParam("id") Long id) {
        return Result.success(iAutoReplyService.selectKeyWordReply(id));
    }

    /**
     * 查看(1:关注回复 2:收到消息回复)自动回复
     *
     * @param replyType
     * @return
     */
    @PostMapping("/select")
    @ExcludeInterceptor
    @ApiOperation(value = "查看(1:关注回复 2:收到消息回复)自动回复", notes = "查看(1:关注回复 2:收到消息回复)自动回复")
    public Result<AutoReplyVO> select(@RequestParam("replyType") Integer replyType) {
        return Result.success(iAutoReplyService.select(replyType));
    }

    /**
     * 查看关键词自动回复规则列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/page")
    @ExcludeInterceptor
    @ApiOperation(value = "查看关键词自动回复规则列表", notes = "查看关键词自动回复规则列表")
    public Result<Page<AutoReplyVO>> getPages(@RequestBody(required = false) @Valid AutoReplyQueryDto dto) {
        if(dto==null){
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        return Result.success(iAutoReplyService.getPages(dto));
    }

//    /**
//     * 功能描述:
//     * 微信文件上传
//     * @param file
//     * @param type
//     * @return:com.yl.common.base.model.vo.Result<me.chanjar.weixin.common.bean.result.WxMediaUploadResult>
//     * @since: 1.0.0
//     * @Author:luhong
//     * @Date: 2021-05-20 15:17
//     */
//    @PostMapping("/upload")
//    @ApiOperation(value = "文件上传", notes = "文件上传")
//    @ExcludeInterceptor
//    public Result<WxMediaUploadResult> upload(@RequestParam @ApiParam(value = "file") MultipartFile file, @ApiParam(value = "type") String type) {
//        return iAutoReplyService.upload(file,type);
//    }
    /**
     * 功能描述:
     * 微信素材列表
     * @return:com.yl.common.base.model.vo.Result<me.chanjar.weixin.common.bean.result.WxMediaUploadResult>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-05-20 15:17
     */
    @PostMapping("/wxMaterial/page")
    @ApiOperation(value = "微信素材列表", notes = "微信素材列表")
    @ExcludeInterceptor
    public Result<WxMpMaterialFileBatchGetResult> wxMaterialPage(@RequestBody @Valid WxMaterialDto wxMaterialDto ) {
        return iAutoReplyService.wxMaterialPage(wxMaterialDto);
    }
    /**
     * 功能描述:
     * 微信素材图文列表
     *
     * @return:com.yl.common.base.model.vo.Result<me.chanjar.weixin.common.bean.result.WxMpMaterialNews>
     * @since: 1.0.0
     * @Author:caiyao
     * @Date: 2021-07-12 15:17
     */
    @PostMapping("/wxMaterial/newsPage")
    @ApiOperation(value = "微信素材图文列表", notes = "微信素材图文列表")
    @ExcludeInterceptor
    public Result<WxFreePublishVo> wxMaterialNewsPage(@RequestBody(required = false) @Valid WxMaterialDto wxMaterialDto) {
        if(wxMaterialDto==null){
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        return iAutoReplyService.wxMaterialNewsPage(wxMaterialDto);
    }
}
