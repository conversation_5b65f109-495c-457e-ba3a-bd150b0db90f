package com.yl.applets.controller;

import com.yl.applets.config.HotCityProperties;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/hotCity")
public class HotCityController extends AppBaseController{

    @Autowired
    private HotCityProperties hotCityProperties;

    @ApiOperation(value = "获取热门城市", notes = "获取热门城市")
    @GetMapping("/list")
    public Result<List<HotCityProperties.City>> list() {
        return success(hotCityProperties.getCitys());
    }


}
