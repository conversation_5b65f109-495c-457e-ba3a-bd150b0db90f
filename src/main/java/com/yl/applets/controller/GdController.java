package com.yl.applets.controller;

import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.AddressPCDDTO;
import com.yl.applets.dto.ForwardRequest;
import com.yl.applets.dto.lmdm.SysAreaNormalDTO;
import com.yl.applets.dto.lmdm.SysAreaPcaNamesDTO;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.OldLmdmFeignClient;
import com.yl.applets.service.IGdAddress;
import com.yl.applets.utils.BdImagesApi;
import com.yl.applets.utils.GdUtils;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.utils.YlStringUtils;
import com.yl.applets.vo.AddressPCDVO;
import com.yl.common.base.controller.BaseController;
import com.yl.common.base.enums.SysAreaTypeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-11-08 15:46 <br>
 */
@RestController
@RequestMapping("/gd")
@Slf4j
public class GdController extends BaseController {

    @Autowired
    private OldLmdmFeignClient sysAreaFeignClient;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    private static final String WORDS_RESULT = "words_result";

    @Autowired
    private IGdAddress gdAddress;


    @Value("${img.ocr.enable:true}")
    private Boolean enabled;


    /**
     * 获取高德地图地址详细信息
     *
     * @param address
     * @return
     */
    @PostMapping("/detail")
    public Result<String> delete(@RequestParam("address") String address) {
        return success(GdUtils.getAddressDtail(address));
    }

    /**
     * 获取高德地图地址详细信息
     *
     * @param address
     * @return
     */
    @PostMapping("/getArea")
    public Result<AddressPCDDTO> getArea(@RequestParam("address") String address) {

        AddressPCDDTO addressPCDDTO = GdUtils.getUserCardDTO(address);
        if (addressPCDDTO != null) {
            List<SysAreaNormalDTO> result = getSysAreaNormalDTOS(addressPCDDTO);
            log.info("高德地图地址根据省市区名称查询省市区接口结果：{}",JsonUtils.toJson(result));
            if (result.size() == 3) {
                result.forEach(s -> {
                    if (s.getType().equals(SysAreaTypeEnum.PROVINCE.getId())) {
                        addressPCDDTO.setProvinceId(s.getId());
                    } else if (s.getType().equals(SysAreaTypeEnum.CITY.getId())) {
                        addressPCDDTO.setCityId(s.getId());
                    } else {
                        addressPCDDTO.setAreaId(s.getId());
                    }
                });
                return success(addressPCDDTO);
            }
        }
        throw new BusinessException(ServiceErrCodeEnum.SYSAREA_CHECK_ERR);
    }

    /**
     * 通过图片解析地址信息
     * @param file
     * @return
     */
    @PostMapping("/intelligentAddressFile")
    public Result<AddressPCDVO> intelligentAddressFile(@RequestParam(value = "file",required = false) MultipartFile file) {

        if (file == null || file.isEmpty()) {
            throw new BusinessException(ServiceErrCodeEnum.FILE_ISNULL);
        }
        StringBuilder word = new StringBuilder();
        StopWatch stopWatch = new StopWatch();
        try {
            byte[] imgBytes = file.getBytes();
            if (enabled){
                if (this.handlePictureAddressByBasic(word, file)) return intelligentAddressRecognitionV2(word.toString());
            }else {
                if (this.handlePictureAddressByApplets(word, stopWatch, imgBytes)) return intelligentAddressRecognitionV2(word.toString());
            }
        } catch (Exception e) {
            log.error("图片识别失败:{}", e);
        }
        throw new BusinessException(ServiceErrCodeEnum.IMAGE_RES_ERROR);
    }

    private boolean handlePictureAddressByApplets(StringBuilder word, StopWatch stopWatch, byte[] imgBytes) {
        stopWatch.start("百度general_basic");
        JSONObject result = BdImagesApi.getResult(imgBytes);
        stopWatch.stop();
        log.info("出参:{}，耗时详情：{}，总耗时：{}",JsonUtils.toJson(result), stopWatch.prettyPrint(), stopWatch.getTotalTimeSeconds());
        if (result != null && result.has(WORDS_RESULT) && result.getJSONArray(WORDS_RESULT) != null && result.getJSONArray(WORDS_RESULT).length() > 0) {
            JSONArray wordsResult = result.getJSONArray(WORDS_RESULT);
            for (int i = 0; i < wordsResult.length(); i++) {
                word.append(wordsResult.getJSONObject(i).getString("words")).append(" ");
            }
            return true;
        }
        return false;
    }

    private boolean handlePictureAddressByBasic(StringBuilder word, MultipartFile file) {
        Result<String> result = channelApiFeignClient.intelligentAddressRecognitionFile(file);
        log.info("调用架构返回图片解析内容,:{}", JSON.toJSONString(result));
        word.append(result.result());
        return true;
    }

    /**
     * 调用百度获取智能识别
     * @param address
     * @return
     */
    @PostMapping("/intelligentAddressRecognitionV2")
    public Result<AddressPCDVO> intelligentAddressRecognitionV2(@RequestParam("address") String address) {
        log.info("调用百度获取智能识别，入参: {}", address);
        return success(gdAddress.intelligentAddressRecognitionV2(address));
    }


    /**
     * 调用高德获取智能识别
     * @param address
     * @return
     */
    @PostMapping("/intelligentAddressRecognition")
    public Result<AddressPCDVO> intelligentAddressRecognition(@RequestParam("address") String address) {
        if (StringUtil.isBlank(address)) {
            throw new BusinessException(ServiceErrCodeEnum.ADDR_STR_ERROR);
        }
        AddressPCDVO addressPCDVO = new AddressPCDVO();
        List<String> strings = YlStringUtils.splitWithAnyChracter(address);
        //处理文本，以符号分隔
        if (strings.isEmpty()) {
            throw new BusinessException(ServiceErrCodeEnum.ADDR_STR_ERROR);
        }
        strings.forEach(s->{
            if (YlStringUtils.isNumeric(s)){
                addressPCDVO.setMobile(s);
            }
            if(s.length()>6 && !YlStringUtils.isNumeric(s)){
                addressPCDVO.setAddress(s);
            }
            if(s.length()<6){
                addressPCDVO.setName(s);
            }
        });
        //清洗省市区
        if(addressPCDVO.getAddress()!=null){
            AddressPCDDTO dto = getAddressPCDDTO(addressPCDVO.getAddress());
            if(dto!=null){
                addressPCDVO.setProvince(dto.getProvince());
                addressPCDVO.setProvinceId(dto.getProvinceId());
                addressPCDVO.setCity(dto.getCity());
                addressPCDVO.setCityId(dto.getCityId());
                addressPCDVO.setArea(dto.getArea());
                addressPCDVO.setAreaId(dto.getAreaId());
            }
        }
        return success(addressPCDVO);
    }


    private AddressPCDDTO getAddressPCDDTO( String address) {
        AddressPCDDTO addressPCDDTO = GdUtils.getUserCardDTO(address);
        if (addressPCDDTO != null) {
            List<SysAreaNormalDTO> result = getSysAreaNormalDTOS(addressPCDDTO);
            if (result.size() == 3) {
                result.forEach(s -> {
                    if (s.getType().equals(SysAreaTypeEnum.PROVINCE.getId())) {
                        addressPCDDTO.setProvinceId(s.getId());
                    } else if (s.getType().equals(SysAreaTypeEnum.CITY.getId())) {
                        addressPCDDTO.setCityId(s.getId());
                    } else {
                        addressPCDDTO.setAreaId(s.getId());
                    }
                });
            }
        }
        return addressPCDDTO;
    }



    private List<SysAreaNormalDTO> getSysAreaNormalDTOS(AddressPCDDTO addressPCDDTO) {
        List<SysAreaPcaNamesDTO> list = new ArrayList<>();

        SysAreaPcaNamesDTO pro = new SysAreaPcaNamesDTO();
        pro.setName(addressPCDDTO.getProvince());
        pro.setType(SysAreaTypeEnum.PROVINCE.getId());

        SysAreaPcaNamesDTO city = new SysAreaPcaNamesDTO();
        city.setName(addressPCDDTO.getCity());
        city.setType(SysAreaTypeEnum.CITY.getId());

        SysAreaPcaNamesDTO area = new SysAreaPcaNamesDTO();
        area.setName(addressPCDDTO.getArea());
        area.setType(SysAreaTypeEnum.AREA.getId());
        list.add(pro);
        list.add(city);
        list.add(area);
//        List<SysAreaNormalDTO> result = sysAreaFeignClient.getByNames(list).result();
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        ForwardRequest request=new ForwardRequest();
        request.setRequestId(requestId);
        request.setRequestUri("/lmdmapi/sysArea/getByNames");
        request.setBody(JSON.toJSONString(list));
        Result<?> forward = channelApiFeignClient.forward(request);
        log.info("请求id==>{}接口返回==>{}",requestId,JSON.toJSONString(forward));
        Object resultO = forward.result();
        List<SysAreaNormalDTO> result = JSON.parseArray(JSON.toJSONString(resultO), SysAreaNormalDTO.class);
        return result;
    }


}
