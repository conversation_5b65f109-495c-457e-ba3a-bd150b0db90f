package com.yl.applets.controller;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.*;
import com.yl.applets.enums.OrderMarkingSourceEnum;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.BaseWordFeignClient;
import com.yl.applets.feign.OrderFeigntClient;
import com.yl.applets.feign.ReceivingPreferencesApiClient;
import com.yl.applets.utils.OrderMarkingHelper;
import com.yl.applets.vo.*;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description: 收件偏好
 * @date 2024-04-16
 */
@RestController
@RequestMapping("/order/preferences")
@Slf4j
public class OrderReceivingPreferencesController extends AppBaseController {


    @Autowired
    private ReceivingPreferencesApiClient receivingPreferencesApiClient;


    @Autowired
    private BaseWordFeignClient baseWordFeignClient;


    @Autowired
    private OrderMarkingHelper orderMarkingHelper;

    @Resource
    private OrikaBeanMapper orikaBeanMapper;

    @Autowired
    private OrderFeigntClient orderFeigntClient;



    @PostMapping("/userSelfOrder")
    public Result<Boolean> userSelfOrder(@RequestBody @Valid OrderUserQueryDto queryDto) {
        log.info("【userSelfOrder】判断是否自己的单子,前端入参==>{}",JSON.toJSONString(queryDto));
        boolean number = NumberUtil.isNumber(queryDto.getOrderId());
        if(!number){
            return Result.success(Boolean.FALSE);
        }
        WxUserVo user = getUser();
        queryDto.setPhone(user.getMobile());
        queryDto.setMemberId(user.getNumberId()+"");
        Result<Boolean> booleanResult = receivingPreferencesApiClient.userSelfOrder(queryDto);
        log.info("【userSelfOrder】判断是否自己的单子,中台入参==>{} 出参==>{}",JSON.toJSONString(queryDto),JSON.toJSONString(booleanResult));
        return Result.success(booleanResult.result());
    }


    @PostMapping("/userSelf")
    public Result<Boolean> userSelf(@RequestBody @Valid OrderUserQueryDto queryDto) {
        log.info("判断是否自己的单子,前端入参==>{}",JSON.toJSONString(queryDto));
        boolean number = NumberUtil.isNumber(queryDto.getOrderId());
        if(!number){
            return Result.success(Boolean.FALSE);
        }
        WxUserVo user = getUser();
        queryDto.setPhone(user.getMobile());
        queryDto.setMemberId(user.getNumberId()+"");
        Result<Boolean> booleanResult = receivingPreferencesApiClient.userSelf(queryDto);
        log.info("判断是否自己的单子,中台入参==>{} 出参==>{}",JSON.toJSONString(queryDto),JSON.toJSONString(booleanResult));
        return Result.success(booleanResult.result());
    }

    /**
     * 敏感词查询
     * @param word
     * @return
     */
    @PostMapping("/checkWord")
    public Result<Boolean> checkWord(@RequestBody  @Valid WordDto dto ) {
        Result<Boolean> marking = baseWordFeignClient.checkWord(dto.getWord());
        log.info("调用基础资料敏感词接口,入参==>{},出参==>{}",JSON.toJSONString(dto),JSON.toJSONString(marking));
        return Result.success(marking.result());
    }



    /**
     * 查询打标
     * @param orderId
     * @return
     */
    @GetMapping("/queryByOrderId")
    public Result<OrderRecvPrefVO> queryByOrderId(@RequestParam(name = "orderId") String orderId) {
        //查询是否自己的单子
        OmsOrderApiVO detail = orderFeigntClient.detailApp(Long.parseLong(orderId)).result();
        log.info("查询打标,订单详情==>{}",JSON.toJSONString(detail));
        WxUserVo user = getUser();
        String mobile = user.getMobile();
        Integer userId = user.getNumberId();
        String receiverMobilePhone = StringUtils.isNotBlank(detail.getReceiverMobilePhone()) ? detail.getReceiverMobilePhone() : detail.getReceiverTelphone();
        String senderMobilePhone = StringUtils.isNotBlank(detail.getSenderMobilePhone()) ? detail.getSenderMobilePhone() : detail.getSenderTelphone();
        Integer memberId = detail.getMemberId();
        //1.登录时
        String last4 = mobile.substring(mobile.length() - 4);
        if ((StringUtils.isNotBlank(receiverMobilePhone) && receiverMobilePhone.endsWith(last4))
                || (StringUtils.isNotBlank(senderMobilePhone) && senderMobilePhone.endsWith(last4))
                || (Objects.equals(userId, memberId))) {

        }else {
            log.info("不是自己的订单,user==>{},order==>{}",JSON.toJSONString(user),JSON.toJSONString(detail));
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_ERROR);
        }
        Result<OrderRecvPrefVO> marking = receivingPreferencesApiClient.queryByOrderId(orderId);
        log.info("订单,查询打标,入参==>{},出参==>{}",orderId,JSON.toJSONString(marking));
        OrderRecvPrefVO result = marking.result();
        return Result.success(result);
    }


    /**
     * 订单打标
     */
    @PostMapping(value = "/accept")
    public Result<Boolean> accept(@RequestBody @Valid OrderMarkingDTO dto){
        WxUserVo user = getUser();
        dto.setPhone(user.getMobile());
        dto.setMemberId(user.getNumberId()+"");
        dto.setDataSource("WXXCX");//微信小程序
        orderMarkingHelper.checkMarking(dto);
        Result<Void> accept = receivingPreferencesApiClient.accept(dto);
        log.info("订单打标,入参==>{},出参==>{}",JSON.toJSONString(dto),JSON.toJSONString(accept));
        accept.result();
        return Result.success(Boolean.TRUE);
    }


    @PostMapping("/mark/phone/save")
    public Result<Boolean> save(@RequestBody OrderMarkPhoneSaveDTO dto) {
        WxUserVo user = getUser();
        dto.setMobilePhone(user.getMobile());
        dto.setDataSource("XCX");
        Result<Void> save = receivingPreferencesApiClient.save(dto);
        log.info("派前电联,新增更改,入参==>{},出参==>{}",JSON.toJSONString(dto),JSON.toJSONString(save));
        save.result();
        return Result.success(Boolean.TRUE);
    }


    /**
     * 详情
     */
    @PostMapping(value = "/mark/phone/detail")
    public Result<OrderMarkPhoneVO> detail() {
        WxUserVo user = getUser();
        OrderMarkPhoneQueryDTO dto = new OrderMarkPhoneQueryDTO();
        dto.setMobilePhone(user.getMobile());
        Result<OrderMarkPhoneVO> detail = receivingPreferencesApiClient.detail(dto);
        log.info("派前电联,查询,入参==>{},出参==>{}",JSON.toJSONString(dto),JSON.toJSONString(detail));
        OrderMarkPhoneVO result = detail.result();
        return Result.success(result);
    }


    /**
     * 获取收件偏好的标签
     *
     * @return
     */
    @GetMapping("/resource")
    public Result<List<OrderMarkingVo>> getResource(@RequestParam(value = "orderId",required = false)String orderId) {
        Result<List<OrderMarkingVo>> resource = receivingPreferencesApiClient.getResource(orderId);
        log.info("调用中台,获取订单标识==>{}",JSON.toJSONString(resource));
        List<OrderMarkingVo> result = resource.result();
        return Result.success(result);
    }


    @PostMapping("/page")
    public Result<Page<RecvPrefConfigVO>> preferencesPage(@RequestBody PreferencesQueryDto dto){
        log.info("收件偏好查询入参==>{}",JSON.toJSONString(dto));
        Page page = new Page<>(dto.getCurrent(), dto.getSize());
        WxUserVo user = getUser();
        if(user!=null&&StringUtils.isNotBlank(user.getMobile())){
            dto.setMobile(user.getMobile());
            Result<Page<RecvPrefConfigVO>> result = receivingPreferencesApiClient.preferencesPage(dto);
            log.info("收件偏好查询,调用中台,入参==>{},出参==>{}",JSON.toJSONString(dto),JSON.toJSONString(result));
            Page<RecvPrefConfigVO> resultPage = result.result();
            page = resultPage;
            //处理封装标签
            handlerMarking(page);
        }
        return Result.success(page);
    }



    @PostMapping("/add")
    public Result<Boolean> preferencesAdd(@RequestBody PreferencesAddDto dto){
        log.info("收件偏好新增入参==>{}",JSON.toJSONString(dto));
        //校验偏好标识
        orderMarkingHelper.checkMarking(dto);
        WxUserVo user = getUser();
        dto.setMobile(user.getMobile());
        dto.setSource(OrderMarkingSourceEnum.weixin.getSource());
        Result<Boolean> result = receivingPreferencesApiClient.preferencesAdd(dto);
        log.info("收件偏好新增,调用中台,入参==>{},出参==>{}",JSON.toJSONString(dto),JSON.toJSONString(result));
        result.result();
        return Result.success(Boolean.TRUE);
    }



    @PostMapping("/update")
    public Result<Boolean> preferencesUpdate(@RequestBody PreferencesUpdateDto dto){
        log.info("收件偏好更新入参==>{}",JSON.toJSONString(dto));
        dto.setWeekdayTwoLevelName("");
        dto.setWeekdayTwoLevelCode("");
        dto.setWeekdayOneLevelCode("");
        dto.setWeekdayOneLevelName("");
        dto.setWeekendTwoLevelName("");
        dto.setWeekendTwoLevelCode("");
        dto.setWeekendOneLevelName("");
        dto.setWeekendOneLevelCode("");
        if(dto.getId()==null){
            throw new ServiceException(ResultCodeEnum.PARAMS_IS_INVALID);
        }
        //校验偏好标识
        orderMarkingHelper.checkMarking(dto);
        dto.setSource(OrderMarkingSourceEnum.weixin.getSource());
        dto.setMobile(getUser().getMobile());
        Result<Boolean> result = receivingPreferencesApiClient.preferencesUpdate(dto);
        log.info("收件偏好更新,调用中台,入参==>{},出参==>{}",JSON.toJSONString(dto),JSON.toJSONString(result));
        result.result();
        return Result.success(Boolean.TRUE);
    }


    @GetMapping("/delete")
    public Result<Boolean> preferencesDelete(@RequestParam("id") String id){
        log.info("收件偏好删除入参==>{}",id);
        if(StringUtils.isBlank(id)){
            throw new ServiceException(ResultCodeEnum.PARAMS_IS_INVALID);
        }
        String mobile = getUser().getMobile();
        Result<Boolean> result = receivingPreferencesApiClient.preferencesDelete(id,mobile, OrderMarkingSourceEnum.weixin.getSource());
        log.info("收件偏好删除,调用中台,入参==>id {},mobile {},出参==>{}",id,mobile,JSON.toJSONString(result));
        result.result();
        return Result.success(Boolean.TRUE);
    }





    private void handlerMarking(IPage<RecvPrefConfigVO> result) {
        Result<List<OrderMarkingVo>> resource = receivingPreferencesApiClient.getResource(null);
        List<OrderMarkingVo> baseResource = resource.result();
        if(CollectionUtils.isNotEmpty(result.getRecords())){
            for (RecvPrefConfigVO dto : result.getRecords()) {
                String weekdayOneLevelName = dto.getWeekdayOneLevelName();//工作日一级偏好
                if(StringUtils.isNotBlank(weekdayOneLevelName)){
                    dto.setWeekdayOneLevelName(weekdayOneLevelName.replace("工作日",""));
                }
                String weekdayOneLevelCode = dto.getWeekdayOneLevelCode();//工作日一级偏好

                String weekdayTwoLevelCode = dto.getWeekdayTwoLevelCode();//工作日二级偏好
                String weekdayTwoLevelName = dto.getWeekdayTwoLevelName();//工作日二级偏好

                String weekendOneLevelCode = dto.getWeekendOneLevelCode();//休息日一级偏好
                String weekendOneLevelName = dto.getWeekendOneLevelName();//休息日一级偏好
                if(StringUtils.isNotBlank(weekendOneLevelName)){
                    dto.setWeekendOneLevelName(weekendOneLevelName.replace("休息日",""));
                }
                String weekendTwoLevelCode = dto.getWeekendTwoLevelCode();//休息日二级偏好编码
                String weekendTwoLevelName = dto.getWeekendTwoLevelName();//休息日二级偏好编码


                List<String> markingCodes = Stream.of(weekendOneLevelCode, weekdayOneLevelCode,weekdayTwoLevelCode,weekendTwoLevelCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                List<OrderMarkingVo> orderMarkingVos = orikaBeanMapper.mapAsList(baseResource, OrderMarkingVo.class);

                orderMarkingVos.stream().forEach(o->{
                    if(markingCodes.contains(o.getCode())){
                        o.setStatus(true);
                    }
                    if(!CollectionUtils.isEmpty(o.getChild())){
                        for (OrderMarkingVo vo : o.getChild()) {
                            if(markingCodes.contains(vo.getCode())){
                                vo.setStatus(true);
                                if(vo.getCode().endsWith("_ZDY")){
                                    String type = o.getType();
                                    if(StringUtils.equals(type,"工作日")){
                                        vo.setName(weekdayTwoLevelName);
                                    }
                                    if(StringUtils.equals(type,"休息日")){
                                        vo.setName(weekendTwoLevelName);
                                    }
                                }
                            }
                        }
                    }
                });
                dto.setMarkingStatusList(orderMarkingVos);
            }
        }
    }



}
