package com.yl.applets.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yl.applets.constant.BaseConstant;
import com.yl.applets.constant.OmsWaybillApiConstant;
import com.yl.applets.dto.*;
import com.yl.applets.dto.lmdm.SysStaffDTO;
import com.yl.applets.enums.MonthlySettlementEnum;
import com.yl.applets.enums.OmsOrderStatusEnum;
import com.yl.applets.enums.OrderChannelTypeEnum;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.*;
import com.yl.applets.service.IOrderService;
import com.yl.applets.service.IUserCardService;
import com.yl.applets.service.impl.NetworkAbnormalServiceImpl;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.utils.SmsAccessUtils;
import com.yl.applets.vo.*;
import com.yl.applets.vo.lmdm.SysStaffVO;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.constant.SaveGroup;
import com.yl.common.base.constant.UpdateGroup;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

import static com.yl.applets.constant.BaseConstant.*;
import static com.yl.common.base.enums.ResultCodeEnum.WARN_CUSTOMER_ERROR;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-08-08 16:34 <br>
 * @Author: zhipeng.liu
 */
@Api(value = "订单", tags = {"订单"})
@RestController
@RequestMapping("/order")
@Slf4j
public class OrderController extends AppBaseController {

    static final String SHARE_PUBLIC_KEY = "share&query&key";
    static final String WAYBILL_MD5_KEY_3 = "hsA1@LsaNJssssl12";
    private static List<Integer> ORDERSTATUS = Arrays.asList(OmsOrderStatusEnum.dispatched_network.getCode(),
            OmsOrderStatusEnum.dispatched_saleman.getCode(),
            OmsOrderStatusEnum.dispatch_proxy_area.getCode(),
            OmsOrderStatusEnum.unassigned.getCode());
    @Autowired
    private IOrderService orderService;
    @Autowired
    private IUserCardService userCardService;
    @Autowired
    private OldLmdmFeignClient sysStaffFeignClient;
    @Resource
    private OrikaBeanMapper orikaBeanMapper;
    @Autowired
    private OrderFeigntClient orderFeigntClient;
    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;
    @Autowired
    private ReceivingPreferencesApiClient receivingPreferencesApiClient;
    @Value("${wx.source.codes:D08,D61,D13,D14}")
    private String sourceCodes;
    @Autowired
    private NetworkAbnormalServiceImpl networkAbnormalService;


    @Autowired
    private NetworkFeighClient networkFeighClient;

    @Value("${isInterceptOrder:1}")
    private String isInterceptOrder;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private SmsAccessUtils smsAccessUtils;

    @Autowired
    private CcmOrderMarkApiFeignClient ccmOrderMarkApiFeignClient;

    /**
     * 订单详情
     *
     * @param waybillNo
     * @return
     */
    @ApiOperation(value = "订单详情", notes = "订单详情")
    @GetMapping("/detailByWaybillNo")
    public Result<AppOmsOrderApiVO> detailByWaybillNo(@RequestParam("waybillNo") String waybillNo) {
        WxUserVo user = getUser();
        log.info("detailByWaybillNo查询订单详情：{}", JSON.toJSONString(user));
        if (StringUtils.isBlank(waybillNo)) {
            throw new ServiceException(ServiceErrCodeEnum.WAYBILLNO_ISNULL);
        }
        if (waybillNo.contains("/")) {
            return Result.success(null);
        }
        AppOmsOrderApiVO appOmsOrderApiVO = null;
        OmsOrderApiVO detail = orderFeigntClient.getOrderInfoByWaybillId(waybillNo).result();
        log.info("订单详情：【{}】", JSON.toJSONString(detail));
        if (detail != null) {
            String last4 = user.getMobile().substring(user.getMobile().length() - 4);
            String senderMobile = StrUtil.isNotBlank(detail.getSenderMobilePhone()) ? detail.getSenderMobilePhone() : detail.getSenderTelphone();
            String receiverMobile = StrUtil.isNotBlank(detail.getReceiverMobilePhone()) ? detail.getReceiverMobilePhone() : detail.getReceiverTelphone();
            if (!(senderMobile.endsWith(last4) || receiverMobile.endsWith(last4))) {
                throw new BusinessException(ServiceErrCodeEnum.PHONE_VERIFY_ERROR);
            }

            OmsOrderConventionVo omsOrderConventionVo = orderFeigntClient.queryOrderConventionByOrderId(detail.getId()).getData();
            log.info("订单详情,获取订单号{}预约时间：【{}】", detail.getId(), JSON.toJSONString(omsOrderConventionVo));
            if (omsOrderConventionVo != null) {
                detail.setBestPickTimeStart(omsOrderConventionVo.getConventionPickStartTime());
                detail.setBestPickTimeEnd(omsOrderConventionVo.getConventionPickEndTime());
            }
            appOmsOrderApiVO = orikaBeanMapper.map(detail, AppOmsOrderApiVO.class);
        }
        return Result.success(appOmsOrderApiVO);
    }


    /**
     * 订单详情
     *
     * @param orderId
     * @return
     */
    @ApiOperation(value = "订单详情", notes = "订单详情")
    @GetMapping("/detail")
    public Result<AppOmsOrderApiVO> detail(@RequestParam("orderId") Long orderId, @RequestParam(value = "type", required = false) Integer type) {
        if (orderId == null) {
            throw new ServiceException(ServiceErrCodeEnum.ORDERID_ISNULL);
        }
        WxUserVo user = getUser();
        OmsOrderApiVO detail = orderService.detail(orderId);
        log.info("订单详情：【{}】", JSON.toJSONString(detail));

        if (!StringUtils.equals(user.getNumberId() + "", detail.getMemberId() + "") &&
                !user.getMobile().equals(detail.getSenderMobilePhone()) && !user.getMobile().equals(detail.getReceiverMobilePhone())) {
            log.warn("当前登录人不是下单人,没有权限查看订单详情");
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_ERROR);
        }
        if (detail != null) {
            OmsOrderConventionVo omsOrderConventionVo = orderFeigntClient.queryOrderConventionByOrderId(orderId).getData();
            log.info("订单详情,获取订单号{}预约时间：【{}】", orderId, JSON.toJSONString(omsOrderConventionVo));

            if (omsOrderConventionVo != null) {
                detail.setBestPickTimeStart(omsOrderConventionVo.getConventionPickStartTime());
                detail.setBestPickTimeEnd(omsOrderConventionVo.getConventionPickEndTime());
            }
        }

        List<OrderMarkCodeVO> orderMarkList = ccmOrderMarkApiFeignClient.queryMarkCodes(Lists.newArrayList(detail.getId())).result();
        if(!CollectionUtils.isEmpty(orderMarkList)){
            OrderMarkCodeVO orderMarkCodeVO = orderMarkList.get(0);
            List<String> markCodes = orderMarkCodeVO.getMarkCodes();
            if(!CollectionUtils.isEmpty(markCodes) ){
                detail.setOrderChannelType(OrderChannelTypeEnum.getMappingCode(markCodes));
            }
        }


        AppOmsOrderApiVO appOmsOrderApiVO = orikaBeanMapper.map(detail, AppOmsOrderApiVO.class);
        if (StringUtils.isNotBlank(detail.getPickStaffCode())) {
            SysStaffDTO sysStaffDTO = new SysStaffDTO();
            sysStaffDTO.setCode(detail.getPickStaffCode());
//            SysStaffVO result = sysStaffFeignClient.getStaffDetail(sysStaffDTO).result();
            String requestId = UUID.randomUUID().toString().replaceAll("-", "");
            ForwardRequest request = new ForwardRequest();
            request.setRequestId(requestId);
            request.setRequestUri("/lmdmapi/sysStaff/detail");
            request.setBody(JSON.toJSONString(sysStaffDTO));
            log.info("请求id==>{}接口入参==>{}", requestId, JSON.toJSONString(request));
            Result<?> forward = channelApiFeignClient.forward(request);
            log.info("请求id==>{}接口返回==>{}", requestId, JSON.toJSONString(forward));
            Object resultO = forward.result();
            SysStaffVO result = JSON.parseObject(JSON.toJSONString(resultO), SysStaffVO.class);
            if (result != null) {
                appOmsOrderApiVO.setStaffMobile(result.getMobile());
            }
        }
        if (type != null) {//展示优惠券信息
            appOmsOrderApiVO.setIsCoupon(false);
            //优惠券信息
            if (detail.getOmsOrderThirdExtVO() != null) {
                String params = detail.getOmsOrderThirdExtVO().getParams();
                if (!com.yl.common.base.util.StringUtils.isEmpty(params)) {
                    log.info("查看订单详情,出参【优惠券信息】===>{}", params);
                    OrderCouponDto dto = JSONObject.parseObject(params, OrderCouponDto.class);
//                    appOmsOrderApiVO.setCouponDto(dto);
                    if (!StringUtils.isEmpty(dto.getCouponCode())) {
                        appOmsOrderApiVO.setIsCoupon(true);
                        if (type == 2) {
                            log.info("查询此优惠券流水信息==>{}", detail.getWaybillId());
                            Result<OmsOrderPayRecordVO> payRecordByCouponCode = orderFeigntClient.getPayRecordByWaybillNo(detail.getWaybillId());
                            log.info("查询此优惠券流水信息==>{}", JSON.toJSONString(payRecordByCouponCode));
                            if (payRecordByCouponCode != null && payRecordByCouponCode.getData() != null) {
                                OmsOrderPayRecordVO data = payRecordByCouponCode.getData();
                                if (data.getCouponAmount() != null) {
                                    appOmsOrderApiVO.setCouponAmount(data.getCouponAmount());//优惠券优惠金额
                                    appOmsOrderApiVO.setOrderAmount(data.getOrderAmount());//订单金额
                                    appOmsOrderApiVO.setOriginalAmount(data.getOriginalAmount());//原金额
                                    appOmsOrderApiVO.setCouponCode(data.getCouponCode());
                                    appOmsOrderApiVO.setCouponId(data.getCouponId());
                                }
                            }
                        }
                    }
                }
            }
        }
        List<OrderMarkingVo> result = Lists.newArrayList();
        try {
            //订单查询标识
            Result<List<OrderMarkingVo>> resource = receivingPreferencesApiClient.getResource(orderId + "");
            log.info("订单详情,调用中台,获取订单标识入参==>{},出参==>{}", orderId, JSON.toJSONString(resource));
            result = resource.result();
        } catch (Exception e) {
            log.error("调用订单标签查询接口异常", e);
        }
        if (!CollectionUtils.isEmpty(result)) {
            appOmsOrderApiVO.setMarkingStatusList(result);
        }
        String paidModeCode = appOmsOrderApiVO.getPaymentModeCode();
        if(StringUtils.equals(OmsWaybillApiConstant.JFYJ, paidModeCode)) {
            String mobile = user.getMobile();
            String last4 = mobile.substring(mobile.length() - 4);
            //寄付月结  我的收件 不展示月结账号  我的寄件正常返回
            if (StringUtils.isNotBlank(appOmsOrderApiVO.getReceiverMobilePhone()) && appOmsOrderApiVO.getReceiverMobilePhone().endsWith(last4)) {
                appOmsOrderApiVO.setCustomerCode(null);
            }
        }
        return Result.success(appOmsOrderApiVO);
    }


    @ApiOperation(value = "订单详情", notes = "订单详情")
    @GetMapping("/v2/detail")
    public Result<AppOmsOrderApiVO> detailV2(@RequestParam("orderId") Long orderId, @RequestParam(value = "type", required = false) Integer type,
                                             @RequestParam("times") String times, @RequestParam("sign") String sign) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        if (orderId == null) {
            throw new ServiceException(ServiceErrCodeEnum.ORDERID_ISNULL);
        }

        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(orderId + times + WAYBILL_MD5_KEY_3));
        log.info("请求id{}，后端MD5加密之后{}", requestId, sign1);
        if (!sign1.equals(sign)) {
            log.info("请求id{},验签失败！", requestId, sign);
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }
        log.info("请求id==>{}订单详情,远程入参==>{}", requestId, orderId);
        OmsOrderApiVO detail = orderService.detail(orderId);
        log.info("请求id==>{}订单详情,远程出参==>【{}】", requestId, JSON.toJSONString(detail));
        WxUserVo user = getUser();
        if (!StringUtils.equals(user.getNumberId() + "", detail.getMemberId() + "") &&
                !user.getMobile().equals(detail.getSenderMobilePhone()) && !user.getMobile().equals(detail.getReceiverMobilePhone())) {
            log.warn("请求id==>{}当前登录人不是下单人,没有权限查看订单详情", requestId);
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_ERROR);
        }
        if (detail != null) {
            OmsOrderConventionVo omsOrderConventionVo = orderFeigntClient.queryOrderConventionByOrderId(orderId).getData();
            log.info("请求id==>{}订单详情,获取订单号{}预约时间：【{}】", requestId, orderId, JSON.toJSONString(omsOrderConventionVo));

            if (omsOrderConventionVo != null) {
                detail.setBestPickTimeStart(omsOrderConventionVo.getConventionPickStartTime());
                detail.setBestPickTimeEnd(omsOrderConventionVo.getConventionPickEndTime());
            }
        }

        AppOmsOrderApiVO appOmsOrderApiVO = orikaBeanMapper.map(detail, AppOmsOrderApiVO.class);
        if (StringUtils.isNotBlank(detail.getPickStaffCode())) {
            SysStaffDTO sysStaffDTO = new SysStaffDTO();
            sysStaffDTO.setCode(detail.getPickStaffCode());
            ForwardRequest request = new ForwardRequest();
            request.setRequestId(requestId);
            request.setRequestUri("/lmdmapi/sysStaff/detail");
            request.setBody(JSON.toJSONString(sysStaffDTO));
            log.info("请求id==>{}接口入参==>{}", requestId, JSON.toJSONString(request));
            Result<?> forward = channelApiFeignClient.forward(request);
            log.info("请求id==>{}接口返回==>{}", requestId, JSON.toJSONString(forward));
            Object resultO = forward.result();
            SysStaffVO result = JSON.parseObject(JSON.toJSONString(resultO), SysStaffVO.class);
//            SysStaffVO result = sysStaffFeignClient.getStaffDetail(sysStaffDTO).result();
            if (result != null) {
                appOmsOrderApiVO.setStaffMobile(result.getMobile());
            }
        }
        if (type != null) {//展示优惠券信息
            appOmsOrderApiVO.setIsCoupon(false);
            //优惠券信息
            if (detail.getOmsOrderThirdExtVO() != null) {
                String params = detail.getOmsOrderThirdExtVO().getParams();
                if (!com.yl.common.base.util.StringUtils.isEmpty(params)) {
                    log.info("请求id==>{}查看订单详情,出参【优惠券信息】===>{}", requestId, params);
                    OrderCouponDto dto = JSONObject.parseObject(params, OrderCouponDto.class);
//                    appOmsOrderApiVO.setCouponDto(dto);
                    if (!StringUtils.isEmpty(dto.getCouponCode())) {
                        appOmsOrderApiVO.setIsCoupon(true);
                        if (type == 2) {
                            log.info("请求id==>{}查询此优惠券流水信息==>{}", requestId, detail.getWaybillId());
                            Result<OmsOrderPayRecordVO> payRecordByCouponCode = orderFeigntClient.getPayRecordByWaybillNo(detail.getWaybillId());
                            log.info("请求id==>{}查询此优惠券流水信息==>{}", requestId, JSON.toJSONString(payRecordByCouponCode));
                            if (payRecordByCouponCode != null && payRecordByCouponCode.getData() != null) {
                                OmsOrderPayRecordVO data = payRecordByCouponCode.getData();
                                if (data.getCouponAmount() != null) {
                                    appOmsOrderApiVO.setCouponAmount(data.getCouponAmount());//优惠券优惠金额
                                    appOmsOrderApiVO.setOrderAmount(data.getOrderAmount());//订单金额
                                    appOmsOrderApiVO.setOriginalAmount(data.getOriginalAmount());//原金额
                                    appOmsOrderApiVO.setCouponCode(data.getCouponCode());
                                    appOmsOrderApiVO.setCouponId(data.getCouponId());
                                }
                            }
                        }
                    }
                }
            }
        }
        return Result.success(appOmsOrderApiVO);
    }

    /**
     * 分享订单，用户查看详情，不做token校验，但加上验签。
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "订单分享-查看详情", notes = "订单分享-查看详情")
    @PostMapping("/share/detail")
    public Result<AppOmsOrderApiVO> shareDetail(@RequestBody(required = false) ShareQueryDTO dto) {
        if (dto == null) {
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        String orderId = dto.getOrderId();
        String mobile = dto.getMobile();
        if (StringUtils.isEmpty(orderId) || StringUtils.isEmpty(mobile) || mobile.length() != 4) {
            throw new ServiceException(ServiceErrCodeEnum.PARAM_ERROR);
        }
        if (!NumberUtil.isNumber(orderId)) {
            log.warn("订单号不为数字{}", orderId);
            throw new BusinessException(ServiceErrCodeEnum.PARAM_ERROR);
        }

        //失败key
        String key = BcRedisKeyEnum.WAYBILL_QUERY_COUNT_PER_MINUTER.keyBuilder(orderId);
        if (redisTemplate.opsForValue().get("LOCK:" + key) != null) {
            throw new ServiceException(ResultCodeEnum.WAYBILL_PERVIFY_ERROR);
        }


        //校验签名
        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(orderId + dto.getTimes() + SHARE_PUBLIC_KEY));
        log.info("后端MD5加密之后:" + sign1);
        if (!sign1.equals(dto.getSign())) {
            //失败错误次数限流
            smsAccessUtils.increment(key, 10, "订单分享-查看详情获取运单明细", 60L, 600L);
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }

        OmsOrderApiVO detail = orderService.detail(Long.parseLong(orderId));
        log.info("订单详情：【{}】", JSON.toJSONString(detail));

        if (!(detail.getSenderMobilePhone().endsWith(mobile) || detail.getReceiverMobilePhone().endsWith(mobile))) {
            log.warn("订单分享-查看详情获取运单明细,当前登录人不是下单人,没有权限查看订单详情");
            //失败错误次数限流
            smsAccessUtils.increment(key, 10, "订单分享-查看详情获取运单明细", 60L, 600L);
            throw new BusinessException(ServiceErrCodeEnum.PHONE_VERIFY_ERROR);
        }

        if (detail != null) {
            OmsOrderConventionVo omsOrderConventionVo = orderFeigntClient.queryOrderConventionByOrderId(Long.parseLong(orderId)).getData();
            log.info("订单详情,获取订单号{}预约时间：【{}】", orderId, JSON.toJSONString(omsOrderConventionVo));

            if (omsOrderConventionVo != null) {
                detail.setBestPickTimeStart(omsOrderConventionVo.getConventionPickStartTime());
                detail.setBestPickTimeEnd(omsOrderConventionVo.getConventionPickEndTime());
            }
        }

        List<OrderMarkCodeVO> orderMarkList = ccmOrderMarkApiFeignClient.queryMarkCodes(Lists.newArrayList(detail.getId())).result();
        if(!CollectionUtils.isEmpty(orderMarkList)){
            OrderMarkCodeVO orderMarkCodeVO = orderMarkList.get(0);
            List<String> markCodes = orderMarkCodeVO.getMarkCodes();
            if(!CollectionUtils.isEmpty(markCodes)){
                detail.setOrderChannelType(OrderChannelTypeEnum.getMappingCode(markCodes));
            }

        }

        AppOmsOrderApiVO appOmsOrderApiVO = orikaBeanMapper.map(detail, AppOmsOrderApiVO.class);
        if (StringUtils.isNotBlank(detail.getPickStaffCode())) {
            SysStaffDTO sysStaffDTO = new SysStaffDTO();
            sysStaffDTO.setCode(detail.getPickStaffCode());
//            SysStaffVO result = sysStaffFeignClient.getStaffDetail(sysStaffDTO).result();
            String requestId = UUID.randomUUID().toString().replaceAll("-", "");
            ForwardRequest request = new ForwardRequest();
            request.setRequestId(requestId);
            request.setRequestUri("/lmdmapi/sysStaff/detail");
            request.setBody(JSON.toJSONString(sysStaffDTO));
            log.info("请求id==>{}接口入参==>{}", requestId, JSON.toJSONString(request));
            Result<?> forward = channelApiFeignClient.forward(request);
            log.info("请求id==>{}接口返回==>{}", requestId, JSON.toJSONString(forward));
            Object resultO = forward.result();
            SysStaffVO result = JSON.parseObject(JSON.toJSONString(resultO), SysStaffVO.class);
            if (result != null) {
                appOmsOrderApiVO.setStaffMobile(result.getMobile());
            }
        }
        //查询订单标识
//        Result<List<OrderMarkingVo>> listResult = channelApiFeignClient.queryMarkingByOrderId(orderId);
//        log.info("订单详情,查询标识.入参==>{},出参==>{}",orderId,JSON.toJSONString(listResult));
//        List<OrderMarkingVo> result = listResult.result();
//        if(!CollectionUtils.isEmpty(result)){
//            appOmsOrderApiVO.setMarkingVoList(result);
//        }
        return Result.success(appOmsOrderApiVO);
    }

    /**
     * 分享订单，用户查看详情，不做token校验，但加上验签。
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "订单分享-查看详情", notes = "订单分享-查看详情")
    @PostMapping("/share/detail/checkOrder")
    public Result<Boolean> checkOrderIdExist(@RequestBody(required = false) ShareQueryDTO dto) {
        if (dto == null) {
            log.warn("【校验订单号】入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        String orderId = dto.getOrderId();
        if (StringUtils.isEmpty(orderId)) {
            throw new ServiceException(ServiceErrCodeEnum.PARAM_ERROR);
        }
        if (!NumberUtil.isNumber(orderId)) {
            log.warn("【校验订单号】订单号不为数字{}", orderId);
            throw new BusinessException(ServiceErrCodeEnum.PARAM_ERROR);
        }
        //校验签名
        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(orderId + dto.getTimes() + SHARE_PUBLIC_KEY));
        log.info("【校验订单号】后端MD5加密之后:" + sign1);
        if (!sign1.equals(dto.getSign())) {
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }

        OmsOrderApiVO detail = orderService.detail(Long.parseLong(orderId));
        log.info("【校验订单号】订单详情：【{}】", JSON.toJSONString(detail));
        return Result.success(Objects.nonNull(detail));
    }

    /**
     * 新增
     *
     * @param dto
     * @return
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增", notes = "新增")
    public Result<OmsOrderApiVO> save(@RequestBody @Validated(value = {SaveGroup.class}) OrderDTO dto) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        dto.setMyRequestId(requestId);
        log.info("请求id==>{}下订单,请求参数：【{}】", requestId, JSON.toJSONString(dto));
        WxUserVo user = getUser();


        LocalDateTime localDateTime = LocalDateTime.now();
        checkHandler(localDateTime, dto.getBestPickTimeStart(), dto.getBestPickTimeEnd());
        if (StringUtils.isEmpty(user.getMobile())) {
            throw new BusinessException(ServiceErrCodeEnum.NOT_BIND_MOBILE);
        }


        if (StringUtils.isNotBlank(dto.getWaybillId())) {
            if (dto.getWaybillId().contains("/")) {
                throw new BusinessException(ServiceErrCodeEnum.WAYBILLI_ERROR_2);
            }
            OmsOrderApiVO detail = orderFeigntClient.getOrderInfoByWaybillId(dto.getWaybillId()).result();
            if (detail != null) {
                throw new BusinessException(ServiceErrCodeEnum.WAYBILLI_ERROR);
            }
        }

        if (dto.getSenderAreaId().equals(dto.getReceiverAreaId()) && dto.getSenderDetailedAddress().equals(dto.getReceiverDetailedAddress())) {
            throw new BusinessException(ServiceErrCodeEnum.DETAILEDADDRESS_EQUALS);
        }
        if (org.apache.commons.lang.StringUtils.equals("0", isInterceptOrder)) {
            DispatchCodeRequestDTO queryDto = networkAbnormalService.orderDTOToDispatchCodeRequestDTO(dto);
            DispatchCodeResponseVO dispatchCodeResponseVO = networkAbnormalService.getNetworkByFetch(queryDto);
            //三段码查网点接口
            if (dispatchCodeResponseVO != null) {
                String netwprtCode = dispatchCodeResponseVO.getDeliverNetworkCode();
                dto.setFetchNetworkId(dispatchCodeResponseVO.getDeliverNetworkId() != null ? dispatchCodeResponseVO.getDeliverNetworkId().longValue() : null);//三段码解析的网点
                Result<OrderExceptionNetworkVO> result = networkFeighClient.getNetworkError(netwprtCode);
                log.info("请求id==>{}网点异常详细返回：{}", requestId, JSONObject.toJSONString(result));
                OrderExceptionNetworkVO networkErrorVoResult = result.getData();
                if (networkErrorVoResult != null) {
                    throw new BusinessException(networkErrorVoResult.getExceptionExplain());
                }
            }
        } else {
            //筛单接口校验
            InterceptOrderDto interceptOrderDto = new InterceptOrderDto();
            interceptOrderDto.setSource("appletWechat");
            InterceptOrderDto.SendAddress sendAddress = new InterceptOrderDto.SendAddress();
            sendAddress.setProvince(dto.getSenderProvinceName());
            sendAddress.setCity(dto.getSenderCityName());
            sendAddress.setArea(dto.getSenderAreaName());
            sendAddress.setDetail(dto.getSenderDetailedAddress());
            interceptOrderDto.setSendAddress(sendAddress);
            InterceptOrderDto.ReceiverAddress receiverAddress = new InterceptOrderDto.ReceiverAddress();
            receiverAddress.setProvince(dto.getReceiverProvinceName());
            receiverAddress.setCity(dto.getReceiverCityName());
            receiverAddress.setArea(dto.getReceiverAreaName());
            receiverAddress.setDetail(dto.getReceiverDetailedAddress());
            interceptOrderDto.setReceiverAddress(receiverAddress);
            log.info("请求id==>{}下单调用筛单接口,入参==>{}", requestId, JSON.toJSONString(interceptOrderDto));
            Result<InterceptOrderVo> interceptOrderVoResult = channelApiFeignClient.orderIntercept(interceptOrderDto);
            log.info("请求id==>{}下单调用筛单接口,出参==>{}", requestId, JSON.toJSONString(interceptOrderVoResult));
            InterceptOrderVo result = interceptOrderVoResult.result();
            if (result.getHit()) {
                log.info("请求id==>{}筛单拦截==>{}", requestId, JSON.toJSONString(result));
                throw new BusinessException(result.getReason());
            }
            //优惠券下单,补全三段码-网点信息
            DispatchCodeRequestDTO queryDto = networkAbnormalService.orderDTOToDispatchCodeRequestDTO(dto);
            log.info("请求id==>{}查询寄件地址三段码,入参==>{}", requestId, JSON.toJSONString(queryDto));
            DispatchCodeResponseVO dispatchCodeResponseVO = networkAbnormalService.getNetworkByFetch(queryDto);
            log.info("请求id==>{}查询寄件地址三段码,出参==>{}", requestId, JSON.toJSONString(dispatchCodeResponseVO));
            //三段码查网点接口
            if (dispatchCodeResponseVO != null && dispatchCodeResponseVO.getDeliverNetworkId() != null) {
                dto.setFetchNetworkId(dispatchCodeResponseVO.getDeliverNetworkId().longValue());//三段码解析的网点
                log.info("请求id==>{}查询寄件地址三段码,解析网点==>{}", requestId, dispatchCodeResponseVO.getDeliverNetworkId());
            }
            // 客户下单预警
            List<OrderMarkExpandVO> markFields = orderWarnCustomer(user.getMobile(), dto.getSenderMobilePhone(), dto.getReceiverMobilePhone(), requestId);
            if (CollectionUtil.isNotEmpty(markFields)) {
                dto.setMarkFields(markFields);
            }

        }
        return success(orderService.save(dto, user));
    }


    /**
     * 修改
     *
     * @param dto
     * @return
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改", notes = "修改")
    public Result<OmsOrderUpdateApiVO> update(@RequestBody @Validated(value = {UpdateGroup.class}) OrderDTO dto) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        dto.setMyRequestId(requestId);
        log.info("请求id==>{}修改订单入参==>{}", requestId, JSON.toJSONString(dto));
        WxUserVo user = getUser();
        OmsOrderApiVO detail = orderService.detail(dto.getId());
        log.info("请求id==>{}查到的订单详情==>{}", requestId, JSON.toJSONString(detail));
        if (detail == null) {
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_NULL);
        }
        dto.setPickStaffCodeUpdate(detail.getPickStaffCode());
        String[] split = sourceCodes.split(",");
        if (!Lists.newArrayList(split).contains(detail.getOrderSourceCode())) {
            log.warn("请求id==>{}当前订单来源,没有权限操作", requestId);
            throw new BusinessException(ServiceErrCodeEnum.ORDER_SOURCE_IS_NOT_OPS);
        }
        if (!StringUtils.equals(user.getNumberId() + "", detail.getMemberId() + "") &&
                !user.getMobile().equals(detail.getSenderMobilePhone()) && !user.getNumberId().equals(detail.getMemberId())) {
            log.warn("请求id==>{}当前登录人不是下单人,没有权限取消订单详情", requestId);
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_ERROR);
        }

        //判断订单状态，是否待上门，
        if (!ORDERSTATUS.contains(detail.getOrderStatusCode())) {
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_ERROR);
        }
        //判断订单是否已经修改过
        if (detail.getUpdateByName() != null) {
            throw new BusinessException(ServiceErrCodeEnum.ORDER_UPDATE_ERROR);
        }
        //如果订单存在预约时间，可以修改预约时间
        if (dto.getBestPickTimeStart() != null) {
            LocalDateTime localDateTime = LocalDateTime.now();
            checkHandler(localDateTime, dto.getBestPickTimeStart(), dto.getBestPickTimeEnd());
        }

//          2025-01-16 修改需求 --owain
//          * 修改订单时：
//          1.如果下单时是寄付现结或到付现结，不允许修改为寄付月结
//          2.如果下单时是寄付月结，不允许修改付款方式（不允许修改为寄付现结或到付现结，不允许修改月结账号）

        //下单时是寄付现结或到付现结
        if(com.yl.common.base.util.StringUtils.isNotEmpty(detail.getPaymentModeCode())
                && (Objects.equals(detail.getPaymentModeCode(), MonthlySettlementEnum.MonthlySettlementPayEnumPay.PP_CASH.code)
                || (Objects.equals(detail.getPaymentModeCode(), MonthlySettlementEnum.MonthlySettlementPayEnumPay.CC_CASH.code)))){
            //判断是否修改为月结
            if(Objects.equals(dto.getPaymentModeCode(),MonthlySettlementEnum.MonthlySettlementPayEnumPay.PP_PM.code)){
                throw new BusinessException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_PAY_CODE_ERROR);
            }
        }
        //下单时是寄付月结
        if(com.yl.common.base.util.StringUtils.isNotEmpty(detail.getPaymentModeCode())
                && (Objects.equals(detail.getPaymentModeCode(), MonthlySettlementEnum.MonthlySettlementPayEnumPay.PP_PM.code))){
            //判断是否修改为月结
            if(!Objects.equals(dto.getPaymentModeCode(),MonthlySettlementEnum.MonthlySettlementPayEnumPay.PP_PM.code)){
                throw new BusinessException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_PAY_MENT_TYPE_ERROR);
            }
            //判断是否切换月结账号
            if(!Objects.equals(dto.getAccount(),detail.getCustomerCode())){
                throw new BusinessException(ResultCodeEnum.CITIC_MONTHLY_SETTLEMENT_PAY_MENT_TYPE_ERROR);
            }
        }




        //只能修改收件人信息
        dto.setInputTime(detail.getInputTime());
        dto.setSenderName(detail.getSenderName());
        dto.setSenderMobilePhone(detail.getSenderMobilePhone());
        dto.setSenderProvinceName(detail.getSenderProvinceName());
        dto.setSenderProvinceId(detail.getSenderProvinceId());
        dto.setSenderCityName(detail.getSenderCityName());
        dto.setSenderCityId(detail.getSenderCityId());
        dto.setSenderAreaName(detail.getSenderAreaName());
        dto.setSenderAreaId(detail.getSenderAreaId());
        dto.setSenderDetailedAddress(detail.getSenderDetailedAddress());
        dto.setSenderCompany(detail.getSenderCompany());
        dto.setOrderSourceCode(detail.getOrderSourceCode());
        if (null != dto.getDeclaredValue() && dto.getDeclaredValue().compareTo(BigDecimal.ZERO) > 0) {
            dto.setInsured(1);
        } else {
            dto.setInsured(0);
        }
        log.info("请求id==>{}下订单,请求参数：【{}】", requestId, JSON.toJSONString(dto));
        if (dto.getSenderAreaId().equals(dto.getReceiverAreaId()) && dto.getSenderDetailedAddress().equals(dto.getReceiverDetailedAddress())) {
            throw new BusinessException(ServiceErrCodeEnum.DETAILEDADDRESS_EQUALS);
        }
        if (org.apache.commons.lang.StringUtils.equals("0", isInterceptOrder)) {
            DispatchCodeRequestDTO queryDto = networkAbnormalService.orderDTOToDispatchCodeRequestDTO(dto);
            DispatchCodeResponseVO dispatchCodeResponseVO = networkAbnormalService.getNetworkByFetch(queryDto);
            //三段码查网点接口
            if (dispatchCodeResponseVO != null) {
                String netwprtCode = dispatchCodeResponseVO.getDeliverNetworkCode();
                dto.setFetchNetworkId(dispatchCodeResponseVO.getDeliverNetworkId() != null ? dispatchCodeResponseVO.getDeliverNetworkId().longValue() : null);//三段码解析的网点
                Result<OrderExceptionNetworkVO> result = networkFeighClient.getNetworkError(netwprtCode);
                log.info("请求id==>{}网点异常详细返回：{}", requestId, JSONObject.toJSONString(result));
                OrderExceptionNetworkVO networkErrorVoResult = result.getData();
                if (networkErrorVoResult != null) {
                    throw new BusinessException(networkErrorVoResult.getExceptionExplain());
                }
            }
        } else {
            //筛单接口校验
            InterceptOrderDto interceptOrderDto = new InterceptOrderDto();
            interceptOrderDto.setSource("appletWechat");
            InterceptOrderDto.SendAddress sendAddress = new InterceptOrderDto.SendAddress();
            sendAddress.setProvince(dto.getSenderProvinceName());
            sendAddress.setCity(dto.getSenderCityName());
            sendAddress.setArea(dto.getSenderAreaName());
            sendAddress.setDetail(dto.getSenderDetailedAddress());
            interceptOrderDto.setSendAddress(sendAddress);
            InterceptOrderDto.ReceiverAddress receiverAddress = new InterceptOrderDto.ReceiverAddress();
            receiverAddress.setProvince(dto.getReceiverProvinceName());
            receiverAddress.setCity(dto.getReceiverCityName());
            receiverAddress.setArea(dto.getReceiverAreaName());
            receiverAddress.setDetail(dto.getReceiverDetailedAddress());
            interceptOrderDto.setReceiverAddress(receiverAddress);
            log.info("请求id==>{}改单调用筛单接口,入参==>{}", requestId, JSON.toJSONString(interceptOrderDto));
            Result<InterceptOrderVo> interceptOrderVoResult = channelApiFeignClient.orderIntercept(interceptOrderDto);
            log.info("请求id==>{}改单调用筛单接口,出参==>{}", requestId, JSON.toJSONString(interceptOrderVoResult));
            InterceptOrderVo result = interceptOrderVoResult.result();
            if (result.getHit()) {
                log.info("请求id==>{}筛单拦截==>{}", requestId, JSON.toJSONString(result));
                throw new BusinessException(result.getReason());
            }
        }
        return success(orderService.update(dto, user));
    }


    /**
     * 批量下单
     *
     * @param dto
     * @return
     */
    @PostMapping("/batchOrder")
    @ApiOperation(value = "新增", notes = "新增")
    public Result<OmsOrderBatchApiVo> batchOrder(@RequestBody OmsOrderBatchApiDto dto) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        dto.setMyRequestId(requestId);
        WxUserVo user = getUser();
        if (StringUtils.isEmpty(user.getMobile())) {
            throw new BusinessException(ServiceErrCodeEnum.NOT_BIND_MOBILE);
        }
        log.info("请求id==>{}批量下订单,请求参数：【{}】", requestId, JSON.toJSONString(dto));
        LocalDateTime localDateTime = LocalDateTime.now();
        //收寄件地址不能相同
        dto.getOrders().forEach(order -> {
            //2023-03-29 解决微信小程序来源  最佳取件时间时间为空的异常
            //批量不传订单来源  直接根据from去映射的
            if (Objects.isNull(order.getBestPickTimeStart()) || Objects.isNull(order.getBestPickTimeEnd())) {
                throw new BusinessException(ServiceErrCodeEnum.BESTPICK_TIME_IS_MISS);
            }
            checkHandler(localDateTime, order.getBestPickTimeStart(), order.getBestPickTimeEnd());

            if (order.getSenderAreaId().equals(order.getReceiverAreaId()) && order.getSenderDetailedAddress().equals(order.getReceiverDetailedAddress())) {
                throw new BusinessException(ServiceErrCodeEnum.DETAILEDADDRESS_EQUALS);
            }
        });
        if (dto.getOrders().size() > 0) {
            if (org.apache.commons.lang.StringUtils.equals("0", isInterceptOrder)) {
                DispatchCodeRequestDTO queryDto = networkAbnormalService.omsOrderApiDTOOToDispatchCodeRequestDTO(dto.getOrders().get(0));
                DispatchCodeResponseVO dispatchCodeResponseVO = networkAbnormalService.getNetworkByFetch(queryDto);
                //三段码查网点接口
                if (dispatchCodeResponseVO != null) {
                    String netwprtCode = dispatchCodeResponseVO.getDeliverNetworkCode();
                    Result<OrderExceptionNetworkVO> result = networkFeighClient.getNetworkError(netwprtCode);
                    log.info("请求id==>{}网点异常详细返回：{}", requestId, JSONObject.toJSONString(result));
                    OrderExceptionNetworkVO networkErrorVoResult = result.getData();
                    if (networkErrorVoResult != null) {
                        throw new BusinessException(networkErrorVoResult.getExceptionExplain());
                    }
                }
            } else {
                dto.getOrders().forEach(x -> {
                    //筛单接口校验
                    InterceptOrderDto interceptOrderDto = new InterceptOrderDto();
                    interceptOrderDto.setSource("appletWechat");
                    InterceptOrderDto.SendAddress sendAddress = new InterceptOrderDto.SendAddress();
                    sendAddress.setProvince(x.getSenderProvinceName());
                    sendAddress.setCity(x.getSenderCityName());
                    sendAddress.setArea(x.getSenderAreaName());
                    sendAddress.setDetail(x.getSenderDetailedAddress());
                    interceptOrderDto.setSendAddress(sendAddress);
                    InterceptOrderDto.ReceiverAddress receiverAddress = new InterceptOrderDto.ReceiverAddress();
                    receiverAddress.setProvince(x.getReceiverProvinceName());
                    receiverAddress.setCity(x.getReceiverCityName());
                    receiverAddress.setArea(x.getReceiverAreaName());
                    receiverAddress.setDetail(x.getReceiverDetailedAddress());
                    interceptOrderDto.setReceiverAddress(receiverAddress);
                    log.info("请求id==>{}下单调用筛单接口,入参==>{}", requestId, JSON.toJSONString(interceptOrderDto));
                    Result<InterceptOrderVo> interceptOrderVoResult = channelApiFeignClient.orderIntercept(interceptOrderDto);
                    log.info("请求id==>{}下单调用筛单接口,出参==>{}", requestId, JSON.toJSONString(interceptOrderVoResult));
                    InterceptOrderVo result = interceptOrderVoResult.result();
                    if (result.getHit()) {
                        log.info("请求id==>{}筛单拦截==>{}", requestId, JSON.toJSONString(result));
                        throw new BusinessException(result.getReason());
                    }
                    // 客户下单预警
                    List<OrderMarkExpandVO> markFields = orderWarnCustomer(user.getMobile(), x.getSenderMobilePhone(), x.getReceiverMobilePhone(), requestId);
                    if (CollectionUtil.isNotEmpty(markFields)) {
                        x.setMarkFields(markFields);
                    }
                });
            }
        }
        return success(orderService.batchOrder(dto));
    }


    /**
     * 取消
     *
     * @param orderIds
     * @return
     */
    @PostMapping("/cancel")
    @ApiOperation(value = "取消", notes = "取消")
    public Result<Boolean> cancel(@RequestBody List<Long> orderIds) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("请求id==>{}取消订单,入参==>{}", requestId, JSON.toJSONString(orderIds));
        if (CollectionUtils.isEmpty(orderIds)) {
            throw new ServiceException(ServiceErrCodeEnum.ORDERID_ISNULL);
        }
        WxUserVo user = getUser();
        for (Long orderId : orderIds) {
            OmsOrderApiVO detail = orderService.detail(orderId);
            log.info("请求id==>{}订单详情：【{}】", requestId, JSON.toJSONString(detail));
            String[] split = sourceCodes.split(",");
            if (!Lists.newArrayList(split).contains(detail.getOrderSourceCode())) {
                log.warn("请求id==>{}当前订单来源,没有权限操作", requestId);
                throw new BusinessException(ServiceErrCodeEnum.ORDER_SOURCE_IS_NOT_OPS);
            }
            if (!StringUtils.equals(user.getNumberId() + "", detail.getMemberId() + "") &&
                    !user.getMobile().equals(detail.getSenderMobilePhone()) && !user.getNumberId().equals(detail.getMemberId())) {
                log.warn("请求id==>{}当前登录人不是下单人,没有权限取消订单", requestId);
                throw new ServiceException(ServiceErrCodeEnum.OAUTH_NO_ACCESS);
            }
        }
        return success(orderService.cancel(orderIds, requestId));
    }


    /**
     * 订单取消原因
     *
     * @param orderCancelDTO
     * @return
     */
    @PostMapping("/v2/cancel")
    @ApiOperation(value = "订单取消原因", notes = "订单取消原因")
    public Result<Boolean> cancel(@RequestBody OrderCancelDTO orderCancelDTO) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("请求id==>{}取消订单,入参==>{}", requestId, JSON.toJSONString(orderCancelDTO));
        if (CollectionUtils.isEmpty(orderCancelDTO.getOrderIds())) {
            throw new ServiceException(ServiceErrCodeEnum.ORDERID_ISNULL);
        }
        WxUserVo user = getUser();
        for (Long orderId : orderCancelDTO.getOrderIds()) {
            OmsOrderApiVO detail = orderService.detail(orderId);
            log.info("请求id==>{}取消订单,订单详情：【{}】", requestId, JSON.toJSONString(detail));
            String[] split = sourceCodes.split(",");
            if (!Lists.newArrayList(split).contains(detail.getOrderSourceCode())) {
                log.warn("请求id==>{}当前订单来源,没有权限操作", requestId);
                throw new BusinessException(ServiceErrCodeEnum.ORDER_SOURCE_IS_NOT_OPS);
            }
            if (!StringUtils.equals(user.getNumberId() + "", detail.getMemberId() + "") &&
                    !user.getMobile().equals(detail.getSenderMobilePhone()) && !user.getNumberId().equals(detail.getMemberId())) {
                log.warn("请求id==>{}当前登录人不是下单人,没有权限取消订单", requestId);
                throw new ServiceException(ServiceErrCodeEnum.OAUTH_NO_ACCESS);
            }
        }
        return success(orderService.cancelReason(orderCancelDTO, requestId));
    }


    /**
     * 删除
     *
     * @param orderId
     * @return
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除", notes = "删除")
    public Result<Boolean> delete(@RequestParam("orderId") String orderId) {
        if (StringUtils.isBlank(orderId)) {
            throw new ServiceException(ServiceErrCodeEnum.ORDERID_ISNULL);
        }
        if (!NumberUtil.isNumber(orderId)) {
            throw new BusinessException(ServiceErrCodeEnum.IS_INVALID_ORDER_ID);
        }
        WxUserVo user = getUser();
        OmsOrderApiVO detail = orderService.detail(Long.parseLong(orderId));
        log.info("订单详情：【{}】", JSON.toJSONString(detail));
        String[] split = sourceCodes.split(",");
        if (!Lists.newArrayList(split).contains(detail.getOrderSourceCode())) {
            log.warn("当前订单来源,没有权限操作");
            throw new BusinessException(ServiceErrCodeEnum.ORDER_SOURCE_IS_NOT_OPS);
        }
        if (!StringUtils.equals(user.getNumberId() + "", detail.getMemberId() + "") &&
                !user.getMobile().equals(detail.getSenderMobilePhone()) && !user.getNumberId().equals(detail.getMemberId())) {
            log.warn("当前登录人不是下单人,没有权限删除订单");
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_ERROR);
        }
//        if (!StringUtils.equals(getUser().getNumberId()+"",detail.getMemberId()+"")) {
//            log.warn("当前登录人不是下单人,没有权限删除订单详情");
//            throw new ServiceException(ServiceErrCodeEnum.OAUTH_NO_ACCESS);
//        }
        return success(orderService.delete(Long.valueOf(orderId)));
    }


    /**
     * 云打印-批量订单
     *
     * @param dto
     * @return
     */
    @PostMapping("/cloudPrintOrder")
    @ApiOperation(value = "云打印-批量订单", notes = "云打印-批量订单")
    public Result<OmsOrderBatchApiVo> cloudPrintOrder(@RequestBody @Valid BatchOrderDTO dto) {
        WxUserVo user = getUser();
        if (StringUtils.isEmpty(user.getMobile())) {
            throw new BusinessException(ServiceErrCodeEnum.NOT_BIND_MOBILE);
        }
        log.info("云打印-批量订单,请求参数：【{}】", JSON.toJSONString(dto));
        // 下单手机号预警
        Set<String> checkPhone = new HashSet<>();
        for (OrderDTO order : dto.getOrders()) {
            checkPhone.add(order.getReceiverMobilePhone());
            checkPhone.add(order.getSenderMobilePhone());
        }
        checkPhone.add(user.getMobile());
        List<CustomerQueryByPhoneVO> warnCustomers = new ArrayList<>();
        try {
            log.info("云打印-批量订单-客户下单预警,入参==>{}", checkPhone);
            Result<List<CustomerQueryByPhoneVO>> orderWarnCustomers = channelApiFeignClient.getOrderWarnCustomers(checkPhone);
            log.info("云打印-批量订单-客户下单预警==>{}", JSON.toJSONString(orderWarnCustomers));
            if (Objects.nonNull(orderWarnCustomers) && orderWarnCustomers.isSucc() && CollectionUtil.isNotEmpty(orderWarnCustomers.getData())) {
                warnCustomers = orderWarnCustomers.getData();
            }
        } catch (Exception e) {
            log.error("客户下单预警接口异常：{}", e);
        }
        if (CollectionUtil.isNotEmpty(warnCustomers)) {
            Set<String> firstWarnPhone = new HashSet<>();
            Set<String> secondWarnPhone = new HashSet<>();
            for (CustomerQueryByPhoneVO warnCustomer : warnCustomers) {
                if (WARN_CUSTOMER_ONE_LEVEL.equals(warnCustomer.getLevelTwoTypeCode())) {
                    firstWarnPhone.add(warnCustomer.getMobilePhone());
                }
                if (WARN_CUSTOMER_TWO_LEVEL.equals(warnCustomer.getLevelTwoTypeCode())) {
                    secondWarnPhone.add(warnCustomer.getMobilePhone());
                }
            }
            if (CollectionUtil.isNotEmpty(secondWarnPhone)) {
                throw new BusinessException(WARN_CUSTOMER_ERROR);
            }
            for (OrderDTO order : dto.getOrders()) {
                if (firstWarnPhone.contains(order.getReceiverMobilePhone()) || firstWarnPhone.contains(order.getSenderMobilePhone()) || firstWarnPhone.contains(user.getMobile())) {
                    List<OrderMarkExpandVO> resourceList = Lists.newArrayList();
                    // 一级客户需要打标
                    OrderMarkExpandVO expandVO = new OrderMarkExpandVO();
                    //构建参数
                    expandVO.setFieldValue(WARN_CUSTOMER_VALUE);
                    expandVO.setFieldName(WARN_CUSTOMER_NAME);
                    expandVO.setEffective(true);
                    resourceList.add(expandVO);
                    //微信小程序下单打标
                    OrderMarkExpandVO wxAppletsRemark = new OrderMarkExpandVO();
                    wxAppletsRemark.setFieldName(BaseConstant.CCM_TERMINAL_ORDER_FROM);
                    wxAppletsRemark.setFieldValue("1");//微信
                    wxAppletsRemark.setEffective(true);

                    resourceList.add(wxAppletsRemark);
                    order.setMarkFields(resourceList);
                }
            }
        }
        return success(orderService.cloudPrintOrder(dto));
    }

    /**
     * 预约时间判断
     *
     * @param localDateTime     当前时间
     * @param bestPickTimeStart 预约开始时间
     * @param bestPickTimeEnd   预约结束时间
     */
    public void checkHandler(LocalDateTime localDateTime, LocalDateTime bestPickTimeStart, LocalDateTime bestPickTimeEnd) {
        if (bestPickTimeStart != null) {
            //预约时间不在9：00到17：00之间
            if (!(9 <= bestPickTimeStart.getHour() && bestPickTimeStart.getHour() <= 17)) {
                throw new BusinessException(ServiceErrCodeEnum.BOOK_TIME_ERROR);
            } else {
                Duration between = LocalDateTimeUtil.between(localDateTime, bestPickTimeStart);
                //预约时间在当前时间之前 且 相差5分钟以上
                if (between.toMinutes() < 0 && between.toMinutes() < -5) {
                    throw new BusinessException(ServiceErrCodeEnum.BOOK_TIME_ERROR);
                }
            }
            if (bestPickTimeEnd == null) {
                throw new BusinessException(ServiceErrCodeEnum.BOOK_TIME_END_ERROR);
            }
        }
    }

    public List<OrderMarkExpandVO> orderWarnCustomer(String loginUserPhone, String senderMobilePhone, String receiverMobilePhone, String requestId) {
        List<OrderMarkExpandVO> resourceList = Lists.newArrayList();
        CustomerQueryByPhoneVO warnCustomerData;
        try {// 客户下单预警
            log.info("请求id==>{}客户下单预警,入参==>{},{},{}", requestId, loginUserPhone, senderMobilePhone, receiverMobilePhone);
            Result<CustomerQueryByPhoneVO> orderWarnCustomer = channelApiFeignClient.getSingleOrderWarnCustomer(loginUserPhone, senderMobilePhone, receiverMobilePhone);
            log.info("请求id==>{}客户下单预警==>{}", requestId, JSON.toJSONString(orderWarnCustomer));
            if(Objects.isNull(orderWarnCustomer) || orderWarnCustomer.isFail()){
                return resourceList;
            }
            warnCustomerData = orderWarnCustomer.getData();
            if (Objects.isNull(warnCustomerData)) {
                return resourceList;
            }
        } catch (Exception e) {
            log.error("客户下单预警接口异常：{}", e);
            return resourceList;
        }

        if (WARN_CUSTOMER_TWO_LEVEL.equals(warnCustomerData.getLevelTwoTypeCode())) {
            throw new BusinessException(WARN_CUSTOMER_ERROR);
        } else if (WARN_CUSTOMER_ONE_LEVEL.equals(warnCustomerData.getLevelTwoTypeCode())) {
            // 一级客户需要打标
            OrderMarkExpandVO expandVO = new OrderMarkExpandVO();
            //构建参数
            expandVO.setFieldValue(WARN_CUSTOMER_VALUE);
            expandVO.setFieldName(WARN_CUSTOMER_NAME);
            expandVO.setEffective(true);
            resourceList.add(expandVO);
        }
        return resourceList;
    }

}
