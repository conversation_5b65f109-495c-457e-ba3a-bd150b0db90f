package com.yl.applets.controller;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.yl.applets.dto.ThirdExpressApiDTO;
import com.yl.applets.enums.OrderSourceEnum;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.service.IExpressDeliveryService;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.ThirdExpressListVO;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.YlPreconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-23
 */
@Api(value = "快件", tags = {"快件"})
@RestController
@RequestMapping("/expressDelivery")
@Slf4j
public class ExpressDeliveryController extends AppBaseController {

    /**
     * 寄件类型
     */
    private static final int EXPRESS_TYPE_SEND = 1;
    /**
     * 收件类型
     */
    private static final int EXPRESS_TYPE_TAKE = 2;
    /**
     * 两者都查
     */
    private static final int EXPRESS_TYPE_ALL= 3;

    /**
     * 待支付
     */
    private static final int EXPRESS_STATUS_PAY = 4;


    @Autowired
    private IExpressDeliveryService expressDeliveryService;

    /**
     * 快件列表
     *
     * @param dto
     * @return
     */
    @GetMapping("/page")
    @ApiOperation(value = "快件列表", notes = "快件列表")
    public Result<Page<ThirdExpressListVO>> getPages(ThirdExpressApiDTO dto) {
        YlPreconditions.checkArgument(Objects.nonNull(dto.getSearchType()), new ServiceException(ServiceErrCodeEnum.SEARCHTYPE_ISNULL));
        if (Objects.isNull(dto.getCurrent())) {
            dto.setCurrent(1);
        }
        if (Objects.isNull(dto.getSize())) {
            dto.setSize(20);
        }
        WxUserVo wxUserVo = getUser();
        log.info("获取缓存用户,入参：{},结果：{}", JsonUtils.toJson(dto),JsonUtils.toJson(wxUserVo));
        dto.setPhoneNo(wxUserVo.getMobile());
        switch (dto.getSearchType()) {
            case EXPRESS_TYPE_SEND://寄件
                dto.setCustomerId(wxUserVo.getNumberId().longValue());
                break;
            case EXPRESS_TYPE_TAKE://收件
                if (StringUtils.isBlank(wxUserVo.getMobile())) {//查询类型为收件且用户没有绑定手机号，直接返回空。
                    return success(new Page<>(dto.getCurrent(), dto.getSize()));
                }
                dto.setPhoneNo(wxUserVo.getMobile());
                break;
            case EXPRESS_STATUS_PAY://待支付
                dto.setOrderSourceCode(OrderSourceEnum.WX_MINIPROGRAM.getCode());
                dto.setCustomerId(wxUserVo.getNumberId().longValue());
                dto.setPhoneNo(null);
                break;
            default:
                throw new ServiceException(ServiceErrCodeEnum.SEARCHTYPE_ERROR);
        }
        return success(expressDeliveryService.getPages(dto));
    }

    /**
     * 功能描述:
     * 寄件快件数量（应订单优化要求，接口拆分）
     * @param dto
     * @return:com.yl.common.base.model.vo.Result<java.util.Map<java.lang.String,java.lang.Long>>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-05-21 13:58
     */
    @PostMapping("/sendCount")
    @ApiOperation(value = "寄件快件数量", notes = "寄件快件数量")
    public Result<Map<String, Long>> sendCount(@RequestBody ThirdExpressApiDTO dto) {
        WxUserVo wxUserVo = getUser();
        dto.setPhoneNo(wxUserVo.getMobile());
        dto.setSearchType(EXPRESS_TYPE_SEND);
        dto.setCustomerId(wxUserVo.getNumberId().longValue());
        LocalDateTime createTime = getUser().getCreateTime();
        LocalDateTime now = LocalDateTime.now();
        if(createTime!=null){
            if(createTime.plusDays(90).isBefore(now)){
                log.info("查订单列表,注册时间在90天以上");
            }else {
                dto.setInputTimeStart(createTime);
                dto.setInputTimeEnd(now);
            }
        }
        log.info("查寄件单数量,入参==>{}", JSON.toJSONString(dto));
        return success(expressDeliveryService.count(dto));
    }

    /**
     * 功能描述:
     * 收件快件数量 （应订单优化要求，接口拆分）
     * @param dto
     * @return:com.yl.common.base.model.vo.Result<java.util.Map<java.lang.String,java.lang.Long>>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-05-21 13:58
     */
    @PostMapping("/taskCount")
    @ApiOperation(value = "收件快件数量", notes = "收件快件数量")
    public Result<Map<String, Long>> taskCount(@RequestBody ThirdExpressApiDTO dto) {
        WxUserVo wxUserVo = getUser();
        dto.setPhoneNo(wxUserVo.getMobile());
        dto.setSearchType(EXPRESS_TYPE_TAKE);
        dto.setCustomerId(wxUserVo.getNumberId().longValue());
        LocalDateTime createTime = getUser().getCreateTime();
        LocalDateTime now = LocalDateTime.now();
        if(createTime!=null){
            if(createTime.plusDays(90).isBefore(now)){
                log.info("查订单列表,注册时间在90天以上");
            }else {
                dto.setInputTimeStart(createTime);
                dto.setInputTimeEnd(now);
            }
        }
        log.info("查收件单数量,入参==>{}", JSON.toJSONString(dto));
        return success(expressDeliveryService.count(dto));
    }


    /**
     * 功能描述:
     * 待支付订单量 （应订单优化要求，接口拆分）
     *
     * @param dto
     * @return:com.yl.common.base.model.vo.Result<java.util.Map<java.lang.String,java.lang.Long>>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-05-21 13:58
     */
    @PostMapping("/payCount")
    @ApiOperation(value = "待支付数量", notes = "待支付数量")
    public Result<Map<String, Long>> payCount(@RequestBody ThirdExpressApiDTO dto) {
        WxUserVo wxUserVo = getUser();
        dto.setSearchType(EXPRESS_STATUS_PAY);
        dto.setCustomerId(wxUserVo.getNumberId().longValue());
        dto.setOrderSourceCode(OrderSourceEnum.WX_MINIPROGRAM.getCode());
        LocalDateTime createTime = getUser().getCreateTime();
        LocalDateTime now = LocalDateTime.now();
        if (createTime != null) {
            if (createTime.plusDays(90).isBefore(now)) {
                log.info("查订单列表,注册时间在90天以上");
            } else {
                dto.setInputTimeStart(createTime);
                dto.setInputTimeEnd(now);
            }
        }
        if (Objects.isNull(dto.getPayStatus()) || dto.getPayStatus() != 2) {
            HashMap<String, Long> map = Maps.newHashMap();
            map.put("payCount",0L);
            return Result.success(map);
        }
        log.info("查待支付数量,入参==>{}", JSON.toJSONString(dto));
        return success(expressDeliveryService.count(dto));
    }

    /**
     * 快件数量
     *
     * @param dto
     * @return
     */
    @GetMapping("/count")
    @ApiOperation(value = "快件数量", notes = "快件数量")
    public Result<Map<String, Long>> count(ThirdExpressApiDTO dto) {
        WxUserVo wxUserVo = getUser();
        dto.setPhoneNo(wxUserVo.getMobile());
        dto.setSearchType(EXPRESS_TYPE_ALL);
        dto.setCustomerId(wxUserVo.getNumberId().longValue());
        return success(expressDeliveryService.count(dto));
    }

}
