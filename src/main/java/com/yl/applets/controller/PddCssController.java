package com.yl.applets.controller;

import com.yl.applets.utils.ParamSignUtils;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.TreeMap;

@RestController
@Slf4j
public class PddCssController extends AppBaseController{

    @Autowired
    private RestTemplate restTemplate;

    @Value("${cssPddKey:abb9c413dc5a49fcb859bdac82aa93e3}")
    private String cssPddKey;

    @Value("${cssPddUrl:http://test-css-pdd.jtexpress.com.cn/csspdd}")
    private String cssPddUrl;

    /**
     * 获取pdd客服游客token
     * @return
     */
    @GetMapping("/css/getClientToken")
    public Result checkToken(){

        Result responseEntity = null;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            //请求体
            TreeMap<String, Object> requestParam = new TreeMap<String, Object>();
            requestParam.put("timestamp",String.valueOf(System.currentTimeMillis()));
            requestParam.put("apiAccount", "1001");
            String digest = ParamSignUtils.getDigest(requestParam, cssPddKey);

            requestParam.put("digest",digest);
            requestParam.put("channelCode","MP");
            requestParam.put("uid",getUser().getOpenid());

            HttpEntity requestBody = new HttpEntity(requestParam, headers);

            responseEntity = restTemplate.postForObject(cssPddUrl+"/css/api/getAppClientToken", requestBody, Result.class);
        } catch (Exception e) {
            log.warn("调用内部接口异常：{}",e);
            throw new BusinessException(ResultCodeEnum.INTERFACE_INNER_INVOKE_ERROR);
        }
        return responseEntity;
    }

    /**
     * 校验token有效性
     * @param token
     * @return
     */
    @PostMapping("/css/checkClientToken")
    public Result checkClientToken(@RequestParam("token") String token){

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        //请求体
        TreeMap<String, Object> requestParam = new TreeMap<String, Object>();
        requestParam.put("timestamp",String.valueOf(System.currentTimeMillis()));
        requestParam.put("apiAccount", "1001");
        String digest = ParamSignUtils.getDigest(requestParam, cssPddKey);

        requestParam.put("digest",digest);
        requestParam.put("token",token);
        requestParam.put("userType",1);

        HttpEntity requestBody = new HttpEntity(requestParam, headers);

        Result responseEntity = restTemplate.postForObject(cssPddUrl+"/css/api/checkToken", requestBody,Result.class);

        return responseEntity;
    }

}
