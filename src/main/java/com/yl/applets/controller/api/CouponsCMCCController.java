package com.yl.applets.controller.api;

import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.CouponsCMCCDTO;
import com.yl.applets.service.CouponsCMCCService;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-03-14 17:20
 * @Version 1.0
 */
@Slf4j
@Api(value = "对接河南移动优惠券", tags = "对接河南移动优惠券")
@RestController
@RequestMapping("/coupons")
public class CouponsCMCCController {

    @Autowired
    private HttpServletRequest httpServletRequest;

    @Autowired
    private CouponsCMCCService couponsCMCCService;


    @PostMapping("/bindings")
    public Result<Boolean> bindCouponsById(@RequestBody @Validated CouponsCMCCDTO dto) {
        log.info("【对接河南移动优惠券】入参：{}", JSON.toJSONString(dto));
        return couponsCMCCService.bindCouponsById(dto,httpServletRequest);
    }
}
