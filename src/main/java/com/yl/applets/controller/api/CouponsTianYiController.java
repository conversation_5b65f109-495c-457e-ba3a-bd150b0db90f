package com.yl.applets.controller.api;

import com.yl.applets.dto.coupons.TianYiTradeDTO;
import com.yl.applets.dto.coupons.TianyiDTO;
import com.yl.applets.dto.coupons.TianyiPublicDTO;
import com.yl.applets.service.coupons.CouponsTianYiService;
import com.yl.common.base.model.ResultVO;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-05-24 16:01
 * @Version 1.0
 */
@Slf4j
@Api(value = "翼支付优惠券对接", tags = "翼支付优惠券对接")
@RestController
@RequestMapping("/ty/coupons")
public class CouponsTianYiController {

    @Autowired
    private CouponsTianYiService couponsTianYiService;

    @Autowired
    private HttpServletRequest request;

    @PostMapping("/checkPhoneAvailability")
    public Result<Boolean> checkPhoneAvailability(@Validated TianyiDTO dto) {
        return Result.success(couponsTianYiService.checkPhoneAvailability(dto, request));
    }

    @PostMapping("/bind/trade")
    public ResultVO bindUserAndTrade(TianYiTradeDTO dto){

        return couponsTianYiService.bindUserAndTrade(dto);
    }

    @PostMapping("/bind/query")
    public ResultVO bindUserAndTradeQuery(TianYiTradeDTO dto){
        return couponsTianYiService.bindUserAndTradeQuery(dto);
    }

}
