package com.yl.applets.controller.api;

import com.yl.applets.controller.AppBaseController;
import com.yl.applets.service.CouponsGOKService;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-04-24 11:41
 * @Version 1.0
 */
@Slf4j
@Api(value = "王者荣耀发放优惠券", tags = "王者荣耀发放优惠券")
@RestController
@RequestMapping("/gok/coupons")
public class CouponsGOKController extends AppBaseController {

    @Autowired
    private CouponsGOKService couponsGOKService;

    @PostMapping("/bindings/active")
    @Deprecated
    public Result<Boolean> bindCouponsById() {
        return Result.success(couponsGOKService.bindGokCoupons(getUser()));
    }
}
