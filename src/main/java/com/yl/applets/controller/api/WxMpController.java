package com.yl.applets.controller.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.yl.applets.dto.MpMenuDto;
import com.yl.applets.entity.AppletsMpMenu;
import com.yl.applets.service.IAppletsMpMenuService;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.CollectionUtils;
import com.yl.common.base.util.GenerationIdUtil;
import com.yl.common.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.menu.WxMenu;
import me.chanjar.weixin.common.bean.menu.WxMenuButton;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.menu.WxMpMenu;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-09-03
 */

@Slf4j
@RestController
@RequestMapping("/api/wx")
public class WxMpController {



    @Autowired
    private IAppletsMpMenuService appletsMpMenuService;

    @Autowired
    private OrikaBeanMapper beanMapper;


    @Autowired
    private WxMpService wxMpService;

    @RequestMapping("/getMenu")
    public Result<MpMenuDto> getMenu(){
        List<Map<String,String>> content = Lists.newArrayList();
        List<MpMenuDto.MediaDto> mediaDtos = Lists.newArrayList();
        List<MpMenuDto.MyWxMenuButton> myMenuButtons = Lists.newArrayList();
        MpMenuDto dto=new MpMenuDto();
        AppletsMpMenu one = appletsMpMenuService.getOne(new LambdaQueryWrapper<AppletsMpMenu>().eq(AppletsMpMenu::getIsDelete, 0).orderByDesc(AppletsMpMenu::getCreateTime));
        if(one==null){
            log.info("本地菜单为空");
            return Result.success(dto);
            //如果本地菜单为空，拉取微信菜单
//            try {
//                WxMpMenu wxMpMenu=wxMpService.getMenuService().menuGet();
//                log.info("从微信服务器获取公众号菜单:"+JSON.toJSONString(wxMpMenu));
//                //转换菜单
//                List<WxMenuButton> wxMenuButtons = wxMpMenu.getMenu().getButtons();
//                myMenuButtons = beanMapper.mapAsList(wxMenuButtons, MpMenuDto.MyWxMenuButton.class);
//                log.info("从微信服务器获取公众号菜单转换之后:"+JSON.toJSONString(wxMenuButtons));
//            } catch (WxErrorException e) {
//                e.printStackTrace();
//                return Result.success(dto);
//            }
        } else {
            myMenuButtons = JSONObject.parseArray(one.getMenuJson(), MpMenuDto.MyWxMenuButton.class);
            if(StringUtils.isNotEmpty(one.getTextContent())){
                content = JSONArray.parseObject(one.getTextContent(),List.class);
            }
            if(StringUtils.isNotEmpty(one.getMediaContent())){
                mediaDtos = JSONArray.parseObject(one.getMediaContent(),List.class);
            }
        }
        dto.setButtons(myMenuButtons);
        dto.setTextContents(content);
        dto.setMediaContents(mediaDtos);
        return Result.success(dto);
    }


    @RequestMapping(value = "/createMenu",method = RequestMethod.POST)
    public Result<Boolean> createMenus(@RequestBody(required = false) MpMenuDto dto){
        if(dto==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        log.info("创建菜单入参：{}",JSON.toJSONString(dto));
//        AppletsMpMenu one = appletsMpMenuService.getOne(new LambdaQueryWrapper<AppletsMpMenu>().eq(AppletsMpMenu::getIsDelete, 0).orderByDesc(AppletsMpMenu::getCreateTime));
        try {
            WxMenu wxMenu=new WxMenu();
            List<WxMenuButton> wxMenuButtons = beanMapper.mapAsList(dto.getButtons(), WxMenuButton.class);
            wxMenu.setButtons(wxMenuButtons);
            wxMpService.getMenuService().menuCreate(wxMenu);
            saveMenu(dto);
            log.info("创建菜单成功！");
            return Result.success(true);
        } catch (WxErrorException e) {
//            returnMenu(one.getMenuJson());
            log.warn("创建菜单失败");
            throw new ServiceException(e.getMessage());
        }
    }


    @RequestMapping(value = "/getMediaById",method = RequestMethod.GET)
    public Result<String> getMediaById(){
        wxMpService.getMaterialService();
        return Result.success("");
    }



    /**
     * 回滚菜单
     * @param menu
     */
    private void returnMenu(String menu) {
        try {
            List<WxMenuButton> wxMpMenus = JSONObject.parseArray(menu, WxMenuButton.class);
            WxMenu wxMenu=new WxMenu();
            wxMenu.setButtons(wxMpMenus);
            wxMpService.getMenuService().menuCreate(wxMenu);
            log.info("回滚菜单！");
        } catch (WxErrorException e) {
            log.error(e.getMessage());
            try {
                Thread.sleep(100);
            } catch (InterruptedException e1) {
                e1.printStackTrace();
            }
            returnMenu(menu);
        }
    }


    private void saveMenu(MpMenuDto dto){
        //保存db
        LocalDateTime now=LocalDateTime.now();
        AppletsMpMenu appletsMpMenu=new AppletsMpMenu();
        appletsMpMenu.setId(GenerationIdUtil.getId());
        appletsMpMenu.setCreateBy(Integer.parseInt(dto.getCreateBy()));
        appletsMpMenu.setUpdateBy(Integer.parseInt(dto.getCreateBy()));
        appletsMpMenu.setCreateTime(now);
        appletsMpMenu.setUpdateTime(now);
        appletsMpMenu.setIsDelete(0);
        appletsMpMenu.setMenuJson(JSON.toJSONString(dto.getButtons()));
        appletsMpMenu.setTextContent(CollectionUtils.isEmpty(dto.getTextContents())?null:JSON.toJSONString(dto.getTextContents()));
        appletsMpMenu.setMediaContent(CollectionUtils.isEmpty(dto.getMediaContents())?null:JSON.toJSONString(dto.getMediaContents()));
        log.info("保存菜单入参：{}", JSON.toJSONString(appletsMpMenu));
        appletsMpMenuService.save(appletsMpMenu);
        log.info("保存菜单完毕");
    }


}
