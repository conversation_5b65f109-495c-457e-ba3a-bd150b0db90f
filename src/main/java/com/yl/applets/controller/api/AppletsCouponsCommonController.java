package com.yl.applets.controller.api;

import com.yl.applets.service.coupons.AppletsCouponsCommonService;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-10-30 15:54
 * @Version 1.0
 */
@RestController
@Slf4j
@RequestMapping("/common/coupons")
@Api(value = "通用优惠券领取-单券限制", tags = "通用优惠券领取-单券限制")
public class AppletsCouponsCommonController {

    @Autowired
    private AppletsCouponsCommonService appletsCouponsCommonService;

    @PostMapping("/bindings")
    public Result<Boolean> bindCouponsById(@RequestParam("type") Integer type) {
        return Result.success(appletsCouponsCommonService.getCoupons(type));
    }

}
