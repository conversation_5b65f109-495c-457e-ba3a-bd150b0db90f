package com.yl.applets.controller;

import com.yl.applets.dto.AppletsLoginLogDTO;
import com.yl.applets.service.AppletsLoginLogService;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2022/10/24 16:24
 * @Version 1.0
 */
@Api(value = "保存小程序登陆日志", tags = "保存小程序登陆日志")
@RestController
@Slf4j
@RequestMapping("/login/log")
public class AppletsLoginLogInfoController extends AppBaseController {

    @Autowired
    private AppletsLoginLogService appletsLoginLogService;

    @PostMapping("/save")
    @ApiOperation(value = "保存日志", notes = "保存日志")
    public Result<Boolean> saveLoginLog(@RequestBody AppletsLoginLogDTO dto) {
        return success(appletsLoginLogService.saveLoginLog(dto, getUser()));
    }

}
