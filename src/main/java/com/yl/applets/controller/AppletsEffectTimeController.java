package com.yl.applets.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yl.applets.dto.AppletsEffectQueryDTO;
import com.yl.applets.dto.AppletsEffectQueryIdDTO;
import com.yl.applets.feign.CustomerPlatformNetworkFeignClient;
import com.yl.applets.feign.EffectDaysApiFeignClient;
import com.yl.applets.vo.BasicAreaVO;
import com.yl.applets.vo.GetProxyAndfranchiseeVO;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 云路科技有限公司 版权所有 Copyright 2023<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2024-07-22 10:52
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/effect/day")
public class AppletsEffectTimeController {

    @Autowired
    private EffectDaysApiFeignClient effectDaysApiFeignClient;

    @Autowired
    private CustomerPlatformNetworkFeignClient customerPlatformNetworkFeignClient;

    @PostMapping("/query")
    public Result<String> effectTime(@RequestBody AppletsEffectQueryIdDTO dto) {
        try {
            log.info("小程序查询时效:{}", JSON.toJSONString(dto));
            List<Long> networkIds = Lists.newArrayList(dto.getInFromAreaId(), dto.getInFromCityId(), dto.getInFromProviderId(), dto.getOutToAreaId(), dto.getOutToCityId(), dto.getOutToProviderId());
            Result<List<BasicAreaVO>> result = customerPlatformNetworkFeignClient.getAreaByAreaIds(networkIds);
            log.info("查询基础资料返回省市区:{}", JSON.toJSONString(result));

            List<BasicAreaVO> voList = result.result();
            Map<Long, String> collect = voList.stream()
                    .collect(Collectors.toMap(
                            vo -> Long.valueOf(vo.getId()), //转换 int 到 Long
                            BasicAreaVO::getCode
                    ));

            AppletsEffectQueryDTO queryDTO = new AppletsEffectQueryDTO();
            queryDTO.setInFromAreaCode(Optional.ofNullable(collect.get(dto.getInFromAreaId())).orElse(""));
            queryDTO.setInFromCityCode(Optional.ofNullable(collect.get(dto.getInFromCityId())).orElse(""));
            queryDTO.setInFromProviderCode(Optional.ofNullable(collect.get(dto.getInFromProviderId())).orElse(""));
            queryDTO.setOutToAreaCode(Optional.ofNullable(collect.get(dto.getOutToAreaId())).orElse(""));
            queryDTO.setOutToCityCode(Optional.ofNullable(collect.get(dto.getOutToCityId())).orElse(""));
            queryDTO.setOutToProviderCode(Optional.ofNullable(collect.get(dto.getOutToProviderId())).orElse(""));
            queryDTO.setTime(dto.getTime());
            log.info("小程序查询时效:{}", JSON.toJSONString(queryDTO));
            Result<String> effectTime = effectDaysApiFeignClient.effectTime(queryDTO);
            log.info("小程序查询时效返回:{}", JSON.toJSONString(effectTime));
            return Result.success(effectTime.result());
        } catch (Exception e) {
            log.error("小程序查询时效失败:{}", e);
        }

        return Result.success(null);
    }
}
