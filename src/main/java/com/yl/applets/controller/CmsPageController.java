package com.yl.applets.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yl.applets.dto.CmsPageStatisticsDto;
import com.yl.applets.feign.CmsPageStatisticFeign;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.utils.JWTUtils;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.annotation.log.operation.ExcludeInterceptor;
import com.yl.common.base.model.vo.Result;
import com.yl.redis.util.RedisUtil;
import io.jsonwebtoken.Claims;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Objects;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2024-03-04
 */
@RestController
@RequestMapping("/cms/page")
@Slf4j
public class CmsPageController  extends AppBaseController{


    @Autowired
    private CmsPageStatisticFeign cmsPageStatisticFeign;




    /**
     * 配置页预览
     * @return
     */
    @GetMapping(value = "/detail")
    @ApiOperation(value = "配置页预览", notes = "配置页预览")
    @ExcludeInterceptor
    public Result detail(@RequestParam("pageId") String pageId, @RequestParam("previewType") String previewType) {
        return cmsPageStatisticFeign.detail(pageId, previewType);
    }


    @PostMapping("/statistics")
    @ExcludeInterceptor
    public Result<Boolean> statistics(@RequestBody @Valid CmsPageStatisticsDto dto){
        log.info("cms配置页埋点,入参:{}", JSON.toJSONString(dto));
        String userId= getUserId();
        dto.setUserId(userId);
        Result<Boolean> statistics = cmsPageStatisticFeign.statistics(dto);
        log.info("cms埋点,调用接口入参==>{},出参==>{}",JSON.toJSONString(dto),JSON.toJSONString(statistics));
        return Result.success(Boolean.TRUE);

    }


    public String getUserId(){
        String token = request.getHeader("authToken");
        if (StringUtils.isEmpty(token)) {
            log.info("没有authToken");
            return null;
        }
        Claims claim = JWTUtils.getClaim(token);
        if(claim == null){
            log.info("JWTUtils未解析出结果");
            return null;
        }
        Integer userId = null;
        try {
            userId = JWTUtils.getUserId(token);
        } catch (Exception e) {
           log.info("JWTUtils.getUserId异常==>{}",e);
           return null;
        }
        if(userId==null){
            log.info("JWTUtils.getUserId为空");
            return null;
        }
        Object obj = RedisUtil.get(BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(userId.toString()));
        if (obj == null) {
            log.info("redis数据为空");
            return null;
        }
        WxUserVo loginVO = JSONObject.parseObject(obj.toString(), WxUserVo.class);
        if (loginVO == null) {
            log.info("redis数据转换USER为空");
            return null;
        }
        if(!token.equals(loginVO.getToken())){
            log.info("redis数据和当前token不一致");
            return null;
        }
        if(!Objects.nonNull(loginVO.getNumberId())){
            log.info("当前用户numberId为空");
            return null;
        }
       return loginVO.getNumberId()+"";
    }

}
