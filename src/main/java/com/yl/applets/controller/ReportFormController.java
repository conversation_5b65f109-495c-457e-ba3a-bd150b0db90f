package com.yl.applets.controller;

import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.VipFormRecordDTO;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.vo.WxUserVo;
import com.yl.redis.util.RedisUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:  小程序埋点类,报表
 * @date 2022-09-20
 */

@RestController
@RequestMapping("/report/form")
@Slf4j
public class ReportFormController extends AppBaseController{


    /**
     * vip流量报表数据采集
     *
     * @param dto
     */
    @PostMapping("/uploadVipRecord")
    @ApiOperation(value = "vip流量报表数据采集", notes = "vip流量报表数据采集")
    public void uploadVipRecord(@RequestBody VipFormRecordDTO dto) {
        log.info("入参==>{}", JSON.toJSONString(dto));
        //保存到redis
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate now = LocalDate.now();
        String day = dateTimeFormatter.format(now);
        Integer type = dto.getType();
        Integer actType = dto.getActionType();
        if(actType==null||type==null){
            log.info("参数为空,不统计==>actType：{}，type：{}",actType,type);
        }else {
            String mapKey = BcRedisKeyEnum.VIP_ACTION_COUNT_DAY.keyBuilder(day);
            String key= type+":"+actType;
            //类型,访问/点击
            RedisUtil.hIncrBy(mapKey,key,1);
            log.info("vip流量报表数据采集,保存成功");
        }
    }


    /**
     * vip流量报表数据采集(pv/uv)
     *
     */
    @PostMapping("/uploadVipRecordPv")
    @ApiOperation(value = "vip流量报表数据采集(pv/uv)", notes = "vip流量报表数据采集(pv/uv)")
    public void uploadVipRecordPv() {
        WxUserVo user = getUser();
        //保存到redis
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate now = LocalDate.now();
        String day = dateTimeFormatter.format(now);
        String[] split = day.split("-");
        String PVKEY = BcRedisKeyEnum.VIP_COUNT_PV_MONTH.keyBuilder(split[1]);
        log.info("pv-key==>{}",PVKEY);
        RedisUtil.incrBy(PVKEY,1);
        if(RedisUtil.getExpire(PVKEY)==-1){
            RedisUtil.expire(PVKEY,getTimeNextMonth());//到下个月
        }
        String UVKEY = BcRedisKeyEnum.VIP_COUNT_UV_MONTH.keyBuilder(split[1]);
        RedisUtil.hIncrBy(UVKEY,user.getId()+"",1);
        log.info("uv-key==>{}",UVKEY);
        if(RedisUtil.getExpire(UVKEY)==-1){
            RedisUtil.expire(UVKEY,getTimeNextMonth());//到下个月
        }
        log.info("vip流量报表数据采集(pv/uv),保存成功");
    }



    /**
     * vip流量报表数据采集
     *
     * @param dto
     */
    @PostMapping("/uploadMemberRecord")
    @ApiOperation(value = "会员流量报表数据采集", notes = "会员流量报表数据采集")
    public void uploadMemberRecord(@RequestBody VipFormRecordDTO dto) {
        //保存到redis
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        log.info("入参==>{}", JSON.toJSONString(dto));
        LocalDate now = LocalDate.now();
        String day = dateTimeFormatter.format(now);
        Integer type = dto.getType();
        Integer actType = dto.getActionType();
        if(actType==null||type==null){
            log.info("参数为空,不统计==>actType：{}，type：{}",actType,type);
        }else {
            String mapKey = BcRedisKeyEnum.MEMBER_ACTION_COUNT_DAY.keyBuilder(day);
            String key= type+":"+actType;
            //类型,访问/点击
            RedisUtil.hIncrBy(mapKey,key,1);
            log.info("会员流量报表数据采集,保存成功");
        }
    }


    /**
     * vip流量报表数据采集(pv/uv)
     *
     */
    @PostMapping("/uploadMemberRecordPv")
    @ApiOperation(value = "会员流量报表数据采集(pv/uv)", notes = "会员流量报表数据采集(pv/uv)")
    public void uploadIndexAccessRecord() {
        WxUserVo user = getUser();
        //保存到redis
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate now = LocalDate.now();
        String day = dateTimeFormatter.format(now);
        String[] split = day.split("-");
        String PVKEY = BcRedisKeyEnum.MEMBER_COUNT_PV_MONTH.keyBuilder(split[1]);
        log.info("pv-key==>{}",PVKEY);
        RedisUtil.incrBy(PVKEY,1);
        if(RedisUtil.getExpire(PVKEY)==-1){
            RedisUtil.expire(PVKEY,getTimeNextMonth());//到下个月
        }
        String UVKEY = BcRedisKeyEnum.MEMBER_COUNT_UV_MONTH.keyBuilder(split[1]);
        log.info("uv-key==>{}",UVKEY);
        RedisUtil.hIncrBy(UVKEY,user.getId()+"",1);
        if(RedisUtil.getExpire(UVKEY)==-1){
            RedisUtil.expire(UVKEY,getTimeNextMonth());//到下个月
        }
        log.info("会员流量报表数据采集pv/uv,保存成功");
    }


    private long getTimeNextMonth(){
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime localDateTime = now.plusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).plusDays(1);
        long seconds = Duration.between(now, localDateTime).getSeconds();
        log.info("失效时间==>{}",seconds);
        return seconds;
    }


}
