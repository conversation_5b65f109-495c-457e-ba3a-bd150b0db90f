package com.yl.applets.controller;

import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.OrderMarkingQueryDto;
import com.yl.applets.dto.OrderMarkingSignDto;
import com.yl.applets.dto.OrderUserQueryDto;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.vo.OrderMarkingVo;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description: 订单打标
 * @date 2024-03-09
 */
@RestController
@RequestMapping("/order/mark")
@Slf4j
public class MarkingOrderController extends AppBaseController {


    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;



    @GetMapping("/user/query")
    public Result<Boolean> orderUserQuery(@RequestParam("waybillNo")String waybillNo) {
        log.info("判断是否自己的单子,前端入参==>{}",waybillNo);
        WxUserVo user = getUser();
        OrderUserQueryDto dto=new OrderUserQueryDto();
        dto.setPhone(user.getMobile());
        dto.setMemberId(user.getNumberId()+"");
        dto.setWaybillId(waybillNo);
        Result<Boolean> booleanResult = channelApiFeignClient.orderUserQuery(dto);
        log.info("判断是否自己的单子,中台入参==>{} 出参==>{}",JSON.toJSONString(dto),JSON.toJSONString(booleanResult));
        return Result.success(booleanResult.result());
    }


    /**
     * 获取订单标识
     *
     * @return
     */
    @GetMapping("/resource")
    public Result<List<OrderMarkingVo>> getResource() {
        Result<List<OrderMarkingVo>> resource = channelApiFeignClient.getResource();
        List<OrderMarkingVo> result = resource.result();
        result.forEach(o->o.setStatus(false));
        log.info("调用中台,获取订单标识==>{}",JSON.toJSONString(resource));
        return Result.success(resource.result());
    }


    @PostMapping("/sign")
    public Result<Boolean> updateMarking(@RequestBody @Valid OrderMarkingSignDto dto)  {
        log.info("订单打标接口,前端入参==>{}", JSON.toJSONString(dto));
        WxUserVo user = getUser();
        dto.setMemberId(user.getNumberId()+"");
        dto.setPhone(user.getMobile());
        Result<Boolean> booleanResult = channelApiFeignClient.updateMarking(dto);
        log.info("调用中台,订单打标入参==>{},出参==>{}",JSON.toJSONString(dto),JSON.toJSONString(booleanResult));
        return Result.success(booleanResult.result());
    }


    /**
     * 1.先查订单数据
     * 2.再查VIP数据
     * 3.VIP存在,以VIP为准
     * 4.VIP不存在,以订单为准
     * @param queryDto
     * @return
     */
    @PostMapping("/query")
    public Result<List<OrderMarkingVo>> queryMarking(@RequestBody OrderMarkingQueryDto queryDto){
        log.info("订单打标查询接口,前端入参==>{}", JSON.toJSONString(queryDto));
        WxUserVo user = getUser();
        queryDto.setMemberId(user.getNumberId()+"");
        queryDto.setPhone(user.getMobile());
        Result<List<OrderMarkingVo>> booleanResult = channelApiFeignClient.queryMarking(queryDto);
        log.info("调用中台,查询订单标识入参==>{},出参==>{}",JSON.toJSONString(queryDto),JSON.toJSONString(booleanResult));
        return Result.success(booleanResult.result());
    }


}
