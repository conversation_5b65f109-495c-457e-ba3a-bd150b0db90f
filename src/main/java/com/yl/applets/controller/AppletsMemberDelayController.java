package com.yl.applets.controller;

import com.yl.applets.service.AppletsMemberDelayService;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2023-02-09 16:11
 * @Version 1.0
 */
@Api(value = "小程序会员延时提示", tags = "小程序会员延时提示")
@RestController
@Slf4j
@RequestMapping("/member/extension")
public class AppletsMemberDelayController extends AppBaseController {


    @Autowired
    private AppletsMemberDelayService appletsMemberDelayService;

    @PostMapping("/info")
    public Result<Boolean> qryMemberIsExtension() {
        return success(appletsMemberDelayService.qryMemberIsExtension(getUser()));
    }

}
