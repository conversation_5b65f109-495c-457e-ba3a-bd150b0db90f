package com.yl.applets.controller.basic_data;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.yl.applets.controller.AppBaseController;
import com.yl.applets.dto.ForwardRequest;
import com.yl.applets.dto.NearbyOutletsDTO;
import com.yl.applets.dto.lmdm.SysStaffDTO;
import com.yl.applets.entity.CommonlyUsedNetwork;
import com.yl.applets.entity.lmdm.SysNetwork;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.CustomerPlatformNetworkFeignClient;
import com.yl.applets.feign.OldLmdmFeignClient;
import com.yl.applets.service.IAppNetworkService;
import com.yl.applets.service.ICommonlyUsedNetworkService;
import com.yl.applets.service.IWxUserService;
import com.yl.applets.vo.EsSearchResult;
import com.yl.applets.vo.lmdm.*;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.CollectionUtils;
import com.yl.common.base.util.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019/6/19 17:05
 */
@Api(value = "app网点资料", tags = {"网点资料"})
@RestController
@RequestMapping("/network")
@Slf4j
public class AppNetworkController extends AppBaseController {

    @Autowired
    private IAppNetworkService appNetworkService;

    @Autowired
    private ICommonlyUsedNetworkService commonlyUsedNetworkService;

    @Autowired
    private IWxUserService wxUserService;

    static final String NEAR_SIGN = "jks21a@dh@SFa7s61HFR";

    static final String STAFF_MD5_KEY = "YoH0DEY+9GFyoXb";

    @Autowired
    private OldLmdmFeignClient sysStaffFeignClient;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Autowired
    private  CustomerPlatformNetworkFeignClient networkFeignClient;


//    @GetMapping(value = "/list")
//    @ApiOperation(value = "A网点资料列表", notes = "网点资料列表")
//    public Result<List<AppNetworkVO>> getAppList() {
//        List<AppNetworkVO> result = appNetworkService.getAppList();
//        return success(result);
//    }

    /**
     *
     * @param nearbyOutletsDTO
     * @return
     */
    @GetMapping(value = "/getNearbyOutlets")
    @ApiOperation(value = "获取附近的网点信息", notes = "获取附近的网点信息")
    public  Result<EsSearchResult>  getNearbyOutlets(@Valid NearbyOutletsDTO nearbyOutletsDTO){
        EsSearchResult esSearchResult = appNetworkService.getNearbyOutlets(nearbyOutletsDTO);
        return  success(esSearchResult);

    }


    /**
     * 免登陆
     * @param nearbyOutletsDTO
     * @return
     */
    @GetMapping(value = "/v2/getNearbyOutlets")
    @ApiOperation(value = "获取附近的网点信息", notes = "获取附近的网点信息")
    public  Result<EsSearchResult>  getNearbyOutletsV2(@Valid NearbyOutletsDTO nearbyOutletsDTO){
        String requestId = UUID.randomUUID().toString().replace("-", "");
        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(nearbyOutletsDTO.getTimes()+NEAR_SIGN));
        log.info("请求id==>{}，后端MD5加密之后{}",requestId,sign1);
        if(!sign1.equals(nearbyOutletsDTO.getSign())){
            log.info("请求id{},验签失败！",requestId);
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
        if(StringUtils.isBlank(nearbyOutletsDTO.getLatitude())||StringUtils.isBlank(nearbyOutletsDTO.getLongitude())){
            log.info("附近网点查询,经纬度不能为空");
            return success(new EsSearchResult());
        }
        EsSearchResult esSearchResult = appNetworkService.getNearbyOutlets(nearbyOutletsDTO);
        return  success(esSearchResult);

    }

    @GetMapping(value = "/commonlyUsedNetwork")
    @ApiOperation(value = "获取常用网点", notes = "获取常用网点")
    public  Result<IPage<CommonlyUsedNetwork>>  commonlyUsedNetwork(String longitude , String latitude){
            //根据numberId查询ids
            List<Long> ids = wxUserService.getIdsByIdAndNumberId(getUser().getNumberId());
           return success(commonlyUsedNetworkService.getPage(longitude,latitude,ids));
    }


    /**
     * 获取网点信息
     * @param networkId
     * @return
     */
    @GetMapping(value = "/getNetwork")
    @ApiOperation(value = "网点信息", notes = "网点信息")
    public Result<SysNetworkSafeVO> getNetwork(@RequestParam("networkId") Integer networkId) {
        return success(appNetworkService.getDetail(networkId));
    }


    /**
     * 获取业务员电话
     * @param staffCode
     * @return
     */
    /*@GetMapping(value = "/getStaffMobile")
    @ApiOperation(value = "获取业务员手机号", notes = "获取业务员手机号")
    public Result<String> getNetwork(@RequestParam("staffCode") String staffCode) {
        SysStaffDTO sysStaffDTO = new SysStaffDTO();
        sysStaffDTO.setCode(staffCode);
        String mobile = "";
//        SysStaffVO result = sysStaffFeignClient.getStaffDetail(sysStaffDTO).result();
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        ForwardRequest request=new ForwardRequest();
        request.setRequestId(requestId);
        request.setRequestUri("/lmdmapi/sysStaff/detail");
        request.setBody(JSON.toJSONString(sysStaffDTO));
        log.info("请求id==>{}接口入参==>{}",requestId,JSON.toJSONString(request));
        Result<?> forward = channelApiFeignClient.forward(request);
        log.info("请求id==>{}接口返回==>{}",requestId,JSON.toJSONString(forward));
        Object resultO = forward.result();
        SysStaffVO result = JSON.parseObject(JSON.toJSONString(resultO), SysStaffVO.class);
        if(result!=null){
            mobile = result.getMobile();
        }
        return success(mobile);
    }
*/

    /**
     * 获取业务员信息
     * @param staffCode
     * @return
     */
    @GetMapping(value = "/getStaffInfo")
    @ApiOperation(value = "获取业务员信息", notes = "获取业务员信息")
    public Result<SysStaffVO> getStaffInfo(@RequestParam("staffCode") String staffCode, @RequestParam("sign") String sign, @RequestParam("timestamp") String timestamp) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        String ciphertext = DigestUtils.md5Hex(DigestUtils.md5Hex(staffCode + timestamp + STAFF_MD5_KEY));
        log.info("请求id{}，后端MD5加密之后{}", requestId, ciphertext);
        if(!ciphertext.equals(sign)){
            log.info("请求id{},验签失败！",sign);
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }

        SysStaffDTO sysStaffDTO = new SysStaffDTO();
        sysStaffDTO.setCode(staffCode);
//        SysStaffVO result = sysStaffFeignClient.getStaffDetail(sysStaffDTO).result();
        ForwardRequest request=new ForwardRequest();
        request.setRequestId(requestId);
        request.setRequestUri("/lmdmapi/sysStaff/detail");
        request.setBody(JSON.toJSONString(sysStaffDTO));
        log.info("请求id==>{}接口入参==>{}",requestId,JSON.toJSONString(request));
        Result<?> forward = channelApiFeignClient.forward(request);
        log.info("请求id==>{}接口返回==>{}",requestId,JSON.toJSONString(forward));
        Object resultO = forward.result();
        SysStaffVO result  = JSON.parseObject(JSON.toJSONString(resultO), SysStaffVO.class);
        //只返回name和状态
        SysStaffVO newStaff=new SysStaffVO();
        newStaff.setName(result.getName());
        newStaff.setIsEnable(result.getIsEnable());
        return success(newStaff);
    }



    /**
     * 获取业务员信息
     * @param staffCode
     * @return
     */
    @GetMapping(value = "/getStaffInfo/new")
    @ApiOperation(value = "获取业务员信息-新版", notes = "获取业务员信息-新版")
    public Result<SysStaffNetWorkVO> getStaffInfoNew(@RequestParam("staffCode") String staffCode, @RequestParam("sign") String sign, @RequestParam("timestamp") String timestamp) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");

        String ciphertext = DigestUtils.md5Hex(DigestUtils.md5Hex(staffCode + timestamp + STAFF_MD5_KEY));
        log.info("请求id{}，后端MD5加密之后{}", requestId, ciphertext);
        if(!ciphertext.equals(sign)){
            log.info("请求id{},验签失败！",sign);
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }
        SysStaffDTO sysStaffDTO = new SysStaffDTO();
        sysStaffDTO.setCode(staffCode);
//        SysStaffVO result = sysStaffFeignClient.getStaffDetail(sysStaffDTO).result();
        ForwardRequest request=new ForwardRequest();
        request.setRequestId(requestId);
        request.setRequestUri("/lmdmapi/sysStaff/detail");
        request.setBody(JSON.toJSONString(sysStaffDTO));
        log.info("请求id==>{}接口入参==>{}",requestId,JSON.toJSONString(request));
        Result<?> forward = channelApiFeignClient.forward(request);
        log.info("请求id==>{}接口返回==>{}",requestId,JSON.toJSONString(forward));
        Object resultO = forward.result();
        SysStaffVO result  = JSON.parseObject(JSON.toJSONString(resultO), SysStaffVO.class);
        //只返回name和状态
        SysStaffNetWorkVO newStaff=new SysStaffNetWorkVO();
        newStaff.setName(result.getName());
        newStaff.setStaffNo(result.getCode());
        newStaff.setIsEnable(result.getIsEnable());
        Integer networkId = result.getNetworkId();
        if(!Objects.isNull(networkId)){
            List<SysNetwork> sysNetworkList = networkFeignClient.getByIds(Lists.newArrayList(networkId)).result();
            if(CollectionUtils.isNotEmpty(sysNetworkList)){
                SysNetwork sysNetwork=sysNetworkList.get(0);
                newStaff.setProviderDesc(sysNetwork.getProviderDesc());
                newStaff.setCityDesc(sysNetwork.getCityDesc());
                newStaff.setAreaDesc(sysNetwork.getAreaDesc());

                newStaff.setProviderId(sysNetwork.getProviderId());
                newStaff.setCityId(sysNetwork.getCityId());
                newStaff.setAreaId(sysNetwork.getAreaId());
            }

        }
        return success(newStaff);
    }

}
