package com.yl.applets.controller.basic_data;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yl.applets.constant.AppCacheConstant;
import com.yl.applets.dto.SysAreaOnlineConfigQueryDTO;
import com.yl.applets.entity.lmdm.SysArea;
import com.yl.applets.enums.SysAreaOnlineConfigTypeEnum;
import com.yl.applets.feign.EdiAreaOnlineConfigSendFeignClient;
import com.yl.applets.feign.OldNetworkFeighClient;
import com.yl.applets.service.IAppBaseDataService;
import com.yl.applets.vo.lmdm.AppAreaVO;
import com.yl.applets.vo.lmdm.AppBaseDataVO;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.controller.BaseController;
import com.yl.common.base.model.vo.Result;
import com.yl.redis.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019/6/19 17:05
 */
@Api(value = "app基础数据", tags = {"app基础数据"})
@RestController
@RequestMapping("/baseData")
@Slf4j
public class AppBaseDataController extends BaseController {

    @Autowired
    private IAppBaseDataService appBaseDataService;

    @Autowired
    private OldNetworkFeighClient appletsCustomerFeignClient;

    @Autowired
    private EdiAreaOnlineConfigSendFeignClient ediAreaOnlineConfigSendFeignClient;


    @Value("${district.cache.time:3600}")
    private Long timeOut = 60*60L;

    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Value("#{'${filter.province.id:330000}'.split(',')}")
    private List<String> provinceIdList;


    @Value("#{'${filter.province.id.gz:520000}'.split(',')}")
    private List<String> gzProvinceIdList;



    @GetMapping(value = "/list")
    @ApiOperation(value = "基础数据列表", notes = "基础数据列表")
    public Result<AppBaseDataVO> getAppList() {
        return success(appBaseDataService.getAppList());
    }


    /**
     * 获取禁寄限类目是否符合要求
     * @param contents
     * @return
     */
    @GetMapping("/getSysForbiddenInfo")
    @ApiOperation(value = "获取禁寄限类目是否符合要求", notes = "获取禁寄限类目是否符合要求")
    Result<Boolean> getSysForbiddenInfo(@RequestParam String contents) throws UnsupportedEncodingException {
        return appletsCustomerFeignClient.getSysForbiddenInfo(URLDecoder.decode(contents, "UTF-8"));
    }



    @GetMapping("/findDistrict")
    @ApiOperation(value = "省市区", notes = "省市区")
    Result<List<AppAreaVO>> findDistrict(@RequestParam(value = "parentId") Integer parentId,@RequestParam("type")Integer type){
        String requestId = UUID.randomUUID().toString().replace("-", "");
        String key = AppCacheConstant.WX_DISTRICT_LIST;
        List<AppAreaVO> appAreaVOS= Lists.newArrayList();
        Object listObject = RedisUtil.get(key);
        if (listObject != null) {
            appAreaVOS = JSON.parseArray(listObject.toString(), AppAreaVO.class);
            log.info("请求id==>{}查询省市区,取缓存,数据量size==>{}",requestId,JSON.toJSONString(appAreaVOS.size()));
        }else {
            SysAreaOnlineConfigQueryDTO sysAreaOnlineConfigQueryDTO=new SysAreaOnlineConfigQueryDTO();
            sysAreaOnlineConfigQueryDTO.setPlateformType(SysAreaOnlineConfigTypeEnum.TYPE_SMALL_ROUTINE.getType());
            log.info("请求id==>{}查询省市区,远程入参==>{}",requestId, JSON.toJSONString(sysAreaOnlineConfigQueryDTO));
            List<SysArea> sysAreas = ediAreaOnlineConfigSendFeignClient.findDistrict(sysAreaOnlineConfigQueryDTO).result();
            log.info("请求id==>{}查询省市区,远程出参==>{}",requestId, JSON.toJSONString(sysAreas));
            appAreaVOS = orikaBeanMapper.mapAsList(sysAreas,AppAreaVO.class);
            com.yl.redis.util.RedisUtil.setEx(key, JSON.toJSONString(appAreaVOS), timeOut);
        }
        if(appAreaVOS.isEmpty()){
            log.info("请求id==>{}查询省市区,未查询到数据{}",requestId);
            return Result.success(Lists.newArrayList());
        }else {
            //过滤
            List<AppAreaVO> collect = appAreaVOS.stream().filter(a -> type.equals(a.getType()) && parentId.equals(a.getParentId())).collect(Collectors.toList());
            log.info("请求id==>{}查询省市区,过滤之后出参==>{}",requestId, JSON.toJSONString(collect));
            return Result.success(collect);
        }
    }

    @GetMapping("/findDistrict/filter")
    @ApiOperation(value = "省市区", notes = "省市区")
    Result<List<AppAreaVO>> findDistrictFilter(@RequestParam(value = "parentId") Integer parentId,@RequestParam("type")Integer type,@RequestParam(value = "filter",required = false)Integer filter){
        String requestId = UUID.randomUUID().toString().replace("-", "");
        String key = AppCacheConstant.WX_DISTRICT_LIST_FILTER;
        List<AppAreaVO> appAreaVOS= Lists.newArrayList();
        Object listObject = RedisUtil.get(key);
        if (listObject != null) {
            appAreaVOS = JSON.parseArray(listObject.toString(), AppAreaVO.class);
            log.info("请求id==>{}过滤查询省市区,取缓存,数据量size==>{}",requestId,JSON.toJSONString(appAreaVOS.size()));
        }else {
            SysAreaOnlineConfigQueryDTO sysAreaOnlineConfigQueryDTO=new SysAreaOnlineConfigQueryDTO();
            sysAreaOnlineConfigQueryDTO.setPlateformType(SysAreaOnlineConfigTypeEnum.TYPE_SMALL_ROUTINE.getType());
            log.info("请求id==>{}过滤查询省市区,远程入参==>{}",requestId, JSON.toJSONString(sysAreaOnlineConfigQueryDTO));
            List<SysArea> sysAreas = ediAreaOnlineConfigSendFeignClient.findDistrict(sysAreaOnlineConfigQueryDTO).result();
            log.info("请求id==>{}过滤查询省市区,远程出参==>{}",requestId, JSON.toJSONString(sysAreas));
            appAreaVOS = orikaBeanMapper.mapAsList(sysAreas,AppAreaVO.class);

            log.info("请求id==>{}过滤查询省市区,apollo配置省==>{}",requestId, JSON.toJSONString(provinceIdList));
            com.yl.redis.util.RedisUtil.setEx(key, JSON.toJSONString(appAreaVOS), timeOut);
        }
        if(appAreaVOS.isEmpty()){
            log.info("请求id==>{}过滤查询省市区,未查询到数据{}",requestId);
            return Result.success(Lists.newArrayList());
        }else {
            if (Objects.equals(2, type)){
                if (Objects.equals(1, filter)){
                    //过滤特定省市区
                    appAreaVOS = appAreaVOS.stream().filter(a -> gzProvinceIdList.contains(a.getId().toString())).collect(Collectors.toList());
                }else {
                    //过滤特定省市区
                    appAreaVOS = appAreaVOS.stream().filter(a -> provinceIdList.contains(a.getId().toString())).collect(Collectors.toList());
                }
            }
            //过滤
            List<AppAreaVO> collect = appAreaVOS.stream().filter(a -> type.equals(a.getType()) && parentId.equals(a.getParentId())).collect(Collectors.toList());
            log.info("请求id==>{}过滤查询省市区,过滤之后出参==>{}",requestId, JSON.toJSONString(collect));
            return Result.success(collect);
        }
    }







}
