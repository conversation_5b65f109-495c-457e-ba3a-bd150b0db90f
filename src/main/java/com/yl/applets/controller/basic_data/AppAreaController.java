package com.yl.applets.controller.basic_data;


import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.AppAreaDTO;
import com.yl.applets.dto.ForwardRequest;
import com.yl.applets.dto.SysAreaBookingDto;
import com.yl.applets.dto.lmdm.SysSettlementDestinationBaseQueryDTO;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.OldLmdmFeignClient;
import com.yl.applets.feign.OldNetworkFeighClient;
import com.yl.applets.service.IAppAreaService;
import com.yl.applets.vo.AreaSearchVO;
import com.yl.applets.vo.SysAreaBookingVo;
import com.yl.applets.vo.lmdm.AppAreaVO;
import com.yl.applets.vo.lmdm.SysSettlementDestinationVO;
import com.yl.common.base.controller.BaseController;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019/6/19 17:05
 */
@Api(value = "app行政区域", tags = {"行政区域"})
@RestController
@RequestMapping("/area")
@Slf4j
public class AppAreaController extends BaseController {

    @Autowired
    private IAppAreaService appAreaService;


    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Autowired
    private OldLmdmFeignClient sysSettlementDestinationFeignClient;

    @Autowired
    private OldNetworkFeighClient sysAreaBookingFeignClient;

    static final String CHILD_SIGN = "lkslk2L17@1hJhfr&@!135G6L";

    static final String AREA_SIGN = "lkPaH@s21ag316Hl135G6L";

    @GetMapping(value = "/list")
    @ApiOperation(value = "App获取行政区域列表", notes = "App获取行政区域列表")
    public Result<List<AppAreaVO>> getAppList() {
        List<AppAreaVO> list = appAreaService.getAppList();
        return Result.success(list);
    }

    /**
     * 功能描述:
     * app收派件区域
     * @param
     * @return:com.yl.common.base.model.vo.Result<java.util.List<com.yl.applets.vo.lmdm.AppAreaVO>>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-03-03 14:36
     */
    @PostMapping(value = "/v2/list")
    @ApiOperation(value = "App获取行政区域列表", notes = "App获取行政区域列表")
    public Result<List<AppAreaVO>> getAppListV2(@RequestBody AppAreaDTO appAreaDTO) {
        List<AppAreaVO> list = appAreaService.getAppListV2(appAreaDTO);
        return Result.success(list);
    }


    @GetMapping(value = "/listChild")
    @ApiOperation(value = "按层级获取行政区域列表", notes = "按层级获取行政区域列表")
    public Result<List<AppAreaVO>> listChild(@RequestParam("parentId") Integer parentId,@RequestParam("type") Integer type) {
        if(parentId == null||type==null){
            throw new BusinessException(ResultCodeEnum.PARAMS_IS_INVALID);
        }
        List<AppAreaVO> list = appAreaService.getAppList(parentId,type);
        return Result.success(list);
    }

    /**
     * 功能描述:
     * 按层级获取行政区域列表
     * @param appAreaDTO
     * @return:com.yl.common.base.model.vo.Result<java.util.List<com.yl.applets.vo.lmdm.AppAreaVO>>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-03-03 15:15
     */
    @PostMapping(value = "/v2/listChild")
    @ApiOperation(value = "按层级获取行政区域列表", notes = "按层级获取行政区域列表")
    public Result<List<AppAreaVO>> listChildV2(@RequestBody(required = false) AppAreaDTO appAreaDTO) {
        if(appAreaDTO==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        if(appAreaDTO.getParentId() == null||appAreaDTO.getType()==null||appAreaDTO.getType()==null){
            throw new BusinessException(ResultCodeEnum.PARAMS_IS_INVALID);
        }
        List<AppAreaVO> list = appAreaService.listChildV2(appAreaDTO);
        return Result.success(list);
    }

    /**
     * 功能描述:
     * 按层级获取行政区域列表,免登陆
     * @param appAreaDTO
     * @return:com.yl.common.base.model.vo.Result<java.util.List<com.yl.applets.vo.lmdm.AppAreaVO>>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-03-03 15:15
     */
    @PostMapping(value = "/v3/listChild")
    @ApiOperation(value = "按层级获取行政区域列表", notes = "按层级获取行政区域列表")
    public Result<List<AppAreaVO>> listChildV3(@RequestBody(required = false) AppAreaDTO appAreaDTO) {
        if(appAreaDTO==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        String requestId = UUID.randomUUID().toString().replace("-", "");
        if(appAreaDTO.getParentId() == null||appAreaDTO.getType()==null){
            throw new BusinessException(ResultCodeEnum.PARAMS_IS_INVALID);
        }
        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex( appAreaDTO.getType()+appAreaDTO.getTimes()+appAreaDTO.getParentId()+CHILD_SIGN));
        log.info("请求id==>{}，后端MD5加密之后{}",requestId,sign1);
        if(!sign1.equals(appAreaDTO.getSign())){
            log.info("请求id{},验签失败！",requestId);
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
        List<AppAreaVO> list = appAreaService.listChildV2(appAreaDTO);
        return Result.success(list);
    }


    @GetMapping(value = "/listQuery")
    @ApiOperation(value = "模糊查找获取行政区域列表", notes = "模糊查找获取行政区域列表")
    public Result<List<AreaSearchVO>> listQuery(@RequestParam("keyWord") String keyWord) {
        if(keyWord == null){
            throw new BusinessException(ResultCodeEnum.PARAMS_IS_INVALID);
        }
        List<AreaSearchVO> list = appAreaService.getAppList(keyWord);
        return Result.success(list);
    }

    /**
     * 功能描述:
     * 模糊查找获取行政区域列表
     * @param appAreaDTO
     * @return:com.yl.common.base.model.vo.Result<java.util.List<com.yl.applets.vo.AreaSearchVO>>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-03-03 15:26
     */
    @PostMapping(value = "/v2/listQuery")
    @ApiOperation(value = "模糊查找获取行政区域列表", notes = "模糊查找获取行政区域列表")
    public Result<List<AreaSearchVO>> listQueryV2(@RequestBody AppAreaDTO appAreaDTO) {
        if(appAreaDTO.getKeyWord() == null){
            throw new BusinessException(ResultCodeEnum.PARAMS_IS_INVALID);
        }
        List<AreaSearchVO> list = appAreaService.listQueryV2(appAreaDTO);
        return Result.success(list);
    }


    /**
     * 通过省市区匹配结算目的地
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/getSettlementDestinationByProvinceCityAreaId")
    @ApiOperation(value = "通过省市区匹配结算目的地", notes = "通过省市区匹配结算目的地")
    public Result<SysSettlementDestinationVO> getSettlementDestinationByProvinceCityAreaId(
            @RequestBody SysSettlementDestinationBaseQueryDTO dto) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("请求id==>{}查询目的结算地,入参==>{}",requestId, JSON.toJSONString(dto));
//        SysSettlementDestinationVO result = sysSettlementDestinationFeignClient.getSettlementDestinationByProvinceCityAreaId(dto).result();
        ForwardRequest requests=new ForwardRequest();
        requests.setRequestId(requestId);
        requests.setRequestUri("/lmdmapi/settlementDestination/getSettlementDestinationByProvinceCityAreaId");
        requests.setBody(JSON.toJSONString(dto));
        //调接口
        Result<?> forwards = channelApiFeignClient.forward(requests);
        log.info("请求id==>{}接口返回==>{}",requestId,JSON.toJSONString(forwards));
        Object results = forwards.result();
        SysSettlementDestinationVO result  = JSON.parseObject(JSON.toJSONString(results), SysSettlementDestinationVO.class);
        log.info("请求id==>{}查询目的结算地,出参==>{}",requestId, JSON.toJSONString(result));
        return success(result);
    }

    /**
     * 通过省市区匹配结算目的地
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/v2/getSettlementDestinationByProvinceCityAreaId")
    @ApiOperation(value = "通过省市区匹配结算目的地", notes = "通过省市区匹配结算目的地")
    public Result<SysSettlementDestinationVO> getSettlementDestinationByProvinceCityAreaIdV2(
            @RequestBody(required = false) SysSettlementDestinationBaseQueryDTO dto) {
        if(dto==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        String requestId = UUID.randomUUID().toString().replace("-", "");
        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex( dto.getProviderId()+""+dto.getCityId()+""+dto.getAreaId()+""+dto.getTimes()+AREA_SIGN));
        log.info("请求id{}，查询目的结算地,后端MD5加密之后{}",requestId,sign1);
        if(!sign1.equals(dto.getSign())){
            log.info("请求id{},查询目的结算地,验签失败！",requestId);
            throw new BusinessException(ResultCodeEnum.FAIL);
        }
        log.info("请求id==>{}查询目的结算地,入参==>{}",requestId, JSON.toJSONString(dto));

        ForwardRequest requests=new ForwardRequest();
        requests.setRequestId(requestId);
        requests.setRequestUri("/lmdmapi/settlementDestination/getSettlementDestinationByProvinceCityAreaId");
        requests.setBody(JSON.toJSONString(dto));
        //调接口
        Result<?> forwards = channelApiFeignClient.forward(requests);
        log.info("请求id==>{}接口返回==>{}",requestId,JSON.toJSONString(forwards));
        Object results = forwards.result();
        SysSettlementDestinationVO result  = JSON.parseObject(JSON.toJSONString(results), SysSettlementDestinationVO.class);

        log.info("请求id==>{}查询目的结算地,出参==>{}",requestId, JSON.toJSONString(result));
        return success(result);
    }

    /**
     * 通过省市区获取可预约时间
     * @param sysAreaBookingDto
     * @return
     */
    @PostMapping(value = "/pingTime")
    @ApiOperation(value = "通过省市区获取可预约时间", notes = "通过省市区获取可预约时间")
    public Result<SysAreaBookingVo> pingTime(@RequestBody @Validated SysAreaBookingDto sysAreaBookingDto){
        SysAreaBookingVo  sysAreaBookingVo=sysAreaBookingFeignClient.pickTime(sysAreaBookingDto).getData();
        if(sysAreaBookingVo != null ){
            sysAreaBookingVo.setNowTime(LocalDateTime.now());
            return Result.success(sysAreaBookingVo);
        }else{
            return Result.success(null);
        }
    }
}
