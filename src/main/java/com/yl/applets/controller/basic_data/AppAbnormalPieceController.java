package com.yl.applets.controller.basic_data;

import com.yl.applets.service.IAppAbnormalPieceService;
import com.yl.applets.vo.lmdm.AppAbnormalPieceVO;
import com.yl.common.base.controller.BaseController;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019/6/19 17:05
 */
@Api(value = "app问题件类型", tags = {"问题件类型"})
@RestController
@RequestMapping("/abnormalPiece")
public class AppAbnormalPieceController extends BaseController {

    @Autowired
    private IAppAbnormalPieceService appAbnormalPieceService;

    @GetMapping(value = "/list")
    @ApiOperation(value = "App问题件类型列表", notes = "问题件类型列表")
    public Result<List<AppAbnormalPieceVO>> getAppList() {
        List<AppAbnormalPieceVO> list = appAbnormalPieceService.getAppList();
        return success(list);
    }
}
