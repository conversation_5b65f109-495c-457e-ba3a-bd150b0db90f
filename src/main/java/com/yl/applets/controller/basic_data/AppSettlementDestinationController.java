package com.yl.applets.controller.basic_data;


import com.yl.applets.service.IAppSettlementDestinationService;
import com.yl.applets.vo.lmdm.AppSettlementDestinationVO;
import com.yl.common.base.controller.BaseController;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019/6/19 17:05
 */
@Api(value = "app结算目的地", tags = {"结算目的地"})
@RestController
@RequestMapping("/settlementDestination")
public class AppSettlementDestinationController extends BaseController {

    @Autowired
    private IAppSettlementDestinationService appSettlementDestinationService;

    @GetMapping(value = "/list")
    @ApiOperation(value = "App结算目的地列表", notes = "结算目的地列表")
    public Result<List<AppSettlementDestinationVO>> getAppList() {
        List<AppSettlementDestinationVO> list = appSettlementDestinationService.getAppList();
        return success(list);
    }

}
