package com.yl.applets.controller.basic_data;

import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.CpAreaIdDTO;
import com.yl.applets.dto.DistrictDto;
import com.yl.applets.feign.OtherFeignClient;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2024-02-21
 */
@RestController
@RequestMapping("/district")
@Slf4j
public class DistrictController {


    @Autowired
    private OtherFeignClient otherFeignClient;


    @PostMapping("/valid")
    public Result<Boolean> isRelation(@RequestBody @Valid DistrictDto dto){
        log.info("校验省市区,前端入参==>{}",JSON.toJSONString(dto));
        CpAreaIdDTO areaIdDTO= new CpAreaIdDTO();
        BeanUtils.copyProperties(dto,areaIdDTO);
        Result<Boolean> relation = otherFeignClient.isRelation(areaIdDTO);
        log.info("查询省市区是否有效,入参==>{},出参==>{}", JSON.toJSONString(areaIdDTO),JSON.toJSONString(relation));
        Boolean result = relation.result();
        return Result.success(result);
    }


}
