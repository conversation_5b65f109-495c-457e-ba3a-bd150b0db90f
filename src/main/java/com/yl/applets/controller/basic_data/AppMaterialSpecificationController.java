package com.yl.applets.controller.basic_data;

import com.yl.applets.service.IAppMaterialSpecificationService;
import com.yl.applets.vo.lmdm.AppMaterialSpecificationVO;
import com.yl.common.base.controller.BaseController;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description: 物料
 * @Project:
 * @CreateDate: Created in 2019-08-05 19:09 <br>
 * @Author: zhipeng.liu
 */
@Api(value = "物料", tags = {"物料"})
@RestController
@RequestMapping("/materialSpecification")
public class AppMaterialSpecificationController extends BaseController {

    @Autowired
    private IAppMaterialSpecificationService appMaterialSpecificationService;

    /**
     * 列表
     */
    @ApiOperation(value = "列表", notes = "列表")
    @PostMapping("/page")
    public Result<List<AppMaterialSpecificationVO>> page() {
        return success(appMaterialSpecificationService.getAppList());
    }


}
