package com.yl.applets.controller;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yl.applets.dto.CustomerPrintDataDTO;
import com.yl.applets.dto.ForwardRequest;
import com.yl.applets.entity.CustomerPrintData;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.OldLmdmFeignClient;
import com.yl.applets.service.ICustomerPrintDataService;
import com.yl.applets.vo.CloudPrintStatusVo;
import com.yl.applets.vo.SysCustomeAppletVO;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-23
 */
@RestController
@RequestMapping("/customer-print-data")
@Slf4j
public class CustomerPrintDataController extends AppBaseController {

    @Autowired
    private ICustomerPrintDataService customerPrintDataService;

    @Autowired
    private OldLmdmFeignClient sysCustomerExtFeignClient;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    /**
     * 我的打印机
     * @return
     */
    @PostMapping("/mine")
    public Result<List<CustomerPrintData>> mine(){
        LambdaQueryWrapper<CustomerPrintData> lambdaQueryWrapper = new LambdaQueryWrapper<CustomerPrintData>().eq(CustomerPrintData::getUserId, getUser().getId());
        lambdaQueryWrapper.orderByDesc(CustomerPrintData::getUpdateTime);
        return success(customerPrintDataService.list(lambdaQueryWrapper));
    }

    /**
     * 获取客户打印机信息
     * @return
     */
    @GetMapping("/getData")
    public Result getData(@RequestParam("id")Integer id){
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        ForwardRequest forwardRequest=new ForwardRequest();
        forwardRequest.setRequestId(requestId);
        forwardRequest.setBody(id+"");
        forwardRequest.setRequestUri("/lmdmapi/sysCustomerPrinterEquipment/getSysCustomerAppletInfo");
        log.info("请求id==>{}channel,入参==>{}",requestId, JSON.toJSONString(forwardRequest));
        Result<?> forward = channelApiFeignClient.forward(forwardRequest);
        log.info("请求id==>{}channel,出参==>{}",requestId, JSON.toJSONString(forward));
        Object result = forward.result();
        SysCustomeAppletVO sysCustomerAppletInfo = JSON.parseObject(JSON.toJSONString(result), SysCustomeAppletVO.class);
        return success(sysCustomerAppletInfo);
    }

    /**
     * 删除客户打印机信息
     * @return
     */
    @PostMapping("/delete")
    public Result<Boolean> del(@RequestParam("id")String id){
        return success(customerPrintDataService.remove(new LambdaQueryWrapper<CustomerPrintData>().eq(CustomerPrintData::getId,id).eq(CustomerPrintData::getUserId,getUser().getId())));
    }


    /**
     * 更新绑定打印机信息
     * @return
     */
    @PostMapping("/bind")
    public Result bind(@RequestBody @Valid CustomerPrintDataDTO customerPrintDataDTO){
        customerPrintDataDTO.setUserId(getUser().getId());
        return success(customerPrintDataService.bind(customerPrintDataDTO));
    }



    /**
     * 打印
     * @return
     */
    @PostMapping("/print")
    public Result print(@RequestParam("orderId") Long orderId){
        return customerPrintDataService.print(orderId,getUser().getId());
    }

    /**
     * 查看打印状态
     * @param orderIds
     * @return
     */
    @PostMapping("/printStatus")
    public Result<List<CloudPrintStatusVo>> printStatus(@RequestBody List<Long> orderIds){
        if (CollectionUtils.isEmpty(orderIds)) {
            throw new ServiceException(ServiceErrCodeEnum.ORDERID_ISNULL);
        }
        return success(customerPrintDataService.printStatus(orderIds));
    }

}
