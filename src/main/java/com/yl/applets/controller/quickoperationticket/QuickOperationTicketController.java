package com.yl.applets.controller.quickoperationticket;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.controller.AppBaseController;
import com.yl.applets.dto.quickoperationticke.CcmWorkDetailQueryDTO;
import com.yl.applets.dto.quickoperationticke.CcmWorkOrderCreateDTO;
import com.yl.applets.dto.quickoperationticke.CcmWorkOrderQueryDTO;
import com.yl.applets.dto.quickoperationticke.WorkOrderjudgmentDTO;
import com.yl.applets.feign.QuickOperationTicketFeignClient;
import com.yl.applets.vo.WxUserVo;
import com.yl.applets.vo.quickoperationticke.CcmWorkOrderVO;
import com.yl.applets.vo.quickoperationticke.WorkOrderCustomerOptionsSelectorVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * @date 2025/6/14 11:48
 */
@RestController
@RequestMapping("/quick/operation/ticket")
@Slf4j
public class QuickOperationTicketController extends AppBaseController {

    @Autowired
    private QuickOperationTicketFeignClient quickOperationTicketFeignClient;


    /**
     *  普通工单创建接口
     * @param dto
     * @return
     */
    @PostMapping("/workOrder/ccm/create")
    Result<Boolean> create(@RequestBody @Validated CcmWorkOrderCreateDTO dto){
        log.info("QuickOperationTicketController create 创建工单 dto:{}", JSONObject.toJSONString(dto));
        WxUserVo user = getUser();
        dto.setPhone(user.getMobile());
        dto.setWxUserId(user.getNumberId()+"");
        return quickOperationTicketFeignClient.create(dto);
    };




    /**
     *  普通工单列表
     * @param dto
     * @return
     */
    @PostMapping("/workOrder/ccm/page")
    Result<Page<CcmWorkOrderVO>> page(@RequestBody @Validated CcmWorkOrderQueryDTO dto){
        log.info("QuickOperationTicketController page 普通工单列表 dto:{}", JSONObject.toJSONString(dto));
        WxUserVo user = getUser();
        dto.setWxUserId(user.getNumberId()+"");
        return quickOperationTicketFeignClient.page(dto);
    };





    /**
     * 普通工单详情查询接口
     */
    @PostMapping("/workOrder/ccm/detail")
    Result<CcmWorkOrderVO> detail(@RequestBody @Validated CcmWorkDetailQueryDTO dto){
        log.info("QuickOperationTicketController detail 普通工单详情查询接口 dto:{}", JSONObject.toJSONString(dto));
        WxUserVo user = getUser();
        dto.setWxUserId(user.getNumberId()+"");
        return quickOperationTicketFeignClient.detail(dto);
    };



    /**
     * 普通工单催单接口
     * @return
     */
    @PostMapping("/workOrder/ccm/reminder")
    Result<Boolean> reminder(@RequestBody @Validated CcmWorkDetailQueryDTO dto){
        log.info("QuickOperationTicketController reminder 催单 dto:{}", JSONObject.toJSONString(dto));
        WxUserVo user = getUser();
        dto.setWxUserId(user.getNumberId()+"");
        return quickOperationTicketFeignClient.reminder(dto);
    };



    /**
     * 普通工单是否超时效
     * @param waybillNo
     * @return
     * 1: 在时效内，2: 不在时效内
     */
    @PostMapping("/workOrder/ccm/isOverdue")
    Result<Integer> isOverdue(@RequestParam("waybillNo") String waybillNo){
        log.info("QuickOperationTicketController isOverdue 普通工单是否超时效 dto:{}", waybillNo);
        if(StringUtils.isBlank(waybillNo)){
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        return quickOperationTicketFeignClient.isOverdue(waybillNo);
    };


    /**
     * 选项列表
     */
    @GetMapping("/workOrder/ccm/optionsSelector")
    Result<List<WorkOrderCustomerOptionsSelectorVO>> optionsSelector(){
        return quickOperationTicketFeignClient.optionsSelector();
    };

    /**
     * 判断是否满足创建的工单
     */
    @PostMapping("/workOrder/ccm/judgmentSelfWork")
    Result<Boolean> judgmentSelfWork(@RequestBody WorkOrderjudgmentDTO dto){
        WxUserVo user = getUser();
        dto.setMemberId(user.getNumberId());
        dto.setMobile(user.getMobile());
        return quickOperationTicketFeignClient.judgmentSelfWork(dto);
    };

}
