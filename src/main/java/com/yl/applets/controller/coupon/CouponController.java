package com.yl.applets.controller.coupon;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yl.applets.controller.AppBaseController;
import com.yl.applets.dto.*;
import com.yl.applets.dto.lmdm.SysStaffDTO;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.DispatchCodeFeignClient;
import com.yl.applets.feign.OldLmdmFeignClient;
import com.yl.applets.feign.PromotionFeighClient;
import com.yl.applets.service.IGdAddress;
import com.yl.applets.service.IMyStaffService;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.*;
import com.yl.applets.vo.lmdm.SysNetworkVO;
import com.yl.applets.vo.lmdm.SysStaffVO;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description: 优惠券
 * @date 2021-12-03
 */

@RestController
@RequestMapping("/coupon")
@Api(value = "小程序优惠券管理", tags = {"小程序优惠券相关查询"})
@Slf4j
public class CouponController extends AppBaseController {


    @Autowired
    private IGdAddress gdAddress;

    /**
     *
     * 1.用户优惠券列表
     *      1.1 优惠券对应的"可使用范围"
     * 2.用户下单时候，选择优惠券
     * 3.选中优惠券之后，校验是否可用，如果可用，返回优惠金额
     * 4.下单时，入参优惠卷code+金额，返回优化金额
     *
     *
     * 5.用户领劵接口   优惠券配置表+ 网点分发表
     *
     */


    @Autowired
    private PromotionFeighClient promotionFeighClient;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;


    @Autowired
    private OldLmdmFeignClient sysNetworkFeignClient;

    @Autowired
    private DispatchCodeFeignClient dispatchCodeFeignClient;


    @Autowired
    private OrikaBeanMapper orikaBeanMapper;

    @Autowired
    private IMyStaffService myStaffService;


    @ApiOperation(value = "扫码二维码领取优惠券列表",notes = "扫码二维码领取优惠券列表")
    @GetMapping("/userGetCouponList")
    public Result<Page<CustUserPromotionVo>> userGetCouponList(CustUserPromotionVo vo){
        log.info("扫码二维码,领取优惠券列表,入参==>{}",JSON.toJSONString(vo));
        if(vo.getNetworkId()==null && StringUtils.isEmpty(vo.getPickStaffCode())){
            throw new com.yl.common.base.exception.BusinessException(ResultCodeEnum.PARAMS_IS_NULL);
        }
        WxUserVo user = getUser();
        vo.setUserAlias(user.getNickName());
        vo.setUserId(user.getId().longValue());
        vo.setUserUuid(user.getOpenid());
        vo.setUserPhone(user.getMobile());
        vo.setNumberId(user.getNumberId().longValue());
        if(vo.getNetworkId()==null && !StringUtils.isEmpty(vo.getPickStaffCode())){
            log.info("扫业务员二维码,绑定专属小哥");
            try {
                bindStaff(vo.getPickStaffCode(),user, vo.getIsExclusive());
            }catch (Exception e){
                log.info("扫业务员二维码,绑定专属小哥异常==>{}",e.getMessage());
            }
        }
        log.info("扫码二维码,领取优惠券列表,远程入参==>{}",JSON.toJSONString(vo));
        return promotionFeighClient.userGetCouponList(vo);
    }

    @Async
    public void bindStaff(String code, WxUserVo userVo,Integer isExclusive){
        myStaffService.bindStaff(code,userVo,isExclusive);
    }

    @ApiOperation(value = "根据后端配置领取优惠券",notes = "根据后端配置领取优惠券")
    @GetMapping("/getCouponListByConfig")
    public Result<CouponConfigVo> getCouponListByConfig(String address){
        String requestId = UUID.randomUUID().toString().replace("-", "").toLowerCase();
        log.info("请求id==>{}小程序弹出领取列表，调用优惠券中台,地址解析入参==>{}",requestId, address);
        if(StringUtils.isEmpty(address)){
            throw new com.yl.common.base.exception.BusinessException(ResultCodeEnum.PARAMS_IS_NULL);
        }
        AddressPCDVO addressPCDVO = gdAddress.intelligentAddressRecognitionV2(address);
        log.info("请求id==>{}小程序弹出领取列表，调用优惠券中台,地址解析出参==>{}",requestId, JSON.toJSONString(addressPCDVO));
        if(addressPCDVO==null||addressPCDVO.getCityId()==null){
            log.warn("请求id==>{}小程序弹出领取列表，调用优惠券中台,地址解析服务==>当前地址无优惠券活动,请刷新定位信息！",requestId);
            throw new ServiceException(ServiceErrCodeEnum.COUPON_APPLETS_ADDRESS_ERROR);
        }
        WxUserVo user = getUser();
        CustUserPromotionVo vo=new CustUserPromotionVo();
        vo.setUserAlias(user.getNickName());
        vo.setUserId(user.getId().longValue());
        vo.setUserUuid(user.getOpenid());
        vo.setUserPhone(user.getMobile());
        vo.setNumberId(user.getNumberId().longValue());
        vo.setCityId(addressPCDVO.getCityId()+"");
        log.info("请求id==>{}小程序根据后端配置领取优惠券，调用优惠券中台,入参==>{}",requestId, JSON.toJSONString(vo));
        return promotionFeighClient.getCouponListByConfig(vo);
    }

    @ApiOperation(value = "小程序领取优惠券",notes = "小程序领取优惠券")
    @PostMapping("/getApplets")
    public Result<Integer> getApplets(@RequestBody PromotionUserGetAppletsDto appletsDto){
        String requestId = UUID.randomUUID().toString().replace("-", "").toLowerCase();
        log.info("请求id==>{}小程序领取优惠券，调用优惠券中台,地址解析入参==>{}",requestId, JSON.toJSONString(appletsDto.getAddress()));
        AddressPCDVO addressPCDVO = gdAddress.intelligentAddressRecognitionV2(appletsDto.getAddress());
        log.info("请求id==>{}小程序领取优惠券，调用优惠券中台,地址解析出参==>{}",requestId, JSON.toJSONString(addressPCDVO));
        if(addressPCDVO==null||addressPCDVO.getCityId()==null){
            log.warn("请求id==>{}小程序领取优惠券，调用优惠券中台,地址解析服务==>当前地址无优惠券活动,请刷新定位信息！",requestId);
            throw new ServiceException(ServiceErrCodeEnum.COUPON_APPLETS_ADDRESS_ERROR);
        }
        WxUserVo user = getUser();
        appletsDto.setUserAlias(user.getNickName());
        appletsDto.setUserId(user.getId().longValue());
        appletsDto.setUserUuid(user.getOpenid());
        appletsDto.setUserPhone(user.getMobile());
        appletsDto.setNumberId(user.getNumberId().longValue());
        appletsDto.setCityId(addressPCDVO.getCityId()+"");
        log.info("请求id==>{}小程序领取优惠券，调用优惠券中台,入参==>{}",requestId, JSON.toJSONString(appletsDto));
        return  promotionFeighClient.getApplets(appletsDto);
    }


    @ApiOperation(value = "网点领取优惠券",notes = "网点领取优惠券")
    @PostMapping("/getNetwork")
    public Result<Integer> getNetwork(@RequestBody PromotionUserGetNetworkDto appletsDto){
        if(appletsDto.getNetworkId()==null){
            throw new ServiceException(ServiceErrCodeEnum.PARAM_ERROR);
        }
        if (!StringUtils.isEmpty(appletsDto.getGrantStaffCode())) {
            SysStaffDTO sysStaffDTO = new SysStaffDTO();
            sysStaffDTO.setCode(appletsDto.getGrantStaffCode());
            log.info("根据业务员获取基础数据网点信息入参：{}", JsonUtils.toJson(sysStaffDTO));
//            SysStaffVO sysStaffVO = sysNetworkFeignClient.getStaffDetail(sysStaffDTO).result();
            String requestId = UUID.randomUUID().toString().replaceAll("-", "");
            ForwardRequest request=new ForwardRequest();
            request.setRequestId(requestId);
            request.setRequestUri("/lmdmapi/sysStaff/detail");
            request.setBody(JSON.toJSONString(sysStaffDTO));
            log.info("请求id==>{}接口入参==>{}",requestId,JSON.toJSONString(request));
            Result<?> forward = channelApiFeignClient.forward(request);
            log.info("请求id==>{}接口返回==>{}",requestId,JSON.toJSONString(forward));
            Object resultO = forward.result();
            SysStaffVO sysStaffVO = JSON.parseObject(JSON.toJSONString(resultO), SysStaffVO.class);
            log.info("根据业务员获取基础数据网点信息出参：{}", JsonUtils.toJson(sysStaffVO));
            appletsDto.setNetworkId(sysStaffVO.getNetworkId().longValue());
            appletsDto.setNetworkName(sysStaffVO.getNetworkName());
            appletsDto.setGrantStaffName(sysStaffVO.getName());
        }else if(StringUtils.isEmpty(appletsDto.getGrantStaffCode())){
            log.info("根据网点获取基础数据网点信息入参：{}", appletsDto.getNetworkId());
//            SysNetworkVO networkVO = sysNetworkFeignClient.getDetailById(appletsDto.getNetworkId().intValue()).result();
            String requestIds = UUID.randomUUID().toString().replaceAll("-", "");
            ForwardRequest requests=new ForwardRequest();
            requests.setRequestId(requestIds);
            requests.setRequestUri("/lmdmapi/network/detail");
            requests.setBody(appletsDto.getNetworkId()+"");
            //调接口
            log.info("请求id==>{}接口入参==>{}",requestIds,JSON.toJSONString(requests));
            Result<?> forwards = channelApiFeignClient.forward(requests);
            log.info("请求id==>{}接口返回==>{}",requestIds,JSON.toJSONString(forwards));
            Object results = forwards.result();
            SysNetworkVO networkVO = JSON.parseObject(JSON.toJSONString(results), SysNetworkVO.class);
            log.info("根据网点获取基础数据网点信息入参：{}", JSON.toJSONString(networkVO));
            appletsDto.setNetworkName(networkVO.getName());
        }
        WxUserVo user = getUser();
        appletsDto.setUserAlias(user.getNickName());
        appletsDto.setUserId(user.getId().longValue());
        appletsDto.setUserUuid(user.getOpenid());
        appletsDto.setNumberId(user.getNumberId().longValue());
        appletsDto.setUserPhone(user.getMobile());
        log.info("小程序领取优惠券，网点领取优惠券,入参==>{}", JSON.toJSONString(appletsDto));
        return  promotionFeighClient.getNetwork(appletsDto);
    }


    @ApiOperation(value = "下单优惠券列表",notes = "下单优惠券列表")
    @PostMapping("/getUserCouponToOrder")
    public Result<Page<CustPromotionUseVo>> getCouponToOrder(@RequestBody OrderPromotionDto dto){
        dto.setUserId(getUser().getId().longValue());
        dto.setNumberId(getUser().getNumberId().longValue());
        log.info("下单优惠券列表,远程入参==>{}",JSON.toJSONString(dto));
        return promotionFeighClient.getCouponToOrder(dto);
    }


    @ApiOperation(value = "查询当前用户优惠券列表", notes = "查询当前用户优惠券列表")
    @PostMapping("/getUserCouponByType")
    public Result<Page<CustPromotionUseVo>> getCouponPageByType(@RequestBody CustPromotionUseDto dto){
        dto.setUserId(getUser().getId().longValue());
        dto.setNumberId(getUser().getNumberId().longValue());
        return promotionFeighClient.getCouponPageByType(dto);
    }


    @ApiOperation(value = "根据优惠券查询可使用范围",notes = "根据优惠券查询可使用范围")
    @GetMapping("/getRangeByCode")
    public Result<List<String>> getRangeByCode(
            @RequestParam("id")
            @NotBlank(message = "优惠券id不能为空")
            @ApiParam(value = "优惠券id")
                    String id,
            @RequestParam("type")
            @NotBlank(message = "查询类型不能为空")
            @ApiParam(value = "类型：1.收件范围 2.寄件范围")
                    String type){
        return promotionFeighClient.getRangeByCode(id,type);
    }

    @GetMapping("/getCouponValueAndRule")
    @ApiOperation(value = "根据优惠券查询优惠金额",notes = "根据优惠券查询优惠金额")
    public Result<CustPromotionInfoVo> getCouponValueAndRule(
            @NotBlank(message = "优惠券ID不能为空")
            @ApiParam(value = "当前优惠券ID")
            @RequestParam("id") String id,
            @NotBlank(message = "优惠券code不能为空")
            @ApiParam(value = "优惠券code")
            @RequestParam("proCode") String proCode,
            @NotBlank(message = "运费不能为空")
            @ApiParam(value = "标准运费")
            @RequestParam("freight") String freight){
        return promotionFeighClient.getCouponValueAndRule(id,proCode,freight);
    }


    @ApiOperation(value = "一键领取优惠券",notes = "一键领取优惠券")
    @PostMapping("/getCouponOnekey")
    public Result<Boolean> getCouponOnekey(@RequestBody CouponOneKeyDto dto){
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id==>{}一键领取优惠券，入参==>{}",requestId,JSON.toJSONString(dto));
        if(dto.getType()==null){
            throw new ServiceException(ResultCodeEnum.PARAMS_NOT_COMPLETE);
        }
        //地址解析
        if(StringUtils.isEmpty(dto.getAddress())){
            throw new ServiceException(ResultCodeEnum.PARAMS_NOT_COMPLETE);
        }
        log.info("请求id==>{}小程序领取优惠券，调用优惠券中台,地址解析入参==>{}",requestId, JSON.toJSONString(dto.getAddress()));
        AddressPCDVO addressPCDVO = gdAddress.intelligentAddressRecognitionV2(dto.getAddress());
        log.info("请求id==>{}小程序领取优惠券，调用优惠券中台,地址解析出参==>{}",requestId, JSON.toJSONString(addressPCDVO));
        if(addressPCDVO==null||addressPCDVO.getCityId()==null){
            log.warn("请求id==>{}小程序领取优惠券，调用优惠券中台,地址解析服务==>当前地址无优惠券活动,请刷新定位信息！",requestId);
            throw new ServiceException(ServiceErrCodeEnum.COUPON_APPLETS_ADDRESS_ERROR);
        }
        WxUserVo user = getUser();
        dto.setUserAlias(user.getNickName());
        dto.setUserId(user.getId().longValue());
        dto.setUserUuid(user.getOpenid());
        dto.setNumberId(user.getNumberId().longValue());
        dto.setUserPhone(user.getMobile());
        if(dto.getType()==1){//小程序类型-根据地址解析cityId
            dto.setCityId(addressPCDVO.getCityId()+"");
            log.info("请求id==>{}小程序领取优惠券，调用优惠券中台,对应城市id==>{}",requestId,dto.getCityId());
        }
        if(dto.getType()==2){//网点类型-根据地址解析网点（三段码）
            if(addressPCDVO.getProvinceId()==null||addressPCDVO.getCityId()==null||addressPCDVO.getAreaId()==null
            ||StringUtils.isEmpty(addressPCDVO.getArea())||StringUtils.isEmpty(addressPCDVO.getCity())||StringUtils.isEmpty(addressPCDVO.getProvince())
            ||StringUtils.isEmpty(addressPCDVO.getAddress())){//三段码信息有一个为空
                log.warn("请求id==>{}小程序领取网点优惠券，调用优惠券中台,地址解析服务==>当前地址无优惠券活动,请刷新定位信息！",requestId);
                throw new ServiceException(ServiceErrCodeEnum.COUPON_APPLETS_ADDRESS_ERROR);
            }
            addressPCDVO.setAddress(dto.getAddress());
            DispatchCodeRequestDTO dispatchCodeRequestDTO = toSendDispatchCodeRequestDTO(addressPCDVO);
            log.info("请求id==>{}小程序领取优惠券，调用优惠券中台,地址解析三段码入参==>{}",requestId, JSON.toJSONString(dispatchCodeRequestDTO));
            DispatchCodeResponseVO sendResponseVO = dispatchCodeFeignClient.fetchCodesNew(dispatchCodeRequestDTO).getData();
            log.info("请求id==>{}小程序领取优惠券，调用优惠券中台,地址解析三段码出参==>{}",requestId, JSON.toJSONString(sendResponseVO));
            if(sendResponseVO == null || sendResponseVO.getDeliverNetworkCode() == null || sendResponseVO.getDeliverNetworkId() == null ){
                log.warn("请求id==>{}小程序领取网点优惠券，调用优惠券中台==>当前地址没有解析出网点信息！",requestId);
                throw new ServiceException(ServiceErrCodeEnum.COUPON_APPLETS_ADDRESS_ERROR);
            }else {
                dto.setAddressNetworkId(sendResponseVO.getDeliverNetworkId()+"");
            }
            dto.setGrantStaffCode(dto.getPickStaffCode());
            if(dto.getNetworkId()==null && StringUtils.isEmpty(dto.getGrantStaffCode())){
                throw new ServiceException(ServiceErrCodeEnum.PARAM_ERROR);
            }else if (!StringUtils.isEmpty(dto.getGrantStaffCode())) {
                SysStaffDTO sysStaffDTO = new SysStaffDTO();
                sysStaffDTO.setCode(dto.getGrantStaffCode());
                log.info("请求id==>{}根据业务员获取基础数据网点信息入参：{}",requestId, JsonUtils.toJson(sysStaffDTO));
//                SysStaffVO sysStaffVO = sysNetworkFeignClient.getStaffDetail(sysStaffDTO).result();
                ForwardRequest request=new ForwardRequest();
                request.setRequestId(requestId);
                request.setRequestUri("/lmdmapi/sysStaff/detail");
                request.setBody(JSON.toJSONString(sysStaffDTO));
                log.info("请求id==>{}接口入参==>{}",requestId,JSON.toJSONString(request));
                Result<?> forward = channelApiFeignClient.forward(request);
                log.info("请求id==>{}接口返回==>{}",requestId,JSON.toJSONString(forward));
                Object resultO = forward.result();
                SysStaffVO sysStaffVO = JSON.parseObject(JSON.toJSONString(resultO), SysStaffVO.class);
                log.info("请求id==>{}根据业务员获取基础数据网点信息出参：{}",requestId, JsonUtils.toJson(sysStaffVO));
                dto.setNetworkId(sysStaffVO.getNetworkId().longValue());
                dto.setNetworkName(sysStaffVO.getNetworkName());
                dto.setNetworkCode(sysStaffVO.getNetworkCode());
                dto.setGrantStaffName(sysStaffVO.getName());
            }else if(StringUtils.isEmpty(dto.getGrantStaffCode())){
                log.info("请求id==>{}根据网点获取基础数据网点信息入参：{}",requestId, dto.getNetworkId());
//                SysNetworkVO networkVO = sysNetworkFeignClient.getDetailById(dto.getNetworkId().intValue()).result();
                ForwardRequest requests=new ForwardRequest();
                requests.setRequestId(requestId);
                requests.setRequestUri("/lmdmapi/network/detail");
                requests.setBody(dto.getNetworkId()+"");
                //调接口
                log.info("请求id==>{}接口入参==>{}",requestId,JSON.toJSONString(requests));
                Result<?> forwards = channelApiFeignClient.forward(requests);
                log.info("请求id==>{}接口返回==>{}",requestId,JSON.toJSONString(forwards));
                Object results = forwards.result();
                SysNetworkVO networkVO = JSON.parseObject(JSON.toJSONString(results), SysNetworkVO.class);
                log.info("请求id==>{}根据网点获取基础数据网点信息入参：{}",requestId, JSON.toJSONString(networkVO));
                dto.setNetworkName(networkVO.getName());
                dto.setNetworkCode(networkVO.getCode());
            }
        }
        log.info("请求id==>{},一键领取,远程入参==>{}",requestId,JSON.toJSONString(dto));
        return promotionFeighClient.getCouponOnekey(dto);
    }

    /**
     *  新增数据
     */
    @PostMapping("/addSystemOperation")
    @ApiOperation(value = "新增系统操作记录表", notes = "新增系统操作记录表")
    public Result<Integer> addSystemOperation(@RequestBody CustSystemOperationAddDto dto){
        WxUserVo user = getUser();
        CustSystemOperationDto addDto=orikaBeanMapper.map(dto,CustSystemOperationDto.class);
        addDto.setOperateTime(LocalDateTime.now());
        addDto.setNumberId(user.getNumberId().longValue());
        addDto.setUserId(user.getId().longValue());
        addDto.setUserName(user.getNickName());
        addDto.setOperateDay(LocalDateTimeUtil.format(addDto.getOperateTime(), DatePattern.NORM_DATE_PATTERN));
        return promotionFeighClient.addSystemOperation(addDto);
    }
//    /coupon/ops/getCouponValueAndRule


    /**
     * 转换Dto
     * @param addressPCDVO
     * @return
     */
    private DispatchCodeRequestDTO toSendDispatchCodeRequestDTO(AddressPCDVO addressPCDVO) {
        DispatchCodeRequestDTO queryDTO=new DispatchCodeRequestDTO();
        queryDTO.setProvinceId(addressPCDVO.getProvinceId()+0L);
        queryDTO.setProvince(addressPCDVO.getProvince());
        queryDTO.setCityId(addressPCDVO.getCityId()+0L);
        queryDTO.setCity(addressPCDVO.getCity());
        queryDTO.setAreaId(addressPCDVO.getAreaId()+0L);
        queryDTO.setArea(addressPCDVO.getArea());
        queryDTO.setDetails(addressPCDVO.getAddress());
        queryDTO.setSenderProviceId(addressPCDVO.getProvinceId()+0L);
        queryDTO.setSenderCityId(addressPCDVO.getCityId()+0L);
        queryDTO.setSenderAreaId(addressPCDVO.getAreaId()+0L);
        queryDTO.setSenderProvice(addressPCDVO.getProvince());
        queryDTO.setSenderCity(addressPCDVO.getCity());
        queryDTO.setSenderArea(addressPCDVO.getArea());
        return queryDTO;
    }

    public static void main(String[] args) {
        Long b=null;
        Long a=b+0L;
        System.out.println(a);
    }


}
