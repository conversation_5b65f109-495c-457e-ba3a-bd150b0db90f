package com.yl.applets.controller.student;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.controller.AppBaseController;
import com.yl.applets.dto.ActivityConfigQueryDTO;
import com.yl.applets.dto.StudentSendingDTO;
import com.yl.applets.service.StudentSendingService;
import com.yl.applets.vo.ActivityConfigVO;
import com.yl.applets.vo.WxUserVo;
import com.yl.applets.vo.studentsending.WXStudentSendingResultVO;
import com.yl.common.base.annotation.log.operation.ExcludeInterceptor;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description 学生专属寄送控制器
 * @date 2025/4/18 10:16
 */
@RestController
@RequestMapping("/student/sending")
@Slf4j
public class StudentSendingController extends AppBaseController {


    @Autowired
    private StudentSendingService studentSendingService;


    @GetMapping("/status")
    public Result<Integer> status() {
        WxUserVo user = getUser();
        return Result.success(studentSendingService.getAuthenticationStatus(user.getNumberId()));
    }


    @PostMapping("/authentication")
    public Result<WXStudentSendingResultVO> authentication(@RequestBody @Validated StudentSendingDTO dto) {
        WxUserVo user = getUser();
        dto.setNumberId(user.getNumberId());
        return studentSendingService.authentication(dto);
    }


}
