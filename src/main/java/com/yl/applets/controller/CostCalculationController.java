package com.yl.applets.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.SpmApiInsuranceTrialDTO;
import com.yl.applets.dto.SpmApiTrialDTO;
import com.yl.applets.enums.DispatchServiceTypeEnum;
import com.yl.applets.enums.ExpressTypeEnum;
import com.yl.applets.service.ICostCalculationService;
import com.yl.applets.vo.SpmCommonCostVO;
import com.yl.common.base.controller.BaseController;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description: 保价费查询
 * @Project:
 * @CreateDate: Created in 2019-08-09 14:17 <br>
 * @Author: zhipeng.liu
 */
@Api(value = "计费", tags = {"计费"})
@RestController
@RequestMapping("/costCalculation")
@Slf4j
public class CostCalculationController extends BaseController {

    @Autowired
    private ICostCalculationService costCalculationService;

    static final String CUST_SIGN = "lkN2H1@KJ5dsAS135G6L";


    /**
     * 保价费计算
     *
     * @param spmApiInsuranceTrialDTO
     * @return
     */
    @PostMapping("/insuranceFee/computationCost")
    @ApiOperation(value = "保价费计算", notes = "保价费计算")
    public Result<BigDecimal> computationCost(@RequestBody(required = false) SpmApiInsuranceTrialDTO spmApiInsuranceTrialDTO) {
        if(spmApiInsuranceTrialDTO==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        log.info("保价费计算,请求参数：【{}】", JSON.toJSONString(spmApiInsuranceTrialDTO));
        spmApiInsuranceTrialDTO.setProductTypeCode(ExpressTypeEnum.ORDINARY_EXPRESS.getCode());
        spmApiInsuranceTrialDTO.setDateTime(LocalDateTime.now());
        log.info("保价费计算,接口请求入参：【{}】", JSON.toJSONString(spmApiInsuranceTrialDTO));
        BigDecimal bigDecimal = costCalculationService.computationCost(spmApiInsuranceTrialDTO);
        log.info("保价费计算,接口请求出参：【{}】", JSON.toJSONString(bigDecimal));
        return success(bigDecimal);
    }

    /**
     * 运费计算
     *
     * @param spmApiTrialDTO
     * @return
     */
    @ApiOperation(value = "运费计算", notes = "运费计算")
    @PostMapping("/spmStandardShippingQuote/comCost")
    public Result<SpmCommonCostVO> computationCost(@RequestBody(required = false) SpmApiTrialDTO spmApiTrialDTO) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        if(spmApiTrialDTO==null){
            log.warn("请求id==>{}入参为空",requestId);
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        log.info("请求id==>{}运费计算,请求入参：【{}】", requestId,JSON.toJSONString(spmApiTrialDTO));
        spmApiTrialDTO.setServiceMethodCode(DispatchServiceTypeEnum.DISPATCH_DOOR.getCode());
        if (StringUtils.isBlank(spmApiTrialDTO.getProductTypeCode())) {
            spmApiTrialDTO.setProductTypeCode(ExpressTypeEnum.ORDINARY_EXPRESS.getCode());
        }else {
            spmApiTrialDTO.setProductTypeCode(spmApiTrialDTO.getProductTypeCode());
        }
        spmApiTrialDTO.setDateTime(LocalDateTime.now());
        log.info("请求id==>{}运费计算,接口请求入参：【{}】", requestId,JSON.toJSONString(spmApiTrialDTO));
        SpmCommonCostVO spmCommonCostVO = costCalculationService.comCost(spmApiTrialDTO);
        spmCommonCostVO.setLaterCost(spmCommonCostVO.getCost().subtract(Objects.isNull(spmCommonCostVO.getFirstWeightPrice()) ? BigDecimal.ZERO : spmCommonCostVO.getFirstWeightPrice()));
        log.info("请求id==>{}运费计算,接口请求出参：【{}】", requestId,JSON.toJSONString(spmCommonCostVO));
        return success(spmCommonCostVO);
    }


    /**
     * 运费计算
     *
     * @param spmApiTrialDTO
     * @return
     */
//    @ApiOperation(value = "运费计算", notes = "运费计算")
//    @PostMapping("/v2/spmStandardShippingQuote/comCost")
//    public Result<SpmCommonCostVO> computationCostV2(@RequestBody SpmApiTrialDTO spmApiTrialDTO) {
//        String requestId = UUID.randomUUID().toString().replace("-", "");
//        log.info("请求id==>{}运费计算,请求入参：【{}】", requestId,JSON.toJSONString(spmApiTrialDTO));
//        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex( spmApiTrialDTO.getStartPointId()+""+spmApiTrialDTO.getTerminalPointId()+""+spmApiTrialDTO.getTimes()+CUST_SIGN));
//        log.info("请求id{}，查询目的结算地,后端MD5加密之后{}",requestId,sign1);
//        if(!sign1.equals(spmApiTrialDTO.getSign())){
//            log.info("请求id{},查询目的结算地,验签失败！",requestId);
//            throw new BusinessException(ResultCodeEnum.FAIL);
//        }
//        spmApiTrialDTO.setServiceMethodCode(DispatchServiceTypeEnum.DISPATCH_DOOR.getCode());
//        spmApiTrialDTO.setProductTypeCode(ExpressTypeEnum.ORDINARY_EXPRESS.getCode());
//        spmApiTrialDTO.setDateTime(LocalDateTime.now());
//        log.info("请求id==>{}运费计算,接口请求入参：【{}】", requestId,JSON.toJSONString(spmApiTrialDTO));
//        SpmCommonCostVO spmCommonCostVO = costCalculationService.comCost(spmApiTrialDTO);
//        log.info("请求id==>{}运费计算,接口请求出参：【{}】", requestId,JSON.toJSONString(spmCommonCostVO));
//        return success(spmCommonCostVO);
//    }


}
