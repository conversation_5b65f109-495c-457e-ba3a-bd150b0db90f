package com.yl.applets.controller.internalemployees;

import com.yl.applets.controller.AppBaseController;
import com.yl.applets.feign.CustomerPlatformNetworkFeignClient;
import com.yl.applets.service.InternalEmployees;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description
 * 内部员工控制器
 * @date 2025/5/16 17:29
 */

@RestController
@RequestMapping("/internal/employees")
@Slf4j
public class InternalEmployeesController extends AppBaseController {


    @Autowired
    private InternalEmployees internalEmployees;
    /**
     * 是否内部员工
     * @return
     */
    @GetMapping("/flag")
    public Result<Boolean> isInternalEmployees(){
        WxUserVo user = getUser();
        String mobile = user.getMobile();
        log.info("InternalEmployeesController isInternalEmployees mobile={}", mobile);
        return Result.success(internalEmployees.isInternalEmployees(mobile));
    }
}
