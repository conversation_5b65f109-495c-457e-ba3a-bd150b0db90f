package com.yl.applets.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.config.WxInvoiceProperties;
import com.yl.applets.dto.ElectronicInvoiceDTO;
import com.yl.applets.dto.invoice.InvoicingEmailSendDTO;
import com.yl.applets.dto.invoice.SmallProgramAddInvoiceRecordDTO;
import com.yl.applets.dto.invoice.SmallProgramDetailsQueryDTO;
import com.yl.applets.dto.invoice.SmallProgramQueryDTO;
import com.yl.applets.feign.InvoiceFeignClient;
import com.yl.applets.vo.OmsElectronicInvoice;
import com.yl.applets.vo.invoice.AddInvoiceRecordResultVO;
import com.yl.applets.vo.invoice.SmallProgramInvoicingDetailsRecordVO;
import com.yl.applets.vo.invoice.SmallProgramInvoicingRecordVO;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.regex.Pattern;

/**
 * <p>
 * 开票抬头表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-21
 */
@Slf4j
@RestController
@RequestMapping("/invoiceRecord")
public class InvoiceRecordController extends AppBaseController {

    @Autowired
    private InvoiceFeignClient invoiceFeignClient;

    @Autowired
    private WxInvoiceProperties wxInvoiceProperties;


    public static final int MAX_SIZE = 100;

    @PostMapping(value = "/pageMyElectronicInvoice")
    @ApiOperation(value = "小程序开票查询开票", notes = "小程序开票查询开票")
    public Result<Page<OmsElectronicInvoice>> pageMyElectronicInvoice(@RequestBody ElectronicInvoiceDTO electronicInvoiceDTO) {
        Integer size= electronicInvoiceDTO.getSize();
        Integer current= electronicInvoiceDTO.getCurrent();
        if(size == null){
            size = 30;
        }
        if (size > MAX_SIZE){
            size = 30;
        }
        if (current == null){
            current = 1;
        }

        electronicInvoiceDTO.setCurrent(current);
        electronicInvoiceDTO.setSize(size);
        electronicInvoiceDTO.setMemberId(getUser().getNumberId());
        electronicInvoiceDTO.setPhone(getUser().getMobile());
        return Result.success(invoiceFeignClient.pageMyElectronicInvoice(electronicInvoiceDTO).result());
    }


    /**
     * 查询开票记录
     *
     * @param queryDTO 查询DTO
     * @return 查询结果
     */
    @PostMapping("/getRecord")
    @ApiOperation(value = "外部查询查询开票记录", notes = "外部查询查询开票记录")
    Result<Page<SmallProgramInvoicingRecordVO>> getRecord(@RequestBody SmallProgramQueryDTO queryDTO) {
        queryDTO.setUserId(String.valueOf(getUser().getNumberId()));
        return success(invoiceFeignClient.getRecord(queryDTO).result());
    }


    /**
     * 查询开票记录详情
     *
     * @param queryDTO 查询DTO
     * @return 查询结果
     */
    @PostMapping("/getDetailRecord")
    @ApiOperation(value = "查询开票记录详情", notes = "查询开票记录详情")
    Result<Page<SmallProgramInvoicingDetailsRecordVO>> getDetailRecord(@RequestBody SmallProgramDetailsQueryDTO queryDTO) {
        return success(invoiceFeignClient.getDetailRecord(queryDTO).result());
    }


    /**
     * 添加开票记录
     *
     * @param addInvoiceRecordDTO 添加开票记录DTO
     * @return 开票结果信息
     */
    @PostMapping(value = "/addInvoiceRecord")
    @ApiOperation(value = "添加开票记录", notes = "添加开票记录")
    Result<AddInvoiceRecordResultVO> addInvoiceRecord(@RequestBody @Valid SmallProgramAddInvoiceRecordDTO addInvoiceRecordDTO) {
        checkData(addInvoiceRecordDTO);
        addInvoiceRecordDTO.setUserId(String.valueOf(getUser().getNumberId()));
        return success(invoiceFeignClient.addInvoiceRecord(addInvoiceRecordDTO).result());
    }

    private void checkData(SmallProgramAddInvoiceRecordDTO addInvoiceRecordDTO) {
        Integer invoiceType = addInvoiceRecordDTO.getInvoiceType();
        if(invoiceType==1){
            // 公司地址，手机号，开户银行，银行账号,邮箱 必填
            if(StringUtils.isBlank(addInvoiceRecordDTO.getInvoicingTel())){
                throw new BusinessException(ResultCodeEnum.INVOICE_TAX_ERROR3);
            }
            if(StringUtils.isBlank(addInvoiceRecordDTO.getBuyerAddressPhone())){
                throw new BusinessException(ResultCodeEnum.INVOICE_TAX_ERROR4);
            }
            if(StringUtils.isBlank(addInvoiceRecordDTO.getBuyerBankAccount())){
                throw new BusinessException(ResultCodeEnum.INVOICE_TAX_ERROR5);
            }
            if(StringUtils.isBlank(addInvoiceRecordDTO.getBuyerBankName())){
                throw new BusinessException(ResultCodeEnum.INVOICE_TAX_ERROR6);
            }
            if(StringUtils.isBlank(addInvoiceRecordDTO.getInvoicingEmail())){
                throw new BusinessException(ResultCodeEnum.INVOICE_TAX_ERROR7);
            }
        }
        Integer type = addInvoiceRecordDTO.getType();
        String taxNumber = addInvoiceRecordDTO.getBuyerTaxNumber();// 纳税人识别号
        if(type==2){
            if(StringUtils.isNotBlank(taxNumber)){
                Pattern regex = Pattern.compile("^9[A-Z0-9]{17}$") ;
                if (!regex.matcher(taxNumber).matches()) {
                    throw new BusinessException(ResultCodeEnum.INVOICE_TAX_ERROR);
                }
            }
        } else if(type==1){
            if(StringUtils.isBlank(taxNumber)){
                throw new BusinessException(ResultCodeEnum.INVOICE_TAX_ERROR2);
            }
            if(StringUtils.isNotBlank(taxNumber)){
                Pattern regex = Pattern.compile("^9[A-Z0-9]{17}$") ;
                if (!regex.matcher(taxNumber).matches()) {
                    throw new BusinessException(ResultCodeEnum.INVOICE_TAX_ERROR);
                }
            }
        }
        String name = addInvoiceRecordDTO.getBuyerName();//抬头名称
        if(name.contains(" ")){
            throw new BusinessException(ResultCodeEnum.INVOICE_TAX_ERROR8);
        }
        String buyerBankAccount = addInvoiceRecordDTO.getBuyerBankAccount();//开户行账号
        String mobile = addInvoiceRecordDTO.getInvoicingTel();//电话
        if(StringUtils.equals(wxInvoiceProperties.getCompanyName(),name)||StringUtils.equals(wxInvoiceProperties.getTaxNumber(),taxNumber)
                ||StringUtils.equals(wxInvoiceProperties.getBankAccount(),buyerBankAccount)||StringUtils.equals(wxInvoiceProperties.getMobile(),mobile)){
            throw new BusinessException(ResultCodeEnum.INVOICE_INFO_ERROR);
        }
    }

    /**
     * 开票邮箱发送
     *
     * @param sendDTO 发送DTO对象
     * @return 结果对象
     */
    @PostMapping(value = "/invoicingEmailSend")
    @ApiOperation(value = "开票邮箱发送", notes = "开票邮箱发送")
    Result<Boolean> invoicingEmailSend(@RequestBody @Valid InvoicingEmailSendDTO sendDTO) {
        return success(invoiceFeignClient.invoicingEmailSend(sendDTO).result());
    }


}
