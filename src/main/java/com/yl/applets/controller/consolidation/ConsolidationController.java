package com.yl.applets.controller.consolidation;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.config.ConsolidationSendProperties;
import com.yl.applets.controller.AppBaseController;
import com.yl.applets.dto.consolidation.ConsolidationCancelDTO;
import com.yl.applets.dto.consolidation.ConsolidationDTO;
import com.yl.applets.dto.consolidation.ConsolidationPageDTO;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.service.IWxUserService;
import com.yl.applets.service.consolidation.IConsolidationService;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.utils.ExpressNumberResolvingUtil;
import com.yl.applets.utils.NumberIdUtils;
import com.yl.applets.vo.WxUserVo;
import com.yl.applets.vo.consolidation.ConsolidationExpressCompanyVO;
import com.yl.applets.vo.consolidation.ConsolidationInfoVO;
import com.yl.applets.vo.consolidation.ConsolidationPageVO;
import com.yl.applets.vo.consolidation.ConsolidationWarehouseVO;
import com.yl.common.base.annotation.log.operation.ExcludeInterceptor;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.StringUtils;
import com.yl.common.base.util.YlPreconditions;
import com.yl.redis.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有 Copyright 2024
 *
 * <AUTHOR>
 * @description 新疆集运
 * @date 2024/11/21 10:27
 */
@Api(value = "新疆集运", tags = {"新疆集运"})
@RestController
@RequestMapping("/consolidation")
@Slf4j
public class ConsolidationController extends AppBaseController {

    @Resource
    private ConsolidationSendProperties consolidationSendProperties;

    //跟【网络终端】共用一个key。
    @Value("${consolidation.express.resolving.url:http://www.kuaidi100.com/autonumber/auto?num=%s&key=COfSypif2587}")
    private String consolidationExpressResolvingUrl;


    static final String WAYBILL_MD5_KEY_ALIPAY = "h3saNnPreisAPPL@e1tejasig";

    @Autowired
    private IConsolidationService consolidationService;

    @Autowired
    private IWxUserService wxUserService;

    @Value("${jwt.expiration:2592000}")
    private Long expiration;
    @Autowired
    private NumberIdUtils numberIdUtils;

    @GetMapping(value = "/warehouse/info")
    @ApiOperation(value = "获取新疆集运的云仓基础信息", notes = "云仓基础信息")
    public Result<ConsolidationWarehouseVO> getWarehouseInfo() {
        WxUserVo user = getUser();
        ConsolidationWarehouseVO res = new ConsolidationWarehouseVO();
        //1.判断集运id是否存在
        Integer jyUserId = user.getJyUserId();
        if (null == jyUserId) {
            //2.初始化获取人员编码
            Object jyUserIdNum = RedisUtil.get(NumberIdUtils.JY_NUMBER_ID_LOCK + user.getNumberId());
            if (jyUserIdNum == null) {
                jyUserId = numberIdUtils.getJyUserId();
                RedisUtil.setEx(NumberIdUtils.JY_NUMBER_ID_LOCK + user.getNumberId(), jyUserId + "", 10);
            } else {
                jyUserId = Integer.valueOf(jyUserIdNum.toString());
            }
            user.setJyUserId(jyUserId);
            //3.更新人员编码
            wxUserService.updateJyUserId(user);
            Long expire = RedisUtil.getExpire(BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(user.getId().toString()));
            if(Objects.isNull(expire)){
                expire=expiration;
            }
            RedisUtil.setEx(BcRedisKeyEnum.OAUTH_APP_TOKEN.keyBuilder(user.getId().toString()), JSON.toJSONString(user), expire);
        }
        res.setConsolidationPhone(consolidationSendProperties.getPhone());
        //4.s输出编码
        String consolidationNumberId = String.format("%09d", jyUserId);
        String address = String.format(consolidationSendProperties.getAddress(), consolidationSendProperties.getAst(), consolidationNumberId);
        res.setConsolidationAddress(address);
        res.setConsolidationAst(consolidationSendProperties.getAst());
        res.setConsolidationNumberId(String.format("%09d", jyUserId));
        return success(res);
    }


    @GetMapping(value = "/express/company/list")
    @ApiOperation(value = "快递公司列表", notes = "快递公司列表")
    public Result<List<ConsolidationExpressCompanyVO>> consolidationCompanyList() {
        List<ConsolidationExpressCompanyVO> list = consolidationService.consolidationExpressCompanyList();
        return success(list);
    }


    /**
     * 跟【网络终端】共用一个key
     *
     * @param preOrderNo
     * @return
     */
    @GetMapping(value = "/express/resolving/orderNo")
    @ApiOperation(value = "根据快递单号解析快递公司", notes = "根据快递单号解析快递公司")
    public Result<ConsolidationExpressCompanyVO> consolidationCompanyList(@RequestParam("preOrderNo") String preOrderNo) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        ConsolidationExpressCompanyVO res = new ConsolidationExpressCompanyVO();
        YlPreconditions.checkArgument(StringUtils.isNotEmpty(preOrderNo), new ServiceException(ServiceErrCodeEnum.PRE_ORDER_NO_ERROR));
        String url = String.format(consolidationExpressResolvingUrl, preOrderNo);
        //获取快递公司
        String company = ExpressNumberResolvingUtil.numberResolving(url);
        if (StringUtils.isNotEmpty(company)) {
            JSONArray jsonObject = null;
            try {
                jsonObject = JSON.parseArray(company);
            } catch (Exception e) {
                log.error("根据快递单号解析快递公司出错==> requestId:[{}],参数:[{}] err:[{}]", requestId, preOrderNo, e.getMessage());
            }
            if (null != jsonObject && jsonObject.size() != 0) {
                JSONObject jsonExpressCompany = jsonObject.getJSONObject(0);

                res.setCode(jsonExpressCompany.getString("comCode"));
                res.setName(jsonExpressCompany.getString("name"));
            }
        }
        return success(res);
    }


    @GetMapping(value = "/judge/pending/payOrder")
    @ApiOperation(value = "判断是否存在待支付订单", notes = "判断是否存在待支付订单")
    public Result<Integer> consolidationProOrderSave() {
        WxUserVo user = getUser();
        Integer numberId = user.getNumberId();
        int payCount = consolidationService.judgePendingPayOrder(Long.parseLong(numberId + ""));
        return success(payCount);
    }


    @GetMapping(value = "/judge/repeat/order")
    @ApiOperation(value = "判断是否已经登记过", notes = "判断是否已经登记过")
    public Result<Boolean> judgeRepeatOrder(@RequestParam("preOrderNo") String preOrderNo) {
        YlPreconditions.checkArgument(StringUtils.isNotEmpty(preOrderNo), new ServiceException(ServiceErrCodeEnum.PRE_ORDER_NO_ERROR));
        Boolean repeatOrder = consolidationService.judgeRepeatOrder(preOrderNo);
        return success(repeatOrder);
    }


    @PostMapping(value = "/proOrder/save")
    @ApiOperation(value = "新增新建集运信息", notes = "新增新建集运信息")
    public Result<String> consolidationProOrderSave(@Validated @RequestBody ConsolidationDTO dto) {
        WxUserVo user = getUser();
        dto.setMemberId(Long.parseLong(user.getNumberId() + ""));
        dto.setSenderName(consolidationSendProperties.getSenderName());
        dto.setSenderProvinceId(consolidationSendProperties.getSenderProvinceId());
        dto.setSenderProvinceName(consolidationSendProperties.getSenderProvinceName());
        dto.setSenderCityId(consolidationSendProperties.getSenderCityId());
        dto.setSenderCityName(consolidationSendProperties.getSenderCityName());
        dto.setSenderAreaId(consolidationSendProperties.getSenderAreaId());
        dto.setSenderAreaName(consolidationSendProperties.getSenderAreaName());
        dto.setSenderDetailedAddress(consolidationSendProperties.getSenderDetailedAddress());
        dto.setSenderMobilePhone(consolidationSendProperties.getSenderMobilePhone());
        dto.setNetworkCode(consolidationSendProperties.getNetworkCode());
        dto.setNetworkId(consolidationSendProperties.getNetworkId());
        dto.setNetworkName(consolidationSendProperties.getNetworkName());
        dto.setRegisterMobilePhone(user.getMobile());
        return success(consolidationService.consolidationProOrderSave(dto));
    }


    @PostMapping(value = "/proOrder/update")
    @ApiOperation(value = "更新新建集运信息", notes = "更新新建集运信息")
    public Result<Boolean> consolidationProOrderUpdate(@Validated @RequestBody ConsolidationDTO dto) {
        YlPreconditions.checkArgument(null != dto.getId(), new ServiceException(ServiceErrCodeEnum.UPDATE_ORDER_INFO_ERROR));
        WxUserVo user = getUser();
        dto.setMemberId(Long.parseLong(user.getNumberId() + ""));
        dto.setSenderName(consolidationSendProperties.getSenderName());
        dto.setSenderProvinceId(consolidationSendProperties.getSenderProvinceId());
        dto.setSenderProvinceName(consolidationSendProperties.getSenderProvinceName());
        dto.setSenderCityId(consolidationSendProperties.getSenderCityId());
        dto.setSenderCityName(consolidationSendProperties.getSenderCityName());
        dto.setSenderAreaId(consolidationSendProperties.getSenderAreaId());
        dto.setSenderAreaName(consolidationSendProperties.getSenderAreaName());
        dto.setSenderDetailedAddress(consolidationSendProperties.getSenderDetailedAddress());
        dto.setSenderMobilePhone(consolidationSendProperties.getSenderMobilePhone());
        dto.setNetworkCode(consolidationSendProperties.getNetworkCode());
        dto.setNetworkId(consolidationSendProperties.getNetworkId());
        dto.setNetworkName(consolidationSendProperties.getNetworkName());
        dto.setRegisterMobilePhone(user.getMobile());
        return success(consolidationService.consolidationProOrderUpdate(dto));
    }


    @PostMapping(value = "/proOrder/cancel")
    @ApiOperation(value = "取消新建集运信息", notes = "取消新建集运信息")
    public Result<Boolean> consolidationProOrderCancel(@Validated @RequestBody ConsolidationCancelDTO dto) {
        YlPreconditions.checkArgument(null != dto.getId(), new ServiceException(ServiceErrCodeEnum.UPDATE_ORDER_INFO_ERROR));
        WxUserVo user = getUser();
        dto.setMemberId(Long.parseLong(user.getNumberId() + ""));
        return success(consolidationService.consolidationProOrderCancel(dto));
    }


    @PostMapping(value = "/proOrder/delete")
    @ApiOperation(value = "删除新建集运信息", notes = "删除新建集运信息")
    public Result<Boolean> consolidationProOrderDelete(@Validated @RequestBody ConsolidationCancelDTO dto) {
        YlPreconditions.checkArgument(null != dto.getId(), new ServiceException(ServiceErrCodeEnum.UPDATE_ORDER_INFO_ERROR));
        WxUserVo user = getUser();
        dto.setMemberId(Long.parseLong(user.getNumberId() + ""));
        return success(consolidationService.consolidationProOrderDelete(dto));
    }


    @PostMapping(value = "/page/list")
    @ApiOperation(value = "列表分页查询", notes = "列表分页查询")
    public Result<Page<ConsolidationPageVO>> consolidationProOrderPages(@Validated @RequestBody ConsolidationPageDTO dto) {
        WxUserVo user = getUser();
        dto.setMemberId(user.getNumberId());
        YlPreconditions.checkArgument(!(null == dto.getOrderStatus() && null == dto.getPayStatus()), new ServiceException(ServiceErrCodeEnum.QUERY_STATUS_INFO_ERROR));
        return consolidationService.consolidationProOrderPages(dto);
    }


    @GetMapping(value = "/proOrder/byId")
    @ApiOperation(value = "通过主键id查询集运详情", notes = "通过主键id查询集运详情")
    public Result<ConsolidationInfoVO> consolidationProOrderInfoById(@RequestParam("id") Long id) {
        YlPreconditions.checkArgument(null != id, new ServiceException(ServiceErrCodeEnum.QUERY_ID_INFO_ERROR));
        WxUserVo user = getUser();
        return consolidationService.consolidationProOrderInfoById(id,user);
    }


    @GetMapping(value = "/proOrder/by/waybillNo")
    @ApiOperation(value = "通过运单号查询集运详情", notes = "通过运单号查询集运详情")
    public Result<ConsolidationInfoVO> consolidationProOrderInfoByNo(@RequestParam("waybillNo") String waybillNo) {
        YlPreconditions.checkArgument(StringUtils.isNotEmpty(waybillNo), new ServiceException(ServiceErrCodeEnum.QUERY_ID_INFO_ERROR));
        WxUserVo user = getUser();
        return consolidationService.consolidationProOrderInfoByNo(waybillNo,user);
    }

    @GetMapping(value = "/proOrder/abnormal/info")
    @ApiOperation(value = "通过收件人手机号查询异常件详情", notes = "通过收件人手机号查询异常件详情")
    @ExcludeInterceptor
    public Result<ConsolidationInfoVO> consolidationAbnormalInfo(@RequestParam("id") String id,
                                                                 @RequestParam("phone") String phone,
                                                                 @RequestParam("sign") String sign,
                                                                 @RequestParam("time") String time) {

        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(phone + time + WAYBILL_MD5_KEY_ALIPAY));
        log.info("[通过收件人手机号查询异常件详情]后端MD5加密之后{}", sign1);
        if (!sign1.equals(sign)) {
            log.info("验签失败！[{}]", sign);
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }
        return consolidationService.consolidationAbnormalInfo(Long.parseLong(id), phone);
    }


}
