package com.yl.applets.controller;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.yl.applets.dto.AddressDTO;
import com.yl.applets.dto.CheckAddressDto;
import com.yl.applets.dto.ForwardRequest;
import com.yl.applets.dto.lmdm.SysAreaNormalDTO;
import com.yl.applets.dto.lmdm.SysAreaPcaNamesDTO;
import com.yl.applets.entity.Address;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.service.IAddressService;
import com.yl.applets.service.IWxUserService;
import com.yl.applets.utils.SessionUtil;
import com.yl.applets.vo.AddressVo;
import com.yl.applets.vo.CheckAddressVo;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.constant.UpdateGroup;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.enums.SysAreaTypeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.YlPreconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 地址管理 前端控制器
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-20
 */
@Api(value = "地址管理", tags = {"地址管理"})
@RestController
@RequestMapping("/address")
@Slf4j
public class AddressController extends AppBaseController {

    @Autowired
    private IAddressService addressService;

    @Autowired
    private SessionUtil sessionUtil;
    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;



    @PostMapping("/checkAddress")
    @ApiOperation(value = "地址解析", notes = "地址解析")
    public Result<CheckAddressVo> checkAddress(@RequestBody CheckAddressDto checkAddressDto){
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("请求id==>{},地址解析入参==>{}",requestId, JSON.toJSONString(checkAddressDto));
        List<SysAreaPcaNamesDTO> list = new ArrayList<>();
        SysAreaPcaNamesDTO pro = new SysAreaPcaNamesDTO();
        pro.setName(cleanCity(checkAddressDto.getProvinceName()));
        pro.setType(SysAreaTypeEnum.PROVINCE.getId());
        SysAreaPcaNamesDTO city = new SysAreaPcaNamesDTO();
        city.setName(checkAddressDto.getCityName());
        city.setType(SysAreaTypeEnum.CITY.getId());
        SysAreaPcaNamesDTO area = new SysAreaPcaNamesDTO();
        area.setName(checkAddressDto.getAreaName());
        area.setType(SysAreaTypeEnum.AREA.getId());
        list.add(pro);
        list.add(city);
        list.add(area);
        log.info("请求id==>{},地址解析调用接口入参==>{}",requestId, JSON.toJSONString(list));
        ForwardRequest request=new ForwardRequest();
        request.setRequestId(requestId);
        request.setRequestUri("/lmdmapi/sysArea/getByNames");
        request.setBody(JSON.toJSONString(list));
        Result<?> forward = channelApiFeignClient.forward(request);
        log.info("请求id==>{}接口返回==>{}",requestId,JSON.toJSONString(forward));
        Object resultO = forward.result();
        List<SysAreaNormalDTO> result = JSON.parseArray(JSON.toJSONString(resultO), SysAreaNormalDTO.class);
        log.info("请求id==>{},地址解析调用接口出参==>{}",requestId, JSON.toJSONString(result));
        CheckAddressVo vo=new CheckAddressVo();
        if (result.size() == 3) {
            result.forEach(s -> {
                if (s.getType().equals(SysAreaTypeEnum.PROVINCE.getId())) {
                    vo.setProvinceName(cleanCity(checkAddressDto.getProvinceName()));
                    vo.setProvinceId(s.getId());
                } else if (s.getType().equals(SysAreaTypeEnum.CITY.getId())) {
                    vo.setCityName(checkAddressDto.getCityName());
                    vo.setCityId(s.getId());
                } else {
                    vo.setAreaName(checkAddressDto.getAreaName());
                    vo.setAreaId(s.getId());
                }
            });
        }else {
            log.info("请求id==>{},地址解析未解析到对应的地址==>{},入参==>{}",requestId, JSON.toJSONString(result),JSON.toJSONString(checkAddressDto));
            throw new ServiceException(ResultCodeEnum.ADDRESS_CHECK_IS_ERROR);
        }
        return success(vo);
    }

    /**
     *  微信直辖市处理
     * @param provinceName
     * @return
     */
    private String cleanCity(String provinceName) {
        if (Lists.newArrayList("北京市", "上海市", "重庆市", "天津市").contains(provinceName)) {
            provinceName = provinceName.replace("市", "");
        }
        return provinceName;
    }


    /**
     * 地址列表
     *
     * @param keyword
     * @return
     */
    @GetMapping("/page")
    @ApiOperation(value = "地址列表", notes = "地址列表")
    public Result<Page<AddressVo>> getPages(@RequestParam(value = "keyword", required = false) String keyword,
                                            @RequestParam(value = "type", required = false) Integer type,
                                            @RequestParam(value = "signs", required = false) List<Long> signs
    ) {
        return success(addressService.getPages(getPage(), keyword, getUser().getId(),type,signs));
    }

    /**
     * 新增
     *
     * @param dto
     * @return
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增", notes = "新增")
    public Result<AddressVo> save(@RequestBody @Valid AddressDTO dto) {
        return success(addressService.save(dto));
    }

    /**
     * 修改
     *
     * @param dto
     * @return
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改", notes = "修改")
    public Result<Boolean> update(@RequestBody @Validated(value = {UpdateGroup.class}) AddressDTO dto) {
        return success(addressService.update(dto));
    }

    /**
     * 删除
     *
     * @param list
     * @return
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除", notes = "删除数据")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> delete(@RequestBody List<String> list) {
        YlPreconditions.checkArgument(!CollectionUtils.isEmpty(list), new BusinessException(ServiceErrCodeEnum.ADDRESSID_ISNULL));
        //根据numberId查询ids
        List<Long> ids = wxUserService.getIdsByIdAndNumberId(getUser().getNumberId());
        return success(addressService.remove(new LambdaQueryWrapper<Address>().in(Address::getUserId,ids).in(Address::getId,list)));
    }

    /**
     * 获取默认寄件地址
     *
     * @return
     */
    @GetMapping("/getDefault")
    @ApiOperation(value = "获取默认寄件地址", notes = "获取默认寄件地址")
    public Result<AddressVo> getDefault() {
        return success(addressService.getDefaultByMobile(sessionUtil.getUser().getMobile()));
    }

}
