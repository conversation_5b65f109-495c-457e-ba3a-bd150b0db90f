//package com.yl.applets.controller;
//
//import com.alibaba.fastjson.JSON;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.google.common.collect.Lists;
//import com.yl.applets.dto.*;
//import com.yl.applets.entity.*;
//import com.yl.applets.feign.OssApi;
//import com.yl.applets.mapper.applets.LuckDrawItemMapper;
//import com.yl.applets.mapper.applets.LuckDrawMapper;
//import com.yl.applets.mapper.applets.LuckDrawPoolMapper;
//import com.yl.applets.service.IActivityPrizeService;
//import com.yl.applets.service.IActivityUserService;
//import com.yl.applets.utils.Base64Utils;
//import com.yl.applets.utils.BcRedisKeyEnum;
//import com.yl.applets.utils.ResultUtil;
//import com.yl.applets.vo.WxUserVo;
//import com.yl.common.base.config.OrikaBeanMapper;
//import com.yl.common.base.enums.ResultCodeEnum;
//import com.yl.common.base.exception.BusinessException;
//import com.yl.common.base.model.vo.Result;
//import com.yl.common.base.util.CollectionUtils;
//import com.yl.common.base.util.GenerationIdUtil;
//import com.yl.common.base.util.StringUtils;
//import com.yl.redis.util.RedisUtil;
//import io.swagger.annotations.Api;
//import lombok.extern.slf4j.Slf4j;
//import org.redisson.api.RLock;
//import org.redisson.api.RedissonClient;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.util.StopWatch;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.time.Duration;
//import java.time.LocalDateTime;
//import java.time.LocalTime;
//import java.time.format.DateTimeFormatter;
//import java.util.*;
//import java.util.concurrent.TimeUnit;
//import java.util.stream.Collectors;
//
///**
// * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
// *
// * @description: 抽奖活动
// * @author: hhf
// * @create: 2022-8-9 19:36:10
// */
//@Api(value = "抽奖活动", tags = {"抽奖活动"})
//@RestController
//@RequestMapping("/prize")
//@Slf4j
//public class ActivityPrizeController extends AppBaseController {
//
//    @Autowired
//    private IActivityPrizeService activityPrizeService;
//
//    @Autowired
//    private IActivityUserService activityUserService;
//
//    @Autowired
//    private LuckDrawPoolMapper luckDrawPoolMapper;
//
//    @Autowired
//    private LuckDrawItemMapper luckDrawItemMapper;
//
//    @Autowired
//    private LuckDrawMapper luckDrawMapper;
//
//    @Autowired
//    private OrikaBeanMapper orikaBeanMapper;
//
//    @Autowired
//    private OssApi ossApi;
//
//    @Autowired
//    private RedissonClient redissonClient;
//
//
//    @Value("${activity.id:0}")
//    private String activityId;
//
//
//    /**
//     * 抽奖接口
//     * @return
//     */
//    @PostMapping("/userGet")
//    public Result<ActivityUserRecordDto> getPrize(@RequestBody PrizeDto paramDto) throws InterruptedException {
//        String requestId = UUID.randomUUID().toString().replace("-", "");
//        WxUserVo user = getUser();
//        log.info("请求id==>{},用户{}抽奖开始==>{}",requestId,JSON.toJSONString(user),JSON.toJSONString(paramDto));
//        LocalDateTime now = LocalDateTime.now();
//        if(paramDto.getTime()!=null){
//            now = paramDto.getTime();
//        }
//        Long id = paramDto.getId();
//        if(id==null){
//            log.info("请求id==>{},用户抽奖活动信息为空",requestId);
//            throw new BusinessException(ResultCodeEnum.CURRENR_NO_ACTIVITY_REPEAT);
//        }
//        StopWatch stopWatch=new StopWatch();
//        stopWatch.start("查询活动");
//        LuckDraw luckDraw = luckDrawMapper.selectById(id);
//        stopWatch.stop();
//        log.info("请求id==>{},用户抽奖活动信息==>{}",requestId,JSON.toJSONString(luckDraw));
//        if(luckDraw==null){
//            log.info("请求id==>{},用户抽奖活动信息为空",requestId);
//            throw new BusinessException(ResultCodeEnum.CURRENR_NO_ACTIVITY_REPEAT);
//        }
//        if(luckDraw.getStartTime().isAfter(now)){//未开始
//            log.info("请求id==>{},用户抽奖活动未生效",requestId,JSON.toJSONString(luckDraw));
//            throw new BusinessException(ResultCodeEnum.CURRENR_NO_ACTIVITY_REPEAT);
//        }
//        if(luckDraw.getEndTime().isBefore(now)){//已结束
//            log.info("请求id==>{},用户抽奖活动已结束",requestId,JSON.toJSONString(luckDraw));
//            throw new BusinessException(ResultCodeEnum.CURRENR_NO_REPEAT);
//        }
//        //如果在23:50-23:59 不可中奖
//        LocalTime nowTime = now.toLocalTime();
//        LocalTime parse = LocalTime.parse("23:50:00");
//        if(!nowTime.isBefore(parse)){//不在23:50之前,不能中奖
//            log.info("请求id==>{},此时间段内,用户不可中奖==>{}",requestId,JSON.toJSONString(user));
//            stopWatch.start("未中奖保存");
//            ActivityUserRecordDto dto = activityPrizeService.noPrize(luckDraw, paramDto, user, now, requestId);
//            stopWatch.stop();
//            return Result.success(dto);
//        }
//        Integer numberId = user.getNumberId();
//        if(paramDto.getMemberId()!=null){
//            numberId = paramDto.getMemberId().intValue();
//            user.setNumberId(numberId);
//        }
//        //根据会员id抽奖
//        String lockKey = BcRedisKeyEnum.APPLETS_PRIZE_MEMBERID.keyBuilder(numberId+"");
//        RLock lock = redissonClient.getLock(lockKey);
//        boolean tryLock = lock.tryLock(0, 3, TimeUnit.SECONDS);
//        try {
//            if (tryLock) {
//                stopWatch.start("查询抽奖流水表");
//                ActivityUserRecord activityUserRecord = activityUserService.getOne(new QueryWrapper<ActivityUserRecord>().eq("number_id", numberId).eq("type","1").eq("activity_id",paramDto.getId()));
//                stopWatch.stop();
//                if(activityUserRecord!=null){
//                    log.info("请求id==>{},该用户已经抽奖==>{}",requestId,numberId);
//                    throw new BusinessException(ResultCodeEnum.USER_PRIZE_EXISTENT);
//                }else {//未抽奖，开始抽奖
//                    //1.随机一次，是否中奖（50%）。
//                    if (new Random().nextInt(2)==0) {//0，去摇奖  1，不去抽奖未中
//                        log.info("请求id==>{},该用户未中奖==>{}",requestId,JSON.toJSONString(user));
//                        stopWatch.start("未中奖保存");
//                        ActivityUserRecordDto dto = activityPrizeService.noPrize(luckDraw, paramDto, user, now, requestId);
//                        stopWatch.stop();
//                        return Result.success(dto);
//                    }else {
//                        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//                        //2.根据当前时间，查询当天的奖池记录（优化放入redis）
//                        String format = dateTimeFormatter.format(now);
//                        String key = BcRedisKeyEnum.PRIZE_POOL_DAY_KEY.keyBuilder(paramDto.getId()+":"+format.split(" ")[0]);
//                        log.info("请求id==>{},读取缓存key==>{}",requestId,key);
//                        stopWatch.start("从redis读取pool");
//                        Object values = RedisUtil.get(key);
//                        stopWatch.stop();
//                        List<LuckDrawPool> listByTime = Lists.newArrayList();
//                        if(values!=null){
//                            listByTime = JSON.parseArray(values.toString(),LuckDrawPool.class);
//                            log.info("请求id==>{},走缓存==>{}",requestId,listByTime.size());
//                        }else {
//                            stopWatch.start("查询"+dateTimeFormatter.format(now)+"奖池数据");
//                            listByTime = luckDrawPoolMapper.getListByTime(paramDto.getId()+"",dateTimeFormatter.format(now).split(" ")[0]);
//                            stopWatch.stop();
//                            if(!CollectionUtils.isEmpty(listByTime)){
//                                //放入缓存,截止到当天结束
//                                RedisUtil.setEx(key,JSON.toJSONString(listByTime), Duration.between(now,now.toLocalDate().plusDays(1).atStartOfDay()).getSeconds());
//                                log.info("请求id==>{},放入缓存==>{}",requestId,listByTime.size());
//                            }
//                        }
//                        if(CollectionUtils.isEmpty(listByTime)){//奖品没有了
//                            log.info("请求id==>{},奖品没了,该用户未中奖==>{}",requestId,JSON.toJSONString(user));
//                            stopWatch.start("未中奖保存");
//                            ActivityUserRecordDto dto = activityPrizeService.noPrize(luckDraw, paramDto, user, now, requestId);
//                            stopWatch.stop();
//                            return Result.success(dto);
//                        }
//                        List<LuckDrawPool> prizePool=Lists.newArrayList();
//                        //3.解密奖品时间
//                        for (LuckDrawPool luckDrawPool : listByTime) {
//                            if(!LocalDateTime.parse(Base64Utils.decode(luckDrawPool.getEncryReleaseTime()),dateTimeFormatter).isAfter(now)){//判断时间
//                                prizePool.add(luckDrawPool);
//                            }
//                        }
//                        if(CollectionUtils.isEmpty(prizePool)){
//                            log.info("请求id==>{},该时间段奖品没了,该用户未中奖==>{}",requestId,JSON.toJSONString(user));
//                            stopWatch.start("未中奖保存");
//                            ActivityUserRecordDto dto = activityPrizeService.noPrize(luckDraw, paramDto, user, now, requestId);
//                            stopWatch.stop();
//                            return Result.success(dto);
//                        }
//                        //3.随机一个奖品
//                        int i = new Random().nextInt(prizePool.size());
//                        LuckDrawPool luckDrawPool = prizePool.get(i);
//                        //4.抢奖品
//                        LuckDrawPool prize = new LuckDrawPool();
//                        prize.setId(luckDrawPool.getId());
//                        prize.setGetTime(now);
//                        prize.setNumberId(numberId);
//                        prize.setPrizeNum(0);
//                        stopWatch.start("中奖保存");
//                        Result<ActivityUserRecordDto> activityUserRecordDtoResult = activityPrizeService.updatePoolAndSaveRecord(luckDraw, luckDrawPool, prize, paramDto, user, now, requestId);
//                        stopWatch.stop();
//                        return activityUserRecordDtoResult;
//                    }
//                }
//            }else {
//                log.info("请求id==>{},该用户抽奖,未获取到锁==>{}",requestId,numberId);
//                throw new BusinessException(ResultCodeEnum.USER_PRIZE_REPEAT);
//            }
//        }finally {
//            if(tryLock&&lock.isLocked()){
//                lock.unlock();
//            }
//            log.info("请求id==>{},抽奖接口执行完毕,总耗时==>{},耗时详情==>{}",requestId,stopWatch.getTotalTimeMillis(),stopWatch.prettyPrint());
//        }
//    }
//
//
//    /**
//     * 是否保存收奖地址
//     * @return
//     */
//    @PostMapping("/isSaveAddress")
//    public Result<Boolean> isSaveAddress(){
//        String requestId = UUID.randomUUID().toString().replace("-", "");
//        WxUserVo user = getUser();
//        Integer numberId = user.getNumberId();
//        //根据numberId查询抽奖流水表，判断是否中奖
//        log.info("请求id==>{},是否保存收奖地址,入参==>{}",requestId,numberId);
//        ActivityPrizeRecordDto prizeInfo = activityUserService.getPrizeInfo(numberId,1);
//        log.info("请求id==>{},是否保存收奖地址,出参==>{}",requestId,JSON.toJSONString(prizeInfo));
//        if(prizeInfo!=null){
//            //中奖且收货手机为空
//            return Result.success(prizeInfo.getIsWin()==1&& StringUtils.isEmpty(prizeInfo.getUserMobile()));
//        }
//        return Result.success(false);
//    }
//
//
//    /**
//     * 保存收奖地址
//     * @return
//     */
//    @PostMapping("/saveAddress")
//    public Result<Boolean> saveAddress(@RequestBody ActivityPrizeAddressDto activityPrizeAddressDto){
//        String requestId = UUID.randomUUID().toString().replace("-", "");
//        log.info("请求id==>{}保存收奖地址,入参==>{}",requestId,JSON.toJSONString(activityPrizeAddressDto));
//        ActivityPrizeRecord map = orikaBeanMapper.map(activityPrizeAddressDto, ActivityPrizeRecord.class);
//        map.setId(GenerationIdUtil.getId());
//        map.setRecordId(activityPrizeAddressDto.getPrizeId());
//        map.setGetTime(LocalDateTime.now());
//        //先查
//        ActivityPrizeRecord record_id = activityPrizeService.getOne(new QueryWrapper<ActivityPrizeRecord>().eq("record_id", activityPrizeAddressDto.getPrizeId()));
//        if(record_id!=null){
//            log.info("请求id==>{}抽奖记录{},已经保存收奖地址",requestId, activityPrizeAddressDto.getPrizeId());
//            return Result.success(false);
//        }
//        boolean save =false;
//        try{
//             save = activityPrizeService.save(map);
//        }catch (Exception e){//有唯一索引
//            log.info("请求id==>{}保存收奖地址异常==>{}",requestId, e);
//            return Result.success(false);
//        }
//        if(save){
//            log.info("请求id==>{}保存收奖地址成功",requestId, JSON.toJSONString(map));
//            return Result.success(true);
//        }
//        return Result.success(false);
//    }
//
//
//
//    /**
//     * 是否已经抽奖
//     * @return
//     */
//    @PostMapping("/isPrize")
//    public Result<ActivityPrizeRecordDto> isPrize(Integer type){
//        String requestId = UUID.randomUUID().toString().replace("-", "");
//        WxUserVo user = getUser();
//        Integer numberId = user.getNumberId();
//        StopWatch stopWatch=new StopWatch();
//        //根据numberId查询抽奖流水表，判断是否中奖，是否有地址
//        log.info("请求id==>{},是否已经抽奖,入参==>{}",requestId,numberId);
//        stopWatch.start("查询用户抽奖信息");
//        ActivityPrizeRecordDto prizeInfo = activityUserService.getPrizeInfo(numberId,1);
//        stopWatch.stop();
//        log.info("请求id==>{},是否已经抽奖,出参==>{}",requestId,JSON.toJSONString(prizeInfo));
//        if(prizeInfo==null){
//            log.info("请求id==>{},是否已经抽奖,未查到抽奖记录",requestId);
//            return Result.success(null);
//        }
//        String key=BcRedisKeyEnum.PRIZE_CONFIG_ITEMSURL_KEY.keyBuilder(activityId);
//        Map<Object, Object> objectObjectMap = RedisUtil.hGetAll(key);
//        if(objectObjectMap!=null){
//            log.info("请求id==>{},是否已经抽奖,奖项的url使用缓存==>{}",requestId,JSON.toJSONString(objectObjectMap));
//            if(objectObjectMap.get(prizeInfo.getWinImgUrl())!=null){
//                prizeInfo.setWinImgUrl(objectObjectMap.get(prizeInfo.getWinImgUrl())+"");
//            }else {
//                stopWatch.start("redis没有查询oss信息");
//                prizeInfo.setWinImgUrl(getOssUrl(prizeInfo.getWinImgUrl()));
//                stopWatch.stop();
//            }
//        }else {
//            stopWatch.start("查询oss信息");
//            prizeInfo.setWinImgUrl(getOssUrl(prizeInfo.getWinImgUrl()));
//            stopWatch.stop();
//        }
//        log.info("请求id==>{},是否已经抽奖耗时详情==>{},总耗时==>{}",requestId,stopWatch.getTotalTimeMillis(),stopWatch.prettyPrint());
//        return  Result.success(prizeInfo);
//    }
//
//    private String getOssUrl(String path) {
//        if (Objects.nonNull(path)) {
//            log.info("请求id==>{},查询当前的活动,调用oss接口,入参==>{}",JSON.toJSONString(path));
//            Result<Map<String, String>> ossResult = ossApi.getDownloadSignedUrlDetail(Arrays.asList(path));
//            log.info("请求id==>{},查询当前的活动,调用oss接口,出参==>{}",JSON.toJSONString(ossResult));
//            ResultUtil.checkFeignResult(ossResult);
//            Map<String, String> ossPathMap = ossResult.getData();
//            if (CollectionUtils.isNotEmpty(ossPathMap.values())) {
//                String url = ossPathMap.get(path);
////                if(StringUtils.isNotEmpty(url)){
////                    String[] param = url.split("\\?")[1].split("&");
////                    String times="";
////                    if(param!=null&&param.length>0){
////                        for (String s : Lists.newArrayList(param)) {
////                            if(s.contains("Expires")){
////                                times = s;
////                                break;
////                            }
////                        }
////                    }
////                }
//                return url;
//            }
//        }
//        return path;
//    }
//
//
//    /**
//     * 奖品轮播图
//     * @return
//     */
//    @PostMapping("/activity")
//    public Result<LuckDrawDto> activity(){
//        String requestId = UUID.randomUUID().toString().replace("-", "");
//        LuckDrawDto drawDto=new LuckDrawDto();
//        WxUserVo user = getUser();
//        //查询当前的活动
//        log.info("请求id==>{},查询当前的活动,入参==>{}",requestId);
//        Long id=null;
//        if (!activityId.equals("0")) {
//            id = Long.parseLong(activityId);
//        }
//        StopWatch stopWatch=new StopWatch();
//        stopWatch.start("查询活动配置");
//        List<LuckDrawDto> luckDrawDtos = luckDrawMapper.selectCurrentActivity(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now()),id);
//        stopWatch.stop();
//        log.info("请求id==>{},查询当前的活动,出参==>{}",requestId,JSON.toJSONString(luckDrawDtos));
//        if(luckDrawDtos!=null){
//            List<LuckDrawItem> luckDrawItems= Lists.newArrayList();
//            for (LuckDrawDto luckDrawDto : luckDrawDtos) {
//                //活动详情
//                drawDto.setId(luckDrawDto.getId());
//                drawDto.setActivityCode(luckDrawDto.getActivityCode());
//                drawDto.setActivityName(luckDrawDto.getActivityName());
//                drawDto.setStartTime(luckDrawDto.getStartTime());
//                drawDto.setEndTime(luckDrawDto.getEndTime());
//                //活动奖品项
//                LuckDrawItem map = orikaBeanMapper.map(luckDrawDto, LuckDrawItem.class);
//                map.setId(luckDrawDto.getItemId());
//                luckDrawItems.add(map);
//            }
//            drawDto.setLuckDrawItems(luckDrawItems);
//            //设置图片url
//            if (!luckDrawItems.isEmpty()) {
//                String key=BcRedisKeyEnum.PRIZE_CONFIG_ITEMSURL_KEY.keyBuilder(activityId);
//                List<String> urls1 = luckDrawItems.stream().filter(o->StringUtils.isNotEmpty(o.getImgUrl())).map(o -> o.getImgUrl()).collect(Collectors.toList());
//                List<String> urls2 = luckDrawItems.stream().filter(o->StringUtils.isNotEmpty(o.getWinImgUrl())).map(o -> o.getWinImgUrl()).collect(Collectors.toList());
//                urls1.addAll(urls2);
//                Map<Object, Object> objectObjectMap = RedisUtil.hGetAll(key);
//                if(objectObjectMap!=null&&objectObjectMap.size()>0){
//                    log.info("请求id==>{},查询当前的活动,奖项的url使用缓存==>{}",requestId,JSON.toJSONString(objectObjectMap));
//                    for (LuckDrawItem luckDrawItem : luckDrawItems) {
//                        if(objectObjectMap.get(luckDrawItem.getImgUrl())!=null){
//                            luckDrawItem.setImgUrl(objectObjectMap.get(luckDrawItem.getImgUrl())+"");
//                        }else {
//                            stopWatch.start("redis没有查询oss信息");
//                            luckDrawItem.setImgUrl(getOssUrl(luckDrawItem.getImgUrl()));
//                            stopWatch.stop();
//                        }
//                    }
//                }else {
//                    // 批量设置url
//                    stopWatch.start("查询oss配置");
//                    log.info("请求id==>{},查询当前的活动,调用oss接口,入参==>{}",requestId,JSON.toJSONString(urls1));
//                    Result<Map<String, String>> ossResult = ossApi.getDownloadSignedUrlDetail(urls1);
//                    log.info("请求id==>{},查询当前的活动,调用oss接口,出参==>{}",requestId,JSON.toJSONString(ossResult));
//                    stopWatch.stop();
//                    ResultUtil.checkFeignResult(ossResult);
//                    Map<String, String> ossPathMap = ossResult.getData();
//                    if (CollectionUtils.isNotEmpty(ossPathMap.values())) {
//                        for (LuckDrawItem luckDrawItem : luckDrawItems) {
//                            luckDrawItem.setImgUrl(ossPathMap.get(luckDrawItem.getImgUrl()));
//                        }
//                        //放入缓存
//                        RedisUtil.hPutAll(key,ossPathMap);
//                        RedisUtil.expire(key,1800L);//保存半个小时
//                        log.info("请求id==>{},查询当前的活动,奖项的url放入缓存",requestId);
//                    }
//                }
//            }
//            log.info("请求id==>{},查询当前的活动,总耗时==>{},耗时详情==>{}",requestId,stopWatch.getTotalTimeMillis(),stopWatch.prettyPrint());
//            return  Result.success(drawDto);
//        }else {
//            throw new BusinessException(ResultCodeEnum.CURRENR_NO_REPEAT);
//        }
//    }
//
//
//
//    private void supplementOssPath(LuckDrawItem drawDto,String path) {
//        if (Objects.nonNull(path)) {
//            log.info("请求id==>{},调用oss接口,入参==>{}",JSON.toJSONString(path));
//            Result<Map<String, String>> ossResult = ossApi.getDownloadSignedUrlDetail(Arrays.asList(path));
//            log.info("请求id==>{},调用oss接口,出参==>{}",JSON.toJSONString(ossResult));
//            ResultUtil.checkFeignResult(ossResult);
//            Map<String, String> ossPathMap = ossResult.getData();
//            if (CollectionUtils.isNotEmpty(ossPathMap.values())) {
//                drawDto.setImgUrl(ossPathMap.get(path));
//            }
//        }
//    }
//
//
//    public static void main(String[] args) {
//        LocalTime parse = LocalTime.parse("23:50:00");
//        LocalDateTime now = LocalDateTime.now();
//        LocalTime localTime = now.toLocalTime();
//        System.out.println(localTime.isBefore(parse));
//    }
//}
