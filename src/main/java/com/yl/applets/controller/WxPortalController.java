package com.yl.applets.controller;

import com.yl.applets.builder.TextBuilder;
import com.yl.applets.config.WxMpProperties;
import com.yl.applets.miniapp.service.MyWXService;
import com.yl.common.base.annotation.log.operation.ExcludeInterceptor;
import com.yl.common.base.model.vo.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpMessageRouter;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> Wang(https://github.com/binarywang)
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/portal")
public class WxPortalController {
    private final WxMpService wxService;
    private final WxMpMessageRouter messageRouter;

    final String submsg = "[GoForIt]\nWelcome 终于等到你！\n微信菜单可以一键下单查件啦\n请保持关注J&T 极兔速递，\n\uD83C\uDF81惊喜正在送达...\uD83C\uDF81";

    @Autowired
    private WxMpProperties wxMpProperties;

    @Autowired
    private MyWXService myWxService;

    @GetMapping(produces = "text/plain;charset=utf-8")
    public String authGet(
                          @RequestParam(name = "signature", required = false) String signature,
                          @RequestParam(name = "timestamp", required = false) String timestamp,
                          @RequestParam(name = "nonce", required = false) String nonce,
                          @RequestParam(name = "echostr", required = false) String echostr) {

        log.info("\n接收到来自微信服务器的认证消息：[{}, {}, {}, {}]", signature,
            timestamp, nonce, echostr);
        if (StringUtils.isAnyBlank(signature, timestamp, nonce, echostr)) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }
        String appId = wxMpProperties.getAppId();

        if (!this.wxService.switchover(appId)) {
            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appId));
        }

        if (wxService.checkSignature(timestamp, nonce, signature)) {
            return echostr;
        }

        return "非法请求";
    }

    @PostMapping(produces = "application/xml; charset=UTF-8")
    public String post(
                       @RequestBody String requestBody,
                       @RequestParam("signature") String signature,
                       @RequestParam("timestamp") String timestamp,
                       @RequestParam("nonce") String nonce,
                       @RequestParam("openid") String openid,
                       @RequestParam(name = "encrypt_type", required = false) String encType,
                       @RequestParam(name = "msg_signature", required = false) String msgSignature) {
        log.info("\n接收微信请求：[openid=[{}], [signature=[{}], encType=[{}], msgSignature=[{}],"
                + " timestamp=[{}], nonce=[{}], requestBody=[\n{}\n] ",
            openid, signature, encType, msgSignature, timestamp, nonce, requestBody);

        String appId = wxMpProperties.getAppId();

        if (!this.wxService.switchover(appId)) {
            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appId));
        }

        if (!wxService.checkSignature(timestamp, nonce, signature)) {
            throw new IllegalArgumentException("非法请求，可能属于伪造的请求！");
        }

        String out = null;
        if (encType == null) {
            // 明文传输的消息
            WxMpXmlMessage inMessage = WxMpXmlMessage.fromXml(requestBody);
            if (inMessage.getMsgType().equals(WxConsts.XmlMsgType.EVENT) ) {
                if(WxConsts.EventType.SUBSCRIBE.equals(inMessage.getEvent())){
                    return new TextBuilder().build(submsg, inMessage, wxService).toXml();
                }
            }

            if (!inMessage.getMsgType().equals(WxConsts.XmlMsgType.TEXT) ) {
                return "";
            }
            String JT = "JT";
            String content = inMessage.getContent().toUpperCase();
            if(!content.startsWith(JT)){
                content = JT.concat(content);
            }
            if(content.length() != 15){
                return "";
            }
            if(isContainChinese(content)){
                return "";
            }

            inMessage.setContent(content);
            WxMpXmlOutMessage outMessage = this.route(inMessage);
            if (outMessage == null) {
                return "";
            }

            out = outMessage.toXml();
        } else if ("aes".equalsIgnoreCase(encType)) {
            // aes加密的消息
            WxMpXmlMessage inMessage = WxMpXmlMessage.fromEncryptedXml(requestBody, wxService.getWxMpConfigStorage(),
                timestamp, nonce, msgSignature);
            log.debug("\n消息解密后内容为：\n{} ", inMessage.toString());
            WxMpXmlOutMessage outMessage = this.route(inMessage);
            if (outMessage == null) {
                return "";
            }

            out = outMessage.toEncryptedXml(wxService.getWxMpConfigStorage());
        }

        log.debug("\n组装回复信息：{}", out);
        return out;
    }

    @GetMapping("/wxMessage")
    @ExcludeInterceptor
    public String check(HttpServletRequest request) {
        String requestId = UUID.randomUUID().toString().replace("-","");
        return myWxService.checkSignature(request,requestId);
    }


    @PostMapping("/wxMessage")
    @ExcludeInterceptor
    public String wxMessage(@RequestBody String requestBody, HttpServletRequest request) {
        return myWxService.wxMessage(request,requestBody,messageRouter);
    }

    @GetMapping("/historyUser")
    @ExcludeInterceptor
    public String historyUser(@RequestParam("checkToken")String checkToken,@RequestParam("nextOpenid")String nextOpenid) {
        myWxService.historyUser(checkToken,nextOpenid);
        return "";
    }

    @GetMapping("/fixUser")
    @ExcludeInterceptor
    public Result fixUser(@RequestParam("checkToken")String checkToken, @RequestParam("nextOpenid")String nextOpenid) {
        myWxService.fixUser(checkToken,nextOpenid);
        return Result.success();
    }

    private WxMpXmlOutMessage route(WxMpXmlMessage message) {
        try {
            return this.messageRouter.route(message);
        } catch (Exception e) {
            log.error("路由消息时出现异常！", e);
        }

        return null;
    }


    public static boolean isContainChinese(String str) {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        }
        return false;
    }

    @PostMapping("/getWxConfig")
    @ExcludeInterceptor
    public Result getWxConfig(@RequestParam("url") String url) throws WxErrorException {
        return Result.success(myWxService.createJsapiSignature(url));
    }

}
