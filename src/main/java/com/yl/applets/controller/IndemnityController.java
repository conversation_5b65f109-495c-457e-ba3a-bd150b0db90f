package com.yl.applets.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yl.applets.dto.indemnity.AddRecordDTO;
import com.yl.applets.dto.indemnity.IndemnityDetailVO;
import com.yl.applets.dto.indemnity.IndemnityVO;
import com.yl.applets.dto.indemnity.SaveIndemnityDTO;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.IndemnityFeignClient;
import com.yl.applets.service.IndemnityService;
import com.yl.applets.utils.BcRedisKeyEnum;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.CollectionUtils;
import com.yl.common.base.util.StringUtils;
import com.yl.common.base.util.YlPreconditions;
import com.yl.redis.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
 * 云路供应链科技有限公司 版权所有 @Copyright 2022
 *
 * @Description：理赔中心
 * @Author： zhanzhihong
 * @Date： 2023/01/09
 */
@Slf4j
@RequestMapping("/indemnity")
@RestController
@Api(value = "理赔中心", tags = {"理赔中心"})
public class IndemnityController extends AppBaseController{

    @Autowired
    private IndemnityService indemnityService;

    @Autowired
    private IndemnityFeignClient indemnityFeignClient;




    @ApiOperation(value = "理赔工单登记",notes = "理赔工单登记")
    @PostMapping("/saveIndemnity")
    public Result saveIndemnity(@RequestBody @Valid SaveIndemnityDTO dto){
        log.info("理赔工单登记入参：{}", JSON.toJSONString(dto));
        WxUserVo user = getUser();
        String name=Objects.equals(user.getNickName(), "极兔用户") ? user.getNickName() : new String(Base64.decodeBase64(user.getNickName()), StandardCharsets.UTF_8);
        dto.setCustomerName(StringUtils.isEmpty(name)?"极兔用户":name);
        dto.setWxUserId(user.getNumberId());
        if(StringUtil.equals(dto.getEditOrAdd(),"1")){//新增
            if(StringUtils.isEmpty(dto.getVerificationCode())){
                throw new BusinessException("验证码不能为空");
            }else {
                //校验验证码
                String key = BcRedisKeyEnum.APPLETS_VERIFYCODE_INDEMNITY.keyBuilder(dto.getCustomerPhone());
                YlPreconditions.checkArgument(dto.getVerificationCode().equals(RedisUtil.get(key)), new ServiceException(ServiceErrCodeEnum.VERIFICATIONCODE_ERROR));
                RedisUtil.delete(key);
            }
        }else {
            //鉴权,编辑的时候,数据是从大数据来的
            List<IndemnityVO> voList = indemnityService.bgApiGetData(user);
            if (CollectionUtils.isEmpty(voList))   throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_ERROR);
            //判断voList是否包含dto中的运单号,返回一个boolean
            boolean match = voList.stream().noneMatch(item -> item.getWaybillNo().equals(dto.getWaybillNo()));
            if(match){
                throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_ERROR);
            }
        }
        log.info("理赔工单登记,远程入参：{}", JSON.toJSONString(dto));
        Result result = indemnityFeignClient.saveIndemnity(dto);
        log.info("理赔工单登记,远程出参：{}", JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "工单查询接口",notes = "工单查询接口")
    @GetMapping("/wxQueryByUserId")
    public Result<List<IndemnityVO>> wxQueryByUserId(@ApiParam(value = "状态 2:处理中，3:已关闭") @RequestParam(value = "status",required = false) Integer status){
        WxUserVo user = getUser();
        return indemnityService.wxQueryByUserId(user,status);
    }

    @ApiOperation(value = "工单详情查询",notes = "工单详情查询")
    @GetMapping("/wxQueryByWorkOrderId")
    public Result<IndemnityDetailVO> wxQueryByWorkOrderId(@ApiParam(value = "工单id")@RequestParam(value = "workOrderId")Long workOrderId){
        WxUserVo user = getUser();
        log.info("工单详情查询入参：{} 用户信息：{}", JSON.toJSONString(workOrderId), JSON.toJSONString(user));
        Result<IndemnityDetailVO> indemnityDetailVOResult = indemnityFeignClient.wxQueryByWorkOrderId(workOrderId);
        log.info("工单详情查询出参：{}", JSON.toJSONString(indemnityDetailVOResult));
        if (indemnityDetailVOResult != null && indemnityDetailVOResult.isSucc() && indemnityDetailVOResult.getData() != null &&
                StrUtil.equals(indemnityDetailVOResult.getData().getCustomerPhone(), user.getMobile())){
            return indemnityDetailVOResult;
        }
        return Result.error(ResultCodeEnum.DATA_NOT_FOUND);
    }

    @ApiOperation(value = "工单添加回复内容和附件",notes = "工单添加回复内容和附件")
    @PostMapping("/wxAddRecord")
    public Result wxAddRecord(@RequestBody AddRecordDTO dto){

        WxUserVo user = getUser();
        log.info("工单添加回复内容和附件入参：{} 用户信息：{}", JSON.toJSONString(dto), JSON.toJSONString(user));
        Result<IndemnityDetailVO> indemnityDetailVOResult = indemnityFeignClient.wxQueryByWorkOrderId(dto.getWorkOrderId());
        log.info("wxAddRecord工单详情查询出参：{}", JSON.toJSONString(indemnityDetailVOResult));
        if (indemnityDetailVOResult != null && indemnityDetailVOResult.isSucc() && indemnityDetailVOResult.getData() != null &&
                StrUtil.equals(indemnityDetailVOResult.getData().getCustomerPhone(), user.getMobile())) {
            Result result = indemnityFeignClient.wxAddRecord(dto);
            log.info("工单添加回复内容和附件出参：{}", JSON.toJSONString(result));
            return result;
        }
        return null;
    }

    @ApiOperation(value = "工单添加评价（满意度）", notes = "工单添加评价（满意度）")
    @PostMapping("/wxAddEvaluate")
    public Result wxAddEvaluate(@RequestBody AddRecordDTO dto) {
        WxUserVo user = getUser();
        log.info("工单添加评价（满意度）入参：{} 用户信息：{}", JSON.toJSONString(dto), JSON.toJSONString(user));
        Result<IndemnityDetailVO> indemnityDetailVOResult = indemnityFeignClient.wxQueryByWorkOrderId(dto.getWorkOrderId());
        log.info("wxAddEvaluate工单详情查询出参：{}", JSON.toJSONString(indemnityDetailVOResult));
        if (indemnityDetailVOResult != null && indemnityDetailVOResult.isSucc() && indemnityDetailVOResult.getData() != null &&
                StrUtil.equals(indemnityDetailVOResult.getData().getCustomerPhone(), user.getMobile())) {
            Result result = indemnityFeignClient.wxAddEvaluate(dto);
            log.info("工单添加评价（满意度）出参：{}", JSON.toJSONString(result));
            return result;
        }
        return null;
    }

}
