package com.yl.applets.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yl.applets.annotation.SmsAccessLimit;
import com.yl.applets.constant.OmsWaybillApiConstant;
import com.yl.applets.dto.*;
import com.yl.applets.dto.lmdm.SysStaffDTO;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.*;
import com.yl.applets.service.IWaybillService;
import com.yl.applets.utils.*;
import com.yl.applets.vo.*;
import com.yl.applets.vo.lmdm.SysStaffVO;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.regex.Pattern;

import static com.yl.applets.controller.LogisticsTrackingController.WAYBILL_MD5_KEY_ALIPAY;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description: 运单管理
 * @Project:
 * @CreateDate: Created in 2019-08-05 19:09 <br>
 * @Author: zhipeng.liu
 */
@Api(value = "运单", tags = {"运单"})
@RestController
@RequestMapping("/waybillOrder")
@Slf4j
public class WaybillController extends AppBaseController {

    @Autowired
    private IWaybillService waybillService;

    @Autowired
    private OldLmdmFeignClient sysStaffFeignClient;

    @Resource
    private OrikaBeanMapper orikaBeanMapper;

    final String YD_PREFIX = "JT";

    static final String SHARE_PUBLIC_KEY = "share&query&key";

    static final String WAYBILL_MD5_KEY = "hsaNJj@waybill12";

    static final String WAYBILL_MD5_KEY_2 = "hsaNJasdhkj@ill12";

    @Autowired
    private OpsOuterFeigntClient opsOuterFeigntClient;

    @Autowired
    private OrderFeigntClient orderFeigntClient;

    private final String regex="^[a-zA-Z0-9]+$";
    @Autowired
    private ChannelApiFeignClient channelApiFeignClientl;

    @Autowired
    private WayBillApiFeignClient wayBillApiFeignClient;

    //官网、支付宝、百度、微信、荣耀、小米、招行
    @Value("${waybill.fee.show.sourceCode:D12,D11,D74,D08,D10,D68}")
    private String showSourceCode;

    @Autowired
    private SmsAccessUtils smsAccessUtils;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${waybill.show.stub:true}")
    private Boolean showStub;


    static final String WAYBILL_MD5_KEY_3 = "hsaNJasga@A3112";


    /**
     * 小程序来源-订单来源code
     */
    @Value("#{'${applets.order.source.code:D66,D68,D73,D10,D24,D962,D942,D08,D11,D24,D74,D13,D14,D61,D1133,D908,D855}'.split(',')}")
    private List<String> appletsOrderSourceList;

    /**
     * 根据运单号获取运单明细
     *
     * @param waybillNo
     * @return
     */
    @GetMapping(value = "/getDetailByNo")
    @ApiOperation(value = "根据运单号获取运单明细", notes = "根据运单号获取运单明细")
    public Result getDetailByNo(@RequestParam("waybillNo") String waybillNo) {
        if (StringUtils.isEmpty(waybillNo)) {
            throw new ServiceException(ServiceErrCodeEnum.WAYBILLNO_ISNULL);
        }
        waybillNo = waybillNo.toUpperCase();
        if(!Pattern.matches(regex,waybillNo)){
            throw new BusinessException(ServiceErrCodeEnum.YD_NO_EXEITS);
        }
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id{}，参数：{}",requestId,waybillNo);
        AppOmsWaybillDetailVO appOmsWaybillDetailVO= channelApiFeignClientl.getDetailByNo(waybillNo).result();
        log.info("请求id{}，响应信息：{}",requestId,JSON.toJSONString(appOmsWaybillDetailVO));
        WxUserVo user = getUser();
        //根据配置，判断，是否展示费用
        boolean isShow=false;
        //1.订单来源
        boolean sourceCode = Lists.newArrayList(showSourceCode.split(",")).contains(appOmsWaybillDetailVO.getOrderSourceCode());
        //2.付款方式
        String paidModeCode = appOmsWaybillDetailVO.getSettlementCode();
        //3.当前登录人的身份：
        String mobile = user.getMobile();
        //寄件人
        String senderMobilePhone = appOmsWaybillDetailVO.getOrderSenderMobile();
        boolean a = StringUtils.equals(mobile,senderMobilePhone);
        //收件人
        String receiverMobilePhone = appOmsWaybillDetailVO.getOrderReceiveMobile();
        boolean b = StringUtils.equals(mobile,receiverMobilePhone);
        //下单人
        Integer customerId = appOmsWaybillDetailVO.getMemberId();
        boolean c = user.getNumberId().equals(customerId);

        //寄件人手机号码(带order的是中台返回解密的  当运单为空，就没有order这个字段返回  用最原始的字段)
        String checkSenderMobilePhone = appOmsWaybillDetailVO.getSenderMobilePhone() == null ? appOmsWaybillDetailVO.getSenderTelphone() : appOmsWaybillDetailVO.getSenderMobilePhone();
        //收件人手机号码带order的是中台返回解密的  当运单为空，就没有order这个字段返回  用最原始的字段)
        String checkReceiverMobilePhone = appOmsWaybillDetailVO.getReceiverMobilePhone() == null ? appOmsWaybillDetailVO.getReceiverTelphone() : appOmsWaybillDetailVO.getReceiverMobilePhone();

        String last4 = mobile.substring(mobile.length() - 4);
        log.info("根据运单号获取运单明细,当前登陆人：{} " , JSON.toJSONString(user));
        boolean flag = c || (StringUtils.isNotBlank(checkSenderMobilePhone) && checkSenderMobilePhone.endsWith(last4)) ||
                (StringUtils.isNotBlank(checkReceiverMobilePhone) && checkReceiverMobilePhone.endsWith(last4));
        if (!flag){
            log.warn("当前登录人和运单数据无关联,没有权限查看运单明细");
            throw new BusinessException(ServiceErrCodeEnum.PHONE_VERIFY_ERROR);
        }
        //寄付现结
        if(StringUtils.equals("PP_CASH",paidModeCode)){
            if(a){//寄件人
                isShow = sourceCode;
            }else if(c){//下单人
                isShow = sourceCode;
            }else if(b){//收件人
                isShow = false;
            }
        }
        //到付现结
        if(StringUtils.equals("CC_CASH",paidModeCode)){
            isShow =true;
        }
        //寄付月结
        if(StringUtils.equals("PP_PM",paidModeCode)){
            isShow =false;
        }
        if(isShow){
            //根据运单号，查询电子存根记录
            log.info("请求id{}，远程请求运单接口,入参==>{}",requestId,waybillNo);
            WaybillQueryDto dto=new WaybillQueryDto();
            dto.setWaybillNo(waybillNo);
            Result<WaybillInfoDto> orderCostByWaybillNo = wayBillApiFeignClient.getOrderCostByWaybillNo(dto);
            log.info("请求id{}，远程请求运单接口,出参==>{}",requestId,JSON.toJSONString(orderCostByWaybillNo));
            WaybillInfoDto result = orderCostByWaybillNo.result();
            if(result!=null){
                if(appOmsWaybillDetailVO.getIsNeedReceipt()==0){//签收单 否
                    result.setReceiptFee(null);
                }
                if(appOmsWaybillDetailVO.getInsured()==0){//保价 否
                    result.setInsuredFee(null);
                }
                if(result.getHandicraftFee()==null||result.getHandicraftFee().compareTo(new BigDecimal("0"))==0){//手工费
                    result.setHandicraftFee(null);
                }
                result.setCodMoney(appOmsWaybillDetailVO.getCodMoney());
            }else {
                result = new WaybillInfoDto();
                result.setCodMoney(appOmsWaybillDetailVO.getCodMoney());
                log.info("请求id{}，远程请求运单接口,出参为空",requestId);
            }
            appOmsWaybillDetailVO.setWaybillFee(result);
        }
        //收寄件手机号脱敏
        appOmsWaybillDetailVO.setReceiverMobilePhone(DesensitizationUtils.desensitizeString(appOmsWaybillDetailVO.getReceiverMobilePhone()));
        appOmsWaybillDetailVO.setSenderMobilePhone(DesensitizationUtils.desensitizeString(appOmsWaybillDetailVO.getSenderMobilePhone()));
        appOmsWaybillDetailVO.setIsApplets(appletsOrderSourceList.contains(appOmsWaybillDetailVO.getOrderSourceCode()) ? 1 : 0);
        if(StringUtils.equals(OmsWaybillApiConstant.JFYJ, paidModeCode)) {
            //寄付月结  我的收件 不展示月结账号  我的寄件正常返回
            if ((StringUtils.isNotBlank(appOmsWaybillDetailVO.getReceiverMobilePhone()) && appOmsWaybillDetailVO.getReceiverMobilePhone().endsWith(last4))) {
                appOmsWaybillDetailVO.setCustomerCode(null);
            }
        }
        log.info("请求id{}，运单详情费用接口,返回==>{}",requestId,JSON.toJSONString(appOmsWaybillDetailVO));
        return Result.success(appOmsWaybillDetailVO);
    }


    /**
     * 根据运单号获取运单明细
     *
     * @param waybillNo
     * @return
     */
    @GetMapping(value = "/v2/getDetailByNo")
    @ApiOperation(value = "根据运单号获取运单明细", notes = "根据运单号获取运单明细")
    public Result getDetailByNoV2(@RequestParam("waybillNo") String waybillNo,@RequestParam("times") String times,@RequestParam("sign") String sign, @RequestParam("mobile") String mobile) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("请求id==>{},入参==>waybillNo:{},times:{},sign:{}",requestId,waybillNo,times,sign);
        if (StringUtils.isEmpty(waybillNo) || StringUtils.isEmpty(times) || StringUtils.isEmpty(sign) || StringUtils.isEmpty(mobile) || mobile.length() < 4) {
            throw new ServiceException(ServiceErrCodeEnum.PARAM_ERROR);
        }
        //失败key
        String key = BcRedisKeyEnum.WAYBILL_QUERY_COUNT_PER_MINUTER.keyBuilder(waybillNo);
        if (redisTemplate.opsForValue().get("LOCK:" + key) != null){
            throw new ServiceException(ResultCodeEnum.WAYBILL_PERVIFY_ERROR);
        }

        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(waybillNo+times + WAYBILL_MD5_KEY_2));
        log.info("请求id{}，后端MD5加密之后{}",requestId,sign1);
        if(!sign1.equals(sign)){
            log.info("请求id{},验签失败！",requestId,sign);
            //失败错误次数限流
            smsAccessUtils.increment(key, 10, "getDetailByNo获取运单明细", 60L, 600L);

            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }

        waybillNo = waybillNo.toUpperCase();
        if(!Pattern.matches(regex,waybillNo)){
            throw new BusinessException(ServiceErrCodeEnum.YD_NO_EXEITS);
        }

        log.info("请求id{}，参数：{}",requestId,waybillNo);
        AppOmsWaybillDetailVO appOmsWaybillDetailVO= channelApiFeignClientl.getDetailByNo(waybillNo).result();
        log.info("请求id{}，响应信息：{}",requestId,JSON.toJSONString(appOmsWaybillDetailVO));


        //寄件人手机号码(带order的是中台返回解密的  当运单为空，就没有order这个字段返回  用最原始的字段)
        String checkSenderMobilePhone = appOmsWaybillDetailVO.getSenderMobilePhone() == null ? appOmsWaybillDetailVO.getSenderTelphone() : appOmsWaybillDetailVO.getSenderMobilePhone();
        //收件人手机号码带order的是中台返回解密的  当运单为空，就没有order这个字段返回  用最原始的字段)
        String checkReceiverMobilePhone = appOmsWaybillDetailVO.getReceiverMobilePhone() == null ? appOmsWaybillDetailVO.getReceiverTelphone() : appOmsWaybillDetailVO.getReceiverMobilePhone();

        boolean flag = (StringUtils.isNotBlank(checkSenderMobilePhone) && checkSenderMobilePhone.endsWith(mobile)) ||
                (StringUtils.isNotBlank(checkReceiverMobilePhone) && checkReceiverMobilePhone.endsWith(mobile));

        if (!flag) {
            log.warn("免登录和运单数据无关联,没有权限查看运单明细");
            //失败错误次数限流
            smsAccessUtils.increment(key, 10, "getDetailByNo获取运单明细", 60L, 600L);
            throw new BusinessException(ServiceErrCodeEnum.PHONE_VERIFY_ERROR);
        }


        //脱敏
        appOmsWaybillDetailVO.setOrderSenderMobile(StrUtil.isNotBlank(appOmsWaybillDetailVO.getOrderSenderMobile()) ? appOmsWaybillDetailVO.getOrderSenderMobile().replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2") : "");
        appOmsWaybillDetailVO.setOrderReceiveMobile(StrUtil.isNotBlank(appOmsWaybillDetailVO.getOrderReceiveMobile()) ? appOmsWaybillDetailVO.getOrderReceiveMobile().replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2") : "");

        return Result.success(appOmsWaybillDetailVO);
    }


    /**
     * 根据运单号获取运单明细
     *
     * @param waybillNo
     * @return
     */
    @GetMapping(value = "/checkWayBillNo")
    @ApiOperation(value = "校验运单号是否存在", notes = "校验运单号是否存在")
    public Result checkWayBillNoExist(@RequestParam("waybillNo") String waybillNo, @RequestParam("times") String times, @RequestParam("sign") String sign) {
        String requestId = UUID.randomUUID().toString().replace("-", "");
        log.info("【校验运单是否存在】请求id==>{},入参==>waybillNo:{},times:{},sign:{}", requestId, waybillNo, times, sign);
        if (StringUtils.isEmpty(waybillNo) || StringUtils.isEmpty(times) || StringUtils.isEmpty(sign)) {
            throw new ServiceException(ServiceErrCodeEnum.WAYBILLNO_ISNULL);
        }
        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(waybillNo + times + WAYBILL_MD5_KEY_2));
        log.info("【校验运单是否存在】请求id{}，后端MD5加密之后{}", requestId, sign1);
        if (!sign1.equals(sign)) {
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }
        waybillNo = waybillNo.toUpperCase();
        if (!Pattern.matches(regex, waybillNo)) {
            throw new BusinessException(ServiceErrCodeEnum.YD_NO_EXEITS);
        }

        log.info("【校验运单是否存在】请求id{}，参数：{}", requestId, waybillNo);
        Boolean result = channelApiFeignClientl.checkWayBillNoExist(waybillNo).result();
        log.info("【校验运单是否存在】请求id{}，响应信息：{}", requestId, result);

        return Result.success(result);
    }


    /**
     * 根据运单号获取运单明细
     * - 需要登录，判断当前登录人手机号和运单手机号不一致。
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/getVerifyDetail")
    @ApiOperation(value = "根据运单号获取运单明细", notes = "根据运单号获取运单明细")
    public Result getVerifyDetailByNo(@RequestBody WaybillQueryDto dto) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id{}，参数：{}",requestId,JSON.toJSONString(dto));
        String waybillNo=dto.getWaybillNo();
        String phoneVerify=dto.getPhoneVerify();
        if (StringUtils.isEmpty(waybillNo)) {
            throw new ServiceException(ServiceErrCodeEnum.WAYBILLNO_ISNULL);
        }
        if (StringUtils.isEmpty(phoneVerify)) {
            throw new ServiceException(ServiceErrCodeEnum.PHONE_VERIFY_IS_NULL);
        }
        //失败key
        String key = BcRedisKeyEnum.WAYBILL_QUERY_COUNT_PER_MINUTER.keyBuilder(dto.getWaybillNo());
        if (redisTemplate.opsForValue().get("LOCK:" + key) != null){
            throw new ServiceException(ResultCodeEnum.WAYBILL_PERVIFY_ERROR);
        }

        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(waybillNo +phoneVerify+ dto.getTimes() + WAYBILL_MD5_KEY));
        log.info("请求id{}，后端MD5加密之后{}",requestId,sign1);
        if(!sign1.equals(dto.getSign())){
            log.info("请求id{},验签失败！",requestId,dto.getSign());
            //失败错误次数限流
            smsAccessUtils.increment(key, 10, "getVerifyDetail查询运单号", 60L, 600L);
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }
        waybillNo = waybillNo.toUpperCase();
        if(!Pattern.matches(regex,waybillNo)){
            throw new BusinessException(ServiceErrCodeEnum.YD_NO_EXEITS);
        }
        log.info("请求id{},查询订单/运单详情,参数{}",requestId,waybillNo);
//        AppOmsWaybillDetailVO  appOmsWaybillDetailVO= getAppOmsWaybillDetailVO(waybillNo);
        AppOmsWaybillDetailVO appOmsWaybillDetailVO= channelApiFeignClientl.getDetailByNo(waybillNo).result();
        log.info("请求id{},查询订单/运单详情出参：{}",requestId,JSON.toJSONString(appOmsWaybillDetailVO));
        WxUserVo user = getUser();
        String receiverMobilePhone = appOmsWaybillDetailVO.getReceiverMobilePhone();
        String senderMobilePhone = appOmsWaybillDetailVO.getSenderMobilePhone();

        String mobile = user.getMobile();

        String last4 = mobile.substring(mobile.length() - 4);
        if((StringUtils.isNotBlank(receiverMobilePhone) && receiverMobilePhone.endsWith(last4))
                || (StringUtils.isNotBlank(senderMobilePhone) && senderMobilePhone.endsWith(last4))){
            log.info("请求id{},接口返回参数{}",requestId,JSON.toJSONString(appOmsWaybillDetailVO));
            return Result.success(appOmsWaybillDetailVO);
        }else{
            log.warn("请求id{},接口提示{}",requestId,JSON.toJSONString(ServiceErrCodeEnum.PHONE_VERIFY_ERROR));
            //失败错误次数限流
            smsAccessUtils.increment(key, 10, "getVerifyDetail查询运单号", 60L, 600L);
            return Result.error(ServiceErrCodeEnum.USER_MOBILE_ERROR);
        }
    }


    /**
     * 根据运单号获取运单明细
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/v2/getVerifyDetail")
    @ApiOperation(value = "根据运单号获取运单明细", notes = "根据运单号获取运单明细")
    public Result getVerifyDetailByNoV2(@RequestBody(required = false) WaybillQueryDto dto) {
        if(dto==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }

        //失败key
        String key = BcRedisKeyEnum.WAYBILL_QUERY_COUNT_PER_MINUTER.keyBuilder(dto.getWaybillNo());
        if (redisTemplate.opsForValue().get("LOCK:" + key) != null){
            throw new ServiceException(ResultCodeEnum.WAYBILL_PERVIFY_ERROR);
        }

        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id{}，参数：{}",requestId,JSON.toJSONString(dto));
        String waybillNo=dto.getWaybillNo();
        String phoneVerify=dto.getPhoneVerify();
        if (StringUtils.isEmpty(waybillNo)) {
            throw new ServiceException(ServiceErrCodeEnum.WAYBILLNO_ISNULL);
        }
        if (StringUtils.isEmpty(phoneVerify) || phoneVerify.length() != 4) {
            throw new ServiceException(ServiceErrCodeEnum.PHONE_VERIFY_IS_NULL);
        }
        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(waybillNo +phoneVerify+ dto.getTimes() + WAYBILL_MD5_KEY));
        log.info("请求id{}，后端MD5加密之后{}",requestId,sign1);
        if(!sign1.equals(dto.getSign())){
            log.info("请求id{},验签失败！",requestId,dto.getSign());
            //失败错误次数限流
            smsAccessUtils.increment(key, 10, "v2-getVerifyDetail查询运单号", 60L, 600L);

            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }
        waybillNo = waybillNo.toUpperCase();
        if(!Pattern.matches(regex,waybillNo)){
            throw new BusinessException(ServiceErrCodeEnum.YD_NO_EXEITS);
        }
        log.info("请求id{},查询订单/运单详情,参数{}",requestId,waybillNo);
//        AppOmsWaybillDetailVO  appOmsWaybillDetailVO= getAppOmsWaybillDetailVO(waybillNo);
        AppOmsWaybillDetailVO appOmsWaybillDetailVO= channelApiFeignClientl.getDetailByNo(waybillNo).result();
        log.info("请求id{},查询订单/运单详情出参：{}",requestId,JSON.toJSONString(appOmsWaybillDetailVO));
        String receiverTelphone = appOmsWaybillDetailVO.getReceiverTelphone();
        String senderTelphone = appOmsWaybillDetailVO.getSenderTelphone();
        if((StringUtils.isNotBlank(appOmsWaybillDetailVO.getReceiverMobilePhone()) && appOmsWaybillDetailVO.getReceiverMobilePhone().endsWith(phoneVerify))
                || (StringUtils.isNotBlank(appOmsWaybillDetailVO.getSenderMobilePhone()) && appOmsWaybillDetailVO.getSenderMobilePhone().endsWith(phoneVerify)
                || (StringUtils.isNotBlank(receiverTelphone) && receiverTelphone.endsWith(phoneVerify))
                || (StringUtils.isNotBlank(senderTelphone) && senderTelphone.endsWith(phoneVerify))) ){
            log.info("请求id{},接口返回参数{}",requestId,JSON.toJSONString(appOmsWaybillDetailVO));
            //手机号脱敏
            appOmsWaybillDetailVO.setOrderReceiveMobile(StrUtil.isNotBlank(appOmsWaybillDetailVO.getOrderReceiveMobile()) ? appOmsWaybillDetailVO.getOrderReceiveMobile().replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2") : "");
            appOmsWaybillDetailVO.setOrderSenderMobile(StrUtil.isNotBlank(appOmsWaybillDetailVO.getOrderSenderMobile()) ? appOmsWaybillDetailVO.getOrderSenderMobile().replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2") : "");
            return Result.success(appOmsWaybillDetailVO);
        }else{
            //失败错误次数限流
            smsAccessUtils.increment(key, 10, "getVerifyDetail查询运单号", 60L, 600L);
            log.warn("请求id{},接口提示{}",requestId,JSON.toJSONString(ServiceErrCodeEnum.PHONE_VERIFY_ERROR));
            return Result.error(ServiceErrCodeEnum.PHONE_VERIFY_ERROR);
        }
    }



    /**
     * 根据运单号获取运单明细
     * 不加token，但需要验签
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/share/getDetailByNo")
    @ApiOperation(value = "分享订单-根据运单号获取运单明细", notes = "分享订单-根据运单号获取运单明细")
    public Result shareGetDetailByNo(@RequestBody(required = false) ShareQueryDTO dto) {
        if(dto==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        String waybillNo = dto.getWaybillNo();
        String mobile = dto.getMobile();
        if (StringUtils.isEmpty(waybillNo) || StringUtils.isEmpty(mobile) || mobile.length() < 4) {
            throw new ServiceException(ResultCodeEnum.PARAMS_NOT_COMPLETE);
        }

        //失败key
        String key = BcRedisKeyEnum.WAYBILL_QUERY_COUNT_PER_MINUTER.keyBuilder(waybillNo);
        if (redisTemplate.opsForValue().get("LOCK:" + key) != null){
            throw new ServiceException(ResultCodeEnum.WAYBILL_PERVIFY_ERROR);
        }

        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(waybillNo + dto.getTimes() + SHARE_PUBLIC_KEY));
        log.info("后端MD5加密之后:"+sign1);
        if(!sign1.equals(dto.getSign())){
            //失败错误次数限流
            smsAccessUtils.increment(key, 10, "分享订单-根据运单号获取运单明细", 60L, 600L);
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }
        waybillNo = waybillNo.toUpperCase();
        if(!Pattern.matches(regex,waybillNo)){
            throw new BusinessException(ServiceErrCodeEnum.YD_NO_EXEITS);
        }
        log.info("请求id{},查询订单/运单详情,参数{}",requestId,waybillNo);
        AppOmsWaybillDetailVO appOmsWaybillDetailVO= channelApiFeignClientl.getDetailByNo(waybillNo).result();
        log.info("请求id{},查询订单/运单详情出参：{}",requestId,JSON.toJSONString(appOmsWaybillDetailVO));

        //安全问题解决增加
        String receiverTelphone = appOmsWaybillDetailVO.getReceiverTelphone();
        String senderTelphone = appOmsWaybillDetailVO.getSenderTelphone();
        if((StringUtils.isNotBlank(appOmsWaybillDetailVO.getReceiverMobilePhone()) && appOmsWaybillDetailVO.getReceiverMobilePhone().endsWith(mobile))
                || (StringUtils.isNotBlank(appOmsWaybillDetailVO.getSenderMobilePhone()) && appOmsWaybillDetailVO.getSenderMobilePhone().endsWith(mobile)
                || (StringUtils.isNotBlank(receiverTelphone) && receiverTelphone.endsWith(mobile))
                || (StringUtils.isNotBlank(senderTelphone) && senderTelphone.endsWith(mobile))) ){

            //脱敏
            appOmsWaybillDetailVO.setOrderSenderMobile(StrUtil.isNotBlank(appOmsWaybillDetailVO.getOrderSenderMobile()) ? appOmsWaybillDetailVO.getOrderSenderMobile().replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2") : "");
            appOmsWaybillDetailVO.setOrderReceiveMobile(StrUtil.isNotBlank(appOmsWaybillDetailVO.getOrderReceiveMobile()) ? appOmsWaybillDetailVO.getOrderReceiveMobile().replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2") : "");

            return Result.success(appOmsWaybillDetailVO);

        }
        log.warn("分享订单-根据运单号获取运单明细,没有权限查看运单明细");
        //失败错误次数限流
        smsAccessUtils.increment(key, 10, "分享订单-根据运单号获取运单明细", 60L, 600L);
        throw new BusinessException(ServiceErrCodeEnum.PHONE_VERIFY_ERROR);
    }

    private String getFullDetaillAddress(String arg[]){
        StringBuffer address = new StringBuffer();
        Arrays.stream(arg).forEach(a->{
            if(a!=null){
                address.append(a);
            }
        });
        return address.toString();
    }


    @GetMapping(value = "/getEleSignPicUrl")
    @ApiOperation(value = "根据运单号获取签收图片", notes = "根据运单号获取签收图片")
    public Result getEleSignPicUrl(@RequestParam("waybillNo") String waybillNo) {
        if (Objects.isNull(waybillNo)) {
            throw new ServiceException(ServiceErrCodeEnum.WAYBILLNO_ISNULL);
        }
        return Result.success(opsOuterFeigntClient.getEleSignPicUrl(waybillNo).result());
    }

    @GetMapping("/getNumber")
    public Result<String> getNumber(@RequestParam("waybillNo") String waybillNo, @RequestParam("sign") String sign,
                                     @RequestParam("time") String time) {
        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(waybillNo + time + WAYBILL_MD5_KEY_ALIPAY));
        log.info("[尾号]后端MD5加密之后{}", sign1);
        if (!sign1.equals(sign)) {
            log.info("请求id{},验签失败！", sign);
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }
        log.info("查询订单/运单详情,参数{}", waybillNo);
        AppOmsWaybillDetailVO appOmsWaybillDetailVO = channelApiFeignClientl.getDetailByNo(waybillNo).result();
        log.info("查询订单/运单详情出参：{}", JSON.toJSONString(appOmsWaybillDetailVO));

        String phone = appOmsWaybillDetailVO.getReceiverMobilePhone();

        if (StringUtils.isBlank(phone) || phone.length() < 4) {
            phone = appOmsWaybillDetailVO.getReceiverTelphone();
        }
        if (StrUtil.isBlank(phone) || phone.length() < 4){
            phone = appOmsWaybillDetailVO.getSenderMobilePhone();
        }
        if (StrUtil.isBlank(phone) || phone.length() < 4){
            phone = appOmsWaybillDetailVO.getSenderTelphone();
        }

        String mobilePhone = phone.substring(phone.length() - 4);
        return Result.success(Base64.encodeBase64String(mobilePhone.getBytes(StandardCharsets.UTF_8)));

    }




    /**
     * 登录时,调用查询运单轨迹和详情
     * @param waybillNo
     * @return
     */
    @GetMapping("/getWaybillDetailAndUser")
    public Result<WaybillDetailVo> getWaybillDetailAndUser(@RequestParam("waybillNo")String waybillNo){
        log.info("[登录]运单轨迹详情接口,入参==>{}",waybillNo);
        //失败key
        String key = BcRedisKeyEnum.WAYBILL_QUERY_COUNT_PER_MINUTER.keyBuilder(waybillNo);
        if (redisTemplate.opsForValue().get("LOCK:" + key) != null){
            throw new ServiceException(ResultCodeEnum.WAYBILL_PERVIFY_ERROR);
        }
        Result<WaybillDetailVo> waybillDetail = channelApiFeignClientl.getWaybillDetail(waybillNo);
        log.info("[登录]运单轨迹详情接口,出参==>{}",JSON.toJSONString(waybillDetail));
        WaybillDetailVo result = waybillDetail.result();
        //鉴权
        checkWaybillIsUser(result.getWaybillDetail(),null,key);
        return Result.success(result);
    }


    /**
     * 非登录时,调用查询运单轨迹和详情
     * @param waybillNo
     * @return
     */
    @GetMapping("/getWaybillDetail")
    public Result<WaybillDetailVo> getWaybillDetailNoLogin(@RequestParam("waybillNo")String waybillNo, @RequestParam("phone")String phone,
                                                           @RequestParam("times") String times, @RequestParam("sign") String sign){
        log.info("运单轨迹详情接口,入参==>{}",waybillNo);

        if (StringUtils.isEmpty(waybillNo) || StringUtils.isEmpty(times) || StringUtils.isEmpty(sign) ||  StringUtils.isEmpty(phone)) {
            throw new ServiceException(ServiceErrCodeEnum.WAYBILLNO_ISNULL);
        }
        //失败key
        String key = BcRedisKeyEnum.WAYBILL_QUERY_COUNT_PER_MINUTER.keyBuilder(waybillNo);
        if (redisTemplate.opsForValue().get("LOCK:" + key) != null){
            throw new ServiceException(ResultCodeEnum.WAYBILL_PERVIFY_ERROR);
        }
        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(waybillNo +phone+ times + WAYBILL_MD5_KEY_3));
        log.info("【运单轨迹详情接口】，后端MD5加密之后{}", sign1);
        if (!sign1.equals(sign)) {
            //失败错误次数限流
            smsAccessUtils.increment(key, 10, "运单轨迹详情接口不登录", 60L, 600L);
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }
        waybillNo = waybillNo.toUpperCase();
        if (!Pattern.matches(regex, waybillNo)) {
            throw new BusinessException(ServiceErrCodeEnum.YD_NO_EXEITS);
        }

        Result<WaybillDetailVo> waybillDetail = channelApiFeignClientl.getWaybillDetail(waybillNo);
        log.info("运单轨迹详情接口,出参==>{}",JSON.toJSONString(waybillDetail));
        WaybillDetailVo result = waybillDetail.result();
        //鉴权
        checkWaybillIsUser(result.getWaybillDetail(),phone,key);
        return Result.success(result);
    }

    /**
     * 根据运单号反查明文收寄件人手机号
     * @param waybillNo
     * @return
     */
    @GetMapping("/getMobilePhoneByWaybillNo")
    public Result<MobilePhoneVO> getMobilePhoneByWaybillNo(@RequestParam("waybillNo") String waybillNo) {
        log.info("根据运单号反查明文收寄件人手机号,入参==>{}",waybillNo);
        Result<MobilePhoneVO> result = channelApiFeignClientl.getMobilePhoneByWaybillNo(waybillNo);
        MobilePhoneVO mobilePhoneVO = result.result();
        WxUserVo user = getUser();
        if(showStub && !Objects.isNull(mobilePhoneVO) && !Objects.equals(user.getNumberId(),mobilePhoneVO.getMemberId())){
            return Result.error(ResultCodeEnum.DATA_NOT_FOUND);
        }
        return result;
    }

    /**
     * 校验用户和后四位
     * @param detail
     */
    private void checkWaybillIsUser(AppOmsWaybillDetailVO detail,String phone,String key) {
        WxUserVo user = null;
        try {
            user = getUser();
        }catch (Exception e){
            log.warn("获取当前用户失败,未登录");
        }
        String receiverMobilePhone = StringUtils.isEmpty(detail.getReceiverMobilePhone())?detail.getReceiverTelphone():detail.getReceiverMobilePhone();
        String senderMobilePhone = StringUtils.isEmpty(detail.getSenderMobilePhone())?detail.getSenderTelphone():detail.getSenderMobilePhone();
        Integer memberId = detail.getMemberId();
        //1.登录时
        if(user!=null){
            String mobile = user.getMobile();
            String last4 = mobile.substring(mobile.length() - 4);
            if ((StringUtils.isNotBlank(receiverMobilePhone) && receiverMobilePhone.endsWith(last4))
                    || (StringUtils.isNotBlank(senderMobilePhone) && senderMobilePhone.endsWith(last4))
                    || (Objects.equals(user.getNumberId(), memberId))) {

            }else {
                log.info("登录情况下,手机号后四位不匹配");
                //失败错误次数限流
                smsAccessUtils.increment(key, 10, "运单轨迹详情接口不登录", 60L, 600L);
                throw new ServiceException(ServiceErrCodeEnum.PHONE_VERIFY_ERROR);

            }
        }else {
            //2.未登录时
            if(StringUtils.isNotBlank(phone)){
                if ((StringUtils.isNotBlank(receiverMobilePhone) && receiverMobilePhone.endsWith(phone))
                        || (StringUtils.isNotBlank(senderMobilePhone) && senderMobilePhone.endsWith(phone))) {

                }else {
                    log.info("未登录情况下,手机号后四位不匹配");
                    //失败错误次数限流
                    smsAccessUtils.increment(key, 10, "运单轨迹详情接口不登录", 60L, 600L);
                    throw  new ServiceException(ServiceErrCodeEnum.PHONE_VERIFY_ERROR);
                }
            }else {
                log.info("未登录情况下,后四位为空");
                //失败错误次数限流
                smsAccessUtils.increment(key, 10, "运单轨迹详情接口不登录", 60L, 600L);
                throw  new ServiceException(ServiceErrCodeEnum.PHONE_VERIFY_ERROR);
            }
        }
        //去掉敏感信息
        detail.setIdNo("");
        detail.setOrderReceiveMobile("");
        detail.setOrderSenderMobile("");
    }

}
