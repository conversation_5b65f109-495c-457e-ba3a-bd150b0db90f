package com.yl.applets.controller;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRResponse;
import com.yl.applets.dto.SaveAppletsUserCardDTO;
import com.yl.applets.entity.UserCard;
import com.yl.applets.enums.CustomerCardTypeEnum;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.service.IOCRService;
import com.yl.applets.service.IUserCardService;
import com.yl.applets.service.IWxUserService;
import com.yl.applets.utils.Base64Utils;
import com.yl.applets.vo.UserCardVo;
import com.yl.applets.vo.WxUserVo;
import com.yl.applets.vo.baidu.IdCardVO;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <p>
 * 用户实名认证表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-22
 */
@RestController
@RequestMapping("/useCard")
@Api(value = "实名制", tags = {"实名制管理"})
@Slf4j
public class UserCardController extends AppBaseController {

    @Autowired
    private IUserCardService userCardService;

    @Autowired
    private OrikaBeanMapper beanMapper;

    @Autowired
    private IOCRService iocrService;

    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Value("${applets.des.key:jkhy@123ijh}")
    private String desKey;

    @Value("${applets.ocr.enable:true}")
    private Boolean useNew;


    /**
     * 获取实名信息
     *
     * @return
     */
    @GetMapping("/info")
    @ApiOperation(value = "实名制信息", notes = "用户实名制信息")
    public Result info() {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        WxUserVo user = getUser();
        log.info("获取实名信息，请求id：{},获取到的用户信息：{}", requestId, JSON.toJSONString(user));
        List<Long> ids = wxUserService.getIdsByIdAndNumberId(user.getNumberId());
        log.info("根据numberId查询用户ids:{}，请求id：{}", ids, requestId);
        List<UserCard> userCards = userCardService.list(new LambdaQueryWrapper<UserCard>().in(UserCard::getUserId, ids));
        if (userCards != null && !userCards.isEmpty()) {
            UserCardVo userCardVo = beanMapper.map(userCards.get(0), UserCardVo.class);
            //加密处理手机号和身份证号
            userCardVo.setMobile(Base64Utils.encode(Base64Utils.encode(userCardVo.getMobile())));
            userCardVo.setCardNum(Base64Utils.encode(Base64Utils.encode(userCardVo.getCardNum())));
            return success(userCardVo);
        }
        return Result.success(ServiceErrCodeEnum.NO_CARD_ERR.getCode(), ServiceErrCodeEnum.NO_CARD_ERR.getMsg());
    }


    /**
     * 获取实名信息,脱敏
     *
     * @return
     */
    @GetMapping("/infoBySecret")
    @ApiOperation(value = "实名制信息-脱敏", notes = "用户实名制信息-脱敏")
    public Result<UserCardVo> infoBySecret() {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");//本次请求id
        WxUserVo user = getUser();
        log.info("获取实名信息，请求id==>{},获取到的用户信息：{}", requestId, JSON.toJSONString(user));
        Result<UserCardVo> result = channelApiFeignClient.infoBySecret(user.getNumberId() + "");
        log.info("请求channel服务,返回==>{}请求id==>{}", JSON.toJSONString(result), requestId);
        UserCardVo result1 = new UserCardVo();
        try {
            result1 = result.result();
        } catch (Exception e) {
            log.info("未实名制==>{}",e);
            Result<UserCardVo> noCard = new Result<>();
            UserCardVo data = new UserCardVo();
            //未实名-放入当前手机号
            data.setMobile(user.getMobile());
            data.setIsSame(1);
            noCard.setCode(135020016);
            noCard.setMsg(e.getMessage());
            noCard.setData(data);
            log.info("noCard==>{}",JSON.toJSONString(noCard));
            return noCard;
        }
        if (StringUtils.equals(result1.getMobile(), user.getMobile())) {
            result1.setIsSame(1);
        } else {
            result1.setIsSame(0);
        }
        return result;
    }


    @PostMapping("/getIDCardInfo")
    public Result getFile(@RequestPart("file") MultipartFile file, @RequestParam(value = "idType", required = false) Integer idType) {

        if (file.isEmpty()) {
            throw new BusinessException(ServiceErrCodeEnum.IDENTITY_CARD_FILE_ERR);
        }
        if (useNew){
            if (Objects.isNull(idType)) idType = CustomerCardTypeEnum.IDENTITY_CARD.getType();

            if (CustomerCardTypeEnum.FOREIGN_IDENTITY_CARD.getType() == idType|| CustomerCardTypeEnum.HOME_RETURN_PERMIT.getType() == idType ) {
                Result<IdCardVO> idCardVOResult = channelApiFeignClient.foreignIdCard(file);
                IdCardVO result = idCardVOResult.result();
                if(CustomerCardTypeEnum.HOME_RETURN_PERMIT.getType() == idType){
                    if(StringUtils.isNotBlank(result.getIdNum())&&result.getIdNum().length()>9){
                        result.setIdNum(result.getIdNum().substring(0,9));
                        log.info("港澳居民来往内地通行证,解析大于9位,截取{}",JSON.toJSONString(result));
                    }
                }
                return Result.success(result);
            }
            return channelApiFeignClient.getIdCardInfo(file);
        }else {
            IDCardOCRResponse idCardInfo = iocrService.getIDCardInfo(file);
            if (idCardInfo == null) {
                throw new BusinessException(ServiceErrCodeEnum.NO_CARD_ERR);
            }
            return success(idCardInfo);
        }
    }


    /**
     * 添加实名信息
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增实名制信息", notes = "新增实名制信息")
    public Result<UserCardVo> save(@RequestBody @Valid SaveAppletsUserCardDTO dto) {
        WxUserVo user = this.getUser();
        dto.setUserId(user.getId().longValue());
        dto.setUniqueLimitId(user.getOpenid());
        dto.setNumberId(user.getNumberId());
        dto.setLoginUserMobile(user.getMobile());
        dto.setSource("yl-jms-applets");
        log.info("新增实名制信息==>入参:{}", JSON.toJSONString(dto));
        return success(channelApiFeignClient.saveAppletsUserCard(dto).result());
    }

    /**
     * 实名是否需要验证码 true 需要验证，false 不需要
     * @param mobile
     * @return
     */
    @PostMapping("/checkChapter")
    @ApiOperation(value = "检查是否生成验证码", notes = "检查是否生成验证码")
    public Result<Boolean> checkChapter(@RequestParam("mobile") String mobile) {
        WxUserVo user = getUser();
        Boolean check = true;
        //校验当前手机号码跟登录用户手机号码是否一致
        if (StringUtils.equals(mobile, user.getMobile())) {
            check = false;
        }

        return success(check);
    }

    public static void main(String[] args) {
        String name = "我是名字";
        int i = name.length() - 1;
        StringBuffer stringBuffer = new StringBuffer(name.substring(0, 1));
        for (int j = 0; j < i; j++) {
            stringBuffer.append("*");
        }
        System.out.println(stringBuffer);
    }


}
