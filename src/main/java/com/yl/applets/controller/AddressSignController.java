package com.yl.applets.controller;


import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.AppletsUserSignQueryDto;
import com.yl.applets.dto.AppletsUserSignUpdateDto;
import com.yl.applets.feign.UserSignChannelApiFeignClient;
import com.yl.applets.vo.AppletsUserSignVo;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 地址管理 前端控制器
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR> zhangzhendong
 * @since Created in 2019-09-20
 */
@Api(value = "地址标签", tags = {"地址标签"})
@RestController
@RequestMapping("/address/sign")
@Slf4j
public class AddressSignController extends AppBaseController {


    @Autowired
    private UserSignChannelApiFeignClient signChannelApiFeignClient;


    @GetMapping("/order/remarks")
    public Result<List<String>> remarks(){
        Result<List<String>> remarks = signChannelApiFeignClient.remarks();
        log.info("小程序下单备注,配置==>{}", JSON.toJSONString(remarks));
        return Result.success(remarks.result());
    }


    @GetMapping("/getList")
    public Result<List<AppletsUserSignVo>> getList(){
        WxUserVo user = getUser();
        AppletsUserSignQueryDto dto=new AppletsUserSignQueryDto();
        dto.setMemberId(user.getNumberId());
        dto.setMemberName(user.getNickName());
        dto.setMobile(user.getMobile());
        log.info("用户查询地址簿标签配置,入参==>{}", JSON.toJSONString(dto));
        Result<List<AppletsUserSignVo>> list = signChannelApiFeignClient.getList(dto);
        log.info("用户查询地址簿标签配置,调用中台接口 出参==>{}", JSON.toJSONString(list));
        List<AppletsUserSignVo> result = list.result();
        return Result.success(result);
    }



    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody @Validated AppletsUserSignUpdateDto dto){
        log.info("用户更新地址簿标签配置,入参==>{}", JSON.toJSONString(dto));
        //查询是否自己的数据
        WxUserVo user = getUser();
        Long id = dto.getId();
        Result<AppletsUserSignVo> detail = signChannelApiFeignClient.detail(id);
        AppletsUserSignVo result = detail.result();
        if(result==null|| !Objects.equals(result.getMemberId(),user.getNumberId())){
            log.info("不是自己的数据");
            throw new BusinessException(ResultCodeEnum.DATA_NOT_FOUND);
        }
        dto.setMemberId(user.getNumberId());
        dto.setMemberName(user.getNickName());
        dto.setMobile(user.getMobile());
        Result<Boolean> update = signChannelApiFeignClient.update(dto);
        log.info("调用中台接口,返回==>{}",JSON.toJSONString(update));
        update.result();
        return Result.success(Boolean.TRUE);
    }


}
