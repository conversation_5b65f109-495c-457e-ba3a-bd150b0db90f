package com.yl.applets.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.ActivityConfigDTO;
import com.yl.applets.dto.ActivityConfigQueryDTO;
import com.yl.applets.dto.ActivityVisitRecordDTO;
import com.yl.applets.entity.ActivityConfig;
import com.yl.applets.service.IActivityPromotionService;
import com.yl.applets.service.IActivityService;
import com.yl.applets.stream.ActivityConfigPushOutputProcessor;
import com.yl.applets.vo.ActivityConfigVO;
import com.yl.applets.vo.ActivityVisitRecordStatisticsVO;
import com.yl.common.base.annotation.log.operation.ExcludeInterceptor;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020<br>
 *
 * @description: 小程序活动管理
 * @author: xiongweibin
 * @create: 2020-08-24 11:41
 */
@Api(value = "营销活动管理", tags = {"营销活动管理"})
@RestController
@RequestMapping("/activity/promotion")
@Slf4j
public class ActivityPromotionController extends AppBaseController {

    @Autowired
    private IActivityPromotionService activityService;

    @Autowired
    private ActivityConfigPushOutputProcessor activityConfigPushOutputProcessor;

    /**
     * 分页查询
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "活动配置分页", notes = "活动配置分页")
    @ExcludeInterceptor
    public Result<Page<ActivityConfigVO>> getPages(@RequestBody(required = false) @Valid ActivityConfigQueryDTO queryDTO) {
        if(queryDTO==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        log.info("活动配置分页查询,入参==>{}", JSON.toJSONString(queryDTO));
        return success(activityService.getPages(queryDTO));
    }

    /**
     * 活动配置集合查询
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/list")
    @ApiOperation(value = "活动配置集合查询", notes = "活动配置集合查询")
    @ExcludeInterceptor
    public Result<List<ActivityConfigVO>> list(@RequestBody(required = false) ActivityConfigQueryDTO queryDTO) {
        if(queryDTO==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        log.info("活动配置查询,入参==>{}", JSON.toJSONString(queryDTO));
        return success(activityService.list(queryDTO));
    }

    /**
     * 新增活动配置
     *
     * @param dto
     * @return
     */
    @PostMapping("/add")
    @ApiOperation(value = "活动配置新增", notes = "活动配置新增")
    @ExcludeInterceptor
    public Result add(@RequestBody(required = false) @Valid ActivityConfigDTO dto) {
        if(dto==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        log.info("活动配置新增,入参==>{}", JSON.toJSONString(dto));
        Result<ActivityConfig> result = activityService.add(dto);
        if(result.isSucc()){
            activityConfigPushOutputProcessor.sendActivityConfigAsynMessage(result.getData());
        }
        return result;
    }


    /**
     * 删除活动配置
     *
     * @param dto
     * @return
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除活动配置", notes = "删除活动配置")
    @ExcludeInterceptor
    public Result delete(@RequestBody(required = false) ActivityConfigDTO dto) {
        if(dto==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        return activityService.delete(dto);
    }

    /**
     * 下线
     *
     * @param dto
     * @return
     */
    @PostMapping("/offline")
    @ApiOperation(value = "下线", notes = "下线")
    @ExcludeInterceptor
    public Result offline(@RequestBody(required = false) ActivityConfigDTO dto) {
        if(dto==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        return activityService.offline(dto);
    }

    /**
     * 更新活动配置
     *
     * @param dto
     * @return
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新活动配置", notes = "更新活动配置")
    @ExcludeInterceptor
    public Result<ActivityConfig> update(@RequestBody(required = false) @Valid ActivityConfigDTO dto) {
        if(dto==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        log.info("活动配置更新,入参==>{}", JSON.toJSONString(dto));
        if(dto.getId()==null){
            return Result.error(ResultCodeEnum.DATA_NOT_FOUND);
        }
        Result<ActivityConfig> result = activityService.update(dto);
        if(result.isSucc()){
            activityConfigPushOutputProcessor.sendActivityConfigAsynMessage(result.getData());
        }
        return result;
    }

    /**
     * 查询活动详情
     *
     * @param id
     * @return
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询活动详情", notes = "查询活动详情")
    @ExcludeInterceptor
    public Result<ActivityConfigVO> getDetailById(@RequestParam("id") Long id) {
        return activityService.getDetailById(id);
    }


}
