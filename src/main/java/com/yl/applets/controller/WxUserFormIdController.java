//package com.yl.applets.controller;
//
//
//import com.yl.applets.entity.WxUserFormId;
//import com.yl.applets.service.IWxUserFormIdService;
//import com.yl.common.base.model.vo.Result;
//import com.yl.common.base.util.StringUtils;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.validation.constraints.NotBlank;
//
///**
// * 小程序用户表单id 前端控制器
// * 云路供应链科技有限公司 版权所有  Copyright 2019
// *
// * <AUTHOR> zhangzhendong
// * @since Created in 2019-10-09
// */
//@Api(value = "小程序用户表单id", tags = {"小程序用户表单id"})
//@RestController
//@RequestMapping("/wxUserFormId")
//@Slf4j
//@Validated
//public class WxUserFormIdController extends AppBaseController {
//
//    @Autowired
//    private IWxUserFormIdService wxUserFormIdService;
//
//    /**
//     * 新增
//     *
//     * @param formid
//     * @return
//     */
//    @PostMapping("/add")
//    @ApiOperation(value = "新增", notes = "新增")
//    public Result<Boolean> save(@RequestParam("formid") @NotBlank(message = "formid不能为空") String formid) {
//        if (StringUtils.isNotEmpty(formid) && !formid.contains("formId")) {
//            WxUserFormId wxUserFormId = new WxUserFormId();
//            wxUserFormId.setFormid(formid).setUserId(getUser().getId());
//            return success(wxUserFormIdService.save(wxUserFormId));
//        }
//        return success(Boolean.FALSE);
//    }
//
//}
