package com.yl.applets.controller.system;

import com.alibaba.fastjson.JSON;
import com.yl.applets.controller.AppBaseController;
import com.yl.applets.dto.*;
import com.yl.applets.entity.WxUser;
import com.yl.applets.enums.ChannelSourceEnum;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.OssApi;
import com.yl.applets.service.IAppLoginService;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.annotation.log.operation.ExcludeInterceptor;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.FileUploadValidator;
import com.yl.common.base.util.IpAddressUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:
 * @CreateDate: Created in 2019-08-05 15:43 <br>
 * @Author: zhipeng.liu
 */
@Api(value = "用户登录", tags = {"用户登录"})
@RestController
@Slf4j
public class LoginController extends AppBaseController {

    @Autowired
    private IAppLoginService appLoginService;

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @Autowired
    private OssApi ossApi;

    static final String PUBLIC_KEY = "APPLETS_KEY";


    @Value("#{'${file.allowedExtensions:jpg,png,jpeg}'.split(',')}")
    private List<String> allowedExtensions;

    @ApiOperation(value = "wx绑定手机号", notes = "wx绑定手机号")
    @PostMapping("/bindPhone")
    @ExcludeInterceptor
    public Result<WxUserVo> phone(@RequestBody(required = false) BindPhoneDTO bindPhoneDTO) {
        if (bindPhoneDTO == null) {
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        log.info("入参：" + JSON.toJSONString(bindPhoneDTO));
        WxUserVo userVo = new WxUserVo();
        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(bindPhoneDTO.getOpenid() + bindPhoneDTO.getTimes() + PUBLIC_KEY));
        log.info("后端MD5加密之后:" + sign1);
        if (!sign1.equals(bindPhoneDTO.getSign())) {
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }
        userVo.setUnionid(bindPhoneDTO.getUnionid());
        userVo.setOpenid(bindPhoneDTO.getOpenid());
        return success(appLoginService.bindPhone(bindPhoneDTO, userVo, false));
    }

    @ApiOperation(value = "wx绑定手机号新版", notes = "wx绑定手机号新版")
    @PostMapping("/v2/bindPhone")
    @ExcludeInterceptor
    public Result<WxUserVo> bindPhoneNew(@RequestBody(required = false) BindPhoneDTO bindPhoneDTO) {
        if (bindPhoneDTO == null) {
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        log.info("微信小程序登录入参：" + JSON.toJSONString(bindPhoneDTO));
        WxUserVo userVo = new WxUserVo();
        String sign1 = DigestUtils.md5Hex(DigestUtils.md5Hex(bindPhoneDTO.getOpenid() + bindPhoneDTO.getTimes() + PUBLIC_KEY));
        log.info("后端MD5加密之后:" + sign1);
        if (!sign1.equals(bindPhoneDTO.getSign())) {
            throw new BusinessException(ResultCodeEnum.IS_NO_ACCESS);
        }
        userVo.setUnionid(bindPhoneDTO.getUnionid());
        userVo.setOpenid(bindPhoneDTO.getOpenid());
        userVo.setProvince(bindPhoneDTO.getProvince());
        userVo.setCity(bindPhoneDTO.getCity());
        userVo.setGender(0);
        return success(appLoginService.bindPhone(bindPhoneDTO, userVo, true));
    }


    /**
     * 登陆接口
     */
    @PostMapping("/login")
    @ApiOperation(value = "登录接口（旧）", notes = "登录接口（旧）")
    public Result<WxUserVo> login(@RequestBody(required = false) WxUserDTO wxUserDTO, HttpServletRequest request) {
        if(wxUserDTO==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        return success(appLoginService.wxLogin(wxUserDTO, request));
    }

    /**
     * 功能描述:
     * 重构后登陆
     * @param wxUserDTO
     * @return:com.yl.common.base.model.vo.Result<com.yl.applets.vo.WxUserVo>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-04-19 13:49
     */
    @PostMapping("/wx/login")
    @ApiOperation(value = "重构后登陆", notes = "重构后登陆，只需code")
    @ExcludeInterceptor
    public Result<WxUserVo> wxLogin(@RequestBody(required = false) WxUserDTO wxUserDTO, HttpServletRequest request) {
        if(wxUserDTO==null){
            log.warn("入参为空");
            return Result.error(ResultCodeEnum.PARAMS_IS_NULL);
        }
        return success(appLoginService.wxLoginV2(wxUserDTO, request));
    }

    /**
     * 功能描述:
     * 更新微信用户信息
     * @param wxUserDTO
     * @return:com.yl.common.base.model.vo.Result<com.yl.applets.vo.WxUserVo>
     * @since: 1.0.0
     * @Author:luhong
     * @Date: 2021-04-19 13:49
     */
    @PostMapping("/wx/update")
    @ApiOperation(value = "更新微信用户信息", notes = "更新微信用户信息")
    public Result<WxUserVo> update(@RequestBody WxUserDTO wxUserDTO) {
        return success(appLoginService.update(wxUserDTO,getUser()));
    }


    @PostMapping("/logout")
    @ApiOperation(value = "登出", notes = "登出")
    public Result<Boolean> logout() {
        return success(appLoginService.logout());
    }


    @ApiOperation(value = "隐私政策记录", notes = "隐私政策记录")
    @PostMapping("/savePrivacyRecord")
    public Result<Boolean> savePrivacyRecord(@RequestBody @Validated RecordPrivacyDto dto) {
        log.info("记录隐私政策，入参：{}",JSON.toJSONString(dto));
        long l = getUser().getId().longValue();
        String ip= IpAddressUtil.getIpAdrress(request);
        dto.setCreateBy(l);
        dto.setCreateTime(LocalDateTime.now());
        dto.setType(ChannelSourceEnum.WX.getKey());
        dto.setUserIp(ip);
        dto.setUserId(l);
        dto.setUserSessionid(request.getSession()==null?"":request.getSession().getId());
        return channelApiFeignClient.saveRecord(dto);
    }

    @PostMapping("/logoff")
    @ApiOperation(value = "注销", notes = "注销")
    public Result<Boolean> logoff() {
        return success(appLoginService.logoff(getUser()));
    }


    /**
     * 是否注销 true是 false否
     * @return
     */
    @GetMapping("/user/isOff")
    @ApiOperation(value = "是否注销", notes = "是否注销")
    public Result<MemberUserOffDto> isOff() {
        WxUserVo user = getUser();
        Result<MemberUserOffDto> off = appLoginService.isOff(user.getMobile());
        log.info("用户是否注销==>{}",JSON.toJSONString(off));
        return off;
    }

    /**
     * 取消注销
     * @return
     */
    @GetMapping("/user/cancelOff")
    @ApiOperation(value = "取消注销", notes = "取消注销")
    public Result<Boolean> cancelOff() {
        WxUserVo user = getUser();
        WxUser wxUser=new WxUser();
        wxUser.setMobile(user.getMobile());
        wxUser.setNumberId(user.getNumberId());
        log.info("用户取消注销,入参==>{}",JSON.toJSONString(wxUser));
        Result<Boolean> booleanResult = channelApiFeignClient.cannelOff(wxUser);
        log.info("用户取消注销,出参==>{}",JSON.toJSONString(booleanResult));
        return booleanResult;
    }

    @PostMapping("/upload/heads")
    @ApiOperation(value = "上传微信头像",notes = "上传微信头像")
    public Result<String> uploadHeads(UpLoadHeadsDTO dto){
        if (dto.getFile()==null||dto.getFile().isEmpty()) {
            throw new BusinessException(ServiceErrCodeEnum.FILE_ISNULL);
        }
        WxUserVo user = getUser();
        long start = System.currentTimeMillis();
        String originalFilename = dto.getFile().getOriginalFilename();
        // 获取文件后缀名
        String suffixName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        //判断是否是图片
        if (!"jpeg".equalsIgnoreCase(suffixName) && !"jpg".equalsIgnoreCase(suffixName) && !"png".equalsIgnoreCase(suffixName) && !"gif".equalsIgnoreCase(suffixName)){
            throw new BusinessException(ServiceErrCodeEnum.UPLOAD_FILE_IMAGE_SUFFIX_NAME_ERROR.getCode(), ServiceErrCodeEnum.UPLOAD_FILE_IMAGE_SUFFIX_NAME_ERROR.getMsg());
        }
        Result<String> result = ossApi.upload("yl-jms-applets", "上传微信头像", dto.getFile());
        long end = System.currentTimeMillis();
        log.info("用户：{}，上传微信头像,返回地址为：{}，耗时：{}",JSON.toJSONString(user),JSON.toJSONString(result),(end - start));
        return result;
    }


    @PostMapping("/upload/file")
    @ApiOperation(value = "上传文件",notes = "上传文件")
    public Result<String> uploadFile(UpLoadHeadsDTO dto){
        if (dto.getFile()==null||dto.getFile().isEmpty()) {
            throw new BusinessException(ServiceErrCodeEnum.FILE_ISNULL);
        }
        String fileName = dto.getFile().getOriginalFilename();
        boolean fileExtensionValid = FileUploadValidator.isFileExtensionValid(fileName,allowedExtensions);
        if(!fileExtensionValid){
            throw new BusinessException(ResultCodeEnum.FILE_TYPE_ERROR);
        }
        WxUserVo user = getUser();
        long start = System.currentTimeMillis();
        Result<String> result = ossApi.uploadFile("yl-jms-applets", "upLoadFile", dto.getFile());
        long end = System.currentTimeMillis();
        log.info("用户：{}，上传微信文件,返回地址为：{}，耗时：{}",JSON.toJSONString(user),JSON.toJSONString(result),(end - start));
        return result;
    }

}
