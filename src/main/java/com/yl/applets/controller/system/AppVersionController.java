package com.yl.applets.controller.system;


import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.ForwardRequest;
import com.yl.applets.dto.lmdm.SysVersionQueryDTO;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.feign.OldLmdmFeignClient;
import com.yl.applets.vo.lmdm.AppVersionVO;
import com.yl.applets.vo.lmdm.SysVersionVO;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.controller.BaseController;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019
 *
 * <AUTHOR>
 * @since Created in 2019/6/19 17:05
 */
@Api(value = "app版本号管理", tags = {"版本号管理"})
@RestController
@RequestMapping("/version")
@Slf4j
public class AppVersionController extends BaseController {

    @Autowired
    private OldLmdmFeignClient sysVersionFeignClient;
    @Autowired
    private OrikaBeanMapper orikaBeanMapper;


    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @GetMapping(value = "/list")
    @ApiOperation(value = "App版本号管理列表", notes = "版本号管理列表")
    public Result<List<AppVersionVO>> getAppList() {
//        List<SysVersionVO> list = sysVersionFeignClient.list(new SysVersionQueryDTO()).result();
        String requestIds = UUID.randomUUID().toString().replaceAll("-", "");
        ForwardRequest requests=new ForwardRequest();
        requests.setRequestId(requestIds);
        requests.setRequestUri("/lmdmapi/sysVersion/list");
        requests.setBody(JSON.toJSONString(new SysVersionQueryDTO()));
        //调接口
        log.info("请求id==>{}接口入参==>{}",requestIds,JSON.toJSONString(requests));
        Result<?> forwards = channelApiFeignClient.forward(requests);
        log.info("请求id==>{}接口返回==>{}",requestIds,JSON.toJSONString(forwards));
        Object results = forwards.result();
        //2.转换出参LIST
        List<SysVersionVO> list = JSON.parseArray(JSON.toJSONString(results), SysVersionVO.class);
        List<AppVersionVO> appVersionVOList = orikaBeanMapper.mapAsList(list, AppVersionVO.class);
        return success(appVersionVOList);
    }

}
