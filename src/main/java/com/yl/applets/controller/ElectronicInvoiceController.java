package com.yl.applets.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.ElectronicInvoiceDTO;
import com.yl.applets.dto.InvoiceApplyDTO;
import com.yl.applets.dto.InvoiceRecordHistoryDTO;
import com.yl.applets.feign.InvoiceRecordFeignClient;
import com.yl.applets.feign.InvoiceTitleHistoryFeignClient;
import com.yl.applets.feign.OrderFeigntClient;
import com.yl.applets.vo.*;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 *  前端控制器 电子发票
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-23
 */
@RestController
@RequestMapping("/electronicInvoice")
public class ElectronicInvoiceController extends AppBaseController{

    public static final int MAX_SIZE = 100;

    @Autowired
    private OrderFeigntClient orderFeigntClient;

    @Autowired
    private InvoiceTitleHistoryFeignClient invoiceTitleHistoryFeignClient;

    @Autowired
    private InvoiceRecordFeignClient invoiceRecordFeignClient;

    /**
     * 获取用户开票运单列表
     * @param current
     * @param size
     * @return
     */
    @GetMapping({"/page"})
    public Result<Page<OmsElectronicInvoice>> page( @RequestParam("current")Integer current, @RequestParam("size")Integer size){
        if(size == null){
            size = 30;
        }
        if (size > MAX_SIZE){
            size = 30;
        }
        if (current == null){
            current = 1;
        }
        ElectronicInvoiceDTO electronicInvoiceDTO = new ElectronicInvoiceDTO();
        electronicInvoiceDTO.setCurrent(current);
        electronicInvoiceDTO.setSize(size);
        electronicInvoiceDTO.setMemberId(getUser().getId());
        electronicInvoiceDTO.setPhone(getUser().getMobile());
// todo  order做查询时间限制
        return  success(orderFeigntClient.pageMyElectronicInvoice(electronicInvoiceDTO).result());
    }

    /**
     * 开票抬头记录
     * @return
     */
    @GetMapping("/history")
    @ApiOperation(value = "客户开票抬头历史记录查询", notes = "客户开票抬头历史记录查询")
    public Result<List<InvoiceTitleHistoryVO>> findListByCustomer( InvoiceRecordHistoryDTO dto ){
        WxUserVo user = getUser();
        dto.setCustomerId(user.getId());
        dto.setCustomerCode(user.getOpenid());
        dto.setDataSource(1);//1小程序
        return success(invoiceTitleHistoryFeignClient.findListByCustomer(dto).result());
    }

    /**
     * 小程序-开票历史查询
     * @return
     */
    @PostMapping("/invoiceRecordHistory")
    @ApiOperation(value = "小程序-开票历史查询", notes = "小程序-开票历史查询")
    public Result<Page<InvoiceRecordHistoryVO>> invoiceRecordHistory(@RequestParam("current") Integer current,@RequestParam("size")Integer size){
        WxUserVo user = getUser();
        InvoiceRecordHistoryDTO dto = new InvoiceRecordHistoryDTO();
        dto.setCustomerId(user.getId());
        dto.setCustomerCode(user.getOpenid());
        dto.setDataSource(1);//1小程序
        dto.setCurrent(current);
        dto.setSize(size);
        return success(invoiceRecordFeignClient.invoiceRecordHistory(dto).result());
    }

    /**
     * 小程序-开票申请
     * @param applyDTO
     * @return
     */
    @PostMapping("/apply")
    @ApiOperation(value = "小程序-开票申请", notes = "小程序-开票申请")
    public Result<List<InvoicingApplyVO>> apply(@RequestBody InvoiceApplyDTO applyDTO){
        WxUserVo user = getUser();
        applyDTO.setCustomerId(user.getId());
        applyDTO.setCustomerCode(user.getOpenid());
        return success(invoiceRecordFeignClient.apply(applyDTO).result());
    }


    @GetMapping("/findUrlById")
    @ApiOperation(value = "小程序-开票PDF、图片查询下载接口", notes = "小程序-开票PDF、图片查询下载接口")
    public Result<InvoiceUrlVO> findUrlById(@RequestParam("applyNo") String applyNo){
        InvoiceRecordHistoryVO invoiceRecordHistoryVO = invoiceRecordFeignClient.detailForOut(applyNo).result();
        if(invoiceRecordHistoryVO.getCustomerId().equals(getUser().getId())) {
            return success(invoiceRecordFeignClient.findUrlById(applyNo).result());
        }
        return success(null);
    }


    @GetMapping("/detailForOut")
    @ApiOperation(value = "小程序-开票详情", notes = "小程序-开票详情")
    public Result<InvoiceRecordHistoryVO> detailForOut(@RequestParam("applyNo") String applyNo){
        InvoiceRecordHistoryVO invoiceRecordHistoryVO = invoiceRecordFeignClient.detailForOut(applyNo).result();
        if(invoiceRecordHistoryVO.getCustomerId().equals(getUser().getId())) {
            return success(invoiceRecordFeignClient.detailForOut(applyNo).result());
        }
        return success(null);
    }

    /**
     *
     * @param applyNo
     * @return
     */
    @PostMapping("/failure")
    @ApiOperation(value = "小程序-开票详情", notes = "小程序-冲销")
    public Result<Boolean> failure(@RequestParam("applyNo") String applyNo){
        InvoiceRecordHistoryVO invoiceRecordHistoryVO = invoiceRecordFeignClient.detailForOut(applyNo).result();
        if(invoiceRecordHistoryVO.getCustomerId().equals(getUser().getId())){
           return success(invoiceRecordFeignClient.failure(applyNo).result());
        }
        return success(false);
    }



}
