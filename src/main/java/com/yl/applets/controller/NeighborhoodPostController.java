package com.yl.applets.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.*;
import com.yl.applets.dto.neiborhood.*;
import com.yl.applets.entity.UserCard;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.feign.MailMiniProgramFeignClient;
import com.yl.applets.service.IUserCardService;
import com.yl.applets.service.IWxUserService;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.model.vo.NeighborhoodResult;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;


/**
 * 邻里驿站
 */
@Slf4j
@RequestMapping("/neighborhood")
@RestController
@Api(value = "邻里驿站", tags = {"邻里驿站"})
public class NeighborhoodPostController extends AppBaseController {

    @Resource
    private MailMiniProgramFeignClient mailMiniProgramFeignClient;
    @Resource
    private IWxUserService wxUserService;
    @Resource
    private IUserCardService userCardService;

    /**
     * 附近驿站
     */
    @PostMapping("/nearbySite")
    @ApiOperation("附近驿站")
    public Result<List<NearbySiteVO>> nearbySite(@Validated @RequestBody NearbySiteRequest request) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id：{},附近驿站：{}", requestId, JSON.toJSONString(request));
        NeighborhoodResult<List<NearbySiteVO>> listNeighborhoodResult = mailMiniProgramFeignClient.nearbySite(request);
        log.info("附近驿站出参：{}", JSON.toJSONString(listNeighborhoodResult));
        return listNeighborhoodResult.convert();
    }

    /**
     * 新增外部订单
     */
    @PostMapping("/addOrderExternal")
    @ApiOperation("新增外部订单")
    public Result<AddOrderExternalResponse> addOrderExternal(@Validated @RequestBody AddOrderExternalRequest request) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id：{},新增外部订单：{}", requestId, JSON.toJSONString(request));
        WxUserVo user = this.getUser();
        log.info("获取实名信息，请求id：{},获取到的用户信息：{}", requestId, JSON.toJSONString(user));
        List<Long> ids = wxUserService.getIdsByIdAndNumberId(user.getNumberId());
        log.info("根据numberId查询用户ids:{}，请求id：{}", ids, requestId);
        List<UserCard> userCards = userCardService.list(new LambdaQueryWrapper<UserCard>().in(UserCard::getUserId, ids));
        if (CollUtil.isNotEmpty(userCards)) {
            log.info("请求id：{}，补全用户实名信息..", requestId);
            UserCard userCard = userCards.get(0);
            request.setRealName(userCard.getName());
            request.setIdCardNo(userCard.getCardNum());
        } else {
            throw new BusinessException(ServiceErrCodeEnum.ID_CARD_NULL);
        }
        request.setUnionId(user.getUnionid());
        NeighborhoodResult<AddOrderExternalResponse> addOrderExternalResponseNeighborhoodResult = mailMiniProgramFeignClient.addOrderExternal(request);
        log.info("新增外部订单出参：{}", JSON.toJSONString(addOrderExternalResponseNeighborhoodResult));
        return addOrderExternalResponseNeighborhoodResult.convert();
    }

    /**
     * 取消外部订单
     */
    @PostMapping("/cancelOrderExternal")
    @ApiOperation("取消外部订单")
    public Result<CancelOrderExternalResponse> cancelOrderExternal(@Validated @RequestBody CancelOrderExternalRequest request) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id：{},取消外部订单：{}", requestId, JSON.toJSONString(request));
        WxUserVo user = this.getUser();
        request.setUnionId(user.getUnionid());
        NeighborhoodResult<CancelOrderExternalResponse> cancelOrderExternalResponseNeighborhoodResult = mailMiniProgramFeignClient.cancelOrderExternal(request);
        log.info("取消外部订单出参：{}", JSON.toJSONString(cancelOrderExternalResponseNeighborhoodResult));
        return cancelOrderExternalResponseNeighborhoodResult.convert();
    }

    /**
     * 订单分页查询
     */
    @PostMapping("/externalOrderPage")
    @ApiOperation("订单分页查询")
    public Result<Page<ExternalOrderPageVO>> externalOrderPage(@Validated @RequestBody ExternalOrderPageRequest request) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id：{},订单分页查询：{}", requestId, JSON.toJSONString(request));
        WxUserVo user = this.getUser();
        request.setUnionId(user.getUnionid());
        NeighborhoodResult<Page<ExternalOrderPageVO>> pageNeighborhoodResult = mailMiniProgramFeignClient.externalOrderPage(request);
        log.info("订单分页查询出参：{}", JSON.toJSONString(pageNeighborhoodResult));
        return pageNeighborhoodResult.convert();
    }

    /**
     * 订单详情
     */
    @PostMapping("/externalOrderDetail")
    @ApiOperation("订单详情")
    public Result<ExternalOrderDetailResponse> externalOrderDetail(@Validated @RequestBody ExternalOrderDetailRequest request) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id：{},订单分页查询：{}", requestId, JSON.toJSONString(request));
        WxUserVo user = this.getUser();
        request.setUnionId(user.getUnionid());
        NeighborhoodResult<ExternalOrderDetailResponse> externalOrderDetailResponseNeighborhoodResult = mailMiniProgramFeignClient.externalOrderDetail(request);
        log.info("订单详情出参：{}", JSON.toJSONString(externalOrderDetailResponseNeighborhoodResult));
        return externalOrderDetailResponseNeighborhoodResult.convert();
    }

    /**
     * 删除订单
     */
    @PostMapping("/deleteOrderExternal")
    @ApiOperation("删除订单")
    public Result<?> deleteOrderExternal(@Validated @RequestBody DeleteOrderExternalRequest request) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id：{},删除订单：{}", requestId, JSON.toJSONString(request));
        WxUserVo user = this.getUser();
        request.setUnionId(user.getUnionid());
        NeighborhoodResult<?> neighborhoodResult = mailMiniProgramFeignClient.deleteOrderExternal(request);
        log.info("删除订单出参：{}", JSON.toJSONString(neighborhoodResult));
        return neighborhoodResult.convert();
    }



    /**
     * 删除订单
     */
    @PostMapping("/deleteOrderExternal")
    @ApiOperation("删除订单")
    public Result<?> deleteOrderExternal(@Validated @RequestBody DeleteOrderExternalRequest request) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id：{},删除订单：{}", requestId, JSON.toJSONString(request));
        WxUserVo user = this.getUser();
        request.setUnionId(user.getUnionid());
        NeighborhoodResult<?> neighborhoodResult = mailMiniProgramFeignClient.deleteOrderExternal(request);
        log.info("删除订单出参：{}", JSON.toJSONString(neighborhoodResult));
        return neighborhoodResult.convert();
    }


}
