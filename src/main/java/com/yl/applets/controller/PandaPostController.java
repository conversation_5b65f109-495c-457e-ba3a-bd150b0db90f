package com.yl.applets.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yl.applets.dto.*;
import com.yl.applets.entity.UserCard;
import com.yl.applets.enums.exception.ServiceErrCodeEnum;
import com.yl.applets.service.IPandaPostOrderService;
import com.yl.applets.service.IUserCardService;
import com.yl.applets.service.IWxUserService;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.vo.PandaNearbyVO;
import com.yl.applets.vo.PandaOrderDetailVO;
import com.yl.applets.vo.WxUserVo;
import com.yl.common.base.annotation.log.operation.ExcludeInterceptor;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.YlPreconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有 © Copyright 2020
 *
 * <AUTHOR>
 * @Description:
 * @date 2021-11-08
 */

@Slf4j
@RequestMapping("/panda")
@RestController
@Api(value = "熊猫驿站", tags = {"熊猫驿站"})
public class PandaPostController extends AppBaseController {

    /**
     * 寄件类型
     */
    private static final int EXPRESS_TYPE_SEND = 1;
    /**
     * 收件类型
     */
    private static final int EXPRESS_TYPE_TAKE = 2;

    @Autowired
    private IPandaPostOrderService pandaPostOrderService;


    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private IUserCardService userCardService;


    /**
     * 订单列表查询
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryOrderPage")
    @ApiOperation(value = "订单列表查询", notes = "订单列表查询")
    public Result<Page<PandaOrderDetailVO>> queryPandaOrder(@RequestBody ThirdExpressApiDTO dto) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id：{},查单入参：{}", requestId, JSON.toJSONString(dto));
        YlPreconditions.checkArgument(Objects.nonNull(dto.getSearchType()), new ServiceException(ServiceErrCodeEnum.SEARCHTYPE_ISNULL));
        if (Objects.isNull(dto.getCurrent())) {
            dto.setCurrent(1);
        }
        if (Objects.isNull(dto.getSize())) {
            dto.setSize(5);
        }
        WxUserVo wxUserVo = getUser();
        log.info("请求id：{}，获取缓存用户,入参：{},结果：{}", requestId, JsonUtils.toJson(dto), JsonUtils.toJson(wxUserVo));
        switch (dto.getSearchType()) {
            case EXPRESS_TYPE_SEND://寄件
                //极兔用户ID
                dto.setCustomerId(wxUserVo.getNumberId().longValue());
                break;
            case EXPRESS_TYPE_TAKE://收件
                if (StringUtils.isBlank(wxUserVo.getMobile())) {//查询类型为收件且用户没有绑定手机号，直接返回空。
                    return success(new Page<>(dto.getCurrent(), dto.getSize()));
                }
                dto.setPhoneNo(wxUserVo.getMobile());
                break;
            default:
                throw new ServiceException(ServiceErrCodeEnum.SEARCHTYPE_ERROR);
        }
        return pandaPostOrderService.queryPage(dto, requestId);
    }


    /**
     * 取消
     *
     * @param orderId
     * @return
     */
    @GetMapping("/cancelOrder")
    @ApiOperation(value = "取消订单", notes = "取消订单")
    public Result<Boolean> cancel(@RequestParam String orderId) {
        WxUserVo user = getUser();
        Integer numberId = user.getNumberId();
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        //是否本人操作
        PandadetailDTO pandadetailDTO = new PandadetailDTO();
        pandadetailDTO.setJtUserId(String.valueOf(numberId));
        pandadetailDTO.setSendId(orderId);
        PandaOrderDetailVO detail = detail(pandadetailDTO).getData();
        if (detail == null) {
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_NULL);
        }
        //只有在订单状态未生成运单号、未揽收（orderState =5）的情况下才可取消。如果线上支付（支付宝、微信）的订单取消成功会原路退回。
        if (detail.getOrderStatusCode() != 5) {
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_ERROR);
        }
        log.info("请求id：{},取消订单入参：{},用户：{}", requestId, JSON.toJSONString(orderId), JSON.toJSONString(user));
        return pandaPostOrderService.cancelOrder(orderId, numberId, requestId);
    }

    /**
     * 删除极兔小程序已取消的订单
     *
     * @param orderId
     * @return
     */
    @GetMapping("/deleteCancelOrder")
    @ApiOperation(value = "删除已取消订单", notes = "删除已取消订单")
    public Result<Boolean> deleteCancelOrder(@RequestParam String orderId) {
        WxUserVo user = getUser();
        Integer numberId = user.getNumberId();
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        //是否本人操作
        PandadetailDTO pandadetailDTO = new PandadetailDTO();
        pandadetailDTO.setJtUserId(String.valueOf(numberId));
        pandadetailDTO.setSendId(orderId);
        PandaOrderDetailVO detail = detail(pandadetailDTO).getData();
        if (detail == null) {
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_NULL);
        }
        //只有在订单状态已取消（orderState =3）的情况下才可删除。
        if (detail.getOrderStatusCode() != 3) {
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_ERROR);
        }
        log.info("请求id：{},删除已取消订单接口入参：{},用户：{}", requestId, JSON.toJSONString(orderId), JSON.toJSONString(user));
        return pandaPostOrderService.deleteCancelOrder(orderId, numberId, requestId);
    }


    @PostMapping("/addOrder")
    @ApiOperation(value = "下单", notes = "下单")
    public Result<PandaOrderDetailVO> addOrder(@RequestBody @Validated PandaOrderDTO dto) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id：{},下单入参：{}", requestId, JSON.toJSONString(dto));
        //是否实名
        WxUserVo user = getUser();
        //极兔用户ID
        dto.setJtUserId(String.valueOf(user.getNumberId()));
        log.info("获取实名信息，请求id：{},获取到的用户信息：{}", requestId, JSON.toJSONString(user));
        List<Long> ids = wxUserService.getIdsByIdAndNumberId(user.getNumberId());
        log.info("根据numberId查询用户ids:{}，请求id：{}", ids, requestId);
        List<UserCard> userCards = userCardService.list(new LambdaQueryWrapper<UserCard>().in(UserCard::getUserId, ids));
        if (userCards != null && !userCards.isEmpty()) {
            log.info("请求id：{}，补全用户实名信息..", requestId);
            UserCard userCard = userCards.get(0);
            dto.setRealName(userCard.getName());
            dto.setCardNo(userCard.getCardNum());
        } else {
            throw new BusinessException(ServiceErrCodeEnum.ID_CARD_NULL);
        }
        return pandaPostOrderService.addOrder(dto, requestId);
    }

    @PostMapping("/updateOrder")
    @ApiOperation(value = "改单", notes = "改单")
    public Result<Boolean> updateOrder(@RequestBody @Validated PandaOrderDTO dto) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id：{},改单入参：{}", requestId, JSON.toJSONString(dto));
        WxUserVo user = getUser();

        PandadetailDTO pandadetailDTO = new PandadetailDTO();
        pandadetailDTO.setJtUserId(String.valueOf(user.getNumberId()));
        pandadetailDTO.setSendId(dto.getSendId());
        PandaOrderDetailVO detail = detail(pandadetailDTO).getData();
        //是否本人的订单
        if (detail == null) {
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_NULL);
        }
        //是否本人
        if (!StringUtils.equals(user.getNumberId() + "", dto.getJtUserId() + "")) {
            //无权限
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_ERROR);
        }
        //只有在订单状态为待确认收款（paymentState = 0）以及未生成运单号、未揽收（orderState =5）的情况下才可编辑。
        if (detail.getOrderStatusCode() != 5 || detail.getOrderPayStatus() != 0) {
            throw new BusinessException(ServiceErrCodeEnum.ORDER_IS_ERROR);
        }

        //不支持修改订单时修改实名信息
        // 是否实名
//        log.info("获取实名信息，请求id：{},获取到的用户信息：{}", requestId, JSON.toJSONString(user));
//        List<Long> ids = wxUserService.getIdsByIdAndNumberId(user.getNumberId());
//        log.info("根据numberId查询用户ids:{}，请求id：{}", ids, requestId);
//        List<UserCard> userCards = userCardService.list(new LambdaQueryWrapper<UserCard>().in(UserCard::getUserId, ids));
//        if (userCards != null && !userCards.isEmpty()) {
//            log.info("请求id：{}，补全用户实名信息..", requestId);
//            UserCard userCard = userCards.get(0);
//            dto.setRealName(userCard.getName());
//            dto.setCardNo(userCard.getCardNum());
//        } else {
//            throw new BusinessException(ServiceErrCodeEnum.ID_CARD_NULL);
//        }
        return pandaPostOrderService.updateOrder(dto, requestId);
    }

    /**
     * 订单详情
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "订单详情", notes = "订单详情")
    @PostMapping("/detail")
    public Result<PandaOrderDetailVO> detail(@RequestBody PandadetailDTO dto) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id：{},订单详情接口入参：{}", requestId, JSON.toJSONString(dto));
        WxUserVo user = getUser();
        dto.setJtUserId(String.valueOf(user.getNumberId()));
        log.info("请求id：{},用户信息:{}", requestId, user);
        return pandaPostOrderService.detail(dto, requestId);
    }


   // @PostMapping("/queryNearStation")
   // @ApiOperation(value = "查询附近驿站", notes = "查询附近驿站")
    public Result<Page<Object>> getNearStation(@RequestBody @Validated PandaNearStationDto dto) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id：{}，查询附近驿站，入参：{}", requestId, JSON.toJSONString(dto));
        Long current = dto.getCurrent();
        Long size = dto.getSize();
        if (current == null) {
            dto.setCurrent(1L);
        }
        if (size == null) {
            dto.setSize(10L);
        }
        return pandaPostOrderService.queryNearStation(dto);
    }


   // @PostMapping("/queryFreight")
   // @ApiOperation(value = "查询费用", notes = "查询费用")
    public Result<Object> queryFreight(@RequestBody SpmApiTrialDTO spmApiTrialDTO) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id：{}查询费用入参：{}", requestId, JSON.toJSONString(spmApiTrialDTO));
        return pandaPostOrderService.queryFreight(spmApiTrialDTO);
    }

    @PostMapping("/nearby")
    @ApiOperation(value = "获取附近5公里熊猫门店", notes = "获取附近5公里熊猫门店")
    public Result<List<PandaNearbyVO>> nearby(@RequestBody PandaNearbyDTO pandaNearbyDTO) {
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id：{}查询费用入参：{}", requestId, JSON.toJSONString(pandaNearbyDTO));
        return pandaPostOrderService.nearby(pandaNearbyDTO, requestId);
    }
}
