package com.yl.applets.controller;

import com.alibaba.fastjson.JSON;
import com.yl.applets.dto.CancelReasonDTO;
import com.yl.applets.feign.ChannelApiFeignClient;
import com.yl.applets.vo.OmsCancelReasonVO;
import com.yl.common.base.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 云路科技有限公司 版权所有 Copyright 2022<br>
 *
 * @Description
 * <AUTHOR>
 * @Date 2022-11-02 14:38
 * @Version 1.0
 */
@RequestMapping("/cancel-reason")
@RestController
@Api(value = "取消原因中台查询接口", tags = "取消原因中台查询接口")
@Slf4j
public class CancelReasonController {

    @Autowired
    private ChannelApiFeignClient channelApiFeignClient;

    @PostMapping("/list")
    @ApiOperation(value = "取消原因查询list", notes = "取消原因查询list")
    public Result<List<OmsCancelReasonVO>> getList(@RequestBody CancelReasonDTO dto) {
        return channelApiFeignClient.qryCancelReasonVO(dto);
    }
}
