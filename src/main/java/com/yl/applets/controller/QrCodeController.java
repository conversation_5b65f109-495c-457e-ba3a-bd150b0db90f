package com.yl.applets.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.alibaba.fastjson.JSON;
import com.yl.applets.config.WxMaConfiguration;
import com.yl.applets.config.WxMaProperties;
import com.yl.applets.entity.CustomizedQrcode;
import com.yl.applets.feign.QrcodeFeignClient;
import com.yl.applets.vo.CustomizedQrcodeVo;
import com.yl.applets.vo.WXUrlSchemeVo;
import com.yl.common.base.annotation.log.operation.ExcludeInterceptor;
import com.yl.common.base.config.OrikaBeanMapper;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.ServiceException;
import com.yl.common.base.model.vo.Result;
import com.yl.common.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 云路供应链科技有限公司 版权所有  Copyright 2019<br>
 *
 * @Description:
 * @Project:  2021年3月2日17:50:08 确认无调用流量  二维码生成不再调用小程序生成
 * @CreateDate: Created in 2019-10-24 19:19 <br>
 * @Author: zhipeng.liu
 */
@RestController
@RequestMapping("/qrCode")
@Slf4j
public class QrCodeController extends AppBaseController{

    @Autowired
    private QrcodeFeignClient qrcodeFeignClient;

    @Autowired
    private OrikaBeanMapper beanMapper;
    @Autowired
    private WxMaProperties wxMaProperties;
//    private static final String MD5 = "MD5";
//
//    @Autowired
//    private WxMaProperties wxMaProperties;
//
//    @Value("${appId:applets}")
//    private String appId;
//
//    @Value("${secret:yl_applets}")
//    private String secret;
//
//    @Autowired
//    private OssApi ossApi;
//
//    @GetMapping("/create")
//    public Result create(@RequestParam("networkId")Integer networkId,@RequestParam("sign")String sign) throws Exception {
//        log.info("第三方业务员二维码调用，生成");
//        String signature = getMD5Str(appId+secret+networkId);
//        if(!signature.equals(sign)){
//            throw new ServiceException(ServiceErrCodeEnum.SIGN_ERR);
//        }
//        String appId = wxMaProperties.getConfigs().get(0).getAppid();
//        final WxMaService wxService = WxMaConfiguration.getMaService(appId);
//        String path = "pages/sendGoods/sendGoods?networkId="+networkId;
//        File file = null;
//        try {
//            file = wxService.getQrcodeService().createQrcode(path);
//        } catch (WxErrorException e) {
//            e.printStackTrace();
//            if(file == null){
//                file = wxService.getQrcodeService().createQrcode(path);
//            }
//        }
//        FileInputStream inputStream = new FileInputStream(file);
//        MultipartFile multipartFile = new MockMultipartFile("file",file.getName(),"text/plain", IOUtils.toByteArray(inputStream) );
//        return success(ossApi.upload("applets","qrcode",multipartFile).result());
//    }
//
//    @GetMapping("/getCustomerPrintQr")
//    public Result getCustomerPrintQr(@RequestParam("customerId")Integer customerId,@RequestParam("sign")String sign) throws Exception {
//        log.info("第三方云打印二维码调用，生成");
//        String signature = getMD5Str(appId+secret+customerId);
//        if(!signature.equals(sign)){
//            throw new ServiceException(ServiceErrCodeEnum.SIGN_ERR);
//        }
//        String appId = wxMaProperties.getConfigs().get(0).getAppid();
//        final WxMaService wxService = WxMaConfiguration.getMaService(appId);
//        String path = "pages/cloudPrint/cloudPrintScan?customerId="+customerId;
//        File file = null;
//        try {
//            file = wxService.getQrcodeService().createQrcode(path);
//        } catch (WxErrorException e) {
//            e.printStackTrace();
//            if(file == null){
//                file = wxService.getQrcodeService().createQrcode(path);
//            }
//        }
//        FileInputStream inputStream = new FileInputStream(file);
//        MultipartFile multipartFile = new MockMultipartFile("file",file.getName(),"text/plain", IOUtils.toByteArray(inputStream) );
//        return success(ossApi.upload("applets","qrcode",multipartFile).result());
//    }
//
//
//    @GetMapping("/getStaffQr")
//    public Result getStaffQr(@RequestParam("staffNo")String staffNo,@RequestParam("sign")String sign) throws Exception {
//        log.info("第三方二维码调用，生成");
//        String signature = getMD5Str(appId+secret+staffNo);
//        if(!signature.equals(sign)){
//            throw new ServiceException(ServiceErrCodeEnum.SIGN_ERR);
//        }
//        String appId = wxMaProperties.getConfigs().get(0).getAppid();
//        final WxMaService wxService = WxMaConfiguration.getMaService(appId);
//        String path = "pages/sendGoods/sendGoods?pickStaffCode="+staffNo;
//        File file =  wxService.getQrcodeService().createQrcode(path);
//
//        FileInputStream inputStream = new FileInputStream(file);
//        MultipartFile multipartFile = new MockMultipartFile("file",file.getName(),"text/plain", IOUtils.toByteArray(inputStream) );
//        return success(ossApi.upload("applets","qrcode",multipartFile).result());
//    }
//
//
//    public static String getMD5Str(String str) {
//        MessageDigest messageDigest = null;
//
//        try {
//            messageDigest = MessageDigest.getInstance(MD5);
//
//            messageDigest.reset();
//
//            messageDigest.update(str.getBytes("UTF-8"));
//
//        } catch (NoSuchAlgorithmException e) {
//            System.exit(-1);
//        } catch (UnsupportedEncodingException e) {
//            e.printStackTrace();
//        }
//
//        byte[] byteArray = messageDigest.digest();
//
//        StringBuffer md5StrBuff = new StringBuffer();
//
//        for (int i = 0; i < byteArray.length; i++) {
//            if (Integer.toHexString(0xFF & byteArray[i]).length() == 1) {
//                md5StrBuff.append("0").append(
//                        Integer.toHexString(0xFF & byteArray[i]));
//            }else{
//                md5StrBuff.append(Integer.toHexString(0xFF & byteArray[i]));
//            }
//
//        }
//
//        return md5StrBuff.toString();
//    }


    /**
     * 定制二维码需求 新 controller
     *
     *
     *
     *
     *
     */
    @GetMapping("/getQrDetailInfo")
    public Result<CustomizedQrcodeVo> getQrDetailInfo(CustomizedQrcode customizedQrcode){
        String requestId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("请求id==>{},查询定制二维码数据，入参:{}",requestId, JSON.toJSONString(customizedQrcode));
        if(customizedQrcode.getId()==null){
            throw new ServiceException(ResultCodeEnum.PARAMS_NOT_COMPLETE);
        }
        log.info("请求id==>{},查询定制二维码数据,调用feign接口，入参：{}",requestId,customizedQrcode.getId());
        Result<CustomizedQrcode> qrDetailInfo = qrcodeFeignClient.getQrDetailInfo(customizedQrcode.getId());
        log.info("请求id==>{},查询定制二维码数据,调用feign接口，出参：{}",requestId,JSON.toJSONString(qrDetailInfo));
        CustomizedQrcode data = qrDetailInfo.getData();
        if(data==null){
            log.warn("请求id==>{},查询定制二维码数据,该二维码不存在！",requestId);
            ResultCodeEnum error = ResultCodeEnum.QC_PICTURE_IS_NULL;
            throw new ServiceException(error.getCode(),error.getMsg());
        }
        //当前时间，方案是生效的
        if(!LocalDateTime.now().isAfter(data.getEndTime())){
            //如果业务员不为空，将网点置空
            if(data!=null&& !StringUtils.isEmpty(data.getSenderStaffCode())){
                data.setSenderNetworkId(null);
                data.setSenderNetworkName(null);
            }
            CustomizedQrcodeVo map = beanMapper.map(data, CustomizedQrcodeVo.class);
            return success(map);
        }else {
            log.warn("请求id==>{},查询定制二维码数据,该二维码已经过期！",requestId);
            ResultCodeEnum error = ResultCodeEnum.QC_PICTURE_IS_INVALID;
            throw new ServiceException(error.getCode(),error.getMsg());
        }
    }

    @GetMapping("/qrIsScan")
    public Result<Boolean> qrIsScan(CustomizedQrcode customizedQrcode){
        log.info("定制二维码被扫描，入参:{}",JSON.toJSONString(customizedQrcode));
        if(customizedQrcode.getId()==null){
            throw new ServiceException(ResultCodeEnum.PARAMS_NOT_COMPLETE);
        }
        log.info("定制二维码被扫描,调用feign接口，入参：{}",customizedQrcode.getId());
        Result<Boolean> booleanResult = qrcodeFeignClient.qrIsScan(customizedQrcode.getId());
        log.info("定制二维码被扫描,调用feign接口，出参：{}",JSON.toJSONString(booleanResult));
        return booleanResult;
    }

    /**
     * 获取微信短链接
     * 接口详见https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/url-scheme/urlscheme.generate.html
     * @param queryParams
     * @return
     */
    @PostMapping("/getSchemeUrl")
    @ExcludeInterceptor
    public Result<String> getSchemeUrl (@RequestParam(value = "queryParams",required = false) String queryParams,@RequestParam(value = "path",required = false)String path) throws WxErrorException {
        log.info("获取微信短链接，入参:queryParams:{},path:{}",JSON.toJSONString(queryParams),path);
//        if(StringUtils.isEmpty(queryParams)){
//            throw new ServiceException(ResultCodeEnum.PARAMS_NOT_COMPLETE);
//        }
        String appId = wxMaProperties.getConfigs().get(0).getAppid();
        final WxMaService wxService = WxMaConfiguration.getMaService(appId);
        Map<String,Object> params=new HashMap<>();
        Map<String,Object> objectMap=new HashMap<>();
        objectMap.put("path",StringUtils.isBlank(path)?"/pages/sendGoods/activeH5":path);
        objectMap.put("query",queryParams);
        params.put("jump_wxa",objectMap);
        params.put("expire_type",1);
        params.put("expire_interval",30);
        log.info("获取微信短链接,post入参:{}",JSON.toJSONString(params));
        String booleanResult= wxService.post("https://api.weixin.qq.com/wxa/generatescheme", JSON.toJSONString(params));
        log.info("获取微信短链接,post出参：{}",JSON.toJSONString(booleanResult));
        if(StringUtils.isNotEmpty(booleanResult)){
            WXUrlSchemeVo urlSchemeVo= JSON.parseObject(booleanResult,WXUrlSchemeVo.class);
            if(urlSchemeVo.getErrcode()!=0){
                return Result.error(-1,urlSchemeVo.getErrmsg());
            }else{
                return Result.success(urlSchemeVo.getOpenlink());
            }
        }
        return Result.error(-1,"获取微信短链接失败,请重试");
    }

}
