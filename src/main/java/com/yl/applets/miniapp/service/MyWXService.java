/**
 * Copyright (C), 2015-2021, 云路供应链科技有限公司
 * FileName: MyWXService
 * Author:   luhong
 * Date:     2021-03-29 14:05
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.miniapp.service;

import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpMessageRouter;

import javax.servlet.http.HttpServletRequest;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2021-03-29
 * @since 1.0.0
 */
public interface MyWXService {

    String wxMessage(HttpServletRequest request, String requestBody, WxMpMessageRouter messageRouter);

    void historyUser(String checkToken,String nextOpenid);

    String checkSignature(HttpServletRequest request,String requestId);

    void fixUser(String checkToken, String nextOpenid);

    WxJsapiSignature createJsapiSignature(String url) throws WxErrorException;
}