/**
 * Copyright (C), 2015-2021, 云路供应链科技有限公司
 * FileName: WXServiceImpl
 * Author:   luhong
 * Date:     2021-03-29 14:06
 * Description:
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.yl.applets.miniapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yl.applets.entity.WxOfficialUser;
import com.yl.applets.miniapp.service.MyWXService;
import com.yl.applets.service.WxOfficialUserService;
import com.yl.applets.thread.WxHistoryUserHandleThread;
import com.yl.applets.utils.JsonUtils;
import com.yl.applets.utils.SHA1;
import com.yl.common.base.enums.ResultCodeEnum;
import com.yl.common.base.exception.BusinessException;
import com.yl.common.base.util.GenerationIdUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpMessageRouter;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.mp.bean.result.WxMpUserList;
import org.apache.commons.codec.binary.Base64;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 〈一句话功能简述〉<br> 
 * 〈〉
 *
 * <AUTHOR>
 * @create 2021-03-29
 * @since 1.0.0
 */
@Slf4j
@Service
public class MyWXServiceImpl implements MyWXService {

    @Autowired
    private WxMpService wxMpService;

    @Autowired
    private WxOfficialUserService wxOfficialUserService;

    private static String SUBSCRIBE = "subscribe";

    private static String UNSUBSCRIBE = "unsubscribe";

    //微信公众号基本配置中的token
    @Value("${wx.mq.token:0f0811cc1d524662b568a633b0db1e66}")
    private String token;

    @Value("${wx.mq.data.token:Aa123456}")
    private String mqDataToken;

    public static final int HISTORY_USER_THREAD_NUM = 10;

    @Autowired
    private Executor appletsExecutor;


    @Override
    public String wxMessage(HttpServletRequest request,String requestBody,WxMpMessageRouter messageRouter) {
        String requestId = UUID.randomUUID().toString().replace("-","");
        //----------------------事件推送
        StopWatch stopWatch = new StopWatch();
        log.info("requestId={},接收到WX推送消息",requestId);
        String echostr = this.checkSignature(request,requestId);
        if(!StringUtils.isEmpty(echostr)){
            return echostr;
        }
//        Map<String, String> stringStringMap = null;
        WxMpXmlMessage inMessage = null;
        try {
//            stringStringMap = parseXml(request);
            inMessage = WxMpXmlMessage.fromXml(requestBody);
        } catch (Exception e) {
            log.info("requestId={},xml文件解析失败",requestId,e);
        }
        WxMpXmlOutMessage wxMpXmlOutMessage = messageRouter.route(inMessage);
        if(wxMpXmlOutMessage==null){
            return "";
        }
        String out = wxMpXmlOutMessage.toXml();
//        String msgType = stringStringMap.get("MsgType");
//        switch (msgType){
//            case "event":
//                this.handleEventMessage(stringStringMap,requestId);
//                break;
//            case "text":
//                this.handleTextMessage(stringStringMap,requestId);
//                break;
//
//        }
        log.info("requestId={}，返回xml信息结果{}",requestId,out);
        return out;
    }

//    private String handleTextMessage(Map<String, String> stringStringMap, String requestId) {
//        String responseMessage = "";
//        String content = stringStringMap.get("Content");
////        if(content.contains(responseKey1)){
////            responseMessage = "";
////        }else if(content.contains(responseKey2))
//
//
//    }

    private void handleEventMessage(Map<String, String> stringStringMap, String requestId) {
        StopWatch stopWatch = new StopWatch();
        String openId = stringStringMap.get("FromUserName");
        log.info("requestId={},解析wx事件消息：{}", requestId,JsonUtils.toJson(stringStringMap));
        WxMpUser wxMpUser = null;
        try {
            wxMpUser = wxMpService.getUserService().userInfo(openId);
        } catch (WxErrorException e) {
            log.warn("requestId=>{},调用微信接口获取用户信息失败：openId:{}",requestId,openId,e);
        }

        if(wxMpUser==null){
            log.info("requestId=>{},获取用户信息失败：{}",requestId,openId);
            return ;
        }
        if(SUBSCRIBE.equalsIgnoreCase(stringStringMap.get("Event"))){
            //关注公众号
            stopWatch.start("开始处理关注公众号事件");
            this.handleSubscribe(wxMpUser,requestId);
            stopWatch.stop();
        }else if(UNSUBSCRIBE.equalsIgnoreCase(stringStringMap.get("Event"))) {
            //取消关注
            stopWatch.start("开始处理取消关注公众号事件");
            this.handleUnsubscribe(wxMpUser,requestId);
            stopWatch.stop();
        }
        log.info("requestId=>{},事件处理完成，耗时：{}",requestId,stopWatch.prettyPrint());
        return;
    }

    @Override
    public void historyUser(String checkToken,String nextOpenid) {
        if(!checkToken.equals(mqDataToken)){
            return;
        }
        new Thread(new Runnable() {
            @Override
            public void run() {
                AtomicInteger count = new AtomicInteger();
                StopWatch stopWatch = new StopWatch();
                stopWatch.start("开始处理历史用户信息");
                handleHistoryUser(nextOpenid,count);
                stopWatch.stop();
                log.info("总共执行条数：{},耗时：{}",count,stopWatch.getLastTaskTimeMillis());
            }
        }).start();

    }

    @Override
    public String checkSignature(HttpServletRequest request,String requestId) {
        String signature=request.getParameter("signature");
        String timestamp=request.getParameter("timestamp");
        String nonce=request.getParameter("nonce");
        String echostr=request.getParameter("echostr");
        log.info("requestId={}，加密入参：timestamp:{},nonce:{},token:{}",requestId,timestamp,nonce,token);
        //这里是对三个参数进行加密
        String jiami = SHA1.getSHA1(token, timestamp, nonce,"");
        if(echostr!=null && jiami.equals(signature)){
            log.info("requestId={}，第一次申请验证成功",requestId);
            return  echostr;
        }
        //判断是否是公众号发来的请求
        if(jiami==null || !jiami.equals(signature)){
            log.info("requestId={},错误的请求,jiami:{},signature:{}",requestId,jiami,signature);
            throw new BusinessException(ResultCodeEnum.PARAMS_IS_INVALID);
        }
        return "";
    }

    @Override
    public void fixUser(String checkToken, String nextOpenid) {
        if(!checkToken.equals(mqDataToken)){
            return;
        }
        new Thread(new Runnable() {
            @Override
            public void run() {
                AtomicInteger count = new AtomicInteger();
                StopWatch stopWatch = new StopWatch();
                stopWatch.start("开始修复历史用户信息");
                handleFixUser(nextOpenid,count);
                stopWatch.stop();
                log.info("总共执行条数：{},耗时：{}",count,stopWatch.getLastTaskTimeMillis());
            }
        }).start();
    }

    @Override
    public WxJsapiSignature createJsapiSignature(String url) throws WxErrorException {
        return wxMpService.createJsapiSignature(url);
    }

    private String handleFixUser(String nextOpenId, AtomicInteger count) {
        count.getAndIncrement();
        log.info("第{}次获取微信用户信息列表，nextopenid:{}",count,nextOpenId);
        WxMpUserList wxMpUserList =null;
        try {
            wxMpUserList = wxMpService.getUserService().userList(nextOpenId);
        } catch (WxErrorException e) {
            log.warn("调用微信用户列表接口失败：{}",nextOpenId,e);
            //重试
            try {
                wxMpUserList = wxMpService.getUserService().userList(nextOpenId);
            } catch (WxErrorException ex) {
                log.warn("重试调用微信用户列表接口失败：{}",nextOpenId,e);
            }

        }
        if(wxMpUserList==null){
            log.info("第{}次获取微信用户信息列表为null",count);
            return null;
        }

        List<String> openids = wxMpUserList.getOpenids();
        int limit = HISTORY_USER_THREAD_NUM;
        int total = openids.size();
        int handleNum = (total % limit == 0) ? total / limit : (total / limit) + 1;
        CountDownLatch latch = new CountDownLatch(limit);
        log.info("开始批量处理微信历史用户，批次：{}，总数：{}", count,total);

        for (int i = 0; i < limit; i++) {
            //分段处理条数开始角标
            int startIdx = i * handleNum;
            //分段处理条数结束角标
            int endIdx = (i + 1) * handleNum;
            if (i == limit - 1) {
                endIdx = total;
            }
            List<String> secList = openids.subList(startIdx,endIdx);
            appletsExecutor.execute(new WxHistoryUserHandleThread(secList, wxMpService, latch, wxOfficialUserService,count,i));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.info("第{}次插入或更新用户信息失败",count);
        }
        log.info("第{}次插入或更新用户信息成功",count);
        return wxMpUserList.getNextOpenid();
    }

    private String handleHistoryUser(String nextOpenId,AtomicInteger count){
        nextOpenId = handleFixUser(nextOpenId,count);

        if(StringUtils.isEmpty(nextOpenId)){
            log.info("第{}次NextOpenid为空",count);
            return null;
        }
        return this.handleHistoryUser(nextOpenId,count);
    };


    private void handleUnsubscribe(WxMpUser wxMpUser,String requestId) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("通过unionID获取用户信息");
        LambdaQueryWrapper<WxOfficialUser> queryWrapper = new LambdaQueryWrapper<WxOfficialUser>().eq(WxOfficialUser::getOpenid,wxMpUser.getOpenId());
        WxOfficialUser wxOfficialUser = wxOfficialUserService.getOne(queryWrapper);
        stopWatch.stop();
        log.info("requestId=>{}，查询数据库获取用户信息耗时：{}，结果：{}",requestId,stopWatch.getLastTaskTimeMillis(),JsonUtils.toJson(wxOfficialUser));
        if(wxOfficialUser==null){
            return;
        }
        stopWatch.start("更新用户信息");
        wxOfficialUser.setSubscribe(0);
        wxOfficialUser.setUpdateTime(LocalDateTime.now());
        wxOfficialUser.setUnsubscribeTime(LocalDateTime.now());
        wxOfficialUserService.updateById(wxOfficialUser);
        stopWatch.stop();
        log.info("requestId=>{},更新用户信息耗时：{}",requestId,stopWatch.getLastTaskTimeMillis());

    }

    private void handleSubscribe(WxMpUser wxMpUser,String requestId) {
        LambdaQueryWrapper<WxOfficialUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WxOfficialUser::getUnionid,wxMpUser.getUnionId());
        WxOfficialUser wxOfficialUser = wxOfficialUserService.getOne(queryWrapper);
        log.info("");
        if(wxOfficialUser!=null){
            wxOfficialUser.setHeadImgUrl(wxMpUser.getHeadImgUrl());
            wxOfficialUser.setNickName(Base64.encodeBase64String(wxMpUser.getNickname().getBytes(StandardCharsets.UTF_8)));
            wxOfficialUser.setSex(wxMpUser.getSex());
            wxOfficialUser.setSubscribeScene(wxMpUser.getSubscribeScene());
            wxOfficialUser.setSubscribeTime(wxMpUser.getSubscribeTime());
            wxOfficialUser.setSubscribe(wxMpUser.getSubscribe()?1:0);
            wxOfficialUser.setProvince(wxMpUser.getProvince());
            wxOfficialUser.setCountry(wxMpUser.getCountry());
            wxOfficialUser.setCity(wxMpUser.getCity());
            wxOfficialUser.setUpdateTime(LocalDateTime.now());
            wxOfficialUserService.updateById(wxOfficialUser);
            log.info("requestId=>{},更新公众号用户信息成功：{}",requestId,JsonUtils.toJson(wxMpUser));
        }else{
            wxOfficialUser = new WxOfficialUser();
            wxOfficialUser.setId(GenerationIdUtil.getId());
            wxOfficialUser.setHeadImgUrl(wxMpUser.getHeadImgUrl());
            wxOfficialUser.setOpenid(wxMpUser.getOpenId());
            wxOfficialUser.setUnionid(wxMpUser.getUnionId());
            wxOfficialUser.setNickName(Base64.encodeBase64String(wxMpUser.getNickname().getBytes(StandardCharsets.UTF_8)));
            wxOfficialUser.setSex(wxMpUser.getSex());
            wxOfficialUser.setSubscribeScene(wxMpUser.getSubscribeScene());
            wxOfficialUser.setSubscribeTime(wxMpUser.getSubscribeTime());
            wxOfficialUser.setSubscribe(wxMpUser.getSubscribe()?1:0);
            wxOfficialUser.setCountry(wxMpUser.getCountry());
            wxOfficialUser.setProvince(wxMpUser.getProvince());
            wxOfficialUser.setCity(wxMpUser.getCity());
            LocalDateTime now = LocalDateTime.now();
            wxOfficialUser.setCreateTime(now);
            wxOfficialUser.setUpdateTime(LocalDateTime.now());
            wxOfficialUserService.save(wxOfficialUser);
            log.info("requestId=>{},新增公众号用户信息成功：{}",requestId,JsonUtils.toJson(wxOfficialUser));
        }
    }

    /**
     * dom4j 解析 xml 转换为 map
     * @param request
     * @return
     * @throws Exception
     */
    public static Map<String, String> parseXml(HttpServletRequest request) throws Exception {
        // 将解析结果存储在HashMap中
        Map<String, String> map = new HashMap<String, String>();
        // 从request中取得输入流
        InputStream inputStream = request.getInputStream();
        // 读取输入流
        SAXReader reader = new SAXReader();
        Document document = reader.read(inputStream);
        // 得到xml根元素
        Element root = document.getRootElement();
        // 得到根元素的所有子节点
        List<Element> elementList = root.elements();

        // 遍历所有子节点
        for (Element e : elementList){
            map.put(e.getName(), e.getText());
        }

        // 释放资源
        inputStream.close();
        inputStream = null;
        return map;
    }
}