<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.oms.OmsWaybillMapper">

    <sql id="base_column_join">
        oow.id,
        oow.waybill_no,
        oow.delivery_time,
        oow.collect_staff_code,
        oow.collect_staff_name,
        oow.customer_id,
        oow.customer_code,
        oow.customer_name,
        oow.express_type_id,
        oow.express_type_code,
        oow.express_type_name,
        oow.dispatch_code,
        oow.dispatch_name,
        oow.sender_name,
        oow.sender_mobile_phone,
        oow.sender_telphone,
        oow.sender_postal_code,
        oow.sender_country_id,
        oow.sender_country_name,
        oow.sender_province_id,
        oow.sender_province_name,
        oow.sender_city_id,
        oow.sender_city_name,
        oow.sender_area_id,
        oow.sender_area_name,
        oow.sender_township,
        oow.sender_street,
        oow.sender_detailed_address,
        oow.origin_id,
        oow.origin_code,
        oow.origin_name,
        oow.is_real_name,
        oow.id_no,
        oow.receiver_name,
        oow.receiver_mobile_phone,
        oow.receiver_telphone,
        oow.receiver_postal_code,
        oow.receiver_country_id,
        oow.receiver_country_name,
        oow.receiver_province_id,
        oow.receiver_province_name,
        oow.receiver_city_id,
        oow.receiver_city_name,
        oow.receiver_area_id,
        oow.receiver_area_name,
        oow.receiver_township,
        oow.receiver_street,
        oow.receiver_detailed_address,
        oow.destination_id,
        oow.destination_code,
        oow.destination_name,
        oow.goods_type_id,
        oow.goods_type_code,
        oow.goods_type_name,
        oow.goods_name,
        oow.package_number,
        oow.package_length,
        oow.package_wide,
        oow.package_high,
        oow.package_charge_weight,
        oow.package_total_weight,
        oow.package_total_volume,
        oow.package_volume,
        oow.box_standard_code,
        oow.box_standard_name,
        oow.box_number,
        oow.box_price,
        oow.settlement_id,
        oow.settlement_code,
        oow.settlement_name,
        oow.cod_need,
        oow.cod_money,
        oow.cod_fee,
        oow.is_cod_receive,
        oow.is_receive,
		oow.is_settlement,
        oow.currency_code,
        oow.currency_name,
        oow.insured,
        oow.insured_amount,
        oow.insured_fee,
        oow.is_need_receipt,
        oow.receipt_no,
        oow.coupon_code,
        oow.coupon_amount,
        oow.package_cost,
        oow.freight,
        oow.handicraft_fee,
        oow.other_fee,
        oow.total_freight,
        oow.remarks,
        oow.waybill_source_code,
        oow.waybill_source_name,
        oow.receive_pay_fee,
        oow.refund_time,
        oow.input_staff_by,
        oow.waybill_status_code,
        oow.sign_time,
        oow.paid_mode_name,
        oow.collect_time,
        oow.pick_network_id,
        oow.pick_network_code,
        oow.pick_network_name,
        oow.miss_flag,
        oow.printer_counterfoil,
        oow.quotetype_code,
        owe.is_privacy
    </sql>


    <select id="selectCompleteByWaybillNo" resultType="com.yl.applets.vo.OmsWaybillDetailVO">
        select
        <choose>
            <when test="dto.columns!=null and dto.columns.size()>0">
                <foreach collection="dto.columns" open="" separator="," close="" item="column">
                    oow.${column}
                </foreach>
                ,oow.id
                ,oow.waybill_no
                ,owe.is_privacy
            </when>
            <otherwise>
                <include refid="base_column_join"/>
            </otherwise>
        </choose>
        from YL_OMS_OMS_WAYBILL oow
        left join OMS_WAYBILL_EXTEND owe on oow.WAYBILL_NO = owe.WAYBILL_NO
        where oow.WAYBILL_NO=#{dto.waybillNo}
        and oow.ORDER_SOURCE_CODE!='D70'
    </select>

    <select id="selectWaybillNoList" resultType="com.yl.applets.vo.OmsWaybillDetailVO">
        select
        /*+ index(oow IDX_WAYBILL_NO_OMS_WAYBILL_0)*/
        oow.id,
        oow.waybill_no,
        oow.delivery_time,
        oow.collect_staff_code,
        oow.collect_staff_name,
        oow.customer_id,
        oow.customer_code,
        oow.customer_name,
        oow.express_type_id,
        oow.express_type_code,
        oow.express_type_name,
        oow.dispatch_code,
        oow.dispatch_name,
        oow.sender_name,
        oow.sender_mobile_phone,
        oow.sender_telphone,
        oow.sender_postal_code,
        oow.sender_country_id,
        oow.sender_country_name,
        oow.sender_province_id,
        oow.sender_province_name,
        oow.sender_city_id,
        oow.sender_city_name,
        oow.sender_area_id,
        oow.sender_area_name,
        oow.sender_township,
        oow.sender_street,
        oow.sender_detailed_address,
        oow.origin_id,
        oow.origin_code,
        oow.origin_name,
        oow.is_real_name,
        oow.id_no,
        oow.receiver_name,
        oow.receiver_mobile_phone,
        oow.receiver_telphone,
        oow.receiver_postal_code,
        oow.receiver_country_id,
        oow.receiver_country_name,
        oow.receiver_province_id,
        oow.receiver_province_name,
        oow.receiver_city_id,
        oow.receiver_city_name,
        oow.receiver_area_id,
        oow.receiver_area_name,
        oow.receiver_township,
        oow.receiver_street,
        oow.receiver_detailed_address,
        oow.destination_id,
        oow.destination_code,
        oow.destination_name,
        oow.goods_type_id,
        oow.goods_type_code,
        oow.goods_type_name,
        oow.goods_name,
        oow.package_number,
        oow.package_length,
        oow.package_wide,
        oow.package_high,
        oow.package_charge_weight,
        oow.package_total_weight,
        oow.package_total_volume,
        oow.package_volume,
        oow.box_standard_code,
        oow.box_standard_name,
        oow.box_number,
        oow.box_price,
        oow.settlement_id,
        oow.settlement_code,
        oow.settlement_name,
        oow.cod_need,
        oow.cod_money,
        oow.cod_fee,
        oow.is_cod_receive,
        oow.is_receive,
        oow.is_settlement,
        oow.currency_code,
        oow.currency_name,
        oow.insured,
        oow.insured_amount,
        oow.insured_fee,
        oow.is_need_receipt,
        oow.receipt_no,
        oow.coupon_code,
        oow.coupon_amount,
        oow.package_cost,
        oow.freight,
        oow.handicraft_fee,
        oow.other_fee,
        oow.total_freight,
        oow.remarks,
        oow.waybill_source_code,
        oow.waybill_source_name,
        oow.receive_pay_fee,
        oow.refund_time,
        oow.input_staff_by,
        oow.waybill_status_code,
        oow.sign_time,
        oow.paid_mode_name,
        oow.collect_time,
        oow.pick_network_id,
        oow.pick_network_code,
        oow.pick_network_name,
        oow.miss_flag,
        oow.printer_counterfoil,
        oow.quotetype_code
        from YL_OMS_OMS_WAYBILL oow
        where 1=1
        <if test="waybillNoList != null and waybillNoList.size() > 0 ">
             and (oow.WAYBILL_NO in
            <!-- 处理in的集合超过1000条时Oracle不支持的情况 -->
            <foreach collection="waybillNoList" item="waybillNo" index="index" open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 999">) OR oow.WAYBILL_NO IN (</when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{waybillNo}
            </foreach>
            )
        </if>
        and oow.INPUT_TIME >= trunc(sysdate)-90
        and oow.INPUT_TIME &lt;= sysdate
    </select>


    <select id="selectF_encrypt" resultType="java.lang.String">
        SELECT f_encrypt(#{mobile}) FROM dual where rownum=1
    </select>

</mapper>
