<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.market.MarketActivityMapper">
    <resultMap id="BaseResultMap" type="com.yl.applets.entity.MarketActivity">
        <result column="ID" property="id"/>
        <result column="CODE" property="code"/>
        <result column="NAME" property="name"/>
        <result column="TYPE" property="type"/>
        <result column="INTRODUCTION" property="introduction"/>
        <result column="STATUS" property="status"/>
        <result column="START_TIME" property="startTime"/>
        <result column="END_TIME" property="endTime"/>
        <result column="CREATE_BY" property="createBy"/>
        <result column="CREATE_BY_NAME" property="createByName"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_BY" property="updateBy"/>
        <result column="UPDATE_BY_NAME" property="updateByName"/>
        <result column="UPDATE_TIME" property="updateTime"/>
    </resultMap>
</mapper>