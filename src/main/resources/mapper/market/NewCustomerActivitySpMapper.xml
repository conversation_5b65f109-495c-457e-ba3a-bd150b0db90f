<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.market.NewCustomerActivitySpMapper">
    <resultMap id="BaseResultMap" type="com.yl.applets.entity.NewCustomerActivitySp">
        <result column="ID" property="id"/>
        <result column="SOURCE" property="source"/>
        <result column="NAME" property="name"/>
        <result column="PHONE" property="phone"/>
        <result column="FULL_ADDRESS" property="fullAddress"/>
        <result column="PROXY_NAME" property="proxyName"/>
        <result column="PROXY_CODE" property="proxyCode"/>
        <result column="FRANCHISEE_NAME" property="franchiseeName"/>
        <result column="FRANCHISEE_CODE" property="franchiseeCode"/>
        <result column="NETWORK_NAME" property="networkName"/>
        <result column="NETWORK_CODE" property="networkCode"/>
        <result column="IS_COOPERATED" property="isCooperated"/>
        <result column="CUSTOMER_CODE" property="customerCode"/>
        <result column="MALL_ID" property="mallId"/>
        <result column="STATUS" property="status"/>
        <result column="MARKET_ACTIVITY_ID" property="marketActivityId"/>
        <result column="MARKET_ACTIVITY_CODE" property="marketActivityCode"/>
        <result column="MARKET_ACTIVITY_NAME" property="marketActivityName"/>
        <result column="CREATE_BY" property="createBy"/>
        <result column="CREATE_BY_NAME" property="createByName"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_BY" property="updateBy"/>
        <result column="UPDATE_BY_NAME" property="updateByName"/>
        <result column="UPDATE_TIME" property="updateTime"/>
    </resultMap>
</mapper>