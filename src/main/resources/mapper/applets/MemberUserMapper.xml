<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.applets.MemberUserMapper">
    <resultMap id="oneInfoMap" type="com.yl.applets.entity.MemberUser">
        <id property="id" column="ID" />
        <result property="memberId" column="MEMBER_ID" />
        <result property="type" column="TYPE" />
        <result property="mobile" column="MOBILE" />
        <result property="growValue" column="GROW_VALUE" />
        <result property="scoreValue" column="SCORE_VALUE" />
        <result property="memberLevel" column="MEMBER_LEVEL" />
        <result property="unionid" column="UNIONID" />
        <result property="openid" column="OPENID" />
        <result property="nickName" column="NICK_NAME" />
        <result property="memberExpiresTime" column="MEMBER_EXPIRES_TIME" />
    </resultMap>


    <select id="getOneMemberUser" resultMap="oneInfoMap">
        select /*+ index(t MEMBER_USER_INDEX1)*/ t.ID,t.MOBILE,t.MEMBER_EXPIRES_TIME,t.MEMBER_ID from  YL_MEMBER_USER t where t.MEMBER_ID=#{memberId}
    </select>

</mapper>
