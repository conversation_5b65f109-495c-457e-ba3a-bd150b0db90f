<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.applets.LuckDrawMapper">

   <select id="selectCurrentActivity" resultType="com.yl.applets.dto.LuckDrawDto" parameterType="java.lang.String">
       select r.id,
              r.ACTIVITY_NAME,
              r.ACTIVITY_CODE,
              r.START_TIME,
              r.END_TIME,
              i.IMG_URL,
              i.WIN_IMG_URL,
              i.ITEM_LEVEL,
              i.ITEM_TYPE,
              i.NAME,
              i.AMOUNT,
              i.id as itemId,
              i.REMAINDER
           from YL_LUCK_DRAW r left join YL_LUCK_DRAW_ITEM i on r.id = i.ACTIVITY_ID
           where i.AMOUNT >0
           <if test="id !=null">
               and r.id = #{id}
           </if>
           <!--<if test="id ==null">
               and r.START_TIME <![CDATA[ <=  ]]> to_date(#{time}, 'yyyy-mm-dd hh24:mi:ss') and r.END_TIME >= to_date(#{time}, 'yyyy-mm-dd hh24:mi:ss')
           </if>-->
   </select>


</mapper>
