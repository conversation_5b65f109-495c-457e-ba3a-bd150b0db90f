<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.applets.ActivityVisitRecordMapper">

    <select id="statisticsActivityVisitQty" parameterType="java.lang.Long" resultType="com.yl.applets.vo.ActivityVisitRecordStatisticsVO">
        SELECT
        to_char ( activity_time, 'yyyy-MM-dd' ) AS statisticsDate,
        sum( CASE WHEN type = 1 THEN 1 ELSE 0 END ) AS exposureQuantity,
        sum( CASE WHEN type = 2 THEN 1 ELSE 0 END ) AS visitQuantity
        FROM
            YL_APPLETS_ACTIVITY_RECORD
        WHERE
            activity_id = #{activityId}
        GROUP BY
            to_char ( activity_time, 'yyyy-MM-dd' )
        ORDER BY
            to_char ( activity_time, 'yyyy-MM-dd' ) DESC
    </select>
</mapper>