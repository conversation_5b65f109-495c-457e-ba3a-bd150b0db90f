<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.market.MarketActivityMapper">


    <select id="getInfoById" resultType="com.yl.applets.entity.MarketActivity">
        select START_TIME,END_TIME,STATUS from YL_MARKET_ACTIVITY where ID IN
        <foreach item="id" collection="ids" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </select>

</mapper>
