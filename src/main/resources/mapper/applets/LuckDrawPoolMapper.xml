<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.applets.LuckDrawPoolMapper">

   <select id="getListByTime" resultType="com.yl.applets.entity.LuckDrawPool" parameterType="java.lang.String">
       select * from YL_LUCK_DRAW_POOL where ACTIVITY_ID=#{activityId} and  "DAY" = #{day} and prize_num >0
   </select>

    <delete id="deleteByTime" parameterType="java.lang.String">
      delete YL_LUCK_DRAW_POOL where release_time between #{startTime} and #{endTime}
    </delete>

    <update id="updateByPrize" parameterType="com.yl.applets.entity.LuckDrawPool">
      update Y<PERSON>_LUCK_DRAW_POOL set prize_num = 0, number_id = #{dto.numberId} , get_time =#{dto.getTime} where id =#{dto.id} and prize_num>0
    </update>

</mapper>
