<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.applets.ActivityConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yl.applets.entity.ActivityConfig">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="title" property="title"/>
        <result column="type" property="type"/>
        <result column="sort" property="sort"/>
        <result column="image_path" property="imagePath"/>
        <result column="activity_line" property="activityLine"/>
        <result column="status" property="status"/>
        <result column="on_time" property="onTime"/>
        <result column="off_time" property="offTime"/>
        <result column="release_time" property="releaseTime"/>
        <result column="version_no" property="versionNo"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_by_code" property="createByCode"/>
        <result column="create_by_name" property="createByName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by_code" property="updateByCode"/>
        <result column="update_by_name" property="updateByName"/>
        <result column="update_time" property="updateTime"/>
        <result column="visit_quantity" property="visitQuantity"/>
        <result column="exposure_quantity" property="exposureQuantity"/>
    </resultMap>


    <select id="countActivityConfig" resultType="java.lang.Long"
            parameterType="com.yl.applets.dto.ActivityConfigQueryDTO">
        SELECT
        COUNT(*)
        FROM
        YL_APPLETS_ACTIVITY_CONFIG t
        <where>
            t.is_delete = 1
            <if test="title != null and title != ''">
                and t.title like CONCAT(CONCAT('%',#{title}),'%')
            </if>
            <choose>
                <when test="type != null">
                    and t.type = #{type}
                </when>
                <otherwise>
                    and t.type between 1  and  80
                </otherwise>
            </choose>
            <if test="sort != null">
                and t.sort = #{sort}
            </if>

            <if test="status != null">
                and t.status = #{status}
            </if>
        </where>
    </select>

    <select id="countActivityConfigPromotion" resultType="java.lang.Long"
            parameterType="com.yl.applets.dto.ActivityConfigQueryDTO">
        SELECT
        COUNT(*)
        FROM
        YL_APPLETS_ACTIVITY_CONFIG t
        <where>
            t.is_delete = 1
            <if test="title != null and title != ''">
                and t.title like CONCAT(CONCAT('%',#{title}),'%')
            </if>
            <choose>
                <when test="type != null">
                    and t.type = #{type}
                </when>
                <otherwise>
                    and t.type between 81  and  99
                </otherwise>
            </choose>
            <if test="sort != null">
                and t.sort = #{sort}
            </if>

            <if test="status != null">
                and t.status = #{status}
            </if>
        </where>
    </select>


    <select id="selectActivityConfigPage" resultType="com.yl.applets.vo.ActivityConfigVO"
            parameterType="com.yl.applets.dto.ActivityConfigQueryDTO">
        select a.* from (
            select rownum as rn ,t.* from (
                SELECT
                t.id,
                t.code,
                t.title,
                t.type,
                t.sort,
                t.image_path,
                t.activity_line,
                t.status,
                t.on_time,
                t.off_time,
                t.release_time,
                t.version_no,
                t.create_by_name,
                t.create_time,
                t.visit_quantity,
                t.exposure_quantity,
                t.is_area
                FROM
                YL_APPLETS_ACTIVITY_CONFIG t
                <where>
                    t.is_delete = 1

                    <if test="title != null and title != ''">
                        and t.title like CONCAT(CONCAT('%',#{title}),'%')
                    </if>
                    <choose>
                        <when test="type != null">
                            and t.type = #{type}
                        </when>
                        <otherwise>
                            and t.type between 1  and  80
                        </otherwise>
                    </choose>
                    <if test="sort != null">
                        and t.sort = #{sort}
                    </if>

                    <if test="status != null">
                        and t.status = #{status}
                    </if>
                </where>
                ORDER BY t.create_time DESC
            ) t
        ) a where a.rn  <![CDATA[ <= ]]> #{current} * #{size} and a.rn>(#{current}-1) * #{size}
    </select>

    <select id="selectActivityConfigPagePromotion" resultType="com.yl.applets.vo.ActivityConfigVO"
            parameterType="com.yl.applets.dto.ActivityConfigQueryDTO">
        select a.* from (
        select rownum as rn ,t.* from (
        SELECT
        t.id,
        t.code,
        t.title,
        t.type,
        t.sort,
        t.image_path,
        t.activity_line,
        t.status,
        t.on_time,
        t.off_time,
        t.release_time,
        t.version_no,
        t.create_by_name,
        t.create_time,
        t.visit_quantity,
        t.exposure_quantity,
        t.is_area
        FROM
        YL_APPLETS_ACTIVITY_CONFIG t
        <where>
            t.is_delete = 1

            <if test="title != null and title != ''">
                and t.title like CONCAT(CONCAT('%',#{title}),'%')
            </if>
            <choose>
                <when test="type != null">
                    and t.type = #{type}
                </when>
                <otherwise>
                    and t.type between 81  and  99
                </otherwise>
            </choose>
            <if test="sort != null">
                and t.sort = #{sort}
            </if>

            <if test="status != null">
                and t.status = #{status}
            </if>
        </where>
        ORDER BY t.create_time DESC
        ) t
        ) a where a.rn  <![CDATA[ <= ]]> #{current} * #{size} and a.rn>(#{current}-1) * #{size}
    </select>

    <select id="selectActivityConfigList" resultType="com.yl.applets.vo.ActivityConfigVO"
            parameterType="com.yl.applets.dto.ActivityConfigQueryDTO">
        SELECT
        t.id,
        t.code,
        t.title,
        t.type,
        t.sort,
        t.image_path,
        t.activity_line,
        t.status,
        t.on_time,
        t.off_time,
        t.release_time,
        t.version_no,
        t.create_by_name,
        t.create_time,
        t.news_text,
        t.button_text,
        t.button_way,
        t.is_area,
        t.WX_APP_ID
        FROM
        YL_APPLETS_ACTIVITY_CONFIG t
        <where>
            t.is_delete = 1
            and t.status = 2
            <if test="title != null and title != ''">
                and t.title like CONCAT(CONCAT('%',#{title}),'%')
            </if>
            <choose>
                <when test="type != null">
                    and t.type = #{type}
                </when>
                <otherwise>
                    and t.type between 1  and  80
                </otherwise>
            </choose>
            <if test="sort != null">
                and t.sort = #{sort}
            </if>

            <if test="type != null and type ==2">
                ORDER BY t.sort asc
            </if>
        </where>
    </select>


    <select id="selectActivityConfigListPromotion" resultType="com.yl.applets.vo.ActivityConfigVO"
            parameterType="com.yl.applets.dto.ActivityConfigQueryDTO">
        SELECT
        t.id,
        t.code,
        t.title,
        t.type,
        t.sort,
        t.image_path,
        t.activity_line,
        t.status,
        t.on_time,
        t.off_time,
        t.release_time,
        t.version_no,
        t.create_by_name,
        t.create_time,
        t.news_text,
        t.button_text,
        t.button_way,
        t.is_area
        FROM
        YL_APPLETS_ACTIVITY_CONFIG t
        <where>
            t.is_delete = 1
            and t.status = 2
            <if test="title != null and title != ''">
                and t.title like CONCAT(CONCAT('%',#{title}),'%')
            </if>
            <choose>
                <when test="type != null">
                    and t.type = #{type}
                </when>
                <otherwise>
                    and t.type between 81  and  99
                </otherwise>
            </choose>
            <if test="sort != null">
                and t.sort = #{sort}
            </if>

            <if test="type == 98 or type ==95 ">
                ORDER BY t.sort asc
            </if>
        </where>
    </select>

    <select id="getDetailById" resultType="com.yl.applets.vo.ActivityConfigVO">
        select t.id,
        t.code,
        t.title,
        t.type,
        t.sort,
        t.image_path,
        t.activity_line,
        t.status,
        t.on_time,
        t.off_time,
        t.release_time,
        t.version_no,
        t.create_by_name,
        t.create_time,
        t.visit_quantity,
        t.exposure_quantity,
        t.news_text,
        t.button_text,
        t.button_way,
        t.is_area,
        t.WX_APP_ID
        from YL_APPLETS_ACTIVITY_CONFIG t where t.id = #{id}
    </select>

    <select id="queryActivityConfigByCondition" resultMap="BaseResultMap"
            parameterType="com.yl.applets.entity.ActivityConfig">
        select t.id,
        t.code,
        t.title,
        t.type,
        t.sort,
        t.image_path,
        t.activity_line,
        t.status,
        t.on_time,
        t.off_time,
        t.release_time,
        t.version_no,
        t.create_by_code,
        t.create_by_name,
        t.create_time,
        t.update_by_code,
        t.update_by_name,
        t.update_time
        from YL_APPLETS_ACTIVITY_CONFIG t
        where t.is_delete = 1 and t.status = 2 and t.type = #{type}
        <if test="sort != null and sort !=''">
            and t.sort = #{sort}
        </if>
    </select>

    <update id="updateActivityConfigByVisitRecord" parameterType="com.yl.applets.dto.ActivityVisitRecordDTO">
        update YL_APPLETS_ACTIVITY_CONFIG t set
        <if test="type == 2">
            t.visit_quantity = t.visit_quantity + 1,
        </if>
        <if test="type == 1">
            t.exposure_quantity = t.exposure_quantity + 1,
        </if>
        t.update_time = sysdate
        where t.id = #{activityId}
    </update>
</mapper>