<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.applets.WxUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yl.applets.entity.WxUser">
        <id column="id" property="id" />
        <result column="unionid" property="unionid" />
        <result column="openid" property="openid" />
        <result column="nick_name" property="nickName" />
        <result column="avatar_url" property="avatarUrl" />
        <result column="mobile" property="mobile" />
        <result column="gender" property="gender" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_delete" property="isDelete" />
    </resultMap>
    <select id="getIdsByIdAndNumberId" parameterType="java.lang.Integer" resultType="java.lang.Long">
        select /*+ index(t WX_USER_IDX5)*/ t.id from YL_APPLETS_WX_USER t where t.is_delete = 0 and t.number_id = #{numberId}
    </select>

    <select id="maxNumberId" resultType="java.lang.Integer">
        select max(number_id) from YL_APPLETS_WX_USER
    </select>

    <select id="maxJYUserId" resultType="java.lang.Integer">
        select max(JY_USER_ID) from YL_APPLETS_WX_USER
    </select>

    <update id="updateJyUserId" >
      update YL_APPLETS_WX_USER set JY_USER_ID=#{jyUserId}
        where ID IN
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </update>


    <select id="getIdListByIdNumberId" parameterType="java.lang.Integer" resultType="java.lang.Long">
        select /*+ index(t WX_USER_IDX5)*/ t.ID from YL_APPLETS_WX_USER t where  t.NUMBER_ID = #{numberId}
    </select>
</mapper>
