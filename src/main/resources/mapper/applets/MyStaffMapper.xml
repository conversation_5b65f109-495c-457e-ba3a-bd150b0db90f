<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.applets.MyStaffMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yl.applets.entity.MyStaff">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="name" property="name" />
        <result column="mobile" property="mobile" />
        <result column="network_id" property="networkId" />
        <result column="network_code" property="networkCode" />
        <result column="network_name" property="networkName" />
        <result column="staff_no" property="staffNo" />
        <result column="start_business_time" property="startBusinessTime" />
        <result column="end_business_time" property="endBusinessTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>


</mapper>
