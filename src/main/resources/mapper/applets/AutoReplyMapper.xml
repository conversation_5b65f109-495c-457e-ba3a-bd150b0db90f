<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.applets.AutoReplyMapper">

    <select id="selectAutoReply" resultType="com.yl.applets.vo.AutoReplyVO">
        select
        t.id,
        t.reply_content
        from YL_APPLETS_AUTO_REPLY t where 1 = 1
        <if test="replyType != null and replyType != ''">
            and t.reply_Type = #{replyType}
        </if>
    </select>

    <select id="selectAutoReplyList" resultType="com.yl.applets.vo.AutoReplyVO"
            parameterType="com.yl.applets.dto.AutoReplyQueryDto">
        select
        t.id,
        t.rule_name,
        t.key_word,
        t.reply_content
        from YL_APPLETS_AUTO_REPLY t where t.reply_Type = 3
        <if test="dto.keyWord != null and dto.keyWord != ''">
            and t.rule_name like CONCAT(CONCAT('%',#{dto.keyWord}),'%')
            or t.key_word like CONCAT(CONCAT('%',#{dto.keyWord}),'%')
        </if>
        ORDER BY t.update_time DESC
    </select>
</mapper>