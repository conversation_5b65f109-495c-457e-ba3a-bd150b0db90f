<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.applets.AddressMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yl.applets.entity.Address">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="name" property="name"/>
        <result column="mobile" property="mobile"/>
        <result column="province_id" property="provinceId"/>
        <result column="province_name" property="provinceName"/>
        <result column="city_id" property="cityId"/>
        <result column="city_name" property="cityName"/>
        <result column="area_id" property="areaId"/>
        <result column="area_name" property="areaName"/>
        <result column="type" property="type"/>
        <result column="detailed_address" property="detailedAddress"/>
        <result column="is_default" property="isDefault"/>
        <result column="country_id" property="countryId"/>
        <result column="is_enable" property="isEnable"/>
        <result column="is_delete" property="isDelete"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by_name" property="createByName"/>
        <result column="company_name" property="companyName"/>
        <result column="update_by_name" property="updateByName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="version" property="version"/>
        <result column="sort" property="sort"/>
        <result column="address_sign" property="addressSign"/>
    </resultMap>

    <select id="getPages" resultType="com.yl.applets.vo.AddressVo">
        SELECT
        t.id,
        t.name,
        t.mobile,
        t.province_id,
        t.province_name,
        t.city_id,
        t.city_name,
        t.area_id,
        t.area_name,
        t.detailed_address,
        t.is_default,
        t.company_name,
        t.type,
        t.address_sign
        FROM
        YL_APPLETS_ADDRESS t
        <where>
            t.is_delete = 1
            and t.is_enable = 1

            <if test="type != null">
                and t.type = #{type}
            </if>
            <if test="userId != null">
                and t.user_id in
                (
                select id from YL_APPLETS_WX_USER where number_id = (select number_id from YL_APPLETS_WX_USER where id =#{userId} )
                )
            </if>
            <if test="keyword != null and keyword != ''">
                and (t.name like CONCAT(CONCAT('%',#{keyword}),'%') or t.mobile like CONCAT(CONCAT('%',#{keyword}),'%'))
            </if>
            <if test="signs != null and signs.size() > 0">
                and t.address_sign in (
                    <foreach collection="signs" item="sign" separator="," >
                        #{sign}
                    </foreach>
                )
            </if>
        </where>
        ORDER BY t.is_default,t.create_time DESC
    </select>

    <update id="updateToNonDefault">
        update YL_APPLETS_ADDRESS
        set is_default = 2
        where id in
        <foreach item="id" collection="ids" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </update>

    <update id="disabledAddress">
        update YL_APPLETS_ADDRESS
        set is_delete = 2
        where id in
        <foreach item="id" collection="ids" separator="," open="(" close=")" index="">
            #{id}
        </foreach>
    </update>

    <select id="getDefaultByMobile" resultType="com.yl.applets.entity.Address">
        select *
        from YL_APPLETS_ADDRESS
        where USER_ID IN (SELECT id FROM YL_APPLETS_WX_USER yawu WHERE MOBILE = #{mobile,jdbcType=VARCHAR})
          and is_default = 1
    </select>

</mapper>
