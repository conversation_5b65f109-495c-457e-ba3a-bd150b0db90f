<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.market.NewCustomerActivitySpMapper">



    <select id="getInfoByPhone" resultType="string">
        select /*+ index(t CAS_PHONE_CODE_IDX)*/    t.MARKET_ACTIVITY_ID from YL_NEW_CUSTOMER_ACTIVITY_SP t where t.PHONE=#{phone}
    </select>

</mapper>
