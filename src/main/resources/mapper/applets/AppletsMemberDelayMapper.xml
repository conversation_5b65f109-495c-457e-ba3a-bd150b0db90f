<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yl.applets.mapper.applets.AppletsMemberDelayMapper">

    <select id="qryMemberCount" resultType="integer">
        SELECT count(*)
        FROM YL_MEMBER_USER
        WHERE MEMBER_ID = #{memberId}
          AND MEMBER_EXPIRES_TIME - 180
            BETWEEN TO_DATE(#{startTime} || ' 00:00:00', 'yyyy-mm-dd hh24:mi:ss') AND TO_DATE(#{endTime} || ' 23:59:59', 'yyyy-mm-dd hh24:mi:ss')
          AND EXTENSION_COUNT = #{number}
    </select>
</mapper>