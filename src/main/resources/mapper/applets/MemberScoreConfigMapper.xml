<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.applets.MemberScoreConfigMapper">
    <!-- 通用查询映射结果 -->
    <!--<resultMap id="BaseResultMap" type="com.yl.applets.entity.MemberScoreConfig">
        <id column="id" property="id"/>
        <result column="member_action" property="memberAction"/>
        <result column="type" property="type"/>
        <result column="grow_value" property="growValue"/>
        <result column="grow_limit" property="growLimit"/>
        <result column="sorce_value" property="scoreValue"/>
        <result column="sorce_limit" property="scoreLimit"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>-->

    <select id="getMemberConfigAndStatus" resultType="com.yl.applets.vo.MemberScoreConfigVo" parameterType="com.yl.applets.vo.MemberScoreConfigVo">
        select
        #{dto.memberId} as memberId,
        T.TYPE,
        T.MEMBER_ACTION,
        T.GROW_VALUE,
        T.GROW_LIMIT,
        T.SCORE_VALUE,
        T.SCORE_LIMIT
        from
        YL_MEMBER_SCORE_CONFIG T
        <where>
            T.IS_DELETE = 0
            <if test="dto.type != null and dto.type != ''">
                and T.type = #{dto.type}
            </if>
        </where>
    </select>


</mapper>