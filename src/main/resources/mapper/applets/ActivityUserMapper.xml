<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yl.applets.mapper.applets.ActivityUserRecordMapper">


    <select id="getPrizeInfo" parameterType="java.lang.Integer" resultType="com.yl.applets.dto.ActivityPrizeRecordDto">
          SELECT  u.ID,
                  u.ACTIVITY_ID,
                  u.ACTIVITY_NAME,
                  u.ACTIVITY_CODE,
                  u.IS_WIN,
                  p.USER_MOBILE,
                  y.ID as itemId,
                  y.WIN_IMG_URL,
                  y.NAME as winName,
                  p.WAYBILL_NO as waybillNo
          FROM YL_ACTIVITY_USER_RECORD u LEFT JOIN YL_ACTIVITY_PRIZE_RECORD p ON u.ID = p.RECORD_ID  LEFT JOIN YL_LUCK_DRAW_ITEM y ON u.ITEM_ID = y.ID
          WHERE u.NUMBER_ID = #{numberId} AND u.TYPE = #{type}
    </select>

</mapper>