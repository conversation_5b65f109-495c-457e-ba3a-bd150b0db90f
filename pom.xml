<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yl-jms-applets</artifactId>
    <groupId>com.yl</groupId>
    <version>1.0.1-RELEASE</version>

    <!--Spring Boot配置-->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.0.1.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <!--配置参数-->
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-boot.version>2.0.1.RELEASE</spring-boot.version>
        <spring-cloud.version>Finchley.RELEASE</spring-cloud.version>
        <mybatis-puls.version>3.0.6</mybatis-puls.version>
        <mysql.version>5.1.46</mysql.version>
        <lombok.version>1.16.20</lombok.version>
        <druid-spring-boot-starter.version>1.1.10</druid-spring-boot-starter.version>
        <jedis.version>2.9.0</jedis.version>
        <guava.version>27.0-jre</guava.version>
        <springfox-swagger2.version>2.9.2</springfox-swagger2.version>
        <springfox-swagger-ui.version>2.9.2</springfox-swagger-ui.version>
        <springfox-swagger-common.version>2.9.2</springfox-swagger-common.version>
        <swagger-annotations.version>1.5.22</swagger-annotations.version>
        <swagger-models.version>1.5.22</swagger-models.version>
        <fastjson.version>1.2.69_noneautotype</fastjson.version>
        <junit.version>4.12</junit.version>
        <dom4j.version>1.6.1</dom4j.version>
        <slf4j.version>1.7.25</slf4j.version>
        <spring-data-redis.version>2.1.5.RELEASE</spring-data-redis.version>
        <spring-boot-admin-starter-server.version>2.0.6</spring-boot-admin-starter-server.version>
        <!--        <spring-security-oauth2.version>2.0.14.RELEASE</spring-security-oauth2.version>-->
        <jjwt.version>0.9.0</jjwt.version>
        <jackson-core.version>2.9.9</jackson-core.version>
        <!--        <thymeleaf-extras-springsecurity4.version>3.0.2.RELEASE</thymeleaf-extras-springsecurity4.version>-->
        <hutool.version>5.7.4</hutool.version>
        <redisson.version>3.10.7</redisson.version>
        <apollo-client.version>1.4.0</apollo-client.version>
        <!--        <caffeine.version>2.6.2</caffeine.version>-->
        <hystrix.version>1.4.4.RELEASE</hystrix.version>
        <fegin.version>3.0.3</fegin.version>
        <feign.starter.version>1.4.7.RELEASE</feign.starter.version>
        <orika.version>1.5.2</orika.version>
        <mybatis-spring-boot-starter.version>1.3.4</mybatis-spring-boot-starter.version>
        <yl-base.version>1.0.1-RELEASE</yl-base.version>
        <yl-redis.version>2.0.1-RELEASE</yl-redis.version>
        <yl-mongo.version>1.0.1-RELEASE</yl-mongo.version>
        <yl-sharding-jdbc-spring-boot-starter.version>4.0.0-RC1</yl-sharding-jdbc-spring-boot-starter.version>
        <mongo.driver>3.10.2</mongo.driver>
        <yl-file-model-api.version>1.0.3-RELEASE</yl-file-model-api.version>
        <yl-msdm-model-api.version>2.1.0-RELEASE</yl-msdm-model-api.version>
        <yl-lmdm-model-api.version>1.5.2.11-RELEASE</yl-lmdm-model-api.version>
        <yl-ops-model-api.version>1.1.33-RELEASE</yl-ops-model-api.version>
<!--        <yl-scm-model.version>1.0.1-RELEASE</yl-scm-model.version>-->
        <yl-spmo-model-api.version>1.1.0-RELEASE</yl-spmo-model-api.version>
        <yl-official-model.version>1.0.1-RELEASE</yl-official-model.version>
        <es-model.version>1.0.8-RELEASE</es-model.version>
        <waybill-api-model.version>1.0.1-RELEASE</waybill-api-model.version>
        <order-api-model.version>1.0.1-RELEASE</order-api-model.version>
        <file-model-api.version>2.0.0-RELEASE</file-model-api.version>
        <operation-log.version>1.0.4-RELEASE</operation-log.version>
    </properties>


    <!--Spring Cloud 版本序列配置-->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-parent</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>

        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.13.58.ALL</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>yl-framework</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.yl</groupId>-->
<!--            <artifactId>es-model</artifactId>-->
<!--            <version>${es-model.version}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.yl</groupId>-->
<!--                    <artifactId>base</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>redis</artifactId>
            <version>${yl-redis.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
            <version>5.3.7.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotations.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>${apollo-client.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.version}</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${springfox-swagger2.version}</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${springfox-swagger-ui.version}</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotations.version}</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>${swagger-models.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid-spring-boot-starter.version}</version>
        </dependency>
        <dependency>
            <groupId>ma.glasnost.orika</groupId>
            <artifactId>orika-core</artifactId>
            <version>${orika.version}</version>
        </dependency>

        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>ojdbc7</artifactId>
            <version>1.0.0</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.apache.shardingsphere</groupId>-->
<!--            <artifactId>sharding-jdbc-spring-boot-starter</artifactId>-->
<!--            <version>${yl-sharding-jdbc-spring-boot-starter.version}</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.yl</groupId>-->
<!--            <artifactId>msdm-model-api</artifactId>-->
<!--            <version>${yl-msdm-model-api.version}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.yl</groupId>-->
<!--                    <artifactId>base</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.yl</groupId>-->
<!--            <artifactId>lmdm-model-api</artifactId>-->
<!--            <version>${yl-lmdm-model-api.version}</version>-->
<!--            <scope>compile</scope>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.yl</groupId>-->
<!--                    <artifactId>base</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.yl</groupId>-->
<!--            <artifactId>ops-model-api</artifactId>-->
<!--            <version>${yl-ops-model-api.version}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.yl</groupId>-->
<!--                    <artifactId>base</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.yl</groupId>-->
<!--            <artifactId>scm-model</artifactId>-->
<!--            <version>${yl-scm-model.version}</version>-->
<!--            <scope>compile</scope>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.yl</groupId>-->
<!--                    <artifactId>base</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>intf-log-record</artifactId>
            <version>1.0.1-RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>3.6.0</version>
        </dependency>

        <dependency>
            <groupId>com.github.jedis-lock</groupId>
            <artifactId>jedis-lock</artifactId>
            <version>1.0.0</version>
        </dependency>



        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-rabbit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
<!--        feignform上传-->
        <dependency>
            <groupId>io.github.openfeign.form</groupId>
            <artifactId>feign-form</artifactId>
            <version>3.0.3</version>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign.form</groupId>
            <artifactId>feign-form-spring</artifactId>
            <version>3.0.3</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.yl</groupId>-->
<!--            <artifactId>file-model-api</artifactId>-->
<!--            <version>${yl-file-model-api.version}</version>-->
<!--            <scope>compile</scope>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.yl</groupId>-->
<!--                    <artifactId>base</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.yl</groupId>-->
<!--            <artifactId>official-model</artifactId>-->
<!--            <version>${yl-official-model.version}</version>-->
<!--            <scope>compile</scope>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.yl</groupId>-->
<!--                    <artifactId>base</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>android-json</artifactId>
                    <groupId>com.vaadin.external.google</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-captcha</artifactId>
            <version>3.1.322</version>
        </dependency>

        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <!-- go to https://search.maven.org/search?q=tencentcloud-sdk-java and get the latest version. -->
            <!-- 请到https://search.maven.org/search?q=tencentcloud-sdk-java查询最新版本 -->
            <version>3.0.93</version>
        </dependency>

<!--        代码生成-->

<!--        &lt;!&ndash; velocity 模板引擎, 默认 &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.apache.velocity</groupId>-->
<!--            <artifactId>velocity-engine-core</artifactId>-->
<!--            <version>2.0</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; freemarker 模板引擎 &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.freemarker</groupId>-->
<!--            <artifactId>freemarker</artifactId>-->
<!--            <version>2.3.28</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; mysql数据库连接驱动 &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>mysql</groupId>-->
<!--            <artifactId>mysql-connector-java</artifactId>-->
<!--            <version>5.1.31</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash;阿里druid数据库链接依赖&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>druid</artifactId>-->
<!--            <version>1.1.12</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.1</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.baidu.aip/java-sdk -->
        <dependency>
            <groupId>com.baidu.aip</groupId>
            <artifactId>java-sdk</artifactId>
            <version>4.13.0</version>
        </dependency>

        <!-- 引入org.json所需依赖 -->
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20160810</version>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
            <version>3.6.0</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.yl</groupId>-->
<!--            <artifactId>order-api-model</artifactId>-->
<!--            <version>${order-api-model.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.0.6</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.yl</groupId>-->
<!--            <artifactId>waybill-api-model</artifactId>-->
<!--            <version>${waybill-api-model.version}</version>-->
<!--        </dependency>-->
        <!--Kaptcha是一个基于SimpleCaptcha的验证码开源项目-->
        <dependency>
            <groupId>com.github.penggle</groupId>
            <artifactId>kaptcha</artifactId>
            <version>2.3.2</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.1.6</version>
        </dependency>

        <dependency>
            <groupId>com.yl.ccm</groupId>
            <artifactId>chaos-client-boot-starter</artifactId>
            <version>1.2.3-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.mzlion</groupId>
            <artifactId>easy-okhttp</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>file-sdk</artifactId>
            <version>2.0.8-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>yl-platform-gray-starter</artifactId>
            <version>1.2.0-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.yl</groupId>
            <artifactId>yl-jms-btf-thirdparty-common</artifactId>
            <version>1.0.0</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!--
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>
            -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>snapshots</id>
            <repositories>
                <repository>
                    <id>snapshots</id>
                    <name>Snapshots</name>
                    <url>https://maven.jtexpress.com.cn/nexus/content/repositories/snapshots</url>
                </repository>
            </repositories>
        </profile>
        <profile>
            <id>alphas</id>
            <repositories>
                <repository>
                    <id>Alpha</id>
                    <name>Alpha</name>
                    <url>https://maven.jtexpress.com.cn/nexus/content/repositories/Alpha</url>
                </repository>
            </repositories>
        </profile>
        <profile>
            <id>betas</id>
            <repositories>
                <repository>
                    <id>Beta</id>
                    <name>Beta</name>
                    <url>https://maven.jtexpress.com.cn/nexus/content/repositories/Beta</url>
                </repository>
            </repositories>
        </profile>
        <profile>
            <id>releases</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <repositories>
                <repository>
                    <id>releases</id>
                    <name>Releases</name>
                    <url>https://maven.jtexpress.com.cn/nexus/content/repositories/releases</url>
                </repository>
            </repositories>
        </profile>
    </profiles>
</project>
